//
//  GGPermissionStatus.swift
//  Plugin
//
//  Created by kiruthikayini S on 21/03/24.
//  Copyright © 2024 Max Lynch. All rights reserved.
//

import Foundation

public struct GGPermissionStatus: Codable {
    var bluetooth: GGPermissionState
    var bluetoothSwitch: GGPermissionState
    var nearByDevices: GGPermissionState?
    var locationSwitch: GGPermissionState
    var location: GGPermissionState
    var notification: GGPermissionState
    var camera: GGPermissionState
}
