//
//  GGBTSetting.swift
//  Plugin
//
//  Created by <PERSON><PERSON> on 17/04/23.
//  Copyright © 2023 Max Lynch. All rights reserved.
//

import Foundation

public struct GGBTSetting: Codable {
    public let key: GGBTSettingType
    public let value: GGBTSettingValue
    
    public init(key: GGBTSettingType, value: GGBTSettingValue) {
        self.key = key
        self.value = value
    }
}

public enum GGBTSettingValue: Codable {
  case bool(Bool)
  case int(Int)
  case string(String)
}

