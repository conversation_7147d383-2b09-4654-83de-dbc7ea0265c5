//
//  GGDevicePreference.swift
//  Plugin
//
//  Created by <PERSON><PERSON> on 25/04/23.
//  Copyright © 2023 Max Lynch. All rights reserved.
//

import Foundation

public struct GGDevicePreference: Codable {
    public var displayName: String?
    public var displayMetrics: [String]?
    public var shouldMeasureImpedance: Bool?
    public var shouldMeasurePulse: Bool?
    public var timeFormat: String?

    public init(
        displayName: String? = nil,
        displayMetrics: [String]? = nil,
        shouldMeasureImpedance: Bool? = nil,
        shouldMeasurePulse: Bool? = nil,
        timeFormat: String? = nil
    ) {
        self.displayName = displayName
        self.displayMetrics = displayMetrics
        self.shouldMeasureImpedance = shouldMeasureImpedance
        self.shouldMeasurePulse = shouldMeasurePulse
        self.timeFormat = timeFormat
    }
}


public struct GGDeviceLogResponse<T: Codable>: Codable {
    public var logs: [DeviceLog]
}

public struct DeviceLog: Codable {
    public var macAddress: String?
    public var log: String?
}
