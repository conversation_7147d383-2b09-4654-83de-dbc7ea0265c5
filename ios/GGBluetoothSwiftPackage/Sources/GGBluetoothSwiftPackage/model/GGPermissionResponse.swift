//
//  GGPermissionResponse.swift
//  Plugin
//
//  Created by kiruthikayi<PERSON> S on 02/04/24.
//  Copyright © 2024 Max Lynch. All rights reserved.
//

import Foundation


public struct GGPermissionResponse: Codable {
      var type: GGPermissionType
      var data: GGPermissionState
}

public struct GGPermissionResponseData: GGScanResponseData {
    public let permissions: [GGPermissionType: GGPermissionState]
    
    // Mark the initializer as public
       public init(permissions: [GGPermissionType: GGPermissionState]) {
           self.permissions = permissions
       }
}
