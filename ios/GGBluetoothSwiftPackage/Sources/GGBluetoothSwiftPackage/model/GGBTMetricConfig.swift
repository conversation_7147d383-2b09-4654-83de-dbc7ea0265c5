//
//  GGBTMetricConfig.swift
//  Plugin
//
//  Created by <PERSON><PERSON> on 17/04/23.
//  Copyright © 2023 Max Lynch. All rights reserved.
//

import Foundation

public struct GGBTMetricConfig: Codable {
    public let id: String
    public let label: String
    public let isEnabled: Bool
    
    public init(id: String, label: String, isEnabled: Bool) {
        self.id = id
        self.label = label
        self.isEnabled = isEnabled
    }
}
