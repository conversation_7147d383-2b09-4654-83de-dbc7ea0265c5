//
//  GGDeviceDetails.swift
//  Plugin
//
//  Created by <PERSON><PERSON> Chittibabu on 19/04/23.
//  Copyright © 2023 Max Lynch. All rights reserved.
//

import Foundation

public struct GGDeviceDetails: Codable, GGScanResponseData {
    public var manufacturerName: String?
    public  var modelNumber: String?
    public var serialNumber: String?
    public var firmwareRevision: String?
    public var hardwareRevision: String?
    public var softwareRevision: String?
    public var systemID: String?
    public var deviceName: String
    public var broadcastId: String?
    public let broadcastIdString: String
    public var password: String?
    public var macAddress: String
    public var wifiMacAddress: String?
    public var identifier: String
    public var protocolType: String?
    public var isWifiConfigured: Bool?
    public var sessionImpedanceSwitchState: Bool?
    public var impedanceSwitchState: Bool?
    public var startAnimationState: Bool?
    public var endAnimationState: Bool?
    public var batteryLevel: Int?
    public var userNumber: Int?
    public var heartRateState: Bool?
   
}

