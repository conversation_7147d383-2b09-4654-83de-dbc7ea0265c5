//
//  GGBTDevice.swift
//  Plugin
//
//  Created by <PERSON><PERSON> on 17/04/23.
//  Copyright © 2023 Max <PERSON>. All rights reserved.
//

import Foundation

public class GGBTDevice: Codable, Equatable, Hashable {
    public let id: UUID
    public var name: String
    public var broadcastId: String
    public var password: String?
    public var token: String?
    public var userNumber: Int?
    public var preference: GGDevicePreference?
    public let syncAllData: Bool?
    public var batteryLevel: Int?
    public var protocolType: String?
    public var macAddress: String?

    public init(name: String, broadcastId: String, password: String?, token: String?, userNumber: Int?, preference: GGDevicePreference?, syncAllData: Bool?, batteryLevel: Int?, protocolType: String?, macAddress: String?) {
        self.id = UUID()
        self.name = name
        self.broadcastId = broadcastId
        self.password = password
        self.token = token
        self.userNumber = userNumber
        self.preference = preference
        self.syncAllData = syncAllData
        self.batteryLevel = batteryLevel
        self.protocolType = protocolType
        self.macAddress = macAddress
    }

    // Equatable implementation
    public static func == (lhs: GGBTDevice, rhs: GGBTDevice) -> Bool {
        return lhs.id == rhs.id
    }

    // Hashable implementation
    public func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}
