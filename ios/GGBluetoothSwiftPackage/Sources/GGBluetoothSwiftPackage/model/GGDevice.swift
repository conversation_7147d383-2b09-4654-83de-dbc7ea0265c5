//
//  GGDevice.swift
//  Plugin
//
//  Created by <PERSON><PERSON> on 17/04/23.
//  Copyright © 2023 Max Lynch. All rights reserved.
//

import Foundation
import GGBluetoothSDKFramework

public struct GGDevice {
    var ggDevice: GGIDeviceInfo
    var bleDevice: GGIBLEDevice?
    var token: String?
    var userNumber: Int?
    let existingDevice: Bool?
    var connectionStatus: ConnectionStatusType = ConnectionStatusType.DISCONNECTED
    let syncAllData: Bool?
}
