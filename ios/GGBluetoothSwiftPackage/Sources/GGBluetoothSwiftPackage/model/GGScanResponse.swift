//
//  GGScanResponse.swift
//  Plugin
//
//  Created by <PERSON><PERSON> on 17/04/23.
//  Copyright © 2023 Max Lynch. All rights reserved.
//

import Foundation

public struct GGScanResponse {
    public var type: ScanResponseType?
    public var data: GGScanResponseData
    
    public init(type: ScanResponseType? = nil, data: GGScanResponseData) {
        self.type = type
        self.data = data
    }

}

public protocol GGScanResponseData {
    
}

public typealias GGBTScanCallback = (Result<GGScanResponse, Error>) -> Void
