//
//  GGBTUserProfile.swift
//  Plugin
//
//  Created by <PERSON><PERSON> on 17/04/23.
//  Copyright © 2023 <PERSON>. All rights reserved.
//

import Foundation
public struct GGBTUserProfile {
    public  let name: String
    public let age: Int?
    public  let sex: String?
    public let unit: String?
    public let height: Double?
    public let weight: Double?
    public let goalWeight: Double?
    public let isAthlete: Bool?
    public let goalType: String?
    public let metrics: [GGBTMetricConfig]?
    
    public init(name: String, age: Int?, sex: String?, unit: String?, height: Double?, weight: Double?, goalWeight: Double?, isAthlete: Bool?, goalType: String?, metrics: [GGBTMetricConfig]?) {
        self.name = name
        self.age = age
        self.sex = sex
        self.unit = unit
        self.height = height
        self.weight = weight
        self.goalWeight = goalWeight
        self.isAthlete = isAthlete
        self.goalType = goalType
        self.metrics = metrics
    }
}
