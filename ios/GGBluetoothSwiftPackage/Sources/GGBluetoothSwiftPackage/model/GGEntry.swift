//
//  GGEntry.swift
//  Plugin
//
//  Created by <PERSON><PERSON> Chittibabu on 17/04/23.
//  Copyright © 2023 Max Lynch. All rights reserved.
//

import Foundation

public protocol GGEntryProtcol {
    var broadcastId: String? { get set }
    var broadcastIdString: String? { get set }
    var protocolType: String? { get set }
    var date: Int? { get set }
}

public struct GGEntry: Codable, GGEntryProtcol, GGScanResponseData {
    public var broadcastId: String?
    public var broadcastIdString: String?
    public var protocolType: String?
    public var date: Int?
    public let bmi: Float
    public let bmr: Int
    public let bodyFat: Float
    public let water: Float
    public let boneMass: Float
    public let metabolicAge: Int
    public let muscleMass: Float
    public let proteinPercent: Float
    public let skeletalMusclePercent: Float
    public let subcutaneousFatPercent: Float
    public let unit: String
    public let visceralFatLevel: Int
    public let weight: Float
    public let weightInKg: Float
    public let impedance: Float
    public let pulse: Int
}

public struct GGBPMEntry: Codable, <PERSON>GEntryProtcol, GGScanResponseData {
    public var broadcastId: String?
    public var broadcastIdString: String?
    public var protocolType: String?
    public var date: Int?
    public var pulse: Int?
    public var systolic: Int?
    public var diastolic: Int?
    public var meanPressure: Int?
    public var userNumber: Int?
}

public struct GGWeightEntry: Codable, GGEntryProtcol, GGScanResponseData {
    public var broadcastId: String?
    public var broadcastIdString: String?
    public var protocolType: String?
    public var date: Int?
    public let weightInMg: Float
    public let displayWeight: Float
    public  let unit: String
}

public struct GGThermometerEntry: Codable, GGEntryProtcol, GGScanResponseData {
    public var broadcastId: String?
    public var broadcastIdString: String?
    public var protocolType: String?
    public var date: Int?
    public var temperature: Float?
    public var unit: String?
}

public struct GGPulseOxyEntry: Codable, GGEntryProtcol, GGScanResponseData {
    public var broadcastId: String?
    public var broadcastIdString: String?
    public var protocolType: String?
    public var date: Int?
    public var pulse: Int?
    public var oxygenSaturation: Int?
    public var pulseAmplitudeIndex: Float?
}

public struct GGBloodGlucoseEntry: Codable, GGEntryProtcol, GGScanResponseData {
    public var broadcastId: String?
    public var broadcastIdString: String?
    public var protocolType: String?
    public var date: Int?
    public var glucose: Double?
    public var unit: String?
    public var mealMark: String?
    public var errorCode: String?
}

public struct GGEntryList: GGScanResponseData {
    public let list: [GGEntry]
}

public struct GGBPMEntryList: GGScanResponseData {
    public let list: [GGBPMEntry]
}

public struct GGThermometerEntryList: GGScanResponseData {
    public let list: [GGThermometerEntry]
}

public struct GGPulseOxyEntryList: GGScanResponseData {
    public let list: [GGPulseOxyEntry]
}

public struct GGBloodGlucoseEntryList: GGScanResponseData {
    public let list: [GGBloodGlucoseEntry]
}
