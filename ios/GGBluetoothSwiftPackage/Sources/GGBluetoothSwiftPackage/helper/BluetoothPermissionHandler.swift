//
//  BluetoothPermissionHandler.swift
//  GreatergoodsGgBluetoothIonicPlugin
//
//  Created by kiruth<PERSON><PERSON><PERSON> S on 05/04/24.
//

import Foundation
import CoreBluetooth

class BluetoothPermissionHandler: GGPermission, CBCentralManagerDelegate {

    var permissionState: GGPermissionState = .NOT_REQUESTED
    var bluetoothManager: CBCentralManager?

    override func initManager() {
        if self.bluetoothManager == nil {
            self.bluetoothManager = CBCentralManager(delegate: self, queue: nil, options: [
                CBCentralManagerOptionShowPowerAlertKey: false
            ])
        }
    }
    
    override func deInitManager() {
        if let manager = self.bluetoothManager {
            manager.stopScan()
            manager.delegate = nil
            self.bluetoothManager = nil
        }
    }
    
    func centralManagerDidUpdateState(_ central: CBCentralManager)  {
        Task{
            let switchState = await self.getSwitchState()
            let permissionState = await self.getPermission()
            self.delegate.onPermissionChanged(GGPermissionResponse(type: .BLUETOOTH_SWITCH, data: switchState))
            self.delegate.onPermissionChanged(GGPermissionResponse(type: .BLUETOOTH, data: permissionState))
        }
    }
    
    override func getPermission() async -> GGPermissionState {
        if #available(iOS 13.0, *) {
            let authorization: CBManagerAuthorization
            if #available(iOS 13.1, *) {
                authorization = CBCentralManager.authorization
            } else {
                // iOS 13.0 requires BT manager initialization for authorization check
                initManager()
                authorization = self.bluetoothManager!.authorization
            }
            switch authorization {
            case .allowedAlways:
                self.permissionState = .ENABLED
            case .denied, .restricted:
                self.permissionState = .DISABLED
            case .notDetermined:
                self.permissionState = .NOT_REQUESTED
            default:
                self.permissionState = .NOT_REQUESTED
            }
            return self.permissionState
        }

    }

    override func requestPermission() async -> GGPermissionState{
        let _ = await self.getPermission()
        if permissionState != .ENABLED {
            self.openSettingsWithURL(NavigateToSettings.APPSETTINGS.stringValue())
        } else {
            let permissionState = await self.getPermission()
            self.delegate.onPermissionChanged(GGPermissionResponse(type: .BLUETOOTH_SWITCH, data: permissionState))
            Log("BluetoothPermissionHandler requestPermission method: Permission enabled")
        }
        return self.permissionState
    }

    override func getSwitchState() async -> GGPermissionState {
        self.initManager()
        guard let bluetoothManager = self.bluetoothManager else {
            return .NOT_REQUESTED
        }
        switch bluetoothManager.state {
        case .poweredOn:
            self.permissionState = .ENABLED
        case .unauthorized, .poweredOff:
            self.permissionState = .DISABLED
        default:
            self.permissionState = .NOT_REQUESTED
        }
        return self.permissionState
    }
    override func requestSwitchState() async -> GGPermissionState {
        let permissionState = await self.getPermission()
        var switchState = await self.getSwitchState()
        
        self.initManager()
        
        guard let manager = bluetoothManager else {
            Log("BluetoothPermissionHandler requestSwitchState: CBCentralManager is not initialized")
            return switchState
        }
        
        if manager.state == .poweredOn {
            switchState = await self.getSwitchState()
            self.delegate.onPermissionChanged(GGPermissionResponse(type: .BLUETOOTH_SWITCH, data: switchState))
            Log("BluetoothPermissionHandler requestSwitchState: Switch state enabled")
            return switchState
        }
        
        let settingsURL = permissionState != .ENABLED ? NavigateToSettings.APPSETTINGS : NavigateToSettings.SETTINGS
        openSettingsWithURL(settingsURL.stringValue())
        
        return await self.getSwitchState()
    }
}
