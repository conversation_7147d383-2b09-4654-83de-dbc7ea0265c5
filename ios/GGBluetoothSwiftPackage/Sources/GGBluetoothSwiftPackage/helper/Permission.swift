//
//  Permission.swift
//  GreatergoodsGgBluetoothIonicPlugin
//
//  Created by kiruthika<PERSON><PERSON> S on 05/04/24.
//


import Foundation
import UIKit

protocol IPermission {
    var delegate: GGIPermissionChangedDelegate { get set }

    func initManager()

    func getPermission() async -> GGPermissionState

    func requestPermission() async -> GGPermissionState

    func getSwitchState() async -> GGPermissionState

    func requestSwitchState() async -> GGPermissionState
    
    func deInitManager()
}


protocol GGIPermissionChangedDelegate {
    func onPermissionChanged(_ permissionResponse: GGPermissionResponse) -> Void
}

protocol GGIScanningPermissionChangedDelegate: AnyObject {
    func onScanningPermissionChanged(_ status: Bool)
}

internal class GGPermission: NSObject, IPermission {
    var delegate: GGIPermissionChangedDelegate
    init(delegate: GGIPermissionChangedDelegate) {
        self.delegate = delegate
        super.init()
        self.initManager()
    }

    func initManager() {

    }

    func getPermission() async -> GGPermissionState {
        return GGPermissionState.NOT_REQUESTED
    }

    func requestPermission() async -> GGPermissionState {
         return GGPermissionState.NOT_REQUESTED
     }

    func getSwitchState() async -> GGPermissionState {
        return GGPermissionState.NOT_REQUESTED
    }

    func requestSwitchState() async -> GGPermissionState{
        return GGPermissionState.NOT_REQUESTED
    }
    
    func deInitManager() {
        
    }

    func openSettingsWithURL(_ urlString: String) {
        if let url = URL(string: urlString), UIApplication.shared.canOpenURL(url) {
            DispatchQueue.main.async {
                UIApplication.shared.open(url, options: [:]) {value in
                    Log("openSettingsWithURL method: Failed to open settings for URL: \(urlString)")
                }
            }
        }
        else{
            Log("openSettingsWithURL method: Unable to open settings.")
        }
    }
}
