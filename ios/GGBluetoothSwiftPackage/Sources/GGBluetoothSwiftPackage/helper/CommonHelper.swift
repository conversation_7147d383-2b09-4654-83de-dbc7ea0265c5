//
//  CommonHelper.swift
//  Plugin
//
//  Created by <PERSON><PERSON> on 17/04/23.
//  Copyright © 2023 Max <PERSON>. All rights reserved.
//

import Foundation
import GGBluetoothSDKFramework


typealias PermissionRequestHandler = () -> Void
typealias PermissionHandler = () -> GGPermissionState

func decodeJSON<T: Codable>(jsObject: JSObject, to type: T.Type) throws -> T {
    guard let jsonData = try? JSONSerialization.data(withJSONObject: jsObject, options: []) else {
        throw NSError(domain: "Invalid JSON string", code: -1, userInfo: nil)
    }
    
    let decoder = JSONDecoder()
    let decodedObject = try decoder.decode(type, from: jsonData)
    return decodedObject
}

func convertObjectToOutput(_ data: [String: Any]) -> Any? {
    do {
        let jsonData = try JSONSerialization.data(withJSONObject: data, options: [])
        let jsonObject = try JSONSerialization.jsonObject(with: json<PERSON>)
        return jsonObject
    } catch {
        print("Error converting object to JSON: \(error.localizedDescription)")
        return nil
    }
}

func convertObjectToOutput<T : Codable>(_ data: T) -> Any {
    do {
        let jsonData =  try JSONEncoder().encode(data)
        let jsonObject = try JSONSerialization.jsonObject(with: jsonData)
        return jsonObject
    } catch {
        print("Error converting object to JSON: \(error.localizedDescription)")
        return Data()
    }
}

func convertDictToStringDict<Key: RawRepresentable, Value: RawRepresentable>(dict: [Key: Value]) -> [String: String] where Key.RawValue == String, Value.RawValue == String {
    return Dictionary(uniqueKeysWithValues: dict.map { (key, value) in
        (key.rawValue, value.rawValue)
    })
}

func convertSringToOutput(_ data: String) -> Any? {
    do {
        if let jsonData = data.data(using: .utf8) {
            return try JSONSerialization.jsonObject(with: jsonData)
        }
    } catch {
        print("Error converting object to JSON: \(error.localizedDescription)")
    }
    return nil
}


//func convertToOutput<T : Codable>(_ data: T) -> T {
////    var ret = [String: Any]()
////    let jsonObject = convertObjectToOutput(data)
////    ret["value"] = jsonObject
////    return ret
//    return data
//}

func convertToDeviceDetails(_ ggDeviceInfo: GGIDeviceInfo) -> GGDeviceDetails {
    return GGDeviceDetails(
        manufacturerName: ggDeviceInfo.getManufacturerName(),
        modelNumber: ggDeviceInfo.getModelNumber(),
        serialNumber: ggDeviceInfo.getSerialNumber(),
        firmwareRevision: ggDeviceInfo.getFirmwareRevision(),
        hardwareRevision: ggDeviceInfo.getHardwareRevision(),
        softwareRevision: ggDeviceInfo.getSoftwareRevision(),
        systemID: ggDeviceInfo.getSystemID(),
        deviceName: ggDeviceInfo.getDeviceName(),
        broadcastId: ggDeviceInfo.getBroadcastId(),
        broadcastIdString: ggDeviceInfo.getBroadcastId(),
        password: ggDeviceInfo.getPassword(),
        macAddress: ggDeviceInfo.getMacAddress(),
        wifiMacAddress: ggDeviceInfo.getWifiMacAddress(),
        identifier: ggDeviceInfo.getIdentifier(),
        protocolType: getProtocolType(ggDeviceInfo.getProtocolType()),
        isWifiConfigured: ggDeviceInfo.getWifiSetupState() == .GG_WIFI_STATE_CONNECTED,
        sessionImpedanceSwitchState: ggDeviceInfo.getSessionImpedanceMeasurementState() == .GG_SWITCH_STATE_ON,
        impedanceSwitchState: ggDeviceInfo.getImpedanceMeasurementState() == .GG_SWITCH_STATE_ON,
        startAnimationState: ggDeviceInfo.getStartAnimationState() == .GG_SWITCH_STATE_ON,
        endAnimationState: ggDeviceInfo.getEndAnimationState() == .GG_SWITCH_STATE_ON,
        batteryLevel: (Int(ggDeviceInfo.getBatteryLevel())),
        heartRateState: ggDeviceInfo.getHeartRateMeasurementState() == .GG_SWITCH_STATE_ON
        
    )
}

func convertToWifiDetails(_ wifiInfo: GGWifiInfo) -> GGWifiDetails {
    return GGWifiDetails(
        macAddress: wifiInfo.getMACAddress(),
        ssid: wifiInfo.getSSID(),
        rssi: wifiInfo.getRSSI(),
        password: wifiInfo.getPassword()
    )
}


func convertToEntry(_ ggBodyMetric: GGIBodyMetricsInfo,_ ggDeviceInfo: GGIDeviceInfo) -> GGEntry {
    return GGEntry(
        broadcastId: ggDeviceInfo.getBroadcastId(),
        broadcastIdString: ggDeviceInfo.getBroadcastId(),
        protocolType: getProtocolType(ggDeviceInfo.getProtocolType()),
        date: Int(ggBodyMetric.getTimeStamp()) * 1000, //Convert to milliseconds
        bmi: ggBodyMetric.getBMI(),
        bmr: Int(ggBodyMetric.getBMR()),
        bodyFat: ggBodyMetric.getBodyFatPercent(),
        water: ggBodyMetric.getBodyWaterPercent(),
        boneMass: ggBodyMetric.getBoneMass(),
        metabolicAge: ggBodyMetric.getMetabolicAge(),
        muscleMass: ggBodyMetric.getMusclePercent(),
        proteinPercent: ggBodyMetric.getProtein(),
        skeletalMusclePercent: ggBodyMetric.getSkeletalMuscle(),
        subcutaneousFatPercent: ggBodyMetric.getSubcutaneousFat(),
        unit: getUnitType(ggBodyMetric.getUnit()),
        visceralFatLevel: Int(ggBodyMetric.getVisceralFat()),
        weight: ggBodyMetric.getWeightInLbs(),
        weightInKg: ggBodyMetric.getWeightInKg(),
        impedance: 0,// Float(ggBodyMetric.getImpedance()),
        pulse: ggBodyMetric.getHeartRate()
    )
}


func convertToBPMEntry(_ ggBloodPressureMetric: GGIBloodPressureInfo,_ ggDeviceInfo: GGIDeviceInfo) -> GGBPMEntry {
    return GGBPMEntry(
        broadcastId: ggDeviceInfo.getBroadcastId(),
        broadcastIdString: ggDeviceInfo.getBroadcastId(),
        protocolType: getProtocolType(ggDeviceInfo.getProtocolType()),
        date: Int(ggBloodPressureMetric.getTimeStamp()) * 1000,
        pulse: Int(Double(ggBloodPressureMetric.getPulseRate())),
        systolic: Int(Double(ggBloodPressureMetric.getSystolicValue())),
        diastolic: Int(Double(ggBloodPressureMetric.getDiastolicValue())),
        meanPressure: Int(Double(ggBloodPressureMetric.getMeanPressureValue())),
        userNumber: ggBloodPressureMetric.getUser()
    )
}

func convertToWeightEntry(_ ggWeightInfo: GGIWeightInfo,_ ggDeviceInfo: GGIDeviceInfo) -> GGWeightEntry {
    return GGWeightEntry(
        broadcastId: ggDeviceInfo.getBroadcastId(),
        broadcastIdString: ggDeviceInfo.getBroadcastId(),
        protocolType: getProtocolType(ggDeviceInfo.getProtocolType()),
        weightInMg: ggWeightInfo.getWeightInMg(),
        displayWeight: ggWeightInfo.getWeight(),
        unit: getUnitType(ggWeightInfo.getUnit())
    )
}

func convertToPulseOxyMeterEntry(_ ggPulseOxyEntryInfo: GGIPulseOximeterInfo, _ ggDeviceInfo: GGIDeviceInfo) -> GGPulseOxyEntry {
    return GGPulseOxyEntry(
        broadcastId: ggDeviceInfo.getBroadcastId(),
        broadcastIdString: ggDeviceInfo.getBroadcastId(),
        protocolType: getProtocolType(ggDeviceInfo.getProtocolType()),
        date: Int(Date().timeIntervalSince1970) * 1000,
        pulse: Int(ggPulseOxyEntryInfo.getPR()),
        oxygenSaturation: Int(ggPulseOxyEntryInfo.getSPO2()),
        pulseAmplitudeIndex: Float(ggPulseOxyEntryInfo.getPulseAmplitudeIndex())
    )
}

public func convertToBloodGlucoseEntry(_ ggBloodGlucoseInfo: GGIBloodGlucoseInfo, _ ggDeviceInfo: GGIDeviceInfo) -> GGBloodGlucoseEntry {
    return GGBloodGlucoseEntry(
        broadcastId: ggDeviceInfo.getBroadcastId(),
        broadcastIdString: ggDeviceInfo.getBroadcastId(),
        protocolType: getProtocolType(ggDeviceInfo.getProtocolType()),
        date: Int(Date().timeIntervalSince1970),
        glucose: ggBloodGlucoseInfo.getValue(),
        unit: String(describing: ggBloodGlucoseInfo.getBgmUnit()),
        mealMark: String(describing: ggBloodGlucoseInfo.getMealMark()),
        errorCode: String(describing: ggBloodGlucoseInfo.getErrorCode())
    )
}

func convertToTemperatureEntry(_ ggTemperatureInfo: GGIHealthTemperatureInfo, _ ggDeviceInfo: GGIDeviceInfo) -> GGThermometerEntry {
    let temperatureUnitString = getTemperatureUnitType(ggTemperatureInfo.getTemperatureUnit())
    var temperatureValue: Float = Float(ggTemperatureInfo.getTemperatureValue())
    
    if temperatureUnitString == "°F" {
        temperatureValue = (temperatureValue * 1.8) + 32
    }
    
    return GGThermometerEntry(
        broadcastId: ggDeviceInfo.getBroadcastId(),
        broadcastIdString: ggDeviceInfo.getBroadcastId(),
        protocolType: getProtocolType(ggDeviceInfo.getProtocolType()),
        temperature: temperatureValue,
        unit: temperatureUnitString
    )
}

func getTemperatureUnitType(_ unit: TemperatureUnit ) -> String {
    switch unit {
    case .TEMPERATURE_CELSIUS:
        return "°C"
    case .TEMPERATURE_FAHRENHEIT:
        return "°F"
    default:
        return "°C"
    }
}


func getUnitType(_ unit: WeightUnit ) -> String {
    switch unit {
    case .WEIGHT_KILOGRAMS:
        return "kg"
    case .WEIGHT_POUNDS:
        return "lb"
    case .WEIGHT_GRAMS:
        return "gm"
    case .WEIGHT_OUNCE:
        return "oz"
    case .WEIGHT_POUND_OUNCE:
        return "lb_oz"
    case .WEIGHT_MILLI_LITRE_WATER:
        return "ml"
    case .WEIGHT_MILLI_LITRE_MILK:
        return "ml_milk"
    case .WEIGHT_FLUID_OUNCE_WATER:
        return "fl_oz"
    case .WEIGHT_FLUID_OUNCE_MILK:
        return "fl_oz_milk"
    default:
        return "lb"
    }
}

func getProtocolType(_ ggProtocolType: GGDeviceProtocolType) -> String {
    switch ggProtocolType {
    case .GG_DEVICE_PROTOCOL_A3:
        return "A3"
    case .GG_DEVICE_PROTOCOL_A6:
        return "A6"
    case .GG_DEVICE_PROTOCOL_R4:
        return "R4"
    case .GG_DEVICE_PROTOCOL_WELLAND_KITCHEN:
        return "WELLAND_KITCHEN"
    case .GG_DEVICE_PROTOCOL_WELLAND_BATH_SCALE:
        return "WELLAND_BATH_SCALE"
    case .GG_DEVICE_PROTOCOL_TK_BGM:
        return "TK_BGM"
    case .GG_DEVICE_PROTOCOL_UNSET:
        return "UNSET"
    case .GG_DEVICE_PROTOCOL_OPEN:
        return "OPEN"
    default:
        return ""
    }
}


func convertLbsToKg(_ lbs: Double) -> Double {
    return (lbs / 2.2046).rounded(toPlaces: 1)
}

public func getSettingValue(from setting: GGBTSetting) -> Any? {
    switch setting.value {
    case .bool(let boolValue):
        return boolValue
    case .int(let intValue):
        return intValue
    case .string(let stringValue):
        return stringValue
    }
}

let bodyMetricsMap: [String: GGWeighingScaleDisplayDetail] = [
    "bmi": GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BMI,
    "bodyFatPercent": GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BODY_FAT,
    "musclePercent": GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_MUSCLE,
    "bodyWaterPercent": GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BODY_WATER,
    "bonePercent": GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BONE_MASS,
    "heartRate": GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_HEART_RATE,
    "visceralFatLevel": GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_VISCERAL_FAT,
    "subcutaneousFatPercent": GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_SUBCUTANEOUS_FAT,
    "proteinPercent": GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_PROTEIN,
    "skeletalMusclePercent": GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_SKELETAL_MUSCLE,
    "bmr": GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BMR,
    "metabolicAge": GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BODY_AGE,
    "goalProgress": GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_TARGET,
    "dailyAverage": GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_DAILY_AVERAGE,
    "weeklyAverage": GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_WEEKLY_AVERAGE,
    "monthlyAverage": GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_MONTHLY_AVERAGE,
]

let unitMap: [String: WeightUnit] = [
    "kg": .WEIGHT_KILOGRAMS,
    "lb": .WEIGHT_POUNDS,
    "lb_oz": .WEIGHT_POUND_OUNCE,
    "oz": .WEIGHT_OUNCE,
    "gm": .WEIGHT_GRAMS,
    "ml": .WEIGHT_MILLI_LITRE_WATER,
    "ml_milk": .WEIGHT_MILLI_LITRE_MILK,
    "fl_oz": .WEIGHT_FLUID_OUNCE_WATER,
    "fl_oz_milk": .WEIGHT_FLUID_OUNCE_MILK,
]

let temperatureUnitMap: [String: TemperatureUnit] = [
    "°C": .TEMPERATURE_CELSIUS,
    "°F" : .TEMPERATURE_FAHRENHEIT,
]

let TimeFormatMap: [String: GGTimeDisplayType] =   [
    "12H": .GG_TIME_DISPLAY_12_HRS,
    "24H": .GG_TIME_DISPLAY_24_HRS
]

extension Double {
    func rounded(toPlaces places: Int) -> Double {
        let divisor = pow(10.0, Double(places))
        return (self * divisor).rounded() / divisor
    }
    
}


let appPermissions: [GGAppType: [GGPermissionType]] = [
    .WEIGHT_GURUS: [.BLUETOOTH, .BLUETOOTH_SWITCH, .LOCATION, .LOCATION_SWITCH, .NOTIFICATION, .CAMERA],
    .BALANCE_HEALTH: [.BLUETOOTH, .BLUETOOTH_SWITCH],
    .SMART_BABY: [.BLUETOOTH, .BLUETOOTH_SWITCH],
    .SMART_KITCHEN: [.BLUETOOTH, .BLUETOOTH_SWITCH],
    .RPM: [.BLUETOOTH, .BLUETOOTH_SWITCH],
    .NONE: [.BLUETOOTH, .BLUETOOTH_SWITCH],
]


let permissionSwitchMap: [GGPermissionType: Bool] = [
    .LOCATION: false,
    .LOCATION_SWITCH: true,
    .BLUETOOTH: false,
    .BLUETOOTH_SWITCH: true,
    .NOTIFICATION: false,
    .CAMERA: false
    
]

func getDefaultPermissionStatus() -> [GGPermissionType: GGPermissionState] {
    return [
        .LOCATION: GGPermissionState.NOT_REQUESTED,
        .LOCATION_SWITCH: GGPermissionState.NOT_REQUESTED,
        .BLUETOOTH: GGPermissionState.NOT_REQUESTED,
        .BLUETOOTH_SWITCH: GGPermissionState.NOT_REQUESTED,
        .NOTIFICATION: GGPermissionState.NOT_REQUESTED,
        .CAMERA: GGPermissionState.NOT_REQUESTED
    ]
}

func Log(_ message: String) {
    print(message)
}

