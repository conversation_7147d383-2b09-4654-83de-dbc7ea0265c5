//
//  GGReceiveData.swift
//  Plugin
//
//  Created by <PERSON><PERSON> on 17/04/23.
//  Copyright © 2023 Max Lynch. All rights reserved.
//

import Foundation
import GGBluetoothSDKFramework

extension GGBluetoothSDKHelper: GGIReceiveDataDelegate {
    func onReceiveTemperatureUnit(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ unit: GGBluetoothSDKFramework.TemperatureUnit) -> GGBluetoothSDKFramework.GGResultType {
        return GGResultType.GG_OK
    }
    
    func onReceiveMuteMode(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ state: GGBluetoothSDKFramework.GGSwitchState) -> GGBluetoothSDKFramework.GGResultType {
        return GGResultType.GG_OK
    }
    
    func onReceiveTher<PERSON>meterErrorCode(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ errorCode: GGBluetoothSDKFramework.GGThermometerErrorCode) -> GGBluetoothSDKFramework.GGResultType {
        return GGResultType.GG_OK
    }
    
    func onReceiveBgmUnit(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ unit: GGBluetoothSDKFramework.BgmUnit) -> GGBluetoothSDKFramework.GGResultType {
        return GGResultType.GG_OK
    }
    
    func onScaleWakeUp(_ device: GGBluetoothSDKFramework.GGIBLEDevice) -> GGBluetoothSDKFramework.GGResultType {
        
        Log("Device wake up")
        guard let ggDeviceInfo = device.getDeviceInfo() else {
            Log("Device info is not available")
            return GGResultType.GG_FAIL
        }
        sendDeviceInfo(device)
        
        guard let pluginCall = self.pluginCall else {
            Log("Plugin call is undefined")
            return GGResultType.GG_FAIL
        }
        
        //Resolve plugin call
        pluginCall(
            .success(GGScanResponse(
                type: ScanResponseType.DEVICE_WAKE_UP,
                data: convertToDeviceDetails(ggDeviceInfo)
            ))
        )
        return GGResultType.GG_OK
    }
    
    func onConnectionFailed(_ bleDevice: GGIBLEDevice) -> GGResultType {
        return GGResultType.GG_UNSUPPORTED
    }
    
    //Receive Data delegate methods
    func onDeviceConnect(_ ggbleDevice: GGIBLEDevice) -> GGResultType {
        guard let ggDeviceInfo = ggbleDevice.getDeviceInfo() else {
            Log("Device info is not available")
            return GGResultType.GG_FAIL
        }
        let broadcastId = ggDeviceInfo.getBroadcastId()
        Log("Device Connected \(ggDeviceInfo.getMacAddress())")
        
        guard var device = devices[broadcastId] else {
            Log("Device not found in device list")
            return GGResultType.GG_FAIL
        }
        device.bleDevice = ggbleDevice
        device.connectionStatus = ConnectionStatusType.CONNECTED
        devices[broadcastId] = device
        let pairedDevice = checkDeviceAlreadyPaired(ggDeviceInfo)
        if ggDeviceInfo.getProtocolType() == .GG_DEVICE_PROTOCOL_A3 {
            ggDeviceInfo.setPassword(password: pairedDevice?.password ?? "")
        }
        addUserAccount(
            scaleToken: device.token ?? "",
            userNumber: pairedDevice != nil ? pairedDevice?.userNumber ?? 0 : device.userNumber ?? 0,
            ggBLEDevice: ggbleDevice,
            pairedDevice?.preference
        )
        return GGResultType.GG_OK
    }
    
    func onDeviceDisconnect(_ bleDevice: GGIBLEDevice) -> GGResultType {
        Log("Device Disconnected")
        guard let ggDeviceInfo = bleDevice.getDeviceInfo() else {
            Log("Device info is not available")
            return GGResultType.GG_FAIL
        }
        let broadcastId = ggDeviceInfo.getBroadcastId()
        guard var device = devices[broadcastId] else {
            Log("Device not found in device list")
            return GGResultType.GG_FAIL
        }
        
        device.connectionStatus = ConnectionStatusType.DISCONNECTED
        devices[broadcastId] = device
        guard let pluginCall = self.pluginCall else {
            Log("Plugin call is undefined")
            return GGResultType.GG_FAIL
        }
        
        //Resolve plugin call
        pluginCall(
            .success(GGScanResponse(
                type: ScanResponseType.DEVICE_DISCONNECTED,
                data: convertToDeviceDetails(ggDeviceInfo)
            ))
        )
        return GGResultType.GG_OK
    }
    
    func onReceiveDeviceInfo(_ result: GGResultType, _ device: GGIBLEDevice, _ deviceInfo: GGIDeviceInfo) -> GGResultType {
        
        Log("Device info received")
        sendDeviceInfo(device)
        return GGResultType.GG_OK
    }
    
    func onReceiveAcknowledgement(_ result: GGResultType, _ device: GGIBLEDevice, _ operation: GGOperationType) -> GGResultType {
        guard let ggDeviceInfo = device.getDeviceInfo() else {
            Log("Device info is not available")
            return GGResultType.GG_FAIL
        }
        
        switch operation {
        case GGOperationType.GG_OPERATION_ACCOUNT_CREATION:
            Log("Device Added")
            let broadcastId = ggDeviceInfo.getBroadcastId()
            switch result {
            case GGResultType.GG_OK: do {
                updateUserCreationStatus(UserCreationResponseType.CREATION_COMPLETED)
                if appType == GGAppType.WEIGHT_GURUS {
                    _ = device.subscribeToLiveData()
                }
                guard let pluginCall = self.pluginCall else {
                    Log("Plugin call is undefined")
                    return GGResultType.GG_FAIL
                }
                if let ggDevice = devices[broadcastId] {
                    pluginCall(
                        .success(GGScanResponse(
                            type: ScanResponseType.DEVICE_CONNECTED,
                            data: convertToDeviceDetails(ggDeviceInfo)
                        ))
                    )
                    sendDeviceInfo(device)
                    var scaleToken: String? = nil;
                    if appType == GGAppType.BALANCE_HEALTH {
                        if let data = pairedDevices.first(where: {$0.broadcastId == broadcastId}),
                           ggDevice.ggDevice.getProtocolType() == .GG_DEVICE_PROTOCOL_A6{
                            if data.syncAllData == true {
                                scaleToken = ggDevice.userNumber ==  1 ? "02" : "01";
                            } else {
                                scaleToken = ggDevice.userNumber ==  1 ? "01" : "02";
                            }
                        }
                    } else if appType == GGAppType.WEIGHT_GURUS {
                        if let token = ggDevice.token {
                            scaleToken = token
                        }
                    }
                    
                    if let scaleToken = scaleToken {
                        _ = device.syncData(Utils.hexStringToByteArray(scaleToken))
                    } else {
                        _ = device.syncData([])
                    }
                }
                
            }
            case GGResultType.GG_DIFFERENT_USER: do {
                updateUserCreationStatus(UserCreationResponseType.DIFFERENT_USER)
            }
            case GGResultType.GG_USER_SELECTION_IN_PROGRESS: do {
                updateUserCreationStatus(UserCreationResponseType.USER_SELECTION_IN_PROGRESS)
            }
            case GGResultType.GG_INPUT_DATA_ERROR: do {
                updateUserCreationStatus(UserCreationResponseType.INPUT_DATA_ERROR)
            }
            case GGResultType.GG_MEMORY_FULL_ERROR: do {
                updateUserCreationStatus(UserCreationResponseType.MEMORY_FULL)
                if(checkDeviceAlreadyPaired(ggDeviceInfo) != nil && pluginCall != nil){
                    pluginCall!(
                        .success( GGScanResponse(
                            type: ScanResponseType.DEVICE_MEMORY_FULL,
                            data: convertToDeviceDetails(ggDeviceInfo)
                        ))
                    )
                }else{
                    Log("paired device is not found")
                }
            }
            case GGResultType.GG_DUPLICATE_USER_ERROR: do {
                updateUserCreationStatus(UserCreationResponseType.DUPLICATE_USER_ERROR)
                if(checkDeviceAlreadyPaired(ggDeviceInfo) != nil && pluginCall != nil){
                    pluginCall!(
                        .success( GGScanResponse (
                            type: ScanResponseType.DEVICE_DUPLICATE_USER,
                            data: convertToDeviceDetails(ggDeviceInfo)
                        ))
                    )
                }else{
                    Log("paired device is not found")
                }
            }
            default: do {
                updateUserCreationStatus(UserCreationResponseType.CREATION_FAILED)
            }
            }
        case GGOperationType.GG_OPERATION_CLEAR_ALL_DATA,
            GGOperationType.GG_OPERATION_CLEAR_USER_ACCOUNT_DATA,
            GGOperationType.GG_OPERATION_CLEAR_HISTORY_DATA,
            GGOperationType.GG_OPERATION_CLEAR_WIFI_NETWORK_DATA,
            GGOperationType.GG_OPERATION_CLEAR_SETTINGS_DATA:
            self.clearDataCallback?("Success")
            self.clearDataCallback = nil
        case GGOperationType.GG_OPERATION_GET_SESSION_IMPEDANCE_SWITCH:
            self.sendDeviceInfo(device)
        default:
            break
        }
        return GGResultType.GG_OK
    }
    
    func onReceiveBatteryLevel(_ result: GGResultType, _ device: GGIBLEDevice, _ batteryLevel: Int) -> GGResultType {
        return GGResultType.GG_OK
    }
    
    func onReceiveUTCTime(_ result: GGResultType, _ device: GGIBLEDevice, _ timeStamp: Int32) -> GGResultType {
        return GGResultType.GG_OK
    }
    
    func onReceiveWeightUnit(_ result: GGResultType, _ device: GGIBLEDevice, _ unit: WeightUnit) -> GGResultType {
        return GGResultType.GG_OK
    }
    
    func onReceiveHeartRateState(_ result: GGResultType, _ device: GGIBLEDevice, _ state: GGSwitchState) -> GGResultType {
        return GGResultType.GG_OK
    }
    
    func onReceiveImpedanceState(_ result: GGResultType, _ device: GGIBLEDevice, _ state: GGSwitchState, _ onlyCurrentSession: Bool) -> GGResultType {
        sendDeviceInfo(device)
        return GGResultType.GG_OK
    }
    
    func onReceiveAnimationDisplayState(_ result: GGResultType, _ device: GGIBLEDevice, _ startPageState: GGSwitchState, _ shutdownPageState: GGSwitchState) -> GGResultType {
        return GGResultType.GG_OK
    }
    
    func onReceiveDisplayDetails(_ result: GGResultType, _ device: GGIBLEDevice, _ displayDetails: [GGWeighingScaleDisplayDetail]) -> GGResultType {
        return GGResultType.GG_OK
    }
    
    func onReceiveFirmwareUpgradeStatus(_ result: GGResultType, _ device: GGIBLEDevice, _ firmwareUpgradeInfo: GGIFirmwareUpgradeInfo?) -> GGResultType {
        return GGResultType.GG_OK
    }
    
    func onReceiveAccountIDList(_ result: GGResultType, _ device: GGIBLEDevice, _ accountIDList: Array<GGAccountInfo>) -> GGResultType {
        var users = [GGBTUser]()
        if !accountIDList.isEmpty {
            users = accountIDList.map {
                let user = GGBTUser(name: $0.name(), token: Utils.byteArrayToHexString(bytes: $0.accountID()), lastActive: $0.getUpdateTimeStamp(),
                                    isBodyMetricsEnabled: $0.impedanceSwitch() == .GG_SWITCH_STATE_ON
                )
                return user
            }
        }
        print("users: \(users)")
        userListCallBack?(GGScaleUserResponse(user: users))
        return GGResultType.GG_OK
    }
    
    func onReceiveAccountIDImpedanceList(_ result: GGResultType, _ device: GGIBLEDevice, _ accountIDList: Array<GGAccountInfo>) -> GGResultType {
        return GGResultType.GG_OK
    }
    
    func onReceiveAccountIDDelete(_ result: GGResultType, _ device: GGIBLEDevice, _ accountID: [byte]) -> GGResultType {
        userDeletionCallBack?(result == .GG_OK ? .SUCCESS: .FAIL)
        if let ggDeviceInfo = device.getDeviceInfo() {
            if(!isDeviceSkipped(ggDeviceInfo.getBroadcastId())){
                sendDeviceInfo(device)
            }
        }
        return GGResultType.GG_OK
    }
    
    
    func onReceiveMeasurementHistory(_ result: GGBluetoothSDKFramework.GGResultType, _ device: GGBluetoothSDKFramework.GGIBLEDevice, _ measurementHistoryList: [GGBluetoothSDKFramework.GGIMeasurementInfo]) -> GGBluetoothSDKFramework.GGResultType {
        guard let ggDeviceInfo = device.getDeviceInfo() else {
            Log("Device info is not available")
            return GGResultType.GG_FAIL
        }
        var historyList = [GGEntry]()
        var bpmHistoryList = [GGBPMEntry]()
        
        // Print the received measurements
           print("Received \(measurementHistoryList.count) measurements.")
           measurementHistoryList.forEach { measurement in
               print("Measurement: \(measurement)")
           }
        
        if device.getDeviceType() == .GG_BPM_DEVICE {
            measurementHistoryList.forEach {
                bpmHistoryList.append(convertToBPMEntry($0 as! GGIBloodPressureInfo, ggDeviceInfo))
            }
        } else if device.getDeviceType() == .GG_WEIGHING_SCALE_DEVICE{
            measurementHistoryList.forEach {
                historyList.append(convertToEntry($0 as! GGIBodyMetricsInfo, ggDeviceInfo))
            }
        }
        
        guard let pluginCall = self.pluginCall else {
            Log("Plugin call is undefined")
            return GGResultType.GG_FAIL
        }
        
        if device.getDeviceType() == .GG_BPM_DEVICE {
            pluginCall(
                .success(   GGScanResponse(
                    type: ScanResponseType.MULTI_ENTRIES,
                    data: GGBPMEntryList(list: bpmHistoryList)
                ))
            )
        } else {
            pluginCall(
                .success(GGScanResponse(
                    type: ScanResponseType.MULTI_ENTRIES,
                    data: GGEntryList(list: historyList)
                ))
            )
        }
        return GGResultType.GG_OK
    }
    
    func onReceiveWifiMacAddress(_ result: GGResultType, _ device: GGIBLEDevice, _ deviceInfo: GGIDeviceInfo) -> GGResultType {
        print("Wifi Mac Address: \(deviceInfo.getWifiMacAddress())")
        wifiMacCallBack?(deviceInfo.getWifiMacAddress())
        return GGResultType.GG_OK
    }
    
    func onReceiveWifiConnectState(_ result: GGResultType, _ device: GGIBLEDevice, _ wifiState: GGWifiState, _ errorCode: String) -> GGResultType {
        wifiSetupCallBack?(GGWifiSetupResponse(wifiState: wifiState.description, errorCode: errorCode))
        guard let pluginCall = self.pluginCall else {
            Log("Plugin call is undefined")
            return GGResultType.GG_FAIL
        }
        guard let ggDeviceInfo = device.getDeviceInfo() else {
            Log("Device info is not available")
            return GGResultType.GG_FAIL
        }
        pluginCall(.success(GGScanResponse(
            type: ScanResponseType.WIFI_STATUS_UPDATE,
            data: convertToDeviceDetails(ggDeviceInfo)
        )))
        print("Wifi Connection State: \(wifiState)")
        if wifiState != GGWifiState.GG_WIFI_STATE_CONNECTED {
            print("Wifi Connection error code: \(errorCode)")
        }
        return GGResultType.GG_OK
    }
    
    func onReceiveWifiList(_ result: GGResultType, _ device: GGIBLEDevice, _ wifiList: [GGWifiInfo]) -> GGResultType {
        if result != GGResultType.GG_OK {
            // GG
        }
        if wifiList.isEmpty {
            _ = device.cancelWifiInfo()
        }
        
        guard let wifiListCallBack = self.wifiListCallBack else {
            Log("wifi callback is not defined")
            return GGResultType.GG_FAIL
        }
        var wifiDetailsList: [GGWifiDetails] = []
        
        for wifiInfo in wifiList {
            wifiDetailsList.append(convertToWifiDetails(wifiInfo))
        }
        
        wifiListCallBack(GGWifiResponse<GGWifiDetails>(wifi: wifiDetailsList))
        return GGResultType.GG_OK
    }
    
    func onReceiveWifiSSID(_ result: GGResultType, _ device: GGIBLEDevice, _ ssid: String!) -> GGResultType {
        Log("Wifi ssid received ssid - \(ssid ?? "")")
        wifiSSIDCallBack?(ssid ?? "")
        return result
    }
    
    func onReceiveAccountList(_ result: GGResultType, _ device: GGIBLEDevice, _ accountIDList: [[UInt8]]) -> GGResultType {
        return GGResultType.GG_OK
    }
    
    func onReceiveAccountConfig(_ result: GGResultType, _ device: GGIBLEDevice, _ status: Int) -> GGResultType {
        Log("Device Added" + String(status))
        switch status {
        case 0x00:
            _ = device.addOnlineAccount()
        case 0xFF: do {
            updateUserCreationStatus(UserCreationResponseType.CREATION_FAILED)
        }
        case 0xF4: do {
            updateUserCreationStatus(UserCreationResponseType.INPUT_DATA_ERROR)
        }
        case 0xF5: do {
            updateUserCreationStatus(UserCreationResponseType.MEMORY_FULL)
        }
        default: do {
            updateUserCreationStatus(UserCreationResponseType.CREATION_FAILED)
        }
        }
        return result
    }
    
    func onReceiveOnlineAccountConfig(_ result: GGResultType, _ device: GGIBLEDevice, _ status: Int) -> GGResultType {
        
        return GGResultType.GG_OK
    }
    
    func onReceiveDeviceLog(_ result: GGResultType, _ device: GGIBLEDevice, _ log: String) {
        _ = device.acknowledgeLogRawDataReceived()
        let mac = device.getDeviceInfo()?.getMacAddress()
        let deviceLog = DeviceLog( macAddress: mac, log: log)
        deviceLogs.append(deviceLog)
        
        if result == GGResultType.GG_OK {
            deviceLogCallBack?( GGDeviceLogResponse<DeviceLog>(logs: deviceLogs))
        }
    }
    
    func onReceiveDeviceLogUpdate(_ result: GGResultType, _ device: GGIBLEDevice, _ deviceLogInfo: GGDeviceLogInfo) {
        print("Log percent - \(deviceLogInfo.getReceivedPercentage()) \(deviceLogs)")
    }
    
    func onReceiveWeightData(_ result: GGResultType, _ device: GGIBLEDevice, _ weightInfo: GGIWeightInfo) -> GGResultType {
        return GGResultType.GG_OK
    }
    
    func onReceiveLiveMeasurement(_ result: GGResultType, _ device: GGIBLEDevice, _ measurementInfo: GGIMeasurementInfo) -> GGResultType {
        switch (measurementInfo.getMeasurementType()) {
        case .GG_BODY_METRICS_MEASUREMENT: do {
            usleep(100000)
            guard let ggDeviceInfo = device.getDeviceInfo() else {
                Log("Device info is not available")
                return GGResultType.GG_FAIL
            }
            guard let pluginCall = self.pluginCall else {
                Log("Plugin call is undefined")
                return GGResultType.GG_FAIL
            }
            let operation = convertToWeightEntry(measurementInfo as! any GGIWeightInfo as GGIWeightInfo, ggDeviceInfo)
            pluginCall(
                .success( GGScanResponse(
                    type: ScanResponseType.LIVE_MEASUREMENT,
                    data: operation
                ))
            )
            self.liveDataCallback?("Success")
        }
            
            break
        case .GG_WEIGHT_MEASUREMENT: do {
            guard let ggDeviceInfo = device.getDeviceInfo() else {
                Log("Device info is not available")
                return GGResultType.GG_FAIL
            }
            guard let pluginCall = self.pluginCall else {
                Log("Plugin call is undefined")
                return GGResultType.GG_FAIL
            }
            let operation = convertToWeightEntry(measurementInfo as! any GGIWeightInfo as GGIWeightInfo, ggDeviceInfo)
            pluginCall(
                .success( GGScanResponse(
                    type: ScanResponseType.LIVE_MEASUREMENT,
                    data: operation
                ))
            )
        }
        default: do {
            
        }
        }
        return GGResultType.GG_OK
    }
    
    func onReceiveMeasurement(_ result: GGBluetoothSDKFramework.GGResultType, _ device: GGBluetoothSDKFramework.GGIBLEDevice, _ measurementInfo: GGBluetoothSDKFramework.GGIMeasurementInfo) -> GGBluetoothSDKFramework.GGResultType {
        guard let ggDeviceInfo = device.getDeviceInfo() else {
            Log("Device info is not available")
            return GGResultType.GG_FAIL
        }
        let protocolType = ggDeviceInfo.getProtocolType()
        let broadcastId = ggDeviceInfo.getBroadcastId()
        guard let pluginCall = self.pluginCall else {
            Log("Plugin call is undefined")
            return GGResultType.GG_FAIL
        }
        switch (measurementInfo.getMeasurementType()) {
        case GGMeasurementType.GG_BODY_METRICS_MEASUREMENT: do {
            let bodyMetricsInfo: GGIBodyMetricsInfo = measurementInfo as! GGIBodyMetricsInfo
            print("received body metrics info: ")
            switch (device.getDeviceType()) {
            case .GG_WEIGHING_SCALE_DEVICE: do {
                let entry = convertToEntry(bodyMetricsInfo, ggDeviceInfo)
                print("received gg Weight entry: \(entry)")
                if protocolType == .GG_DEVICE_PROTOCOL_A6 || protocolType == .GG_DEVICE_PROTOCOL_A3 || protocolType == .GG_DEVICE_PROTOCOL_WELLAND_BATH_SCALE {
                    pluginCall(
                        .success( GGScanResponse(
                            type: ScanResponseType.SINGLE_ENTRY,
                            data: entry
                        ))
                    )
                } else if protocolType == .GG_DEVICE_PROTOCOL_R4 {

                    switch(bodyMetricsInfo.getDeviceMatchingAccountIDStatus() ) {
                    case 0x00:
                        guard let deviceInfo = devices[broadcastId] else {
                            Log("Device not found in device list")
                            return GGResultType.GG_FAIL
                        }
                        if  deviceInfo.token != nil && deviceInfo.token ==  Utils.byteArrayToHexString(bytes: bodyMetricsInfo.getDeviceMatchingAccountID()) {
                            if (bodyMetricsInfo.getWeight() > 0) {
                                do {
                                    Log(String(format: "Weight: %f BMI: %f Data: %s", bodyMetricsInfo.getWeight(), bodyMetricsInfo.getBMI(), String(data: try JSONEncoder().encode(entry), encoding: String.Encoding.utf8) ?? ""))
                                } catch {
                                    print(error)
                                }
                                
                                let operation = convertToEntry(measurementInfo as! any GGIWeightInfo as GGIWeightInfo as! GGIBodyMetricsInfo, ggDeviceInfo)
                                print("operation in plugin: \(operation)")
                                pluginCall(
                                    .success( GGScanResponse(
                                        type: ScanResponseType.SINGLE_ENTRY,
                                        data: operation
                                    ))
                                )
//                                
//                                pluginCall(
//                                    .success( GGScanResponse(
//                                        type: ScanResponseType.SINGLE_ENTRY,
//                                        data: entry
//                                    ))
//                                )
                            }
                            bodyMetricsInfo.reset()
                            // Acknowledge weight data received for informing live
                            // weight data received from scale
                            // (skipping entry in scale history)
                            _ = device.acknowledgeDeviceMatchingAccountIDReceived(GGResultType.GG_OK)
                        }
                        break
                    default:
                        print("Measurement error")
                    }
                }
            }
            
            default:
                break
            }
            
          
            
            bodyMetricsInfo.reset()
        }
            break;
        case GGMeasurementType.GG_BLOOD_PRESSURE_MEASUREMENT: do  {
            let operation = convertToBPMEntry(measurementInfo as! GGIBloodPressureInfo, ggDeviceInfo)
            if protocolType == .GG_DEVICE_PROTOCOL_A6 {
                pluginCall(
                    .success( GGScanResponse(
                        type: ScanResponseType.SINGLE_ENTRY,
                        data: operation
                    ))
                )
            }
        }
        case GGMeasurementType.GG_WEIGHT_MEASUREMENT: do {
            let operation = convertToWeightEntry(measurementInfo as! GGIWeightInfo, ggDeviceInfo)
            pluginCall(
                .success( GGScanResponse(
                    type: ScanResponseType.SINGLE_ENTRY,
                    data: operation
                ))
            )
        }
        case GGMeasurementType.GG_HEALTH_THERMOMETER_MEASUREMENT: do {
            let measurement = measurementInfo as! GGIHealthTemperatureInfo
            let operation = convertToTemperatureEntry(measurementInfo as! GGIHealthTemperatureInfo, ggDeviceInfo)
            pluginCall(
                .success( GGScanResponse(
                    type: ScanResponseType.SINGLE_ENTRY,
                    data: operation as! GGScanResponseData
                ))
            )
            
        }
        case GGMeasurementType.GG_PULSE_OXIMETER_MEASUREMENT: do {
            let measurement = measurementInfo as! GGIPulseOximeterInfo
            print("")
            let operation = convertToPulseOxyMeterEntry(measurement as! GGIPulseOximeterInfo, ggDeviceInfo)
            pluginCall(
                .success( GGScanResponse(
                    type: ScanResponseType.SINGLE_ENTRY,
                    data: operation as! GGScanResponseData
                ))
            )
        }
        case GGMeasurementType.GG_BLOOD_GLUCOSE_METER_MEASUREMENT: do {
            let measurement = measurementInfo as! GGIBloodGlucoseInfo
            let operation = convertToBloodGlucoseEntry(measurement as! GGIBloodGlucoseInfo, ggDeviceInfo)
            pluginCall(
                .success( GGScanResponse(
                    type: ScanResponseType.SINGLE_ENTRY,
                    data: operation as! GGScanResponseData
                ))
            )
        }
        default: do {
            
        }
        }
        return result
    }
    
    internal func updateUserCreationStatus(_ status: UserCreationResponseType) {
        userCreationCallBack?(status)
        userUpdationCallBack?(status)
        self.userCreationCallBack = nil
        self.userUpdationCallBack = nil
    }
    
    internal func sendDeviceInfo(_ ggBleDevice: GGIBLEDevice){
        
        guard let ggDeviceInfo = ggBleDevice.getDeviceInfo() else {
            Log("Device info is not available")
            return
        }
        
        let broadcastId = ggDeviceInfo.getBroadcastId()
        
        guard var device = devices[broadcastId] else {
            Log("Device not found in device list")
            return
        }
        
        guard let pluginCall = self.pluginCall else {
            Log("Plugin call is undefined")
            return
        }
        
        device.bleDevice = ggBleDevice
        device.ggDevice = ggDeviceInfo
        devices[broadcastId] = device
        if(checkDeviceAlreadyPaired(ggBleDevice.getDeviceInfo()!) != nil){
            pluginCall(
                .success(  GGScanResponse (
                    type: ScanResponseType.DEVICE_INFO_UPDATE,
                    data: convertToDeviceDetails(ggDeviceInfo)
                ))
            )
        }else{
            Log("paired device is not found")
            return
        }
        
    }
}
