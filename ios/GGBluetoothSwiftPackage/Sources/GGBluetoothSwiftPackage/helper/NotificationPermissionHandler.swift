//
//  NotificationPermissionHandler.swift
//  GreatergoodsGgBluetoothIonicPlugin
//
//  Created by kir<PERSON><PERSON><PERSON><PERSON> <PERSON> on 05/04/24.
//

import Foundation
import UserNotifications
import UIKit

class NotificationPermissionHandler: GGPermission  {

    let NotificationDelegate = UNUserNotificationCenter.current()
    var permissionState: GGPermissionState = .NOT_REQUESTED
    private var observer: NSObjectProtocol?

    override func initManager() {
        if observer == nil {
            observer = NotificationCenter.default.addObserver(forName: UIApplication.willEnterForegroundNotification, object: nil, queue: OperationQueue.main) { [weak self] _ in
                guard let strongSelf = self else { return }
                Task {
                    let permissionState = await strongSelf.getPermission()
                    strongSelf.delegate.onPermissionChanged(GGPermissionResponse(type: .NOTIFICATION, data: permissionState))
                }
            }
        }
    }
    
    override func deInitManager() {
           // Remove observer if it exists
           if let observer = observer {
               NotificationCenter.default.removeObserver(observer)
               self.observer = nil
           }
    }
       
    
    override func getPermission() async -> GGPermissionState {
        let notificationCenter = UNUserNotificationCenter.current()
        let settings = await notificationCenter.notificationSettings()
        switch  settings.authorizationStatus {
        case .authorized, .ephemeral, .provisional:
            self.permissionState = .ENABLED
        case .denied:
            self.permissionState = .DISABLED
        case .notDetermined:
            self.permissionState = .NOT_REQUESTED
        default:
            self.permissionState = .NOT_REQUESTED
        }
        return self.permissionState
    }
    override func requestPermission() async -> GGPermissionState {
        let options: UNAuthorizationOptions = [.alert, .sound, .badge]
        do {
            let isAuthorized = try await UNUserNotificationCenter.current().requestAuthorization(options: options)
            if (!isAuthorized) {
                self.openSettingsWithURL(NavigateToSettings.APPSETTINGS.stringValue())
            }
        } catch {
            Log("NotificationPermissionHandler requestPermission method: Error requesting authorization:  \(error)")
        }
        let _ = await self.getPermission()
        self.delegate.onPermissionChanged(GGPermissionResponse(type: .NOTIFICATION, data: self.permissionState))
        return self.permissionState
    }
}
