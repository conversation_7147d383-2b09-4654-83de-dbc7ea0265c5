//
//  GGPermissionHandler.swift
//  Plugin
//
//  Created by kiruth<PERSON><PERSON><PERSON> on 20/03/24.
//  Copyright © 2024 Max Lynch. All rights reserved.
//

import Foundation
import CoreBluetooth
import UserNotifications


class GGPermissionHandler: GGIPermissionChangedDelegate {
    static let shared = GGPermissionHandler()
    private var appType: GGAppType = GGAppType.NONE
    private var permissionStatus: [GGPermissionType: GGPermissionState] = getDefaultPermissionStatus()
    internal var pluginCall: GGBTScanCallback?
    private var permissionHandlerMap: [GGPermissionType: IPermission?] = [:]
    private var locationPermissionHandler: LocationPermissionHandler?
    private var bluetoothPermissionHandler: BluetoothPermissionHandler?
    private var notificationPermissionHandler: NotificationPermissionHandler?
    private var cameraPermissionHandler: CameraPermissionHandler?
    weak var permissionDelegate: GGIScanningPermissionChangedDelegate?
    private let permissionQueue = DispatchQueue(label: "com.greatergoods.permissionQueue", attributes: .concurrent)


    private func initMethods() {
        // Fetch app permissions for the given appType
        guard let permissions = appPermissions[self.appType] else {
            Log("No permissions found for appType: \(self.appType)")
            return
        }

        // Reset the map before initializing handlers
        self.permissionHandlerMap = [:]

        // Initialize permission handlers based on the appType's permissions
        if permissions.contains(.BLUETOOTH) || permissions.contains(.BLUETOOTH_SWITCH) {
            self.bluetoothPermissionHandler = BluetoothPermissionHandler(delegate: self)
            self.permissionHandlerMap[.BLUETOOTH] = bluetoothPermissionHandler
            self.permissionHandlerMap[.BLUETOOTH_SWITCH] = bluetoothPermissionHandler
        }
        if permissions.contains(.LOCATION) || permissions.contains(.LOCATION_SWITCH) {
            self.locationPermissionHandler = LocationPermissionHandler(delegate: self)
            self.permissionHandlerMap[.LOCATION] = locationPermissionHandler
            self.permissionHandlerMap[.LOCATION_SWITCH] = locationPermissionHandler
        }
        if permissions.contains(.NOTIFICATION) {
            self.notificationPermissionHandler = NotificationPermissionHandler(delegate: self)
            self.permissionHandlerMap[.NOTIFICATION] = notificationPermissionHandler
        }
        if permissions.contains(.CAMERA) {
            self.cameraPermissionHandler = CameraPermissionHandler(delegate: self)
            self.permissionHandlerMap[.CAMERA] = cameraPermissionHandler
        }
    }

    func subscribePermissions(call: @escaping GGBTScanCallback, appType: GGAppType) {
        self.pluginCall = call
        self.setAppType(appType)
        self.initMethods()
        guard let permissions = appPermissions[self.appType] else {
            return
        }
        permissionQueue.async(flags: .barrier) {
            Task {
                for permission in permissions {
                    guard let handler = self.permissionHandlerMap[permission], let permissionHandler = handler else {
                        continue
                    }
                    permissionHandler.initManager()
                    guard let isSwitch = permissionSwitchMap[permission] else {
                        continue
                    }
                    let permissionState: GGPermissionState
                    if isSwitch == true {
                        permissionState = await permissionHandler.getSwitchState()
                    } else {
                        permissionState = await permissionHandler.getPermission()
                    }
                    self.permissionStatus[permission] = permissionState
                }
                self.respondBluetoothPermissionCallBack()
                self.respondPermissionCallback()
            }
        }
    }
    
    func requestPermission(permission: GGPermissionType) async -> GGPermissionState{
        guard let handler = self.permissionHandlerMap[permission], let permissionHandler = handler else {
            return .NOT_REQUESTED
        }
        if permissionSwitchMap[permission]! == true {
            let switchState = await permissionHandler.requestSwitchState()
            return switchState
        } else {
            let permissionState = await permissionHandler.requestPermission()
            return permissionState
        }
    }
    
    func onPermissionChanged(_ permissionResponse: GGPermissionResponse) {
        permissionQueue.async(flags: .barrier) {
            self.permissionStatus[permissionResponse.type] = permissionResponse.data
            if permissionResponse.type == .BLUETOOTH || permissionResponse.type == .BLUETOOTH_SWITCH {
                self.respondBluetoothPermissionCallBack()
            }
            self.respondPermissionCallback()
        }
    }
    
    func unsubscribePermissions() {
        // Deinitialize or nullify permission handlers
        self.locationPermissionHandler?.deInitManager()
        self.bluetoothPermissionHandler?.deInitManager()
        self.notificationPermissionHandler?.deInitManager()
        self.cameraPermissionHandler?.deInitManager()
        
        // Reset permission states
        self.permissionStatus = getDefaultPermissionStatus()
        
        // Reset plugin call
        self.pluginCall = nil
    }
    
    private func respondBluetoothPermissionCallBack() {
        let isPermissionsEnabled = self.permissionStatus[.BLUETOOTH] == .ENABLED && self.permissionStatus[.BLUETOOTH_SWITCH] == .ENABLED
        permissionDelegate?.onScanningPermissionChanged(isPermissionsEnabled)
    }
    
    private func setAppType(_ appType: GGAppType) {
        self.appType = appType
    }

    private func respondPermissionCallback() {
        guard let pluginCall = self.pluginCall else {
            return
        }
        pluginCall(
            .success(GGScanResponse (type: .PERMISSION_STATUS, data: GGPermissionResponseData(permissions: self.permissionStatus)))
        )
    }
}
