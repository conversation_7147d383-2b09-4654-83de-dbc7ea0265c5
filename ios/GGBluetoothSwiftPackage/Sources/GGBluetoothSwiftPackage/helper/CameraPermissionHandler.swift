//
//  CameraPermissionHandler.swift
//  GreatergoodsGgBluetoothIonicPlugin
//
//  Created by kir<PERSON><PERSON><PERSON><PERSON> on 05/04/24.
//

import Foundation
import AVFoundation

class CameraPermissionHandler: GGPermission {

    var permissionState: GGPermissionState = .NOT_REQUESTED

    override func getPermission() async -> GGPermissionState {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .denied, .restricted:
            self.permissionState = .DISABLED
        case .authorized:
            self.permissionState = .ENABLED
        case .notDetermined:
            self.permissionState = .NOT_REQUESTED
        default:
            self.permissionState = .NOT_REQUESTED
        }
        return self.permissionState
    }

    override func requestPermission() async -> GGPermissionState {
        // Check current camera authorization status first
        let cameraAuthorizationStatus = AVCaptureDevice.authorizationStatus(for: .video)
        switch cameraAuthorizationStatus {
        case .notDetermined:
            // The user has not been asked to grant camera access yet.
            let isAuthorized = await AVCaptureDevice.requestAccess(for: .video)
            if isAuthorized {
                Log("CameraPermissionHandler requestPermission method: User authorized the camera permission")
            } else {
                Log("CameraPermissionHandler requestPermission method: User denied the camera permission")
            }
            break
        case .restricted, .denied:
            // The user has denied or restricted camera access, navigate to settings.
            DispatchQueue.main.async {
                self.openSettingsWithURL(NavigateToSettings.APPSETTINGS.stringValue())
            }
            break
        case .authorized:
            // The user has already granted permission, you can proceed.
            Log("CameraPermissionHandler: Camera access already authorized")
            break
        @unknown default:
            Log("CameraPermissionHandler: Unrecognized authorization status for camera permission")
            break
        }
        // Fetch and update permission state regardless of the outcome
        let _ = await self.getPermission()
        self.delegate.onPermissionChanged(GGPermissionResponse(type: .CAMERA, data: self.permissionState))
        return self.permissionState
    }

}
