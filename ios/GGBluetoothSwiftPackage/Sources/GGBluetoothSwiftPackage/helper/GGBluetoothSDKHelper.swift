//
//  GGBluetoothSDKHelper.swift
//  Plugin
//
//  Created by Barath Chittibabu on 17/04/23.
//  Copyright © 2023 Max Lynch. All rights reserved.
//

import Foundation
import GGBluetoothSDKFramework

class GGBluetoothSDKHelper: GGIScanningPermissionChangedDelegate {
    
    public static var shared = GGBluetoothSDKHelper()
    private let TAG = "GGBluetoothIonicPlugin"
    internal var userCreationCallBack: ((UserCreationResponseType) -> Void)?
    internal var userUpdationCallBack: ((UserCreationResponseType) -> Void)?
    internal var userDeletionCallBack: ((UserDeletionResponseType) -> Void)?
    internal var wifiListCallBack: ((GGWifiResponse<GGWifiDetails>) -> Void)?
    internal var userListCallBack: ((GGScaleUserResponse) -> Void)?
    internal var wifiMacCallBack: ((String) -> Void)?
    internal var wifiSetupCallBack: ((GGWifiSetupResponse) -> Void)?
    internal var wifiSSIDCallBack: ((String) -> Void)?
    internal var deviceLogCallBack: ((GGDeviceLogResponse<DeviceLog>) -> Void)?
    internal var liveDataCallback: ((String) -> Void)?
    internal var clearDataCallback: ((String) -> Void)?
    internal var devices = [String: GGDevice]()
    internal var deviceLogs: [DeviceLog] = []
    internal var showPairingState = false
    internal var pairedDevices = [GGBTDevice]()
    private var account: GGBTUserProfile?
    private let temporarySkipTime = 30 * 1000 // skip time 30s
    private var temporarySkipDevices = [String: Int]()
    private var skipDevices = [String]()
    private var bleHandler: GGIBluetoothHandler?
    internal var pluginCall: GGBTScanCallback?
    internal var appType: GGAppType = .NONE
    private var ggPermissionHandler = GGPermissionHandler.shared
    private var isContinuousScanningStarted: Bool = false
    private var canPauseDeviceScan: Bool = false
    private var resyncScanInterval: Timer?
    private var timer: DispatchSourceTimer?
    private var isScanInitialized: Bool = false
    private var isPermissionsEnabled: Bool = false
    private var isScanIntervalInitialized: Bool = false
    private var isPairingStarted: Bool = false
    private var canPauseContinuousScan: Bool = false
    private var scanDuration = 20.0;
    private var delayBetweenScan = 10.0;
    init() {
        ggPermissionHandler.permissionDelegate = self
    }
    
    // This method get triggered whenever the bluetooth permissions changes.
    func onScanningPermissionChanged(_ status: Bool) {
        self.isPermissionsEnabled = status
        if self.isScanInitialized && self.isPermissionsEnabled && !self.isContinuousScanningStarted && !self.isScanIntervalInitialized {
            self.isContinuousScanningStarted = true
            self.canPauseContinuousScan = false
            timer?.cancel()
            self.scanDevices()
            self.timer = DispatchSource.makeTimerSource(queue: DispatchQueue.main)
            self.timer?.schedule(deadline: .now(), repeating: .seconds(Int(self.scanDuration + self.delayBetweenScan)))
            self.timer?.setEventHandler { [weak self] in
                guard let self = self else { return }
                self.isScanIntervalInitialized = true
                if self.isPairingStarted || self.canPauseContinuousScan {
                    return
                }
                self.stopScan(true)
                DispatchQueue.main.asyncAfter(deadline: .now() + delayBetweenScan) {
                    if self.isPairingStarted || self.canPauseContinuousScan {
                        return
                    }
                    self.scanDevices()
                }
            }
            timer?.resume()
        }
        else if !self.isPermissionsEnabled {
            timer?.cancel()
            self.stopScan(true)
            self.isContinuousScanningStarted = false
            self.isScanIntervalInitialized = false
        }
    }
    
    func scanForPairing() {
        self.isPairingStarted = true
        self.clearPairedDevices()
        self.disconnectDevices()
        self.stopScan(true)
        self.scanDevices()
    }
    
    func initializeScan(userProfile: GGBTUserProfile, pluginCall: @escaping GGBTScanCallback) {
        self.account = userProfile
        self.pluginCall = pluginCall
        self.isScanInitialized = true
    }
    
    func setAccountData(userProfile: GGBTUserProfile) {
        self.account = userProfile
    }
    
    func setPairedDevices(devices: [GGBTDevice]) {
        self.pairedDevices = devices
    }
    
    func clearPairedDevices() {
        for (_, device) in devices {
            if device.connectionStatus == ConnectionStatusType.CONNECTED {
                device.bleDevice?.disconnect()
            }
        }
        self.pairedDevices.removeAll()
    }
    
    func setBleHandler(handler: GGIBluetoothHandler) {
        self.bleHandler = handler
    }
    
    func scanDevices() {
        guard pluginCall != nil, self.bleHandler != nil else {
            Log("Error: pluginCall or bleHandler is nil.")
            return
        }
        self.bleHandler?.startScan(GGScanData.shared)
    }
    
    func stopScan(_ forceStop: Bool = false, _ canDeninitializeScan: Bool = false) {
        Log("Stop scanning")
        if(forceStop){
            clearSkipDevices()
        }
        if let bleHandler = self.bleHandler {
            bleHandler.stopScan()
        }
        if canDeninitializeScan {
            timer?.cancel()
            self.canPauseContinuousScan = true
            self.isContinuousScanningStarted = false
            self.isScanIntervalInitialized = false
            self.isScanInitialized = false
        }
    }
    
    func pauseScan() {
        self.canPauseDeviceScan = true
    }
    
    func resumeScan(_ clearOnlyPairing: Bool) {
        if clearOnlyPairing {
            self.isPairingStarted = false
        } else {
            self.isPairingStarted = false
            self.canPauseDeviceScan = false
        }
    }
    
    private func clearSkipDevices() {
        Log("Clearing skip devices")
        temporarySkipDevices.removeAll()
        skipDevices.removeAll()
    }
    
    func cancelWifi(_ device: GGBTDevice) {
        Log("Cancel wifi info")
        let isDeviceAvailable = devices.keys.contains(device.broadcastId)
        if isDeviceAvailable {
            if let device = devices[device.broadcastId], let bleDevice = device.bleDevice {
                _ = bleDevice.cancelWifiInfo()
            } else {
                Log ("cancelWifi - Device not available")
            }
        }
    }
    
    func getConnectedWifiSSID(_ device: GGBTDevice, callback: @escaping (String) -> Void) {
        Log("Get connected wifi info")
        let isDeviceAvailable = devices.keys.contains(device.broadcastId)
        if isDeviceAvailable {
            let device = devices[device.broadcastId]
            if device?.bleDevice != nil {
                wifiSSIDCallBack = callback
                _ = device?.bleDevice?.getWifiSSID()
            }
        }
    }
    
    func getDeviceLogs(_ device: GGBTDevice, callback: @escaping (GGDeviceLogResponse<DeviceLog>) -> Void) {
        Log("Get connected device log")
        let isDeviceAvailable = devices.keys.contains(device.broadcastId)
        if isDeviceAvailable {
            let device = devices[device.broadcastId]
            if device?.bleDevice != nil {
                deviceLogCallBack = callback
                _ = device?.bleDevice?.synchronizeLog()
            }
        }
    }
    
    func updateSetting(settings: [GGBTSetting], ggBTDevice: GGBTDevice) {
        Log("Updating Setting\(settings)")
        let device = getBLEByDevice(device: ggBTDevice)
        if let device = device, device.connectionStatus == ConnectionStatusType.CONNECTED {
            settings.forEach { setting in
                self.updateDeviceConfig(setting, ggDevice: device)
                usleep(10000)
            }
        }
    }
    
    private func updateDeviceConfig(_ ggBTSetting: GGBTSetting, ggDevice: GGDevice) {
        let deviceInfo = ggDevice.bleDevice?.getDeviceInfo()
        switch ggBTSetting.key {
        case .IMPEDANCE:
            _ = ggDevice.bleDevice?.setImpedanceSwitch(getSettingValue(from: ggBTSetting) as! Bool, false)
        case .SESSION_IMPEDANCE:
            _ = ggDevice.bleDevice?.setImpedanceSwitch(getSettingValue(from: ggBTSetting) as! Bool , true)
        case .HEART_RATE:
            _ = ggDevice.bleDevice?.setHeartRateSwitch(getSettingValue(from: ggBTSetting) as! Bool)
        case .RESTORE_FACTORY:
            _ = ggDevice.bleDevice?.resetFactorySettings()
        case .INITIAL_LOGO_ANIM:
            _ = ggDevice.bleDevice?.setAnimationPageSwitch(getSettingValue(from: ggBTSetting) as! Bool, deviceInfo?.getEndAnimationState() == .GG_SWITCH_STATE_ON)
        case .FINAL_LOGO_ANIM:
            _ = ggDevice.bleDevice?.setAnimationPageSwitch(deviceInfo?.getStartAnimationState() == .GG_SWITCH_STATE_ON, getSettingValue(from: ggBTSetting) as! Bool)
        case .RESET_FIRMWARE:
            _ = ggDevice.bleDevice?.resetFirmware()
        case .TIME_FORMAT:
            _ = ggDevice.bleDevice?.setTime(GGTimeData.Builder()
                .setUTCTime(Utils.getUTCTimeInSeconds())
                .setTZOffset(Int32(Utils.getTimeZoneInMinutes()))
                .setDisplayType(TimeFormatMap[getSettingValue(from: ggBTSetting) as! String ]  ?? .GG_TIME_DISPLAY_24_HRS)
                .build())
        case .UNIT:
            _ = ggDevice.bleDevice?.setWeightUnit(unitMap[getSettingValue(from: ggBTSetting) as! String ] ?? WeightUnit.WEIGHT_UNKNOWN)
            
        case .TEMPERATURE_UNIT:
            _ = ggDevice.bleDevice?.setTemperatureUnit(temperatureUnitMap[getSettingValue(from: ggBTSetting) as! String] ?? TemperatureUnit.TEMPERATURE_UNKNOWN)
            
        case .SET_MUTEMODE:
            _ = ggDevice.bleDevice?.setMuteMode(.GG_SWITCH_STATE_ON)
            
        case .DISABLE_MUTEMODE:
            _ = ggDevice.bleDevice?.setMuteMode(.GG_SWITCH_STATE_OFF)
            
        case .SUBSCRIBE_TO_LIVE_DATA:
            _ = ggDevice.bleDevice?.subscribeToLiveData()
            
        case .TARE:
            _ = ggDevice.bleDevice?.tare()
            
        case .CLEAR_ALL_DATA:
            _ = ggDevice.bleDevice?.clearAllData()
            
        case .CLEAR_HISTORY_DATA:
            _ = ggDevice.bleDevice?.clearHistoryData()
            
        case .CLEAR_SETTINGS_DATA:
            _ = ggDevice.bleDevice?.clearSettingsData()
            
        case .CLEAR_USER_ACCOUNT_DATA:
            _ = ggDevice.bleDevice?.clearUserAccountData()
            
        case .CLEAR_WIFI_NETWORK_DATA:
            _ = ggDevice.bleDevice?.clearWifiNetworkData()
            
        }
    }
    
    func checkAndPair(connectTo: GGBTDevice, callback: @escaping (UserCreationResponseType) -> Void) {
        
        let token = connectTo.token ?? ""
        let userNumber = connectTo.userNumber ?? 0
        Log("Checking device and start adding account token - \(token) user number - \(userNumber)")
        userCreationCallBack = callback
        if var device = devices[connectTo.broadcastId] {
            Log("Device is available \(token)")
            device.token = token
            device.userNumber = userNumber
            devices[connectTo.broadcastId] = device
            
            if device.connectionStatus == .CONNECTED && device.bleDevice != nil {
                self.showPairingState = true
                self.addUserAccount(scaleToken: token, userNumber: userNumber, ggBLEDevice: device.bleDevice!, connectTo.preference)
            } else {
                Log("New Device pairing \(token)")
                connectDevice(device.ggDevice, true)
            }
            
        } else {
            Log ("checkAndPair - Device not available")
            self.updateUserCreationStatus(UserCreationResponseType.CREATION_FAILED)
        }
    }
    
    func updateAccount(ggBTDevice: GGBTDevice, callback: @escaping (UserCreationResponseType) -> Void) {
        let isDeviceAvailable = devices.keys.contains(ggBTDevice.broadcastId)
        if isDeviceAvailable {
            devices[ggBTDevice.broadcastId]?.token = ggBTDevice.token
            
            if let device = devices[ggBTDevice.broadcastId],
               let bleDevice = device.bleDevice,
               let token = ggBTDevice.token {
                userUpdationCallBack = callback
                self.addUserAccount(
                    scaleToken: token,
                    userNumber: device.userNumber ?? 0,
                    ggBLEDevice: bleDevice,
                    ggBTDevice.preference
                )
            } else {
                callback(.CREATION_FAILED)
            }
        } else {
            callback(.CREATION_FAILED)
        }
    }
    
    func restoreAccount(
        device: GGBTDevice,
        accountName: String,
        callback: @escaping (UserCreationResponseType) -> Void
    ) {
       
        guard var ggDevice = getBLEByDevice(device: device),
              let bleDevice = ggDevice.bleDevice,
              ggDevice.connectionStatus == ConnectionStatusType.CONNECTED else {
            callback(.CREATION_FAILED)
            return
        }
        
        getUserList(device: device) { userList in
            
            if let token = userList.user.first(where: { $0.name == accountName })?.token {
                
                var updatedDevice = device
                updatedDevice.token = token
                
                self.deleteAccount(ggBTDevice: updatedDevice, disconnect: false) { deleteResponse in
                    if deleteResponse == .SUCCESS {
                        self.userCreationCallBack = callback
                        self.updateAccount(ggBTDevice: updatedDevice, callback: {_ in
                            
                        })
                    } else {
                        Log("Failed to delete account for \(accountName)")
                        callback(.CREATION_FAILED)
                    }
                }
            } else {
                Log("Account with name \(accountName) not found")
                callback(.CREATION_FAILED)
            }
        }
    }
    
    func updateProfile(userProfile: GGBTUserProfile, callback: @escaping (String) -> Void) {
        setAccountData(userProfile: userProfile)
        updateProfileInConnectedDevices()
        callback("true")
    }
    
    func getLicenseKey(_ appType: GGAppType) -> String {
        self.appType = appType
        var key = ""
        switch appType {
        case .WEIGHT_GURUS:
            key = "a7b3f494990146d083530e3e1befa2907fd256ce2a2840d49a1a979193fd7c83"
        case .BALANCE_HEALTH:
            key = "ec670222a28f41a9bb52eb8e16750318384cafa405cd46978e2f20486284bcd7"
        case .SMART_BABY:
            key = "226eacbf2c93407da9e6768e009cdbeb462f4d96feed4de1aa2fbad778ab9815"
        case .SMART_KITCHEN:
            key = "81e417fc98d148c981c0c6f2fa0538c2b9c79ebd4a1f40d49c99a4e516f4e1ae"
        case .RPM:
            key = "9ee29d74c441464fafc64138fa0552c983d9b6592b4f4e3892645ba6ed4c7db3"
        case .NONE:
            break
        }
        return key
    }
    
    private func updateProfileInConnectedDevices(){
        for pairedDevice in pairedDevices {
            if let device = devices[pairedDevice.broadcastId], let bleDevice = device.bleDevice, let token = device.token, device.connectionStatus == ConnectionStatusType.CONNECTED {
                self.addUserAccount(scaleToken: token,userNumber: pairedDevice.userNumber ?? 0, ggBLEDevice: bleDevice, pairedDevice.preference)
            } else {
                Log ("updateProfileInConnectedDevices - Device info / token not available")
            }
        }
    }
    
    func deleteAccount(ggBTDevice: GGBTDevice, disconnect: Bool, callback: @escaping (UserDeletionResponseType) -> Void) {
        let isDeviceAvailable = devices.keys.contains(ggBTDevice.broadcastId)
        if isDeviceAvailable {
            if let device = devices[ggBTDevice.broadcastId], let bleDevice = device.bleDevice {
                userDeletionCallBack = callback
                if disconnect {
                    skipDevice(deviceBroadcastId: ggBTDevice.broadcastId)
                }
                if let token = ggBTDevice.token {
                    _ = bleDevice.deleteAccount(Utils.hexStringToByteArray(token))
                }
                if disconnect {
                    bleDevice.disconnect()
                }
                clearSkipDevices()
            } else {
                Log ("deleteAccount - Device info not available")
            }
        }
    }
    
    func getWifiList(device: GGBTDevice, callback: @escaping (GGWifiResponse<GGWifiDetails>) -> Void) {
        Log("Get Wifi List Called")
        let isDeviceAvailable = devices.keys.contains(device.broadcastId)
        if isDeviceAvailable {
            if let btDevice = devices[device.broadcastId], let bleDevice = btDevice.bleDevice {
                wifiListCallBack = callback
                _ = bleDevice.getWifiList()
            }
            else {
                Log ("getWifiList - Device info not available")
            }
        }
    }
    
    func getUserList(device: GGBTDevice, callback: @escaping (GGScaleUserResponse) -> Void) {
        Log("Get user List Called")
        let isDeviceAvailable = devices.keys.contains(device.broadcastId)
        print("inside pkg: \(isDeviceAvailable)")
        if isDeviceAvailable {
            let device = devices[device.broadcastId]
            print("device: \(device)")
            if device?.bleDevice != nil {
                userListCallBack = callback
                let result = device?.bleDevice?.fetchAccountIDList(needImpedance: true)
                print("fetched AccountId List: \(result)")
            }
        } else {
            Log("Device is Not available")
        }
    }
    
    func getWifiMacAddress(device: GGBTDevice, callback: @escaping (String) -> Void) {
        Log("Get Wifi Mac Called")
        let isDeviceAvailable = devices.keys.contains(device.broadcastId)
        if isDeviceAvailable {
            let device = devices[device.broadcastId]
            if device?.bleDevice != nil {
                wifiMacCallBack = callback
                _ = device?.bleDevice?.readWifiMACAddress()
            }
        }
    }
    
    func getStartAnimationState(device: GGBTDevice, callback: @escaping (String) -> Void) {
        Log("Get Wifi Mac Called")
        let isDeviceAvailable = devices.keys.contains(device.broadcastId)
        if isDeviceAvailable {
            let device = devices[device.broadcastId]
            if device?.bleDevice != nil {
                wifiMacCallBack = callback
                _ = GGIDeviceInfo.self
            }
        }
    }
    
    func getDeviceInfo(device: GGBTDevice, callback: @escaping (GGDeviceDetails?) -> Void) {
        Log("Get Device Info")
        let isDeviceAvailable = devices.keys.contains(device.broadcastId)
        if isDeviceAvailable {
            if let btDevice = devices[device.broadcastId], let bleDevice = btDevice.bleDevice {
                callback(convertToDeviceDetails(bleDevice.getDeviceInfo()!))
            }
        } else {
            callback(nil)
        }
    }
    
    func startFirmwareUpdate(device: GGBTDevice, timestamp: Long) {
        Log("Start Firmware Update Called")
        let isDeviceAvailable = devices.keys.contains(device.broadcastId)
        if isDeviceAvailable {
            let device = devices[device.broadcastId]
            if device?.bleDevice != nil {
                _ = device?.bleDevice?.upgradeFirmware(timestamp)
            }
        }
    }
    func setupWifi(device: GGBTDevice, wifiConfig: GGBTWifiConfig, callback: @escaping (GGWifiSetupResponse) -> Void) {
        Log("Setup WiFi called")
        if let ggDevice = getBLEByDevice(device: device), let bleDevice = ggDevice.bleDevice {
            wifiSetupCallBack = callback
            let ggWifiInfo = GGWifiInfo()
            ggWifiInfo.setSSID(wifiConfig.ssid)
            ggWifiInfo.setPassword(wifiConfig.password)
            Log("Wifi Config \(wifiConfig)")
            _ = bleDevice.selectWifiInfo(ggWifiInfo)
        }
    }
    
    func disconnectDevice(deviceBroadcastId: String) {
        if let device = getBLEByDevice(device: GGBTDevice(name: "", broadcastId: deviceBroadcastId,password: nil, token: nil, userNumber: 0, preference: nil, syncAllData: nil, batteryLevel: 99, protocolType: ""
                                                          , macAddress: ""
                                                         )), let bleDevice = device.bleDevice {
            bleDevice.disconnect()
        }
    }
    
    func restoreFirmware(deviceBroadcastId: String) {
        if let device = getBLEByDevice(device: GGBTDevice(name: "", broadcastId: deviceBroadcastId,password: nil, token: nil, userNumber: 0, preference: nil, syncAllData: nil, batteryLevel: 99, protocolType: ""
                                                          , macAddress: ""
                                                         )), let bleDevice = device.bleDevice {
            bleDevice.resetFirmware()
        }
    }
    
    func resetFactorySettings(deviceBroadcastId: String) {
        if let device = getBLEByDevice(device: GGBTDevice(name: "", broadcastId: deviceBroadcastId,password: nil, token: nil, userNumber: 0, preference: nil, syncAllData: nil, batteryLevel: 99, protocolType: ""
                                                          , macAddress: ""
                                                         )), let bleDevice = device.bleDevice {
            bleDevice.resetFactorySettings()
        }
    }
        
    func skipDevice(deviceBroadcastId: String) {
        if temporarySkipDevices[deviceBroadcastId] != nil {
            skipDevices.append(deviceBroadcastId)
            temporarySkipDevices.removeValue(forKey: deviceBroadcastId)
        } else {
            temporarySkipDevices[deviceBroadcastId] = Int(Utils.getCurrentTime())
        }
        disconnectDevice(deviceBroadcastId: deviceBroadcastId)
    }
    
    func isDeviceSkipped(_ deviceBroadcastId: String) -> Bool {
        return (temporarySkipDevices[deviceBroadcastId] != nil || skipDevices.contains(deviceBroadcastId))
    }
    
    func onNewDeviceFound(ggDeviceInfo: GGIDeviceInfo) {
        guard canPairDevices(ggDeviceInfo) else { return }
        
        if self.canPauseDeviceScan && self.isPairingStarted {
            self.handleNewDeviceInPairingMode(ggDeviceInfo)
        } else {
            self.handleNewDeviceInRegularMode(ggDeviceInfo)
        }
    }
    
    private func handleNewDeviceInPairingMode(_ ggDeviceInfo: GGIDeviceInfo) {
        if (ggDeviceInfo.getProtocolType() == .GG_DEVICE_PROTOCOL_A3 && ggDeviceInfo.getIsInPairingMode() == false) {
            return
        }
        if handleAlreadyPairedDevice(ggDeviceInfo) {
            return
        }
        self.addDeviceToList(ggDeviceInfo, isPaired: false)
        
        guard let pluginCall = self.pluginCall else {
            Log("Plugin call is undefined")
            return
        }
        
        pluginCall(.success( GGScanResponse(
            type: .NEW_DEVICE,
            data: convertToDeviceDetails(ggDeviceInfo)
        )))
    }
            
    private func handleNewDeviceInRegularMode(_ ggDeviceInfo: GGIDeviceInfo) {
        if handleAlreadyPairedDevice(ggDeviceInfo) {
            return
        }
        if !self.canPauseDeviceScan {
            self.addDeviceToList(ggDeviceInfo, isPaired: false)
            guard let pluginCall = self.pluginCall else {
                Log("Plugin call is undefined")
                return
            }
            guard let pluginCall = self.pluginCall else {
                Log("Plugin call is undefined")
                return
            }
            pluginCall(.success(GGScanResponse(
                type: .NEW_DEVICE,
                data: convertToDeviceDetails(ggDeviceInfo)
            )))
        }
    }
  
    func getMeasurementLiveData(_ device: GGBTDevice, callback: @escaping (String) -> Void) {
        Log("Get measurement live data")
        let isDeviceAvailable = devices.keys.contains(device.broadcastId)
        if isDeviceAvailable {
            let device = devices[device.broadcastId]
            if device?.bleDevice != nil {
                liveDataCallback = callback
            }
        }
    }
    
    func clearData(_ device: GGBTDevice,_ clearDataType: ClearDataType, callback: @escaping (String) -> Void ) {
        Log("Clear data - \(clearDataType)")
        
        if let device = devices[device.broadcastId] {
            
            switch(clearDataType) {
            case .ALL:
                _ = device.bleDevice?.clearAllData()
                break
            case .ACCOUNT:
                _ = device.bleDevice?.clearUserAccountData()
                break
            case .HISTORY:
                _ = device.bleDevice?.clearHistoryData()
                break
            case .WIFI:
                _ = device.bleDevice?.clearWifiNetworkData()
                break
            case .SETTINGS:
                _ = device.bleDevice?.clearSettingsData()
                break
            }
            
            clearDataCallback = callback
        }
    }
    
    private func handleAlreadyPairedDevice(_ ggDeviceInfo: GGIDeviceInfo) -> Bool {
        if let device = checkDeviceAlreadyPaired(ggDeviceInfo) {
            let broadcastId = ggDeviceInfo.getBroadcastId()
            
            if ((!devices.keys.contains(broadcastId) || (devices[broadcastId]?.connectionStatus == ConnectionStatusType.DISCONNECTED)) && !isDeviceSkipped(broadcastId) ) {
                self.addDeviceToList(ggDeviceInfo, isPaired: true, scaleToken: device.token)
                self.connectDevice(ggDeviceInfo, false)
                return true
            }
        }
        return false
    }
    
    private func getBLEByDevice(device: GGBTDevice) -> GGDevice? {
        let isDeviceAvailable = devices.keys.contains(device.broadcastId)
        if isDeviceAvailable {
            return devices[device.broadcastId]
        }
        return nil
    }
    
    internal func addUserAccount(scaleToken: String, userNumber: Int, ggBLEDevice: GGIBLEDevice, _ preference: GGDevicePreference?) {
        
        guard let account = self.account else {
            Log("Account info is not available")
            return
        }
        
        switch appType {
        case .BALANCE_HEALTH:
            if let accountInfo = getAccountInfoForBPM(account: account, userNumber: userNumber) {
                _ = ggBLEDevice.addAccount(accountInfo, self.showPairingState)
            }
        case .WEIGHT_GURUS: do {
            if let (accountInfo, userPreference, metrics) = getAccountInfoAndPreferenceForScales(account: account,
                                                                                                 scaleToken: scaleToken,
                                                                                                 userNumber: userNumber,
                                                                                                 ggBLEDevice: ggBLEDevice,
                                                                                                 preference: preference) {
                
                print("1:account info: accountId\(accountInfo.accountID())   \(accountInfo.name()), \(accountInfo.age()), height:\(accountInfo.height()),weight:\(accountInfo.weight()), \(accountInfo.gender()) , User:\(accountInfo.userNumber()), isAthelete:\(accountInfo.isAthlete()), Target weight: \(accountInfo.targetWeight())")
                print("1:userPreferance: \(userPreference.getWeightUnit())")
                print("1:metrics:        \(metrics)")
                //print("1:can Measure Impedance: \(userPreference.")
                //       print("1:can Measure Pulse: \(userPreference.canMeasureHeartRate())")
                //print("1: can Measure impedance: \(accountInfo.ca)")
                _ = ggBLEDevice.addAccount(accountInfo, userPreference, metrics, self.showPairingState)
            }
        }
        case .SMART_KITCHEN: do {
            pluginCall?(
                .success(GGScanResponse(
                    type: ScanResponseType.DEVICE_CONNECTED,
                    data: convertToDeviceDetails(ggBLEDevice.getDeviceInfo()!)
                ))
            )
            _ = ggBLEDevice.subscribeToLiveData()
            print("creation completed")
            updateUserCreationStatus(UserCreationResponseType.CREATION_COMPLETED)
        }
        case .SMART_BABY: do{
            print("pairing state",showPairingState)
            pluginCall?(
                .success(GGScanResponse(
                    type: ScanResponseType.DEVICE_CONNECTED,
                    data: convertToDeviceDetails(ggBLEDevice.getDeviceInfo()!)
                ))
            )
            _ = ggBLEDevice.subscribeToLiveData()
            updateUserCreationStatus(UserCreationResponseType.CREATION_COMPLETED)
            if let (accountInfo, userPreference, _) = getAccountInfoAndPreferenceForScales(account: account,
                                                                                           scaleToken: scaleToken,
                                                                                           userNumber: userNumber,
                                                                                           ggBLEDevice: ggBLEDevice,
                                                                                           preference: preference) {
                _ = ggBLEDevice.addAccount(accountInfo, userPreference, [], self.showPairingState)
            }
            
        }
        case .RPM: do{
            pluginCall?(
                .success(GGScanResponse(
                    type: ScanResponseType.DEVICE_CONNECTED,
                    data: convertToDeviceDetails(ggBLEDevice.getDeviceInfo()!)
                ))
            )
            _ = ggBLEDevice.subscribeToLiveData()
            updateUserCreationStatus(UserCreationResponseType.CREATION_COMPLETED)
        }
        default:
            break
        }
        self.showPairingState = false
    }
    
    private func getAccountInfoForBPM(account: GGBTUserProfile, userNumber: Int) -> GGAccountInfo? {
        let accountInfo = GGAccountInfo()
        accountInfo.userNumber(userNumber)
        accountInfo.gender(account.sex?.lowercased() == "male" ? GGGender.GG_GENDER_MALE : GGGender.GG_GENDER_FEMALE)
        accountInfo.age(account.age ?? 0)
        accountInfo.name(account.name)
        return accountInfo
    }
    
    private func getAccountInfoAndPreferenceForScales(account: GGBTUserProfile,
                                                      scaleToken: String,
                                                      userNumber: Int,
                                                      ggBLEDevice: GGIBLEDevice,
                                                      preference: GGDevicePreference?) -> (GGAccountInfo, GGUserPreference, [GGWeighingScaleDisplayDetail])? {
        var metrics: [GGWeighingScaleDisplayDetail] = [
            .GG_WEIGHING_SCALE_DISPLAY_BMI,
            .GG_WEIGHING_SCALE_DISPLAY_BODY_FAT,
            .GG_WEIGHING_SCALE_DISPLAY_MUSCLE,
            .GG_WEIGHING_SCALE_DISPLAY_BODY_WATER,
            .GG_WEIGHING_SCALE_DISPLAY_BONE_MASS,
            .GG_WEIGHING_SCALE_DISPLAY_HEART_RATE,
            .GG_WEIGHING_SCALE_DISPLAY_VISCERAL_FAT,
            .GG_WEIGHING_SCALE_DISPLAY_SUBCUTANEOUS_FAT,
            .GG_WEIGHING_SCALE_DISPLAY_PROTEIN,
            .GG_WEIGHING_SCALE_DISPLAY_SKELETAL_MUSCLE,
            .GG_WEIGHING_SCALE_DISPLAY_BMR,
            .GG_WEIGHING_SCALE_DISPLAY_BODY_AGE,
            .GG_WEIGHING_SCALE_DISPLAY_TARGET,
            .GG_WEIGHING_SCALE_DISPLAY_DAILY_AVERAGE,
            .GG_WEIGHING_SCALE_DISPLAY_WEEKLY_AVERAGE,
            .GG_WEIGHING_SCALE_DISPLAY_MONTHLY_AVERAGE
        ]
        var name = account.name
        var canMeasureImpedance = true
        var canMeasurePulse = false
        var timeFormat = GGTimeDisplayType.GG_TIME_DISPLAY_12_HRS
        if let preference = preference {
            if let displayMetrics = preference.displayMetrics {
                metrics = []
                for item in displayMetrics {
                    if let displayMetric = bodyMetricsMap[item] {
                        metrics.append(displayMetric)
                    }
                }
            }
            name = preference.displayName ?? account.name
            canMeasureImpedance = preference.shouldMeasureImpedance ?? true
            canMeasurePulse = preference.shouldMeasurePulse ?? false
            timeFormat = preference.timeFormat ?? "12" == "24" ? GGTimeDisplayType.GG_TIME_DISPLAY_24_HRS : GGTimeDisplayType.GG_TIME_DISPLAY_12_HRS
        }
        
        let accountInfo = GGAccountInfo()
        accountInfo.accountID(Utils.hexStringToByteArray(scaleToken))
        accountInfo.name(name)
        accountInfo.userNumber(userNumber)
        accountInfo.gender(account.sex?.lowercased() == "male" ? GGGender.GG_GENDER_MALE : GGGender.GG_GENDER_FEMALE)
        accountInfo.age(account.age ?? 0)
        account.height.flatMap { accountInfo.height(Int($0)) }
        var targetWeight = 0
        if ggBLEDevice.getProtocolType() == .GG_DEVICE_PROTOCOL_R4 {
            account.weight.flatMap { accountInfo.weight(Int($0 * 100)) }
            if let goalWeight = account.goalWeight {
                targetWeight = Int(goalWeight * 100)
            }
        } else {
            account.weight.flatMap { accountInfo.weight(Int( convertLbsToKg($0) * 100)) }
            account.goalWeight.flatMap { accountInfo.targetWeight(Int(convertLbsToKg($0) * 100)) }
        }
        
        account.isAthlete.flatMap { accountInfo.isAthlete($0) }
        var goalType: GGGoalType
        switch account.goalType {
        case "lose":
            goalType = GGGoalType.GG_GOAL_LOSE
            if ggBLEDevice.getProtocolType() == .GG_DEVICE_PROTOCOL_R4 {
                accountInfo.targetWeight(targetWeight)
                accountInfo.maintainWeight(0)
            }
            break
        case "gain":
            goalType = GGGoalType.GG_GOAL_GAIN
            if ggBLEDevice.getProtocolType() == .GG_DEVICE_PROTOCOL_R4 {
                accountInfo.targetWeight(targetWeight)
                accountInfo.maintainWeight(0)
            }
            break
        case "maintain":
            goalType = GGGoalType.GG_GOAL_MAINTAIN
            if ggBLEDevice.getProtocolType() == .GG_DEVICE_PROTOCOL_R4 {
                accountInfo.targetWeight(0)
                accountInfo.maintainWeight(targetWeight)
            }
            break
        default:
            goalType = GGGoalType.GG_GOAL_UNSET
        }
        accountInfo.goalType(goalType)
        let unit: WeightUnit = account.unit == "kg" ? .WEIGHT_KILOGRAMS : .WEIGHT_POUNDS
        
        let userPreference = GGUserPreference.Builder()
            .setWeightUnit(unit)
            .setTimeData(
                GGTimeData.Builder()
                    .setUTCTime(Utils.getUTCTimeInSeconds())
                    .setTZOffset(Int32(Utils.getTimeZoneInMinutes()))
                    .setDisplayType(timeFormat)
                    .build()
            )
            .setMeasureImpedance(canMeasureImpedance)
            .setMeasureHeartRate(canMeasurePulse)
            .build()
        
        print("1: can show impedance: \(canMeasureImpedance), can show pulse: \(canMeasurePulse)")
        return (accountInfo, userPreference, metrics)
    }
    
    private func addDeviceToList(_ ggDeviceInfo: GGIDeviceInfo, isPaired: Bool, scaleToken: String? = nil) {
        let broadcastId = ggDeviceInfo.getBroadcastId()
        devices[broadcastId] = GGDevice(ggDevice: ggDeviceInfo, bleDevice: nil, token: scaleToken, existingDevice: isPaired, syncAllData: false)
    }
    
    private func connectDevice(_ ggDeviceInfo: GGIDeviceInfo,_ isPairing: Bool) {
        self.showPairingState = isPairing
        let broadcastId = ggDeviceInfo.getBroadcastId()
        if var device = devices[broadcastId] {
            device.connectionStatus = .CONNECTING
            devices[broadcastId] = device
        }
        if let bluetoothHandler = bleHandler {
            let result = bluetoothHandler.connectDevice(ggDeviceInfo, self)
            print("conenct device result: \(result)")
        }
    }
    
    internal func checkDeviceAlreadyPaired(_ ggDeviceInfo: GGIDeviceInfo) -> GGBTDevice? {
        return pairedDevices.first { $0.broadcastId == ggDeviceInfo.getBroadcastId() }
    }
    
    internal func disconnectDevices() {
        for (key, var device) in devices {
            if device.connectionStatus == ConnectionStatusType.CONNECTED {
                device.connectionStatus = ConnectionStatusType.DISCONNECTED
                devices[key] = device
                guard let pluginCall = self.pluginCall else {
                    Log("Plugin call is undefined")
                    continue
                }
                
                //Resolve plugin call
                pluginCall(
                    .success(GGScanResponse(
                        type: ScanResponseType.DEVICE_DISCONNECTED,
                        data: convertToDeviceDetails(device.ggDevice)
                    ))
                )
            }
            
        }
        
    }
    
    private func canPairDevices(_ ggDeviceInfo: GGIDeviceInfo) -> Bool {
        let broadcastId = ggDeviceInfo.getBroadcastId()
        if skipDevices.contains(broadcastId) {
            return false
        } else if let scaleTime = temporarySkipDevices[broadcastId] {
            let currentTime = Utils.getCurrentTime()
            return (scaleTime + temporarySkipTime) >= currentTime
        }
        return true
    }
}
