//
//  LocationPermissionHandler.swift
//  GreatergoodsGgBluetoothIonicPlugin
//
//  Created by kiruth<PERSON><PERSON><PERSON> <PERSON> on 05/04/24.
//

import Foundation
import CoreLocation
import UIKit

class LocationPermissionHandler: GGPermission, CLLocationManagerDelegate {
    var permissionState: GGPermissionState = .NOT_REQUESTED
    var clLocationManager: CLLocationManager?
    private var observer: NSObjectProtocol?

    override func initManager() {
        DispatchQueue.main.async {
            if self.clLocationManager == nil {
                self.clLocationManager = CLLocationManager()
                self.clLocationManager?.delegate = self
                self.observer = NotificationCenter.default.addObserver(forName: UIApplication.willEnterForegroundNotification, object: nil, queue: OperationQueue.main) { [weak self] _ in
                    guard let strongSelf = self else { return }
                    strongSelf.updatePermissionState()
                }
            }
        }
        
    }
    
    override func deInitManager() {
        if let manager = self.clLocationManager {
            manager.stopUpdatingLocation()
            manager.delegate = nil
            self.clLocationManager = nil
        }
        if let observer = observer {
            NotificationCenter.default.removeObserver(observer)
            self.observer = nil
        }
    }
    
    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager)   {
        Task{
            let permissionState = await self.getPermission()
            self.delegate.onPermissionChanged(GGPermissionResponse(type: .LOCATION, data: permissionState))
            let permissionSwitchState = await self.getSwitchState()
            self.delegate.onPermissionChanged(GGPermissionResponse(type: .LOCATION_SWITCH, data: permissionSwitchState))
        }
    }
    override func getPermission() async -> GGPermissionState {
        let authorizationStatus = CLLocationManager.authorizationStatus()
        switch authorizationStatus {
        case .notDetermined:
            self.permissionState = .NOT_REQUESTED
        case .denied, .restricted:
            self.permissionState = .DISABLED
        case .authorizedAlways, .authorizedWhenInUse:
            if #available(iOS 14.0, *) {
                let preciseLocationEnabled = clLocationManager?.accuracyAuthorization == .fullAccuracy
                if preciseLocationEnabled == false {
                    self.permissionState  = .APPROX_LOCATION
                    return self.permissionState 
                }
            }
            self.permissionState = .ENABLED
        default:
            self.permissionState = .NOT_REQUESTED

        }
        return self.permissionState
    }

    override func requestPermission() async -> GGPermissionState {
        let _ = await self.getPermission()
        guard let locationManager = self.clLocationManager else { return self.permissionState  }
        switch permissionState{
        case .NOT_REQUESTED:
            // This method does not wait for user input, so we return the current state immediately.
            locationManager.requestAlwaysAuthorization()
        case .DISABLED:
            openSettingsWithURL(NavigateToSettings.APPSETTINGS.stringValue())
        case .APPROX_LOCATION:
            if #available(iOS 14.0, *) {
                let queue = DispatchQueue.global()
                queue.async {
                    locationManager.requestTemporaryFullAccuracyAuthorization(withPurposeKey: "wifi setup") { error in
                        if let error = error {
                            Log("LocationPermissionHandler requestPermission method: Error when requesting temporary full location accuracy authorization: \(error)")
                        }
                    }
                }
            }
        case .ENABLED:
            Log("LocationPermissionHandler requestPermission method: Permission Enabled")
        }
        return self.permissionState
    }

    override func getSwitchState() async -> GGPermissionState {
        self.initManager()
        let value = CLLocationManager.locationServicesEnabled()
        if value {
            self.permissionState = .ENABLED
        } else  {
            self.permissionState = .DISABLED
        }
        return self.permissionState
    }


    override func requestSwitchState() async -> GGPermissionState {
        let _ = await self.getSwitchState()
        if permissionState != .ENABLED {
            openSettingsWithURL(NavigateToSettings.LOCATION_SETTING.stringValue())
        }
        return self.permissionState
    }
    
    private func updatePermissionState() {
        Task {
            let permissionState = await self.getPermission()
            self.delegate.onPermissionChanged(GGPermissionResponse(type: .LOCATION, data: permissionState))
            let permissionSwitchState = await self.getSwitchState()
            self.delegate.onPermissionChanged(GGPermissionResponse(type: .LOCATION_SWITCH, data: permissionSwitchState))
        }
    }
}
