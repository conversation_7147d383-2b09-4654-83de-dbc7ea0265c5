//
//  GGScanData.swift
//  Plugin
//
//  Created by <PERSON><PERSON> on 17/04/23.
//  Copyright © 2023 Max Lynch. All rights reserved.
//

import Foundation
import GGBluetoothSDKFramework

class GGScanData: GGIScanDelegate {
    func onConnectionFailed(_ bleDevice: any GGBluetoothSDKFramework.GGIDeviceInfo) {
        
    }
    
    public static var shared = GGScanData()
    func onBLEAdapterStateChanged(_ state: GGBluetoothAdapterState) {
        
        switch state {
            case .GG_BLUETOOTH_ADAPTER_UNAUTHORIZED,
                .GG_BLUETOOTH_ADAPTER_OFF: do {
                    print("Ble adapter state changed - \(state)")
                }
        default:
            print("Ble adapter state changed - \(state)")
        }
    }
   
    func onDeviceFound(_ deviceInfo: GGIDeviceInfo) {
        print("New device found \(deviceInfo.getBroadcastId())")
        GGBluetoothSDKHelper.shared.onNewDeviceFound(ggDeviceInfo: deviceInfo)
    }
    
    func onDeviceConnect(_ bleDevice: GGIBLEDevice) {
        print("Device connected")
    }
    
    func onDeviceDisconnect(_ bleDevice: GGIBLEDevice) {
        print("Device disconnected")
    }
}
