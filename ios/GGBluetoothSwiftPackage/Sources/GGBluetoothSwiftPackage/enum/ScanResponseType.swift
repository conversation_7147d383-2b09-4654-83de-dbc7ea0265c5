//
//  ScanResponseType.swift
//  Plugin
//
//  Created by <PERSON><PERSON> on 17/04/23.
//  Copyright © 2023 <PERSON>. All rights reserved.
//

import Foundation

public enum ScanResponseType: String {
    case KNOWN_DEVICE = "KNOWN_DEVICE"
    case NEW_DEVICE = "NEW_DEVICE"
    case DEVICE_CONNECTED = "DEVICE_CONNECTED"
    case DEVICE_DISCONNECTED = "DEVICE_DISCONNECTED"
    case SINGLE_ENTRY = "SINGLE_ENTRY"
    case MULTI_ENTRIES = "MULTI_ENTRIES"
    case WIFI_STATUS_UPDATE = "WIFI_STATUS_UPDATE"
    case DEVICE_WAKE_UP = "DEVICE_WAKE_UP"
    case DEVICE_INFO_UPDATE = "DEVICE_INFO_UPDATE"
    case DEVICE_MEMORY_FULL = "DEVICE_MEMORY_FULL"
    case DEVICE_DUPLICATE_USER = "DEVICE_DUPLICATE_USER"
    case PERMISSION_STATUS = "PERMISSION_STATUS"
    case LIVE_MEASUREMENT = "LIVE_MEASUREMENT"
}
