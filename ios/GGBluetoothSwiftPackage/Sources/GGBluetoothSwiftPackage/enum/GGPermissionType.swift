//  GGPermissionName.swift
//  Plugin
//
//  Created by k<PERSON><PERSON><PERSON><PERSON><PERSON> on 20/03/24.
//  Copyright © 2024 Max Lynch. All rights reserved.
//

import Foundation

public enum GGPermissionType: String, Codable {
    case ALL = "ALL"
    case NOTIFICATION = "NOTIFICATION"
    case BLUETOOTH_SWITCH = "BLUETOOTH_SWITCH"
    case BLUETOOTH = "BLUETOOTH"
    case NEARBY_DEVICE = "NEARBY_DEVICE"
    case LOCATION = "LOCATION"
    case LOCATION_SWITCH = "LOCATION_SWITCH"
    case CAMERA = "CAMERA"
}
