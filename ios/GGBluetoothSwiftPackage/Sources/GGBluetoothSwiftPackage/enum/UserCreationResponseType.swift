//
//  UserCreationResponseType.swift
//  Plugin
//
//  Created by <PERSON><PERSON> on 17/04/23.
//  Copyright © 2023 <PERSON>. All rights reserved.
//

import Foundation

public enum UserCreationResponseType: String, Codable {
    case CREATION_COMPLETED = "CREATION_COMPLETED"
    case CREATION_FAILED = "CREATION_FAILED"
    case INPUT_DATA_ERROR = "INPUT_DATA_ERROR"
    case MEMORY_FULL = "MEMORY_FULL"
    case DUPLICATE_USER_ERROR = "DUPLICATE_USER_ERROR"
    case USER_SELECTION_IN_PROGRESS = "USER_SELECTION_IN_PROGRESS"
    case DIFFERENT_USER = "DIFFERENT_USER"
    case NOT_IN_PAIRING_MODE = "NOT_IN_PAIRING_MODE"
}

public enum UserDeletionResponseType: String, Codable {
    case SUCCESS = "SUCCESS"
    case FAIL = "FAIL"
    case EXCEPTION_ENCOUNTERED = "EXCEPTION_ENCOUNTERED"
}
