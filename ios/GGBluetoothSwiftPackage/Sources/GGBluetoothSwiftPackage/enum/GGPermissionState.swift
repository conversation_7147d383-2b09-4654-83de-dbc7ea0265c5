//
//  GGPermissionState.swift
//  Plugin
//
//  Created by kir<PERSON><PERSON><PERSON><PERSON> on 20/03/24.
//  Copyright © 2024 <PERSON>. All rights reserved.
//

import Foundation
import UIKit

public enum GGPermissionState : String, Codable {
  case ENABLED = "ENABLED"
  case NOT_REQUESTED = "NOT_REQUESTED"
  case DISABLED = "DISABLED"
  case APPROX_LOCATION = "APPROX_LOCATION"
}


public enum NavigateToSettings: String {
    case SETTINGS
    case APPSETTINGS
    case LOCATION_SETTING
    func stringValue() -> String {
        switch self {
        case .SETTINGS:
            return "App-prefs:Bluetooth"
        case .APPSETTINGS:
            return UIApplication.openSettingsURLString
        case .LOCATION_SETTING:
            return "App-prefs:LOCATION_SERVICES"
        }
    }
}
