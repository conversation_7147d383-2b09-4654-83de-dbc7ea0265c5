//
//  GGBTSettingType.swift
//  Plugin
//
//  Created by <PERSON><PERSON> on 17/04/23.
//  Copyright © 2023 <PERSON>. All rights reserved.
//

import Foundation

public enum GGBTSettingType: String, Codable {
    case HEART_RATE = "HEART_RATE"
    case IMPEDANCE = "IMPEDANCE"
    case SESSION_IMPEDANCE = "SESSION_IMPEDANCE"
    case RESTORE_FACTORY = "RESTORE_FACTORY"
    case INITIAL_LOGO_ANIM = "INITIAL_LOGO_ANIM"
    case FINAL_LOGO_ANIM = "FINAL_LOGO_ANIM"
    case TIME_FORMAT = "TIME_FORMAT"
    case RESET_FIRMWARE = "RESET_FIRMWARE"
    case UNIT = "UNIT"
    case TARE = "TARE"
    case TEMPERATURE_UNIT = "TEMPERATURE_UNIT"
    case SET_MUTEMODE = "MUTEMODE"
    case DISABLE_MUTEMODE = "DISABLE_MUTEMODE"
    case SUBSCRIBE_TO_LIVE_DATA = "SUBSCRIBE_TO_LIVE_DATA"
    case CLEAR_ALL_DATA = "CLEAR_ALL_DATA"
    case CLEAR_USER_ACCOUNT_DATA = "CLEAR_USER_ACCOUNT_DATA"
    case CLEAR_HISTORY_DATA = "CLEAR_HISTORY_DATA"
    case CLEAR_WIFI_NETWORK_DATA = "CLEAR_WIFI_NETWORK_DATA"
    case CLEAR_SETTINGS_DATA = "CLEAR_SETTINGS_DATA"
}
