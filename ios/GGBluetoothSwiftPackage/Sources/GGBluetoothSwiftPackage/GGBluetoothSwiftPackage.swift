import Foundation
import GGBluetoothSDKFramework

public class GGBluetoothSwiftPackage {
    public static var shared = GGBluetoothSwiftPackage()
    private var bleHandler = GGIStub.getHandler()
    private let ggBluetoothSDKHelper = GGBluetoothSDKHelper.shared
    private let ggPermissionHandler = GGPermissionHandler.shared
    
    public init() {
    }
    
    public func scan(_ appType: GGAppType, _ profile: GGBTUserProfile,_ callback: @escaping GGBTScanCallback) {
        _ = GGIStub.setLicenseKey(licenseKey: ggBluetoothSDKHelper.getLicenseKey(appType))
        bleHandler = GGIStub.getHandler()
        ggBluetoothSDKHelper.setBleHandler(handler: bleHandler)
        ggBluetoothSDKHelper.initializeScan(userProfile: profile, pluginCall: callback)
        checkPermission(appType, callback)
       // ggPermissionHandler.subscribePermissions(call: callback, appType: appType)
    }
    
    public func checkPermission(_ appType: GGAppType, _ callback: @escaping GGBTScanCallback) {
        ggPermissionHandler.subscribePermissions(call: callback, appType: appType)
    }
    
    public func scanForPairing() {
        ggBluetoothSDKHelper.scanForPairing()
    }
    
    
    public func confirmPair(_ device: GGBTDevice) async -> UserCreationResponseType {
        return await withCheckedContinuation { continuation in
            ggBluetoothSDKHelper.checkAndPair(connectTo: device) { result in
                continuation.resume(with: .success(result))
            }
        }
    }
    
    public func updateAccount(_ device: GGBTDevice) async -> UserCreationResponseType {
        return await withCheckedContinuation { continuation in
            ggBluetoothSDKHelper.updateAccount(ggBTDevice: device) { result in
                continuation.resume(with:.success(result))
            }
        }

    }
    
    public func restoreAccount(device: GGBTDevice, accountName: String) async -> UserCreationResponseType {
        return await withCheckedContinuation { continuation in
            ggBluetoothSDKHelper.restoreAccount(device: device, accountName: accountName) { result in
                continuation.resume(with: .success(result))
            }
        }
    }
    
    public func deleteUser(_ device: GGBTDevice, canDisconnect: Bool) async -> UserDeletionResponseType {
        return await withCheckedContinuation { continuation in
            ggBluetoothSDKHelper.deleteAccount(ggBTDevice: device, disconnect: canDisconnect) { result in
                continuation.resume(with:.success(result))
            }
        }
    }
    
    public func getWifiList(_ device: GGBTDevice) async -> GGWifiResponse<GGWifiDetails> {
        return await withCheckedContinuation { continuation in
            ggBluetoothSDKHelper.getWifiList(device: device) { result in
                continuation.resume(with:.success(result))
            }
        }
    }
    
    public func setupWifi(_ device: GGBTDevice,_ wifiConfig: GGBTWifiConfig) async -> GGWifiSetupResponse   {
        return await withCheckedContinuation { continuation in
            ggBluetoothSDKHelper.setupWifi(device: device, wifiConfig: wifiConfig) { result in
                continuation.resume(with:.success(result))
            }
        }
    }
    
    public func getUsers(_ device: GGBTDevice) async -> GGScaleUserResponse {
        return await withCheckedContinuation { continuation in
            ggBluetoothSDKHelper.getUserList(device: device) { result in
                continuation.resume(with: .success(result))
            }
        }
    }
    
    public func getWifiMacAddress(_ device: GGBTDevice) async -> String {
        return await withCheckedContinuation { continuation in
            ggBluetoothSDKHelper.getWifiMacAddress(device: device) { result in
                continuation.resume(with: .success(result))
            }
        }
    }
    
    public func getDeviceInfo(_ device: GGBTDevice) async -> GGDeviceDetails {
        return await withCheckedContinuation { continuation in
            ggBluetoothSDKHelper.getDeviceInfo(device: device) { result in
                if result == nil {
                    continuation.resume(with:.failure("Device Information not available" as! Error as! Never))
                } else {
                    continuation.resume(with:.success(result!))
                }
            }
        }
    }
    
    public func updateProfile(profile: GGBTUserProfile) async -> Bool  {
        return await withCheckedContinuation { continuation in
            ggBluetoothSDKHelper.updateProfile(userProfile: profile) { _ in
                continuation.resume(with:.success(true))
            }
        }
    }
    
    public func startFirmwareUpdate(_ device: GGBTDevice,_ timestamp: Long) {
        ggBluetoothSDKHelper.startFirmwareUpdate(device: device, timestamp: Long(timestamp))
    }
    
    public func syncDevices(_ devices: [GGBTDevice]){
        ggBluetoothSDKHelper.setPairedDevices(devices: devices)
    }
    
    public func pauseScan() {
        ggBluetoothSDKHelper.pauseScan()
    }
    
    public func stop() {
        ggPermissionHandler.unsubscribePermissions()
        ggBluetoothSDKHelper.stopScan(true, true)
    }
    
    public func resumeScan(_ clearOnlyPairing: Bool) {
        ggBluetoothSDKHelper.resumeScan(clearOnlyPairing)
    }
    
    public func clearDevices() {
        ggBluetoothSDKHelper.clearPairedDevices()
    }
    
    public func updateSetting(_ device: GGBTDevice,_ settings: [GGBTSetting])  {
        ggBluetoothSDKHelper.updateSetting(settings: settings, ggBTDevice: device)
    }
    
    public func disconnectDevice(_ broadcastId: String) {
        ggBluetoothSDKHelper.disconnectDevice(deviceBroadcastId: broadcastId)
    }
        
    public func skipDevice(_ broadcastId: String) {
        ggBluetoothSDKHelper.skipDevice(deviceBroadcastId: broadcastId)
    }
    
    public func cancelWifi(_ device: GGBTDevice) {
        ggBluetoothSDKHelper.cancelWifi(device)
    }
    
    public func getConnectedWifiSSID(_ device: GGBTDevice) async -> String {
        return await withCheckedContinuation { continuation in
            ggBluetoothSDKHelper.getConnectedWifiSSID(device){ result in
                continuation.resume(with:.success(result))
            }
        }
    }
    
    public func getDeviceLogs(_ device: GGBTDevice) async -> GGDeviceLogResponse<DeviceLog> {
        return await withCheckedContinuation { continuation in
            ggBluetoothSDKHelper.getDeviceLogs(device){ result in
                continuation.resume(with:.success(result))
            }
        }
    }
    
    public func getMeasurementLiveData(_ device: GGBTDevice) async -> String {
        return await withCheckedContinuation { continuation in
            ggBluetoothSDKHelper.getMeasurementLiveData(device){ result in
                continuation.resume(with:.success(result))
            }
        }
    }
    
    public func clearData(_ device: GGBTDevice,_ dataType: ClearDataType) async -> String {
        return await withCheckedContinuation { continuation in
            ggBluetoothSDKHelper.clearData(device, dataType){ result in
                continuation.resume(with:.success(result))
            }
        }
    }
    
  
    public func requestPermission(permissionType: GGPermissionType) async -> String {
        return await withCheckedContinuation { continuation in
            Task {
                let permissionHandler = GGPermissionHandler.shared
                let result = await permissionHandler.requestPermission(permission: permissionType)
                continuation.resume(with:.success(result.rawValue))
            }
        }
       
    }
    
}
