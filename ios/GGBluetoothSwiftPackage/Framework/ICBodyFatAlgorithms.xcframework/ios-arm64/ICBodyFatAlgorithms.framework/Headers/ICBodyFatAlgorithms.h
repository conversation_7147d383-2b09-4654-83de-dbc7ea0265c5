//
//  ICBodyFatAlgorithms.h
//  ICAlgorithm
//
//  Created by <PERSON><PERSON><PERSON> on 2018/9/18.
//  Copyright © 2018年 icomon. All rights reserved.
//

#import <UIKit/UIKit.h>

#define ENABLE_WLA01   1

#define ENABLE_WLA02   1

#define ENABLE_WLA03   1

#define ENABLE_WLA04   1

#define ENABLE_WLA05   1

#define ENABLE_WLA06   1

#define ENABLE_WLA07   1

#define ENABLE_WLA09   1

#define ENABLE_WLA10   1

#define ENABLE_WLA11   1

#define ENABLE_WLA12   1

#define ENABLE_WLA13   1

#define ENABLE_WLA14   1

#define ENABLE_WLA15   1

#define ENABLE_WLA16   1

#define ENABLE_WLA17   1

#define ENABLE_WLA18   1

#define ENABLE_WLA19   1

#define ENABLE_WLA20   1

#define ENABLE_WLA22   1

#define ENABLE_WLA23   1

#define ENABLE_WLA24   1

#define ENABLE_WLA25   1

#define ENABLE_WLA26   1

#define ENABLE_WLA27   1

#define ENABLE_WLA28   1

#define ENABLE_WLA29   1

#define ENABLE_WLA30   1

#define ENABLE_WLA31   1

#define ENABLE_WLA32   1

#define ENABLE_WLA33   1

#define ENABLE_WLA34   1

#define ENABLE_WLA35   1

#define ENABLE_WLA36   1

#define ENABLE_WLA37   1

#define ENABLE_WLA38   1

///////////////////////////////////////【从1001开始，就是一些别的算法，例如客户的】////////////////////////////////////////
#define ENABLE_WLA1001   1
///////////////////////////////////////【End】////////////////////////////////////////

typedef NS_ENUM(NSUInteger, ICBodyFatAlgorithmsSex) {
    ICBodyFatAlgorithmsSexMale = 1,
    ICBodyFatAlgorithmsSexFemale = 2,
};

/**
 * 算法类型
 */
typedef enum : NSUInteger {
#if ENABLE_WLA01
    /*
     * 含水肌肉率
     */
    ICBodyFatAlgorithmsTypeWLA01 = 0,
#endif
#if ENABLE_WLA02
    /*
     * 不含水肌肉率
     */
    ICBodyFatAlgorithmsTypeWLA02 = 1,
#endif
#if ENABLE_WLA03
    /*
     * 新算法1
     */
    ICBodyFatAlgorithmsTypeWLA03 = 2,
#endif
#if ENABLE_WLA04
    /*
     * 新算法2
     */
    ICBodyFatAlgorithmsTypeWLA04 = 3,
#endif
#if ENABLE_WLA05
    /*
     * 新算法3
     */
    ICBodyFatAlgorithmsTypeWLA05 = 4,
#endif
#if ENABLE_WLA06
    /*
     * 新算法4
     */
    ICBodyFatAlgorithmsTypeWLA06 = 5,
#endif
#if ENABLE_WLA07
    /*
     * WLA07
     */
    ICBodyFatAlgorithmsTypeWLA07 = 6,
#endif
#if ENABLE_WLA08
    /*
     * WLA08
     */
    ICBodyFatAlgorithmsTypeWLA08 = 7,
#endif
#if ENABLE_WLA09
    /*
     * WLA09
     */
    ICBodyFatAlgorithmsTypeWLA09 = 8,
#endif
#if ENABLE_WLA10
    /*
     * WLA10
     */
    ICBodyFatAlgorithmsTypeWLA10 = 9,
#endif
#if ENABLE_WLA11
    /*
     * WLA11
     */
    ICBodyFatAlgorithmsTypeWLA11 = 10,
#endif
#if ENABLE_WLA12
    /*
     * WLA12
     */
    ICBodyFatAlgorithmsTypeWLA12 = 11,
#endif
#if ENABLE_WLA13
    /*
     * WLA13
     */
    ICBodyFatAlgorithmsTypeWLA13 = 12,
#endif
#if ENABLE_WLA14
    /*
     * WLA14
     */
    ICBodyFatAlgorithmsTypeWLA14 = 13,
#endif
#if ENABLE_WLA15
    /*
     * WLA15
     */
    ICBodyFatAlgorithmsTypeWLA15 = 14,
#endif
#if ENABLE_WLA16
    /*
     * WLA16
     */
    ICBodyFatAlgorithmsTypeWLA16 = 15,
#endif
#if ENABLE_WLA17
    /*
     * WLA17
     */
    ICBodyFatAlgorithmsTypeWLA17 = 16,
#endif
#if ENABLE_WLA18
    /*
     * WLA18
     */
    ICBodyFatAlgorithmsTypeWLA18 = 17,
#endif
#if ENABLE_WLA19
    /*
     * WLA19
     */
    ICBodyFatAlgorithmsTypeWLA19 = 18,
#endif
#if ENABLE_WLA20
    /*
     * WLA20
     */
    ICBodyFatAlgorithmsTypeWLA20 = 19,
#endif
#if ENABLE_WLA22
    /*
     * WLA22
     */
    ICBodyFatAlgorithmsTypeWLA22 = 21,
#endif
#if ENABLE_WLA23
    /*
     * WLA23
     */
    ICBodyFatAlgorithmsTypeWLA23 = 22,
#endif
#if ENABLE_WLA24
    /*
     * WLA24
     */
    ICBodyFatAlgorithmsTypeWLA24 = 23,
#endif
#if ENABLE_WLA25
    /*
     * WLA25
     */
    ICBodyFatAlgorithmsTypeWLA25 = 24,
#endif
#if ENABLE_WLA27
    /*
     * WLA27
     */
    ICBodyFatAlgorithmsTypeWLA27 = 26,
#endif
#if ENABLE_WLA28
    /*
     * WLA28
     */
    ICBodyFatAlgorithmsTypeWLA28 = 27,
#endif
#if ENABLE_WLA29
    /*
     * WLA29
     */
    ICBodyFatAlgorithmsTypeWLA29 = 28,
#endif
#if ENABLE_WLA30
    /*
     * WLA30
     */
    ICBodyFatAlgorithmsTypeWLA30 = 29,
#endif
#if ENABLE_WLA31
    /*
     * WLA31
     */
    ICBodyFatAlgorithmsTypeWLA31 = 30,
#endif
#if ENABLE_WLA32
    /*
     * WLA32
     */
    ICBodyFatAlgorithmsTypeWLA32 = 31,
#endif
#if ENABLE_WLA33
    /*
     * WLA33
     */
    ICBodyFatAlgorithmsTypeWLA33 = 32,
#endif
#if ENABLE_WLA34
    /*
     * WLA34
     */
    ICBodyFatAlgorithmsTypeWLA34 = 33,
#endif
#if ENABLE_WLA35
    /*
     * WLA35
     */
    ICBodyFatAlgorithmsTypeWLA35 = 34,
#endif
#if ENABLE_WLA36
    /*
     * WLA36
     */
    ICBodyFatAlgorithmsTypeWLA36 = 35,
#endif
#if ENABLE_WLA37
    /*
     * WLA37
     */
    ICBodyFatAlgorithmsTypeWLA37 = 36,
#endif
#if ENABLE_WLA38
    /*
     * WLA38
     */
    ICBodyFatAlgorithmsTypeWLA38 = 37,
#endif
#if ENABLE_WLA1001
    /*
     * WLA1001
     */
    ICBodyFatAlgorithmsTypeWLA1001 = 1000,
#endif
    ICBodyFatAlgorithmsTypeRev = 99,
} ICBodyFatAlgorithmsType;


/**
 * 人类别
 */
typedef enum : NSUInteger {
    /**
     * 普通人
     */
    ICBodyFatAlgorithmsPeopleTypeNormal = 0,
    /**
     * 运动员
     */
    ICBodyFatAlgorithmsPeopleTypeSportsMan = 1,
} ICBodyFatAlgorithmsPeopleType;



#define ICBodyFatAlgorithmsBMI_Standard_WHO  0
#define ICBodyFatAlgorithmsBMI_Standard_ZHCN 1
#define ICBodyFatAlgorithmsBMI_Standard_ASIA 2
#define ICBodyFatAlgorithmsBMI_Standard_ZHTW 3


@interface ICBodyFatAlgorithmsParams : NSObject

@property(nonatomic, assign) CGFloat weight;

@property(nonatomic, assign) NSUInteger height;

@property(nonatomic, assign) NSUInteger sex;

@property(nonatomic, assign) NSUInteger age;

@property(nonatomic, assign) ICBodyFatAlgorithmsType algType;

@property(nonatomic, assign) ICBodyFatAlgorithmsPeopleType peopleType;

@property(nonatomic, assign) NSUInteger bmiStandard;

@property(nonatomic,assign) CGFloat imp1;
@property(nonatomic,assign) CGFloat imp2;
@property(nonatomic,assign) CGFloat imp3;
@property(nonatomic,assign) CGFloat imp4;
@property(nonatomic,assign) CGFloat imp5;

@property(nonatomic,copy) NSArray<NSNumber *> *imps;

@end



@interface ICBodyFatAlgorithmsResult : NSObject

@property (nonatomic, assign) CGFloat        bmi;    //身体质量指数
@property (nonatomic, assign) CGFloat        bfr;    // 体脂率
@property (nonatomic, assign) CGFloat        muscle;    // 肌肉率
@property (nonatomic, assign) CGFloat        subcutfat;    // 皮下脂肪率
@property (nonatomic, assign) CGFloat        vfal;    // 内脏脂肪指数
@property (nonatomic, assign) CGFloat        bone;    //骨量
@property (nonatomic, assign) CGFloat        water;    //体水分率
@property (nonatomic, assign) CGFloat        protein;     //蛋白质率
@property (nonatomic, assign) CGFloat        sm;    // 骨骼肌率
@property (nonatomic, assign) NSUInteger     bmr;    //基础代谢率
@property (nonatomic, assign) NSUInteger     age;     //身体年龄
@property (nonatomic, assign) CGFloat        bodyScore;     // 身体得分
@property (nonatomic, assign) CGFloat        weightTarget;     // 体重目标
@property (nonatomic, assign) CGFloat        bfmControl;     // 体脂量控制
@property (nonatomic, assign) CGFloat        ffmControl;     // 肌肉量控制
@property (nonatomic, assign) CGFloat        weightControl;     // 体重控制
@property (nonatomic, assign) NSUInteger     bodyType;     // 体型

@property (nonatomic, assign) CGFloat        smi; // 骨骼肌质量指数
@property (nonatomic, assign) NSUInteger      obesityDegree;    // 肥胖程度
@property (nonatomic, assign) CGFloat        whr; // 腰臀比

@property (nonatomic, assign) CGFloat        weightStandard;    // 标准体重
@property (nonatomic, assign) CGFloat        bfmStandard;       // 标准脂肪量
@property (nonatomic, assign) CGFloat        bmiStandard;       // 标准BMI
@property (nonatomic, assign) int            bmrStandard;       // 标准BMR
@property (nonatomic, assign) CGFloat        smmStandard;       // 标准骨骼肌量
@property (nonatomic, assign) CGFloat        ffmStandard;       // 标准去脂体重
@property (nonatomic, assign) CGFloat        bfpStandard;       // 标准体脂率

@property (nonatomic, assign) CGFloat     trunkBodyfatPercentage;     //躯干体脂率
@property (nonatomic, assign) CGFloat     leftArmBodyfatPercentage;     //左手体脂率
@property (nonatomic, assign) CGFloat     rightArmBodyfatPercentage;     //右手体脂率
@property (nonatomic, assign) CGFloat     leftLegBodyfatPercentage;     //左脚体脂率
@property (nonatomic, assign) CGFloat     rightLegBodyfatPercentage;     //右脚体脂率

@property (nonatomic, assign) CGFloat     trunkBodyfatMass;     //躯干脂肪量
@property (nonatomic, assign) CGFloat     leftArmBodyfatMass;     //左手脂肪量
@property (nonatomic, assign) CGFloat     rightArmBodyfatMass;     //右手脂肪量
@property (nonatomic, assign) CGFloat     leftLegBodyfatMass;     //左脚脂肪量
@property (nonatomic, assign) CGFloat     rightLegBodyfatMass;     //右脚脂肪量

@property (nonatomic, assign) CGFloat     trunkMuscle;     //躯干肌肉率
@property (nonatomic, assign) CGFloat     leftArmMuscle;     //左手肌肉率
@property (nonatomic, assign) CGFloat     rightArmMuscle;     //右手肌肉率
@property (nonatomic, assign) CGFloat     leftLegMuscle;     //左脚肌肉率
@property (nonatomic, assign) CGFloat     rightLegMuscle;     //右脚肌肉率

@property (nonatomic, assign) CGFloat     trunkMuscleMass;     //躯干肌肉量
@property (nonatomic, assign) CGFloat     leftArmMuscleMass;     //左手肌肉量
@property (nonatomic, assign) CGFloat     rightArmMuscleMass;     //右手肌肉量
@property (nonatomic, assign) CGFloat     leftLegMuscleMass;     //左脚肌肉量
@property (nonatomic, assign) CGFloat     rightLegMuscleMass;     //右脚肌肉量


@property (nonatomic, assign) CGFloat      bmiMax;              // bmi标准的最大值
@property (nonatomic, assign) CGFloat      bmiMin;              // bmi标准的最小值
@property (nonatomic, assign) CGFloat      bfmMax;              // 脂肪量标准的最大值
@property (nonatomic, assign) CGFloat      bfmMin;              // 脂肪量标准的最小值
@property (nonatomic, assign) CGFloat      bfpMax;              // 脂肪率标准的最大值
@property (nonatomic, assign) CGFloat      bfpMin;              // 脂肪率标准的最小值
@property (nonatomic, assign) CGFloat      weightMax;           // 体重标准的最大值
@property (nonatomic, assign) CGFloat      weightMin;           // 体重标准的最小值
@property (nonatomic, assign) CGFloat      smmMax;              // 骨骼肌量标准的最大值
@property (nonatomic, assign) CGFloat      smmMin;              // 骨骼肌量标准的最小值
@property (nonatomic, assign) CGFloat      boneMax;             // 骨量标准的最大值
@property (nonatomic, assign) CGFloat      boneMin;             // 骨量标准的最小值
@property (nonatomic, assign) CGFloat      waterMassMax;        // 含水量标准的最大值
@property (nonatomic, assign) CGFloat      waterMassMin;        // 含水量标准的最小值
@property (nonatomic, assign) CGFloat      proteinMassMax;      // 蛋白量标准的最大值
@property (nonatomic, assign) CGFloat      proteinMassMin;      // 蛋白量标准的最小值
@property (nonatomic, assign) CGFloat      muscleMassMax;       // 肌肉量标准的最大值
@property (nonatomic, assign) CGFloat      muscleMassMin;       // 肌肉量标准的最小值
@property (nonatomic, assign) NSUInteger   bmrMax;              // bmr标准的最大值
@property (nonatomic, assign) NSUInteger   bmrMin;              // bmr标准的最小值

@end


/**
 体脂算法
 */
@interface ICBodyFatAlgorithms : NSObject

/**
 BMI
 
 @param weight 体重,单位:kg，精度:0.1
 @param height 身高,单位:cm
 @param type 选用算法的类型
 @return 身体指数，精度:0.1
 */
+ (float)getBMI:(float)weight height:(NSUInteger)height type:(ICBodyFatAlgorithmsType)type peopleType:(ICBodyFatAlgorithmsPeopleType)peopleType;


/**
 体脂率
 
 @param weight 体重,单位:kg，精度:0.1
 @param height 身高,单位:cm
 @param age 年龄
 @param adc 阻抗,单位:ohm
 @param sex 性别, 1:男, 2:女
 @param type 选用算法的类型
 @return 体脂率,精度:0.01
 */
+ (float)getBodyFatPercent:(float)weight height:(NSUInteger)height age:(NSUInteger)age adc:(float)adc adc2:(float)adc2  sex:(ICBodyFatAlgorithmsSex)sex type:(ICBodyFatAlgorithmsType)type peopleType:(ICBodyFatAlgorithmsPeopleType)peopleType;

/**
 皮下脂肪率
 
 @param weight 体重,单位:kg，精度:0.1
 @param height 身高,单位:cm
 @param age 年龄
 @param adc 阻抗,单位:ohm
 @param sex 性别, 1:男, 2:女
 @param type 选用算法的类型
 @return 皮下脂肪率,精度:0.01
 */
+ (float)getSubcutaneousFatPercent:(float)weight height:(NSUInteger)height age:(NSUInteger)age adc:(float)adc adc2:(float)adc2  sex:(ICBodyFatAlgorithmsSex)sex type:(ICBodyFatAlgorithmsType)type peopleType:(ICBodyFatAlgorithmsPeopleType)peopleType;

/**
 内脏脂肪率
 
 @param weight 体重,单位:kg，精度:0.1
 @param height 身高,单位:cm
 @param age 年龄
 @param adc 阻抗,单位:ohm
 @param sex 性别, 1:男, 2:女
 @param type 选用算法的类型
 @return 内脏脂肪率,精度:0.01
 */
+ (float)getVisceralFat:(float)weight height:(NSUInteger)height age:(NSUInteger)age adc:(float)adc adc2:(float)adc2  sex:(ICBodyFatAlgorithmsSex)sex type:(ICBodyFatAlgorithmsType)type peopleType:(ICBodyFatAlgorithmsPeopleType)peopleType;

/**
 肌肉率
 
 @param weight 体重,单位:kg，精度:0.1
 @param height 身高,单位:cm
 @param age 年龄
 @param adc 阻抗,单位:ohm
 @param sex 性别, 1:男, 2:女
 @param type 选用算法的类型
 @return 肌肉率,精度:0.01
 */
+ (float)getMusclePercent:(float)weight height:(NSUInteger)height age:(NSUInteger)age adc:(float)adc adc2:(float)adc2  sex:(ICBodyFatAlgorithmsSex)sex type:(ICBodyFatAlgorithmsType)type peopleType:(ICBodyFatAlgorithmsPeopleType)peopleType;

/**
 基础代谢率
 
 @param weight 体重,单位:kg，精度:0.1
 @param height 身高,单位:cm
 @param age 年龄
 @param adc 阻抗,单位:ohm
 @param sex 性别, 1:男, 2:女
 @param type 选用算法的类型
 @return 基础代谢率,精度:0.01
 */
+ (NSUInteger)getBMR:(float)weight height:(NSUInteger)height age:(NSUInteger)age adc:(float)adc adc2:(float)adc2  sex:(ICBodyFatAlgorithmsSex)sex type:(ICBodyFatAlgorithmsType)type peopleType:(ICBodyFatAlgorithmsPeopleType)peopleType;

/**
 骨重
 
 @param weight 体重,单位:kg，精度:0.1
 @param height 身高,单位:cm
 @param age 年龄
 @param adc 阻抗,单位:ohm
 @param sex 性别, 1:男, 2:女
 @param type 选用算法的类型
 @return 骨量,精度:0.01,单位:kg
 */
+ (float)getBoneMass:(float)weight height:(NSUInteger)height age:(NSUInteger)age adc:(float)adc adc2:(float)adc2  sex:(ICBodyFatAlgorithmsSex)sex type:(ICBodyFatAlgorithmsType)type peopleType:(ICBodyFatAlgorithmsPeopleType)peopleType;

/**
 水分率
 
 @param weight 体重,单位:kg，精度:0.1
 @param height 身高,单位:cm
 @param age 年龄
 @param adc 阻抗,单位:ohm
 @param sex 性别, 1:男, 2:女
 @param type 选用算法的类型
 @return 水分率,精度:0.01
 */
+ (float)getMoisturePercent:(float)weight height:(NSUInteger)height age:(NSUInteger)age adc:(float)adc adc2:(float)adc2  sex:(ICBodyFatAlgorithmsSex)sex type:(ICBodyFatAlgorithmsType)type peopleType:(ICBodyFatAlgorithmsPeopleType)peopleType;

/**
 身体年龄
 
 @param weight 体重,单位:kg，精度:0.1
 @param height 身高,单位:cm
 @param age 年龄
 @param adc 阻抗,单位:ohm
 @param sex 性别, 1:男, 2:女
 @param type 选用算法的类型
 @return 身体年龄
 */
+ (NSUInteger)getPhysicalAge:(float)weight height:(NSUInteger)height age:(NSUInteger)age adc:(float)adc adc2:(float)adc2  sex:(ICBodyFatAlgorithmsSex)sex type:(ICBodyFatAlgorithmsType)type peopleType:(ICBodyFatAlgorithmsPeopleType)peopleType;

/**
 蛋白率
 
 @param weight 体重,单位:kg，精度:0.1
 @param height 身高,单位:cm
 @param age 年龄
 @param adc 阻抗,单位:ohm
 @param sex 性别, 1:男, 2:女
 @param type 选用算法的类型
 @return 蛋白率
 */
+ (float)getProtein:(float)weight height:(NSUInteger)height age:(NSUInteger)age adc:(float)adc adc2:(float)adc2  sex:(ICBodyFatAlgorithmsSex)sex type:(ICBodyFatAlgorithmsType)type peopleType:(ICBodyFatAlgorithmsPeopleType)peopleType;


/**
 骨骼肌率
 
 @param weight 体重,单位:kg，精度:0.1
 @param height 身高,单位:cm
 @param age 年龄
 @param adc 阻抗,单位:ohm
 @param sex 性别, 1:男, 2:女
 @param type 选用算法的类型
 @return 骨骼肌率
 */
+ (float)getSkeletalMuscle:(float)weight height:(NSUInteger)height age:(NSUInteger)age adc:(float)adc adc2:(float)adc2  sex:(ICBodyFatAlgorithmsSex)sex type:(ICBodyFatAlgorithmsType)type peopleType:(ICBodyFatAlgorithmsPeopleType)peopleType;


+ (ICBodyFatAlgorithmsResult *)calc:(ICBodyFatAlgorithmsParams *)params;

+ (ICBodyFatAlgorithmsResult *)calc_other:(double)weight height:(int)height sex:(ICBodyFatAlgorithmsSex)sex age:(int)age bmiStandard:(int)bmiStandard bfr:(double)bfr;
/**
 SDK版本
 
 @return SDK版本
 */
+ (NSString *)version;

@end
