//
//  GGBluetoothSDKFramework.h
//  GGBluetoothSDKFramework
//
//  Created by <PERSON><PERSON> Chitti<PERSON> on 08/02/23.
//

#import <Foundation/Foundation.h>

#import <GGBluetoothSDKFramework/BhBodyCompositionEnum.h>
#import <GGBluetoothSDKFramework/BhTwoLegs140.h>
#import <GGBluetoothSDKFramework/abyonCalculate.h>


//! Project version number for GGBluetoothSDKFramework.
FOUNDATION_EXPORT double GGBluetoothSDKFrameworkVersionNumber;

//! Project version string for GGBluetoothSDKFramework.
FOUNDATION_EXPORT const unsigned char GGBluetoothSDKFrameworkVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <GGBluetoothSDKFramework/PublicHeader.h>


