// swift-interface-format-version: 1.0
// swift-compiler-version: Apple Swift version 6.0.3 effective-5.10 (swiftlang-6.0.3.1.10 clang-1600.0.30.1)
// swift-module-flags: -target arm64-apple-ios13.0 -enable-objc-interop -enable-library-evolution -swift-version 5 -enforce-exclusivity=checked -O -enable-bare-slash-regex -module-name GGBluetoothSDKFramework
// swift-module-flags-ignorable: -no-verify-emitted-module-interface
import Combine
import CoreBluetooth
import Foundation
@_exported import GGBluetoothSDKFramework
import ICBodyFatAlgorithms
import Swift
import _Concurrency
import _StringProcessing
import _SwiftConcurrencyShims
public protocol GGIWifiDataCallback {
  func onReceiveWifiMacAddress(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ deviceInfo: any GGBluetoothSDKFramework.GGIDeviceInfo) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveWifiConnectState(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ wifiState: GGBluetoothSDKFramework.GGWifiState, _ displayErrorCode: Swift.String) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveWifiList(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ wifiList: [GGBluetoothSDKFramework.GGWifiInfo]) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveWifiSSID(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ ssid: Swift.String!) -> GGBluetoothSDKFramework.GGResultType
}
public struct BlockModeOption : Swift.OptionSet {
  public let rawValue: Swift.Int
  public init(rawValue: Swift.Int)
  @usableFromInline
  internal static let none: GGBluetoothSDKFramework.BlockModeOption
  @usableFromInline
  internal static let initializationVectorRequired: GGBluetoothSDKFramework.BlockModeOption
  @usableFromInline
  internal static let paddingRequired: GGBluetoothSDKFramework.BlockModeOption
  @usableFromInline
  internal static let useEncryptToDecrypt: GGBluetoothSDKFramework.BlockModeOption
  public typealias ArrayLiteralElement = GGBluetoothSDKFramework.BlockModeOption
  public typealias Element = GGBluetoothSDKFramework.BlockModeOption
  public typealias RawValue = Swift.Int
}
public enum GGBGErrorCode : Swift.Int {
  case E0
  case E1
  case E2
  case E3
  case E4
  case E5
  case E6
  case E7
  case E8
  case E9
  case E10
  public func getMessage() -> Swift.String
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc public class BluetoothPeripheral : ObjectiveC.NSObject, CoreBluetooth.CBPeripheralDelegate {
  public var MAX_MTU: Swift.Int
  public init(fromPeripheral peripheral: CoreBluetooth.CBPeripheral, bluetoothCentralManager: GGBluetoothSDKFramework.BluetoothCentralManager, withScanResult scanResult: GGBluetoothSDKFramework.ScanResult, andDelegate delegate: (any GGBluetoothSDKFramework.BluetoothPeripheralDelegate)?)
  public func getPeripheral() -> CoreBluetooth.CBPeripheral
  public func getName() -> Swift.String
  public func getPeripheralId() -> Swift.String
  public func getAddress() -> Swift.String
  public func cancelConnection()
  public func getServices() -> [CoreBluetooth.CBService]
  public func getService(_ serviceUUID: CoreBluetooth.CBUUID) -> CoreBluetooth.CBService?
  public func getCharacteristic(_ serviceUUID: CoreBluetooth.CBUUID?, _ characteristicUUID: CoreBluetooth.CBUUID?) -> CoreBluetooth.CBCharacteristic?
  @discardableResult
  public func readCharacteristic(_ serviceUUID: CoreBluetooth.CBUUID?, _ characteristicUUID: CoreBluetooth.CBUUID?) throws -> GGBluetoothSDKFramework.boolean
  @discardableResult
  public func readCharacteristic(characteristic: CoreBluetooth.CBCharacteristic?) -> GGBluetoothSDKFramework.boolean
  public func writeCharacteristic(_ characteristic: CoreBluetooth.CBCharacteristic, _ value: [GGBluetoothSDKFramework.byte], _ writeType: CoreBluetooth.CBCharacteristicWriteType) throws -> GGBluetoothSDKFramework.boolean
  @discardableResult
  public func setNotify(_ serviceUUID: CoreBluetooth.CBUUID?, _ characteristicUUID: CoreBluetooth.CBUUID?, _ enable: GGBluetoothSDKFramework.boolean) throws -> GGBluetoothSDKFramework.boolean
  @discardableResult
  public func setNotify(_ characteristic: CoreBluetooth.CBCharacteristic?, _ enable: GGBluetoothSDKFramework.boolean) -> GGBluetoothSDKFramework.boolean
  public func requestMtu(_ mtu: GGBluetoothSDKFramework.int) -> GGBluetoothSDKFramework.boolean
  @objc public func peripheralDidUpdateName(_ peripheral: CoreBluetooth.CBPeripheral)
  @objc public func peripheral(_ peripheral: CoreBluetooth.CBPeripheral, didDiscoverServices error: (any Swift.Error)?)
  @objc public func peripheral(_ peripheral: CoreBluetooth.CBPeripheral, didDiscoverCharacteristicsFor service: CoreBluetooth.CBService, error: (any Swift.Error)?)
  @objc public func peripheral(_ peripheral: CoreBluetooth.CBPeripheral, didUpdateValueFor characteristic: CoreBluetooth.CBCharacteristic, error: (any Swift.Error)?)
  @objc deinit
}
public protocol GGIScanDelegate {
  func onDeviceFound(_ deviceInfo: any GGBluetoothSDKFramework.GGIDeviceInfo)
  func onDeviceConnect(_ bleDevice: any GGBluetoothSDKFramework.GGIBLEDevice)
  func onDeviceDisconnect(_ bleDevice: any GGBluetoothSDKFramework.GGIBLEDevice)
  func onConnectionFailed(_ bleDevice: any GGBluetoothSDKFramework.GGIDeviceInfo)
  func onBLEAdapterStateChanged(_ state: GGBluetoothSDKFramework.GGBluetoothAdapterState)
}
public enum GGBGStatus : Swift.Int {
  case GGBG_STATUS_UNKNOWN
  case GGBG_STATUS_TEST_RESULT
  case GGBG_STATUS_INSERT_STRIP
  case GGBG_STATUS_COLLECTING
  case GGBG_STATUS_COLLECTED
  case GGBG_STATUS_RESULT
  case GGBG_STATUS_ALARM
  case GGBG_STATUS_POWER_OFF
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@inlinable internal func rotateLeft(_ value: Swift.UInt8, by: Swift.UInt8) -> Swift.UInt8 {
  ((value << by) & 0xff) | (value >> (8 - by))
}
@inlinable internal func rotateLeft(_ value: Swift.UInt16, by: Swift.UInt16) -> Swift.UInt16 {
  ((value << by) & 0xffff) | (value >> (16 - by))
}
@inlinable internal func rotateLeft(_ value: Swift.UInt32, by: Swift.UInt32) -> Swift.UInt32 {
  ((value << by) & 0xffffffff) | (value >> (32 - by))
}
@inlinable internal func rotateLeft(_ value: Swift.UInt64, by: Swift.UInt64) -> Swift.UInt64 {
  (value << by) | (value >> (64 - by))
}
@inlinable internal func rotateRight(_ value: Swift.UInt16, by: Swift.UInt16) -> Swift.UInt16 {
  (value >> by) | (value << (16 - by))
}
@inlinable internal func rotateRight(_ value: Swift.UInt32, by: Swift.UInt32) -> Swift.UInt32 {
  (value >> by) | (value << (32 - by))
}
@inlinable internal func rotateRight(_ value: Swift.UInt64, by: Swift.UInt64) -> Swift.UInt64 {
  ((value >> by) | (value << (64 - by)))
}
@inlinable internal func reversed(_ uint8: Swift.UInt8) -> Swift.UInt8 {
  var v = uint8
  v = (v & 0xf0) >> 4 | (v & 0x0f) << 4
  v = (v & 0xcc) >> 2 | (v & 0x33) << 2
  v = (v & 0xaa) >> 1 | (v & 0x55) << 1
  return v
}
@inlinable internal func reversed(_ uint32: Swift.UInt32) -> Swift.UInt32 {
  var v = uint32
  v = ((v >> 1) & 0x55555555) | ((v & 0x55555555) << 1)
  v = ((v >> 2) & 0x33333333) | ((v & 0x33333333) << 2)
  v = ((v >> 4) & 0x0f0f0f0f) | ((v & 0x0f0f0f0f) << 4)
  v = ((v >> 8) & 0x00ff00ff) | ((v & 0x00ff00ff) << 8)
  v = ((v >> 16) & 0xffff) | ((v & 0xffff) << 16)
  return v
}
@inlinable internal func xor<T, V>(_ left: T, _ right: V) -> Swift.ArraySlice<Swift.UInt8> where T : Swift.RandomAccessCollection, V : Swift.RandomAccessCollection, T.Element == Swift.UInt8, T.Index == Swift.Int, V.Element == Swift.UInt8, V.Index == Swift.Int {
  return xor(left, right).slice
}
@inlinable internal func xor<T, V>(_ left: T, _ right: V) -> Swift.Array<Swift.UInt8> where T : Swift.RandomAccessCollection, V : Swift.RandomAccessCollection, T.Element == Swift.UInt8, T.Index == Swift.Int, V.Element == Swift.UInt8, V.Index == Swift.Int {
  let length = Swift.min(left.count, right.count)

  let buf = UnsafeMutablePointer<UInt8>.allocate(capacity: length)
  buf.initialize(repeating: 0, count: length)
  defer {
    buf.deinitialize(count: length)
    buf.deallocate()
  }

   
  for i in 0..<length {
    buf[i] = left[left.startIndex.advanced(by: i)] ^ right[right.startIndex.advanced(by: i)]
  }

  return Array(UnsafeBufferPointer(start: buf, count: length))
}
@inline(__always) @inlinable internal func bitPadding(to data: inout Swift.Array<Swift.UInt8>, blockSize: Swift.Int, allowance: Swift.Int = 0) {
  let msgLength = data.count
   
   
  data.append(0x80)

   
  let max = blockSize - allowance  
  if msgLength % blockSize < max {  
    data += Array<UInt8>(repeating: 0, count: max - 1 - (msgLength % blockSize))
  } else {
    data += Array<UInt8>(repeating: 0, count: blockSize + max - 1 - (msgLength % blockSize))
  }
}
open class BinaryDataReader {
  public var readIndex: Swift.Int {
    get
  }
  public init(_ data: GGBluetoothSDKFramework.BinaryData, readIndex: Swift.Int = 0)
  open func read(_ bigEndian: Swift.Bool? = nil) throws -> Swift.UInt8
  open func read(_ bigEndian: Swift.Bool? = nil) throws -> Swift.Int8
  open func read(_ bigEndian: Swift.Bool? = nil) throws -> Swift.UInt16
  open func read(_ bigEndian: Swift.Bool? = nil) throws -> Swift.Int16
  open func read(_ bigEndian: Swift.Bool? = nil) throws -> Swift.UInt32
  open func read(_ bigEndian: Swift.Bool? = nil) throws -> Swift.Int32
  open func read(_ bigEndian: Swift.Bool? = nil) throws -> Swift.UInt64
  open func read(_ bigEndian: Swift.Bool? = nil) throws -> Swift.Int64
  open func read() throws -> Swift.Float32
  open func read() throws -> Swift.Float64
  open func readNullTerminatedUTF8() throws -> Swift.String
  open func readUTF8(_ length: Swift.Int) throws -> Swift.String
  open func read(_ length: Swift.Int) throws -> GGBluetoothSDKFramework.BinaryData
  @objc deinit
}
public enum GGResultType : Swift.Int {
  case GG_FAIL
  case GG_CALLBACK_NOT_REGISTERED
  case GG_EXCEPTION_ENCOUNTERED
  case GG_UNSUPPORTED
  case GG_RANGE_ERROR
  case GG_HAS_PREVIOUS_VALUE
  case GG_NOT_FOUND_ERROR
  case GG_NOT_IN_PAIRING_MODE
  case GG_DISABLED
  case GG_WIFI_CONNECTION_ERROR
  case GG_MEMORY_FULL_ERROR
  case GG_INPUT_DATA_ERROR
  case GG_USER_SELECTION_IN_PROGRESS
  case GG_DUPLICATE_USER_ERROR
  case GG_DIFFERENT_USER
  case GG_CANCELLED
  case GG_PASSWORD_ERROR
  case GG_IN_PAIRING_MODE
  case GG_MEASUREMENT_ERROR
  case GG_RESULT_UNSET
  case GG_OK
  public var description: Swift.String {
    get
  }
  public func toString() -> Swift.String
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
extension CoreBluetooth.CBUUID {
  public func toString() -> Swift.String
}
public protocol GGIDataDelegate {
  func onDeviceConnect(_ bleDevice: any GGBluetoothSDKFramework.GGIBLEDevice) -> GGBluetoothSDKFramework.GGResultType
  func onDeviceDisconnect(_ bleDevice: any GGBluetoothSDKFramework.GGIBLEDevice) -> GGBluetoothSDKFramework.GGResultType
  func onConnectionFailed(_ bleDevice: any GGBluetoothSDKFramework.GGIBLEDevice) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveBatteryLevel(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ batteryLevel: Swift.Int) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveAcknowledgement(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ operation: GGBluetoothSDKFramework.GGOperationType) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveAccountIDList(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ accountIDList: Swift.Array<GGBluetoothSDKFramework.GGAccountInfo>) -> GGBluetoothSDKFramework.GGResultType
}
public protocol GGIDeviceInfo {
  func getProtocolType() -> GGBluetoothSDKFramework.GGDeviceProtocolType
  func getDeviceType() -> GGBluetoothSDKFramework.GGBLEDeviceType
  func getSystemID() -> Swift.String
  func getManufacturerName() -> Swift.String
  func getModelNumber() -> Swift.String
  func getSerialNumber() -> Swift.String
  func getHardwareRevision() -> Swift.String
  func getSoftwareRevision() -> Swift.String
  func getFirmwareRevision() -> Swift.String
  func getVersion() -> Swift.String
  func getDeviceName() -> Swift.String
  func getMacAddress() -> Swift.String
  func getIdentifier() -> Swift.String
  func getBroadcastId() -> Swift.String
  func getPassword() -> Swift.String
  func setPassword(password: Swift.String)
  func getIsInPairingMode() -> GGBluetoothSDKFramework.boolean
  func getDiscoveryTime() -> Swift.Int64
  func getBatteryLevel() -> Swift.UInt8
  func getDeviceUnit() -> GGBluetoothSDKFramework.WeightUnit
  func getBgmUnit() -> GGBluetoothSDKFramework.BgmUnit
  func getWifiSetupState() -> GGBluetoothSDKFramework.GGWifiState
  func getWifiMacAddress() -> Swift.String
  func getImpedanceMeasurementState() -> GGBluetoothSDKFramework.GGSwitchState
  func getSessionImpedanceMeasurementState() -> GGBluetoothSDKFramework.GGSwitchState
  func getHeartRateMeasurementState() -> GGBluetoothSDKFramework.GGSwitchState
  func getStartAnimationState() -> GGBluetoothSDKFramework.GGSwitchState
  func getEndAnimationState() -> GGBluetoothSDKFramework.GGSwitchState
}
public enum AddressType : Swift.UInt16 {
  case ADDRESS_TYPE_PUBLIC
  case ADDRESS_TYPE_RANDOM
  case ADDRESS_TYPE_UNKNOWN
  public init?(rawValue: Swift.UInt16)
  public typealias RawValue = Swift.UInt16
  public var rawValue: Swift.UInt16 {
    get
  }
}
public enum GGDeviceProtocolType : Swift.String {
  case GG_DEVICE_PROTOCOL_UNSET
  case GG_DEVICE_PROTOCOL_OPEN
  case GG_DEVICE_PROTOCOL_A3
  case GG_DEVICE_PROTOCOL_A6
  case GG_DEVICE_PROTOCOL_R4
  case GG_DEVICE_PROTOCOL_TK_BGM
  case GG_DEVICE_PROTOCOL_WELLAND_KITCHEN
  case GG_DEVICE_PROTOCOL_WELLAND_BATH_SCALE
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
public enum GGWeighingScaleDisplayDetail : Swift.UInt8 {
  case GG_WEIGHING_SCALE_DISPLAY_UNSET
  case GG_WEIGHING_SCALE_DISPLAY_BMI
  case GG_WEIGHING_SCALE_DISPLAY_BODY_FAT
  case GG_WEIGHING_SCALE_DISPLAY_MUSCLE
  case GG_WEIGHING_SCALE_DISPLAY_BODY_WATER
  case GG_WEIGHING_SCALE_DISPLAY_BONE_MASS
  case GG_WEIGHING_SCALE_DISPLAY_HEART_RATE
  case GG_WEIGHING_SCALE_DISPLAY_VISCERAL_FAT
  case GG_WEIGHING_SCALE_DISPLAY_SUBCUTANEOUS_FAT
  case GG_WEIGHING_SCALE_DISPLAY_PROTEIN
  case GG_WEIGHING_SCALE_DISPLAY_SKELETAL_MUSCLE
  case GG_WEIGHING_SCALE_DISPLAY_BMR
  case GG_WEIGHING_SCALE_DISPLAY_BODY_AGE
  case GG_WEIGHING_SCALE_DISPLAY_TARGET
  case GG_WEIGHING_SCALE_DISPLAY_DAILY_AVERAGE
  case GG_WEIGHING_SCALE_DISPLAY_WEEKLY_AVERAGE
  case GG_WEIGHING_SCALE_DISPLAY_MONTHLY_AVERAGE
  public func toString() -> Swift.String
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
public enum GGMeasurementType : Swift.UInt8 {
  case GG_MEASUREMENT_UNSET
  case GG_WEIGHT_MEASUREMENT
  case GG_BODY_METRICS_MEASUREMENT
  case GG_BLOOD_PRESSURE_MEASUREMENT
  case GG_PULSE_OXIMETER_MEASUREMENT
  case GG_BLOOD_GLUCOSE_METER_MEASUREMENT
  case GG_HEALTH_THERMOMETER_MEASUREMENT
  public static func fromValue(_ rawValue: Swift.Int) -> GGBluetoothSDKFramework.GGMeasurementType
  public var description: Swift.String {
    get
  }
  public func toString() -> Swift.String
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
public enum WeightUnit : Swift.Int, Swift.CustomStringConvertible {
  case WEIGHT_UNKNOWN
  case WEIGHT_KILOGRAMS
  case WEIGHT_POUNDS
  case WEIGHT_CATTY
  case WEIGHT_STONES
  case WEIGHT_LB_OZ
  case WEIGHT_MILLIGRAMS
  case WEIGHT_GRAMS
  case WEIGHT_OUNCE
  case WEIGHT_POUND_OUNCE
  case WEIGHT_MILLI_LITRE_WATER
  case WEIGHT_MILLI_LITRE_MILK
  case WEIGHT_FLUID_OUNCE_WATER
  case WEIGHT_FLUID_OUNCE_MILK
  public var description: Swift.String {
    get
  }
  public var value: GGBluetoothSDKFramework.int {
    get
  }
  public static func fromValue(_ rawValue: Swift.Int) -> GGBluetoothSDKFramework.WeightUnit
  public func toString() -> Swift.String
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public enum GGGender : Swift.UInt8 {
  case GG_GENDER_UNSET
  case GG_GENDER_MALE
  case GG_GENDER_FEMALE
  case GG_GENDER_DONT_DISCLOSE
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
public class GGMeasurementInfo : Swift.Codable, GGBluetoothSDKFramework.GGIMeasurementInfo {
  public init(_ measurementType: GGBluetoothSDKFramework.GGMeasurementType)
  public func getMeasurementType() -> GGBluetoothSDKFramework.GGMeasurementType
  public func setTimeStamp(_ timeStamp: Swift.UInt32)
  public func getTimeStamp() -> Swift.UInt32
  public func describeContents() -> Swift.Int
  required public init(from decoder: any Swift.Decoder) throws
  public func encode(to encoder: any Swift.Encoder) throws
  @objc deinit
}
public enum GGMealMark : Swift.Int {
  case MEAL_MARK_UNKNOWN
  case MEAL_MARK_FPG
  case MEAL_MARK_PPG
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public protocol Updatable {
  mutating func update(withBytes bytes: Swift.ArraySlice<Swift.UInt8>, isLast: Swift.Bool) throws -> Swift.Array<Swift.UInt8>
  mutating func update(withBytes bytes: Swift.ArraySlice<Swift.UInt8>, isLast: Swift.Bool, output: (_ bytes: Swift.Array<Swift.UInt8>) -> Swift.Void) throws
}
extension GGBluetoothSDKFramework.Updatable {
  @inlinable public mutating func update(withBytes bytes: Swift.ArraySlice<Swift.UInt8>, isLast: Swift.Bool = false, output: (_ bytes: Swift.Array<Swift.UInt8>) -> Swift.Void) throws {
    let processed = try update(withBytes: bytes, isLast: isLast)
    if !processed.isEmpty {
      output(processed)
    }
  }
  @inlinable public mutating func update(withBytes bytes: Swift.ArraySlice<Swift.UInt8>, isLast: Swift.Bool = false) throws -> Swift.Array<Swift.UInt8> {
    try self.update(withBytes: bytes, isLast: isLast)
  }
  @inlinable public mutating func update(withBytes bytes: Swift.Array<Swift.UInt8>, isLast: Swift.Bool = false) throws -> Swift.Array<Swift.UInt8> {
    try self.update(withBytes: bytes.slice, isLast: isLast)
  }
  @inlinable public mutating func update(withBytes bytes: Swift.Array<Swift.UInt8>, isLast: Swift.Bool = false, output: (_ bytes: Swift.Array<Swift.UInt8>) -> Swift.Void) throws {
    try self.update(withBytes: bytes.slice, isLast: isLast, output: output)
  }
  @inlinable public mutating func finish(withBytes bytes: Swift.ArraySlice<Swift.UInt8>) throws -> Swift.Array<Swift.UInt8> {
    try self.update(withBytes: bytes, isLast: true)
  }
  @inlinable public mutating func finish(withBytes bytes: Swift.Array<Swift.UInt8>) throws -> Swift.Array<Swift.UInt8> {
    try self.finish(withBytes: bytes.slice)
  }
  @inlinable public mutating func finish() throws -> Swift.Array<Swift.UInt8> {
    try self.update(withBytes: [], isLast: true)
  }
  @inlinable public mutating func finish(withBytes bytes: Swift.ArraySlice<Swift.UInt8>, output: (_ bytes: Swift.Array<Swift.UInt8>) -> Swift.Void) throws {
    let processed = try update(withBytes: bytes, isLast: true)
    if !processed.isEmpty {
      output(processed)
    }
  }
  @inlinable public mutating func finish(withBytes bytes: Swift.Array<Swift.UInt8>, output: (_ bytes: Swift.Array<Swift.UInt8>) -> Swift.Void) throws {
    try self.finish(withBytes: bytes.slice, output: output)
  }
  @inlinable public mutating func finish(output: (Swift.Array<Swift.UInt8>) -> Swift.Void) throws {
    try self.finish(withBytes: [], output: output)
  }
}
public enum GGUserType : Swift.UInt8 {
  case GG_USER_GUEST
  case GG_USER_1
  case GG_USER_2
  case GG_USER_3
  case GG_USER_4
  case GG_USER_5
  case GG_USER_6
  case GG_USER_7
  case GG_USER_8
  public static func fromValue(_ rawValue: Swift.UInt8) -> GGBluetoothSDKFramework.GGUserType
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
extension Swift.Array {
  public mutating func add(_ newElement: Element)
  public static func copyOf<T>(_ src: [T], _ size: Swift.Int) -> [T]? where T : Swift.ExpressibleByIntegerLiteral
  public static func copyOfRange<T>(_ arr: [T], _ from: Swift.Int, _ to: Swift.Int) -> [T]? where T : Swift.ExpressibleByIntegerLiteral
}
public protocol GGIBloodGlucoseInfo : GGBluetoothSDKFramework.GGIMeasurementInfo {
  func getValue() -> Swift.Double
  func getBgmUnit() -> GGBluetoothSDKFramework.BgmUnit
  func getMealMark() -> GGBluetoothSDKFramework.GGMealMark
  func getErrorCode() -> GGBluetoothSDKFramework.GGBGErrorCode
}
@_hasMissingDesignatedInitializers public class GGDeviceLogInfo {
  public func getTotalLength() -> GGBluetoothSDKFramework.int
  public func setReceivedLength(_ receivedLength: GGBluetoothSDKFramework.int)
  public func addReceivedLength(_ receivedLength: GGBluetoothSDKFramework.int)
  public func getReceivedLength() -> GGBluetoothSDKFramework.int
  public func getReceivedPercentage() -> GGBluetoothSDKFramework.float
  public func getStartAddress() -> GGBluetoothSDKFramework.int
  public func setData(_ data: Swift.String)
  public func getData() -> Swift.String
  final public class Builder {
    public init()
    final public func setTotalLength(_ totalLength: GGBluetoothSDKFramework.int) -> GGBluetoothSDKFramework.GGDeviceLogInfo.Builder
    final public func setStartAddress(_ startAddress: GGBluetoothSDKFramework.int) -> GGBluetoothSDKFramework.GGDeviceLogInfo.Builder
    final public func build() -> GGBluetoothSDKFramework.GGDeviceLogInfo
    @objc deinit
  }
  @objc deinit
}
public enum TemperatureUnit : Swift.Int {
  case TEMPERATURE_UNKNOWN
  case TEMPERATURE_CELSIUS
  case TEMPERATURE_FAHRENHEIT
  public static func fromValue(_ rawValue: Swift.Int) -> GGBluetoothSDKFramework.TemperatureUnit
  public var description: Swift.String {
    get
  }
  public func toString() -> Swift.String
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc @_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers public class BluetoothCentralManager : ObjectiveC.NSObject, CoreBluetooth.CBCentralManagerDelegate {
  public static func getInstance(withDelegate delegate: any GGBluetoothSDKFramework.BluetoothCentralManagerDelegate) -> GGBluetoothSDKFramework.BluetoothCentralManager
  public func setDelegate(delegate: any GGBluetoothSDKFramework.BluetoothCentralManagerDelegate)
  public func setPeripheralDelegate(peripheralDelegate: any GGBluetoothSDKFramework.BluetoothPeripheralDelegate)
  public func scanForPeripherals()
  public func scanForPeripheralsWithServices(serviceUUIDs: [CoreBluetooth.CBUUID])
  public func stopScan()
  public func connectPeripheral(peripheral: GGBluetoothSDKFramework.BluetoothPeripheral, peripheralDelegate: any GGBluetoothSDKFramework.BluetoothPeripheralDelegate)
  public func cancelConnection(peripheral: GGBluetoothSDKFramework.BluetoothPeripheral, peripheralDelegate: any GGBluetoothSDKFramework.BluetoothPeripheralDelegate)
  @objc public func centralManagerDidUpdateState(_ central: CoreBluetooth.CBCentralManager)
  @objc public func centralManager(_ central: CoreBluetooth.CBCentralManager, didDiscover peripheral: CoreBluetooth.CBPeripheral, advertisementData: [Swift.String : Any], rssi RSSI: Foundation.NSNumber)
  @objc public func centralManager(_ central: CoreBluetooth.CBCentralManager, didDisconnectPeripheral peripheral: CoreBluetooth.CBPeripheral, error: (any Swift.Error)?)
  @objc public func centralManager(_ central: CoreBluetooth.CBCentralManager, didFailToConnect peripheral: CoreBluetooth.CBPeripheral, error: (any Swift.Error)?)
  @objc public func centralManager(_ central: CoreBluetooth.CBCentralManager, didConnect peripheral: CoreBluetooth.CBPeripheral)
  @objc deinit
}
open class GGReceiveDataDelegate : GGBluetoothSDKFramework.GGIReceiveDataDelegate {
  public init()
  @discardableResult
  open func onDeviceConnect(_ bleDevice: any GGBluetoothSDKFramework.GGIBLEDevice) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onDeviceDisconnect(_ bleDevice: any GGBluetoothSDKFramework.GGIBLEDevice) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onConnectionFailed(_ bleDevice: any GGBluetoothSDKFramework.GGIBLEDevice) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onScaleWakeUp(_ device: any GGBluetoothSDKFramework.GGIBLEDevice) -> GGBluetoothSDKFramework.GGResultType
  open func onReceiveDeviceInfo(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ deviceInfo: any GGBluetoothSDKFramework.GGIDeviceInfo) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveAcknowledgement(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ operation: GGBluetoothSDKFramework.GGOperationType) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveBatteryLevel(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ batteryLevel: Swift.Int) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveWeightUnit(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ unit: GGBluetoothSDKFramework.WeightUnit) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveHeartRateState(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ state: GGBluetoothSDKFramework.GGSwitchState) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveImpedanceState(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ state: GGBluetoothSDKFramework.GGSwitchState, _ onlyCurrentSession: Swift.Bool) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveAnimationDisplayState(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ startPageState: GGBluetoothSDKFramework.GGSwitchState, _ shutdownPageState: GGBluetoothSDKFramework.GGSwitchState) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveDisplayDetails(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ displayDetails: [GGBluetoothSDKFramework.GGWeighingScaleDisplayDetail]) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveFirmwareUpgradeStatus(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ firmwareUpgradeInfo: (any GGBluetoothSDKFramework.GGIFirmwareUpgradeInfo)?) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveAccountIDList(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ accountIDList: Swift.Array<GGBluetoothSDKFramework.GGAccountInfo>) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveAccountIDImpedanceList(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ accountIDList: Swift.Array<GGBluetoothSDKFramework.GGAccountInfo>) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveAccountIDDelete(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ accountID: [GGBluetoothSDKFramework.byte]) -> GGBluetoothSDKFramework.GGResultType
  open func onReceiveLiveMeasurement(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ measurementInfo: any GGBluetoothSDKFramework.GGIMeasurementInfo) -> GGBluetoothSDKFramework.GGResultType
  open func onReceiveMeasurement(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ measurementInfo: any GGBluetoothSDKFramework.GGIMeasurementInfo) -> GGBluetoothSDKFramework.GGResultType
  open func onReceiveMeasurementHistory(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ measurementHistoryList: [any GGBluetoothSDKFramework.GGIMeasurementInfo]) -> GGBluetoothSDKFramework.GGResultType
  open func onReceiveWifiMacAddress(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ deviceInfo: any GGBluetoothSDKFramework.GGIDeviceInfo) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveWifiConnectState(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ wifiState: GGBluetoothSDKFramework.GGWifiState, _ errorCode: Swift.String) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveWifiList(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ wifiList: [GGBluetoothSDKFramework.GGWifiInfo]) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveWifiSSID(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ ssid: Swift.String!) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveAccountList(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ accountIDList: [[Swift.UInt8]]) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveAccountConfig(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ status: Swift.Int) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveOnlineAccountConfig(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ status: Swift.Int) -> GGBluetoothSDKFramework.GGResultType
  open func onReceiveDeviceLog(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ log: Swift.String)
  open func onReceiveDeviceLogUpdate(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ deviceLogInfo: GGBluetoothSDKFramework.GGDeviceLogInfo)
  open func onReceiveBgmUnit(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ unit: GGBluetoothSDKFramework.BgmUnit) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveTemperatureUnit(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ unit: GGBluetoothSDKFramework.TemperatureUnit) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveMuteMode(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ state: GGBluetoothSDKFramework.GGSwitchState) -> GGBluetoothSDKFramework.GGResultType
  @discardableResult
  open func onReceiveThermometerErrorCode(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ errorCode: GGBluetoothSDKFramework.GGThermometerErrorCode) -> GGBluetoothSDKFramework.GGResultType
  @objc deinit
}
public enum GGWifiState : Swift.UInt8 {
  case GG_WIFI_STATE_UNSET
  case GG_WIFI_STATE_CONNECTED
  case GG_WIFI_STATE_NOT_CONNECTED
  case GG_WIFI_STATE_DISCONNECTED
  case GG_WIFI_STATE_CANCELLED
  public var description: Swift.String {
    get
  }
  public func toString() -> Swift.String
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
public class GGUserInfo {
  public func getName() -> Swift.String
  public func setName(_ name: Swift.String)
  public func getSex() -> GGBluetoothSDKFramework.GGSex
  public func setSex(_ sex: GGBluetoothSDKFramework.GGSex)
  public func getAge() -> Swift.Int
  public func setAge(_ age: Swift.Int)
  public func getHeight() -> Swift.Float
  public func setHeight(_ height: Swift.Float)
  public func getWeight() -> Swift.Float
  public func setWeight(_ weight: Swift.Float)
  public func isAthlete() -> Swift.Bool
  public func setAthlete(_ isAthlete: Swift.Bool)
  public func getUserType() -> GGBluetoothSDKFramework.GGUserType
  public func setUserType(_ userType: GGBluetoothSDKFramework.GGUserType)
  public init()
  @objc deinit
}
public protocol GGIR4DataCallback {
  func onScaleWakeUp(_ device: any GGBluetoothSDKFramework.GGIBLEDevice) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveHeartRateState(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ state: GGBluetoothSDKFramework.GGSwitchState) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveImpedanceState(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ state: GGBluetoothSDKFramework.GGSwitchState, _ onlyCurrentSession: Swift.Bool) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveAnimationDisplayState(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ startPageState: GGBluetoothSDKFramework.GGSwitchState, _ shutdownPageState: GGBluetoothSDKFramework.GGSwitchState) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveDisplayDetails(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ displayDetails: [GGBluetoothSDKFramework.GGWeighingScaleDisplayDetail]) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveFirmwareUpgradeStatus(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ firmwareUpgradeInfo: (any GGBluetoothSDKFramework.GGIFirmwareUpgradeInfo)?) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveAccountIDImpedanceList(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ accountIDList: Swift.Array<GGBluetoothSDKFramework.GGAccountInfo>) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveAccountIDDelete(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ accountID: [GGBluetoothSDKFramework.byte]) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveAccountConfig(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ status: Swift.Int) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveOnlineAccountConfig(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ status: Swift.Int) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveDeviceLog(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ log: Swift.String)
  func onReceiveDeviceLogUpdate(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ deviceLogInfo: GGBluetoothSDKFramework.GGDeviceLogInfo)
}
public typealias byte = Swift.UInt8
public typealias int = Swift.Int
public typealias float = Swift.Float
public typealias boolean = Swift.Bool
public typealias double = Swift.Double
public typealias Long = Swift.UInt32
public typealias long = Swift.UInt32
@objc @_inheritsConvenienceInitializers public class GGAccountInfo : ObjectiveC.NSObject {
  @objc override dynamic public init()
  public func accountID(_ accountId: [Swift.UInt8])
  public func accountID() -> [Swift.UInt8]
  public func passwordBytes(passwordBytes: [Swift.UInt8])
  public func passwordBytes() -> [Swift.UInt8]
  public func userNumber(_ userNumber: Swift.Int)
  public func userNumber() -> Swift.Int
  public func name() -> Swift.String
  public func name(_ name: Swift.String)
  public func age() -> Swift.Int
  public func age(_ age: Swift.Int)
  public func gender() -> GGBluetoothSDKFramework.GGGender
  public func gender(_ gender: GGBluetoothSDKFramework.GGGender)
  public func height() -> Swift.Int
  public func height(_ heightInCM: Swift.Int)
  public func isAthlete() -> Swift.Bool
  public func isAthlete(_ isAthlete: Swift.Bool)
  public func weight() -> Swift.Int
  public func weight(_ weight: Swift.Int)
  public func unit() -> GGBluetoothSDKFramework.WeightUnit
  public func unit(_ unit: GGBluetoothSDKFramework.WeightUnit)
  public func goalType() -> GGBluetoothSDKFramework.GGGoalType
  public func goalType(_ goalType: GGBluetoothSDKFramework.GGGoalType)
  public func targetWeight() -> Swift.Int
  public func targetWeight(_ targetWeight: Swift.Int)
  public func maintainWeight() -> GGBluetoothSDKFramework.int
  public func maintainWeight(_ maintainWeight: GGBluetoothSDKFramework.int)
  public func impedanceSwitch() -> GGBluetoothSDKFramework.GGSwitchState
  public func impedanceSwitch(_ impedanceSwitch: GGBluetoothSDKFramework.GGSwitchState)
  public func othersImpedanceSwitch() -> GGBluetoothSDKFramework.GGSwitchState
  public func othersImpedanceSwitch(_ othersImpedanceSwitch: GGBluetoothSDKFramework.GGSwitchState)
  public func getUpdateTimeStamp() -> GGBluetoothSDKFramework.int
  public func setUpdateTimeStamp(_ updateTimeStamp: GGBluetoothSDKFramework.int)
  public func getR4AccountInfoBytes() -> [Swift.UInt8]
  public static func getR4OnlineAccountInfoBytes(_ accountID: Swift.String) -> [GGBluetoothSDKFramework.byte]
  public func getR4OnlineAccountInfoBytes() -> [GGBluetoothSDKFramework.byte]
  public func getR4AccountIDImpedanceInfoBytes() -> [GGBluetoothSDKFramework.byte]
  public func toString() -> Swift.String
  @objc deinit
}
public enum GGTimeDisplayType : Swift.UInt32 {
  case GG_TIME_DISPLAY_UNSET
  case GG_TIME_DISPLAY_12_HRS
  case GG_TIME_DISPLAY_24_HRS
  public init?(rawValue: Swift.UInt32)
  public typealias RawValue = Swift.UInt32
  public var rawValue: Swift.UInt32 {
    get
  }
}
public enum BinaryDataErrors : Swift.Error {
  case notEnoughData
  case failedToConvertToString
  public static func == (a: GGBluetoothSDKFramework.BinaryDataErrors, b: GGBluetoothSDKFramework.BinaryDataErrors) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public protocol CipherModeWorker {
  var cipherOperation: GGBluetoothSDKFramework.CipherOperationOnBlock { get }
  var additionalBufferSize: Swift.Int { get }
  @inlinable mutating func encrypt(block plaintext: Swift.ArraySlice<Swift.UInt8>) -> Swift.Array<Swift.UInt8>
  @inlinable mutating func decrypt(block ciphertext: Swift.ArraySlice<Swift.UInt8>) -> Swift.Array<Swift.UInt8>
}
public protocol BlockModeWorker : GGBluetoothSDKFramework.CipherModeWorker {
  var blockSize: Swift.Int { get }
}
public protocol CounterModeWorker : GGBluetoothSDKFramework.CipherModeWorker {
  associatedtype Counter
  var counter: Self.Counter { get set }
}
public protocol SeekableModeWorker : GGBluetoothSDKFramework.CipherModeWorker {
  mutating func seek(to position: Swift.Int) throws
}
public protocol StreamModeWorker : GGBluetoothSDKFramework.CipherModeWorker {
}
public protocol FinalizingEncryptModeWorker : GGBluetoothSDKFramework.CipherModeWorker {
  mutating func finalize(encrypt ciphertext: Swift.ArraySlice<Swift.UInt8>) throws -> Swift.ArraySlice<Swift.UInt8>
}
public protocol FinalizingDecryptModeWorker : GGBluetoothSDKFramework.CipherModeWorker {
  @discardableResult
  mutating func willDecryptLast(bytes ciphertext: Swift.ArraySlice<Swift.UInt8>) throws -> Swift.ArraySlice<Swift.UInt8>
  mutating func didDecryptLast(bytes plaintext: Swift.ArraySlice<Swift.UInt8>) throws -> Swift.ArraySlice<Swift.UInt8>
  mutating func finalize(decrypt plaintext: Swift.ArraySlice<Swift.UInt8>) throws -> Swift.ArraySlice<Swift.UInt8>
}
public protocol GGIMeasurementInfo {
  func getMeasurementType() -> GGBluetoothSDKFramework.GGMeasurementType
  func getTimeStamp() -> GGBluetoothSDKFramework.long
}
public class BluetoothBytesParser {
  public static var FORMAT_UINT8: Swift.Int
  public static var FORMAT_UINT16: Swift.Int
  public static var FORMAT_UINT24: Swift.Int
  public static var FORMAT_UINT32: Swift.Int
  public static var FORMAT_SINT8: Swift.Int
  public static var FORMAT_SINT16: Swift.Int
  public static var FORMAT_SINT24: Swift.Int
  public static var FORMAT_SINT32: Swift.Int
  public static var FORMAT_SFLOAT: Swift.Int
  public static var FORMAT_FLOAT: Swift.Int
  public static var LITTLE_ENDIAN: Swift.UInt32
  public static var BIG_ENDIAN: Swift.UInt32
  public init(_ value: Foundation.Data, _ offset: Swift.Int, _ byteOrder: CoreFoundation.CFByteOrder) throws
  public func getLongValue() throws -> Swift.Int64
  public func getLongValue(_ byteOrder: CoreFoundation.CFByteOrder) throws -> Swift.Int64
  public func getLongValue(_ offset: Swift.Int, _ byteOrder: CoreFoundation.CFByteOrder) throws -> Swift.Int64
  public func getIntValue(_ formatType: Swift.Int) throws -> Swift.Int
  public func getIntValue(_ formatType: Swift.Int, _ byteOrder: CoreFoundation.CFByteOrder) throws -> Swift.Int
  public func getIntValue(_ formatType: Swift.Int, _ offset: Swift.Int, _ byteOrder: CoreFoundation.CFByteOrder) throws -> Swift.Int
  public func getFloatValue(_ formatType: Swift.Int) throws -> Swift.Float
  public func getFloatValue(_ formatType: Swift.Int, _ byteOrder: CoreFoundation.CFByteOrder) throws -> Swift.Float
  public func getFloatValue(_ formatType: Swift.Int, _ offset: Swift.Int, _ byteOrder: CoreFoundation.CFByteOrder) throws -> Swift.Float
  public func getStringValue() throws -> Swift.String
  public func getStringValueOfLength(_ length: Swift.Int) -> Swift.String
  public func getStringValueTill(_ value: Swift.Int) -> Swift.String
  public func getStringValue(_ offset: Swift.Int) throws -> Swift.String
  public func getByteArray(_ length: Swift.Int) -> [Swift.UInt8]
  public func getDateTime() throws -> Foundation.Date
  public func getDateTime(_ offset: Swift.Int) throws -> Foundation.Date
  public func getValue() -> Foundation.Data
  public func getByteValue() -> Swift.Array<GGBluetoothSDKFramework.byte>
  public func setIntValue(_ value: Swift.Int, _ formatType: Swift.Int, _ offset: Swift.Int) throws
  public func setIntValue(_ value: Swift.Int, _ formatType: Swift.Int) throws
  public func setLong(_ value: Swift.Int64) throws
  public func setLong(_ value: Swift.Int64, _ offset: Swift.Int) throws
  public func setFloatValue(_ mantissa: Swift.Int, _ exponent: Swift.Int, _ formatType: Swift.Int, _ offset: Swift.Int) throws
  public func setFloatValue(_ value: Swift.Float, _ precision: Swift.Int) throws
  public func setByteArray(_ value: [Swift.UInt8])
  public func setByteArray(_ value: [Swift.UInt8], _ offset: Swift.Int)
  public func setString(_ value: Swift.String)
  public func setString(_ value: Swift.String, _ offset: Swift.Int)
  public func setCurrentTime(_ date: Foundation.Date)
  public func setDateTime(_ date: Foundation.Date)
  public static func bytes2String(_ bytes: [Swift.UInt8]?) -> Swift.String
  public static func string2bytes(_ hexString: Swift.String?) -> [Swift.UInt8]
  public static func swapInt(_ i: Swift.Int) -> Swift.Int
  public func getLength() -> Swift.Int
  public func getOffset() -> Swift.Int
  public func setOffset(_ offset: Swift.Int) throws
  public func addOffset(_ offset: Swift.Int) throws
  public func setByteOrder(_ byteOrder: CoreFoundation.CFByteOrder)
  public func getByteOrder() -> CoreFoundation.CFByteOrder
  @objc deinit
}
@_hasMissingDesignatedInitializers public class GGPulseOximeterMeasurementStatus {
  public func parse(_ value: GGBluetoothSDKFramework.int)
  @objc deinit
}
public func getServiceUUID(_ advertisementData: [Swift.String : Any]) -> Swift.Array<CoreBluetooth.CBUUID>?
public func getProtocolTypeFromServiceUUID(_ advertisementData: [Swift.String : Any], _ deviceName: Swift.String!) -> GGBluetoothSDKFramework.GGDeviceProtocolType
public func getDeviceTypeFromServiceUUID(_ advertisementData: [Swift.String : Any], _ deviceName: Swift.String!) -> GGBluetoothSDKFramework.GGBLEDeviceType
public func getDeviceTypeFromDeviceName(_ deviceName: Swift.String) -> GGBluetoothSDKFramework.GGBLEDeviceType
@_hasMissingDesignatedInitializers public class BloodPressureData : GGBluetoothSDKFramework.GGMeasurementInfo, GGBluetoothSDKFramework.GGIBloodPressureInfo {
  public func getSystolicValue() -> Swift.Int
  public func setSystolicValue(_ systolicValue: GGBluetoothSDKFramework.int)
  public func getDiastolicValue() -> GGBluetoothSDKFramework.int
  public func setDiastolicValue(_ diastolicValue: GGBluetoothSDKFramework.int)
  public func getMeanPressureValue() -> GGBluetoothSDKFramework.int
  public func setMeanPressureValue(_ meanPressureValue: GGBluetoothSDKFramework.int)
  public func getBloodPressureStatus() -> GGBluetoothSDKFramework.int
  public func setBloodPressureStatus(_ bloodPressureStatus: GGBluetoothSDKFramework.int)
  public func getPulseRate() -> GGBluetoothSDKFramework.int
  public func setPulseRate(_ pulseRate: GGBluetoothSDKFramework.int)
  public func getUser() -> GGBluetoothSDKFramework.int
  public func setUser(_ user: GGBluetoothSDKFramework.int)
  required public init(from decoder: any Swift.Decoder) throws
  override public func encode(to encoder: any Swift.Encoder) throws
  @objc deinit
}
public protocol BluetoothPeripheralDelegate : AnyObject {
  func onServicesDiscovered(_ peripheral: GGBluetoothSDKFramework.BluetoothPeripheral)
  func onCharacteristicUpdate(_ peripheral: GGBluetoothSDKFramework.BluetoothPeripheral, _ value: Swift.Array<GGBluetoothSDKFramework.byte>, _ characteristic: CoreBluetooth.CBCharacteristic) throws
  func onCharacteristicWrite(_ peripheral: GGBluetoothSDKFramework.BluetoothPeripheral, _ characteristic: CoreBluetooth.CBCharacteristic) throws
}
public func getStringFromData(_ data: Foundation.Data) -> Swift.String
public func getHexStringFromData(_ data: Foundation.Data) -> Swift.String
public func getByteArray<T>(from value: T) -> [Swift.UInt8] where T : Swift.FixedWidthInteger
public protocol GGIHealthTemperatureInfo : GGBluetoothSDKFramework.GGIMeasurementInfo {
  func getTemperatureValue() -> Swift.Double
  func getTemperatureUnit() -> GGBluetoothSDKFramework.TemperatureUnit
}
public enum GGFirmwareUpgradeStatus : Swift.UInt8 {
  case GG_FIRMWARE_UPGRADE_UNSET
  case GG_FIRMWARE_UPGRADE_START_SUCCESS
  case GG_FIRMWARE_UPGRADE_FAILED_TO_START
  case GG_FIRMWARE_UPGRADE_IN_PROGRESS
  case GG_FIRMWARE_UPGRADE_FINISHED
  case GG_FIRMWARE_UPGRADE_FAILED
  case GG_FIRMWARE_UPGRADE_SCHEDULE_TIME_SET_SUCCESS
  case GG_FIRMWARE_UPGRADE_SCHEDULE_TIME_SET_FAILED
  case GG_FIRMWARE_UPGRADE_WIFI_NOT_CONFIGURED
  public func toString() -> Swift.String
  public var rawValue: Swift.UInt8 {
    get
  }
  public static func fromValue(_ value: Swift.Int) -> GGBluetoothSDKFramework.GGFirmwareUpgradeStatus
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
}
public struct BinaryData : Swift.ExpressibleByArrayLiteral {
  public typealias Element = Swift.UInt8
  public let data: [Swift.UInt8]
  public let bigEndian: Swift.Bool
  public init(arrayLiteral elements: GGBluetoothSDKFramework.BinaryData.Element...)
  public init(data: [Swift.UInt8], bigEndian: Swift.Bool = true)
  public init(data: Foundation.Data, bigEndian: Swift.Bool = true)
  public func get(_ offset: Swift.Int, bigEndian: Swift.Bool? = nil) throws -> Swift.UInt8
  public func get(_ offset: Swift.Int, bigEndian: Swift.Bool? = nil) throws -> Swift.UInt16
  public func get(_ offset: Swift.Int, bigEndian: Swift.Bool? = nil) throws -> Swift.UInt32
  public func get(_ offset: Swift.Int, bigEndian: Swift.Bool? = nil) throws -> Swift.UInt64
  public func get(_ offset: Swift.Int, bigEndian: Swift.Bool? = nil) throws -> Swift.Int8
  public func get(_ offset: Swift.Int, bigEndian: Swift.Bool? = nil) throws -> Swift.Int16
  public func get(_ offset: Swift.Int, bigEndian: Swift.Bool? = nil) throws -> Swift.Int32
  public func get(_ offset: Swift.Int, bigEndian: Swift.Bool? = nil) throws -> Swift.Int64
  public func get(_ offset: Swift.Int) throws -> Swift.Float32
  public func get(_ offset: Swift.Int) throws -> Swift.Float64
  public func getNullTerminatedUTF8(_ offset: Swift.Int) throws -> Swift.String
  public func getUTF8(_ offset: Swift.Int, length: Swift.Int) throws -> Swift.String
  public func tail(_ offset: Swift.Int) throws -> GGBluetoothSDKFramework.BinaryData
  public func subData(_ offset: Swift.Int, _ length: Swift.Int) throws -> GGBluetoothSDKFramework.BinaryData
  public typealias ArrayLiteralElement = GGBluetoothSDKFramework.BinaryData.Element
}
public enum GGOperationType : Swift.Int {
  case GG_OPERATION_UNSET
  case GG_OPERATION_REGISTRATION
  case GG_OPERATION_BIND_DEVICE
  case GG_OPERATION_UNBIND_DEVICE
  case GG_OPERATION_SET_TIME
  case GG_OPERATION_GET_TIME
  case GG_OPERATION_SET_UNIT
  case GG_OPERATION_GET_UNIT
  case GG_OPERATION_SET_HEART_RATE_SWITCH
  case GG_OPERATION_GET_HEART_RATE_SWITCH
  case GG_OPERATION_SET_IMPEDANCE_SWITCH
  case GG_OPERATION_GET_IMPEDANCE_SWITCH
  case GG_OPERATION_SET_SESSION_IMPEDANCE_SWITCH
  case GG_OPERATION_GET_SESSION_IMPEDANCE_SWITCH
  case GG_OPERATION_GET_WIFI_STATUS
  case GG_OPERATION_SET_BM_DISPLAY_ORDER
  case GG_OPERATION_GET_BM_DISPLAY_ORDER
  case GG_OPERATION_SET_ANIMATION_DISPLAY_SWITCH
  case GG_OPERATION_GET_ANIMATION_DISPLAY_SWITCH
  case GG_OPERATION_SET_BT_PAIR_MODE_ENTER
  case GG_OPERATION_SET_BT_PAIR_MODE_COMPLETE
  case GG_OPERATION_CLEAR_ALL_DATA
  case GG_OPERATION_CLEAR_USER_ACCOUNT_DATA
  case GG_OPERATION_CLEAR_HISTORY_DATA
  case GG_OPERATION_CLEAR_WIFI_NETWORK_DATA
  case GG_OPERATION_CLEAR_SETTINGS_DATA
  case GG_OPERATION_GET_BT_PAIR_MODE
  case GG_OPERATION_FIRMWARE_RESET
  case GG_OPERATION_ACCOUNT_ID_DISTRIBUTION_START_SUCCESS
  case GG_OPERATION_ACCOUNT_ID_DISTRIBUTION_START_FAILED
  case GG_OPERATION_ACCOUNT_ID_DISTRIBUTION_FINISH_SUCCESS
  case GG_OPERATION_ACCOUNT_ID_DISTRIBUTION_FINISH_FAILED
  case GG_OPERATION_ACCOUNT_CREATION
  case GG_OPERATION_ACCOUNT_CREATE_DIFFERENT_ACCOUNT
  case GG_OPERATION_ACCOUNT_IMPEDANCE_DISTRIBUTION_FINISH_SUCCESS
  case GG_OPERATION_ACCOUNT_IMPEDANCE_DISTRIBUTION_FINISH_FAILED
  case GG_OPERATION_SET_ACCOUNT_IMPEDANCE
  case GG_OPERATION_GET_BATTERY_LEVEL
  case GG_OPERATION_SET_OTA_DISABLED
  case GG_OPERATION_SET_OTA_ENABLED
  case GG_OPERATION_SET_WIFI
  public var rawValue: Swift.Int {
    get
  }
  public var value: Swift.Int {
    get
  }
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
}
public protocol Cryptors : AnyObject {
  func makeEncryptor() throws -> any GGBluetoothSDKFramework.Cryptor & GGBluetoothSDKFramework.Updatable
  func makeDecryptor() throws -> any GGBluetoothSDKFramework.Cryptor & GGBluetoothSDKFramework.Updatable
  static func randomIV(_ blockSize: Swift.Int) -> Swift.Array<Swift.UInt8>
}
extension GGBluetoothSDKFramework.Cryptors {
  public static func randomIV(_ count: Swift.Int) -> Swift.Array<Swift.UInt8>
}
public protocol Cryptor {
  mutating func seek(to: Swift.Int) throws
}
public protocol GGIPulseOximeterInfo {
  func getSPO2() -> GGBluetoothSDKFramework.int
  func getPR() -> GGBluetoothSDKFramework.int
  func getPulseAmplitudeIndex() -> GGBluetoothSDKFramework.float
}
public protocol GGIBluetoothHandler {
  func startScan(_ scanCallback: any GGBluetoothSDKFramework.GGIScanDelegate)
  func stopScan()
  func connectDevice(_ ggDeviceInfo: any GGBluetoothSDKFramework.GGIDeviceInfo, _ dataCallback: any GGBluetoothSDKFramework.GGIReceiveDataDelegate) -> GGBluetoothSDKFramework.GGResultType
}
public enum BgmUnit : Swift.CustomStringConvertible {
  case BGM_UNKNOWN
  case BGM_MMOL
  case BGM_MG
  public var description: Swift.String {
    get
  }
  public var value: GGBluetoothSDKFramework.int {
    get
  }
  public func toString() -> Swift.String
  public static func == (a: GGBluetoothSDKFramework.BgmUnit, b: GGBluetoothSDKFramework.BgmUnit) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
extension Swift.String {
  public func toLowerCase() -> Swift.String
  public func toUpperCase() -> Swift.String
  public func subString(beginIndex: GGBluetoothSDKFramework.int, endIndex: GGBluetoothSDKFramework.int) -> Swift.String
  public func equals(_ equalsTo: Swift.String) -> Swift.Bool
  public func toHexEncodedString(uppercase: Swift.Bool = true, prefix: Swift.String = "", separator: Swift.String = "") -> Swift.String
}
@_hasMissingDesignatedInitializers public class ScanFilter {
  public func setServiceUuid(uuid: CoreBluetooth.CBUUID)
  public func getServiceUuid() -> CoreBluetooth.CBUUID
  @objc deinit
}
public enum CipherError : Swift.Error {
  case encrypt
  case decrypt
  public static func == (a: GGBluetoothSDKFramework.CipherError, b: GGBluetoothSDKFramework.CipherError) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public protocol Cipher : AnyObject {
  var keySize: Swift.Int { get }
  func encrypt(_ bytes: Swift.ArraySlice<Swift.UInt8>) throws -> Swift.Array<Swift.UInt8>
  func encrypt(_ bytes: Swift.Array<Swift.UInt8>) throws -> Swift.Array<Swift.UInt8>
  func decrypt(_ bytes: Swift.ArraySlice<Swift.UInt8>) throws -> Swift.Array<Swift.UInt8>
  func decrypt(_ bytes: Swift.Array<Swift.UInt8>) throws -> Swift.Array<Swift.UInt8>
}
extension GGBluetoothSDKFramework.Cipher {
  public func encrypt(_ bytes: Swift.Array<Swift.UInt8>) throws -> Swift.Array<Swift.UInt8>
  public func decrypt(_ bytes: Swift.Array<Swift.UInt8>) throws -> Swift.Array<Swift.UInt8>
}
public protocol BluetoothCentralManagerDelegate : AnyObject {
  func onConnectingPeripheral(peripheral: GGBluetoothSDKFramework.BluetoothPeripheral)
  func onConnectedPeripheral(peripheral: GGBluetoothSDKFramework.BluetoothPeripheral)
  func onDisconnectingPeripheral(peripheral: GGBluetoothSDKFramework.BluetoothPeripheral)
  func onDisconnectedPeripheral(peripheral: GGBluetoothSDKFramework.BluetoothPeripheral)
  func onConnectionFailed(peripheral: GGBluetoothSDKFramework.BluetoothPeripheral)
  func onDiscoveredPeripheral(peripheral: GGBluetoothSDKFramework.BluetoothPeripheral, scanResult: GGBluetoothSDKFramework.ScanResult)
  func onBluetoothAdapterStateChanged(state: GGBluetoothSDKFramework.int)
}
extension CoreBluetooth.CBCharacteristic {
  public func getUuid() -> Swift.String
}
@_hasMissingDesignatedInitializers public class GGErrorObject : GGBluetoothSDKFramework.GGObject {
  public func getErrorDescription() -> Swift.String
  @objc deinit
}
final public class AES {
  public enum Error : Swift.Error {
    case invalidKeySize
    case dataPaddingRequired
    case invalidData
    public static func == (a: GGBluetoothSDKFramework.AES.Error, b: GGBluetoothSDKFramework.AES.Error) -> Swift.Bool
    public func hash(into hasher: inout Swift.Hasher)
    public var hashValue: Swift.Int {
      get
    }
  }
  public enum Variant : Swift.Int {
    case aes128, aes192, aes256
    public init?(rawValue: Swift.Int)
    public typealias RawValue = Swift.Int
    public var rawValue: Swift.Int {
      get
    }
  }
  @usableFromInline
  final internal let variantNr: Swift.Int
  @usableFromInline
  final internal let variantNb: Swift.Int
  @usableFromInline
  final internal let variantNk: Swift.Int
  public static let blockSize: Swift.Int
  final public let keySize: Swift.Int
  final public let variant: GGBluetoothSDKFramework.AES.Variant
  @usableFromInline
  final internal let blockMode: any GGBluetoothSDKFramework.BlockMode
  @usableFromInline
  final internal let padding: GGBluetoothSDKFramework.Padding
  @usableFromInline
  final internal var expandedKey: Swift.Array<Swift.Array<Swift.UInt32>> {
    get
    set
  }
  @usableFromInline
  final internal var expandedKeyInv: Swift.Array<Swift.Array<Swift.UInt32>> {
    get
    set
  }
  @usableFromInline
  internal static let T0: [Swift.UInt32]
  @usableFromInline
  internal static let T0_INV: [Swift.UInt32]
  @usableFromInline
  internal static let T1: [Swift.UInt32]
  @usableFromInline
  internal static let T1_INV: [Swift.UInt32]
  @usableFromInline
  internal static let T2: [Swift.UInt32]
  @usableFromInline
  internal static let T2_INV: [Swift.UInt32]
  @usableFromInline
  internal static let T3: [Swift.UInt32]
  @usableFromInline
  internal static let T3_INV: [Swift.UInt32]
  @usableFromInline
  internal static let U1: [Swift.UInt32]
  @usableFromInline
  internal static let U2: [Swift.UInt32]
  @usableFromInline
  internal static let U3: [Swift.UInt32]
  @usableFromInline
  internal static let U4: [Swift.UInt32]
  public init(key: Swift.Array<Swift.UInt8>, blockMode: any GGBluetoothSDKFramework.BlockMode, padding: GGBluetoothSDKFramework.Padding = .noPadding) throws
  @inlinable final internal func encrypt(block: Swift.ArraySlice<Swift.UInt8>) -> Swift.Array<Swift.UInt8>? {
    if self.blockMode.options.contains(.paddingRequired) && block.count != AES.blockSize {
      return Array(block)
    }

    let rounds = self.variantNr
    let rk = self.expandedKey

    let b00 = UInt32(block[block.startIndex.advanced(by: 0)])
    let b01 = UInt32(block[block.startIndex.advanced(by: 1)]) << 8
    let b02 = UInt32(block[block.startIndex.advanced(by: 2)]) << 16
    let b03 = UInt32(block[block.startIndex.advanced(by: 3)]) << 24
    var b0 = b00 | b01 | b02 | b03

    let b10 = UInt32(block[block.startIndex.advanced(by: 4)])
    let b11 = UInt32(block[block.startIndex.advanced(by: 5)]) << 8
    let b12 = UInt32(block[block.startIndex.advanced(by: 6)]) << 16
    let b13 = UInt32(block[block.startIndex.advanced(by: 7)]) << 24
    var b1 = b10 | b11 | b12 | b13

    let b20 = UInt32(block[block.startIndex.advanced(by: 8)])
    let b21 = UInt32(block[block.startIndex.advanced(by: 9)]) << 8
    let b22 = UInt32(block[block.startIndex.advanced(by: 10)]) << 16
    let b23 = UInt32(block[block.startIndex.advanced(by: 11)]) << 24
    var b2 = b20 | b21 | b22 | b23

    let b30 = UInt32(block[block.startIndex.advanced(by: 12)])
    let b31 = UInt32(block[block.startIndex.advanced(by: 13)]) << 8
    let b32 = UInt32(block[block.startIndex.advanced(by: 14)]) << 16
    let b33 = UInt32(block[block.startIndex.advanced(by: 15)]) << 24
    var b3 = b30 | b31 | b32 | b33

    let tLength = 4
    let t = UnsafeMutablePointer<UInt32>.allocate(capacity: tLength)
    t.initialize(repeating: 0, count: tLength)
    defer {
      t.deinitialize(count: tLength)
      t.deallocate()
    }

    for r in 0..<rounds - 1 {
      t[0] = b0 ^ rk[r][0]
      t[1] = b1 ^ rk[r][1]
      t[2] = b2 ^ rk[r][2]
      t[3] = b3 ^ rk[r][3]

      let lb00 = AES.T0[Int(t[0] & 0xff)]
      let lb01 = AES.T1[Int((t[1] >> 8) & 0xff)]
      let lb02 = AES.T2[Int((t[2] >> 16) & 0xff)]
      let lb03 = AES.T3[Int(t[3] >> 24)]
      b0 = lb00 ^ lb01 ^ lb02 ^ lb03

      let lb10 = AES.T0[Int(t[1] & 0xff)]
      let lb11 = AES.T1[Int((t[2] >> 8) & 0xff)]
      let lb12 = AES.T2[Int((t[3] >> 16) & 0xff)]
      let lb13 = AES.T3[Int(t[0] >> 24)]
      b1 = lb10 ^ lb11 ^ lb12 ^ lb13

      let lb20 = AES.T0[Int(t[2] & 0xff)]
      let lb21 = AES.T1[Int((t[3] >> 8) & 0xff)]
      let lb22 = AES.T2[Int((t[0] >> 16) & 0xff)]
      let lb23 = AES.T3[Int(t[1] >> 24)]
      b2 = lb20 ^ lb21 ^ lb22 ^ lb23

      let lb30 = AES.T0[Int(t[3] & 0xff)]
      let lb31 = AES.T1[Int((t[0] >> 8) & 0xff)]
      let lb32 = AES.T2[Int((t[1] >> 16) & 0xff)]
      let lb33 = AES.T3[Int(t[2] >> 24)]
      b3 = lb30 ^ lb31 ^ lb32 ^ lb33
    }

     
    let r = rounds - 1

    t[0] = b0 ^ rk[r][0]
    t[1] = b1 ^ rk[r][1]
    t[2] = b2 ^ rk[r][2]
    t[3] = b3 ^ rk[r][3]

     
    b0 = F1(t[0], t[1], t[2], t[3]) ^ rk[rounds][0]
    b1 = F1(t[1], t[2], t[3], t[0]) ^ rk[rounds][1]
    b2 = F1(t[2], t[3], t[0], t[1]) ^ rk[rounds][2]
    b3 = F1(t[3], t[0], t[1], t[2]) ^ rk[rounds][3]

    let encrypted: Array<UInt8> = [
      UInt8(b0 & 0xff), UInt8((b0 >> 8) & 0xff), UInt8((b0 >> 16) & 0xff), UInt8((b0 >> 24) & 0xff),
      UInt8(b1 & 0xff), UInt8((b1 >> 8) & 0xff), UInt8((b1 >> 16) & 0xff), UInt8((b1 >> 24) & 0xff),
      UInt8(b2 & 0xff), UInt8((b2 >> 8) & 0xff), UInt8((b2 >> 16) & 0xff), UInt8((b2 >> 24) & 0xff),
      UInt8(b3 & 0xff), UInt8((b3 >> 8) & 0xff), UInt8((b3 >> 16) & 0xff), UInt8((b3 >> 24) & 0xff)
    ]
    return encrypted
  }
  @usableFromInline
  final internal func decrypt(block: Swift.ArraySlice<Swift.UInt8>) -> Swift.Array<Swift.UInt8>?
  @objc deinit
}
extension GGBluetoothSDKFramework.AES {
  @usableFromInline
  @inline(__always) final internal func F1(_ x0: Swift.UInt32, _ x1: Swift.UInt32, _ x2: Swift.UInt32, _ x3: Swift.UInt32) -> Swift.UInt32
}
extension GGBluetoothSDKFramework.AES : GGBluetoothSDKFramework.Cipher {
  @inlinable final public func encrypt(_ bytes: Swift.ArraySlice<Swift.UInt8>) throws -> Swift.Array<Swift.UInt8> {
    let blockSize = self.blockMode.customBlockSize ?? AES.blockSize
    let chunks = bytes.batched(by: blockSize)

    var oneTimeCryptor = try makeEncryptor()
    var out = Array<UInt8>(reserveCapacity: bytes.count)
    for chunk in chunks {
      out += try oneTimeCryptor.update(withBytes: chunk, isLast: false)
    }
     
    out += try oneTimeCryptor.finish()

    if self.blockMode.options.contains(.paddingRequired) && (out.count % AES.blockSize != 0) {
      throw Error.dataPaddingRequired
    }

    return out
  }
  @inlinable final public func decrypt(_ bytes: Swift.ArraySlice<Swift.UInt8>) throws -> Swift.Array<Swift.UInt8> {
    if self.blockMode.options.contains(.paddingRequired) && (bytes.count % AES.blockSize != 0) {
      throw Error.dataPaddingRequired
    }

    var oneTimeCryptor = try makeDecryptor()
    let chunks = bytes.batched(by: AES.blockSize)
    if chunks.isEmpty {
      throw Error.invalidData
    }

    var out = Array<UInt8>(reserveCapacity: bytes.count)

    var lastIdx = chunks.startIndex
    chunks.indices.formIndex(&lastIdx, offsetBy: chunks.count - 1)

     
     
    for idx in chunks.indices {
      out += try oneTimeCryptor.update(withBytes: chunks[idx], isLast: idx == lastIdx)
    }
    return out
  }
}
extension GGBluetoothSDKFramework.AES : GGBluetoothSDKFramework.Cryptors {
  @inlinable final public func makeEncryptor() throws -> any GGBluetoothSDKFramework.Cryptor & GGBluetoothSDKFramework.Updatable {
    let blockSize = blockMode.customBlockSize ?? AES.blockSize
    let worker = try blockMode.worker(blockSize: blockSize, cipherOperation: encrypt, encryptionOperation: encrypt)
    if worker is StreamModeWorker {
      return try StreamEncryptor(blockSize: blockSize, padding: padding, worker)
    }
    return try BlockEncryptor(blockSize: blockSize, padding: padding, worker)
  }
  @inlinable final public func makeDecryptor() throws -> any GGBluetoothSDKFramework.Cryptor & GGBluetoothSDKFramework.Updatable {
    let blockSize = blockMode.customBlockSize ?? AES.blockSize
    let cipherOperation: CipherOperationOnBlock = blockMode.options.contains(.useEncryptToDecrypt) == true ? encrypt : decrypt
    let worker = try blockMode.worker(blockSize: blockSize, cipherOperation: cipherOperation, encryptionOperation: encrypt)
    if worker is StreamModeWorker {
      return try StreamDecryptor(blockSize: blockSize, padding: padding, worker)
    }
    return try BlockDecryptor(blockSize: blockSize, padding: padding, worker)
  }
}
public protocol GGIReceiveDataDelegate : GGBluetoothSDKFramework.GGIDataDelegate, GGBluetoothSDKFramework.GGIR4DataCallback, GGBluetoothSDKFramework.GGIWifiDataCallback {
  func onReceiveDeviceInfo(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ deviceInfo: any GGBluetoothSDKFramework.GGIDeviceInfo) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveWeightUnit(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ unit: GGBluetoothSDKFramework.WeightUnit) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveLiveMeasurement(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ measurementInfo: any GGBluetoothSDKFramework.GGIMeasurementInfo) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveMeasurement(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ measurementInfo: any GGBluetoothSDKFramework.GGIMeasurementInfo) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveMeasurementHistory(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ measurementHistoryList: [any GGBluetoothSDKFramework.GGIMeasurementInfo]) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveBgmUnit(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ unit: GGBluetoothSDKFramework.BgmUnit) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveTemperatureUnit(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ unit: GGBluetoothSDKFramework.TemperatureUnit) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveMuteMode(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ state: GGBluetoothSDKFramework.GGSwitchState) -> GGBluetoothSDKFramework.GGResultType
  func onReceiveThermometerErrorCode(_ result: GGBluetoothSDKFramework.GGResultType, _ device: any GGBluetoothSDKFramework.GGIBLEDevice, _ errorCode: GGBluetoothSDKFramework.GGThermometerErrorCode) -> GGBluetoothSDKFramework.GGResultType
}
@_hasMissingDesignatedInitializers public class GGTimeData {
  public static let A3UTCTimeDifference: Swift.Int
  public func getUTCTime() -> Swift.UInt32
  public func getTZOffset() -> Swift.Int32
  public func getDisplayType() -> GGBluetoothSDKFramework.GGTimeDisplayType
  public class Builder {
    public init()
    public func setUTCTime(_ utcTime: Swift.UInt32) -> GGBluetoothSDKFramework.GGTimeData.Builder
    public func setTZOffset(_ tzOffset: Swift.Int32) -> GGBluetoothSDKFramework.GGTimeData.Builder
    public func setDisplayType(_ displayType: GGBluetoothSDKFramework.GGTimeDisplayType) -> GGBluetoothSDKFramework.GGTimeData.Builder
    public func build() -> GGBluetoothSDKFramework.GGTimeData
    @objc deinit
  }
  public static func getR4TimeZoneFlag(_ fromMinute: Swift.Int) -> Swift.Int
  @objc deinit
}
public typealias CipherOperationOnBlock = (_ block: Swift.ArraySlice<Swift.UInt8>) -> Swift.Array<Swift.UInt8>?
public protocol BlockMode {
  var options: GGBluetoothSDKFramework.BlockModeOption { get }
  @inlinable func worker(blockSize: Swift.Int, cipherOperation: @escaping GGBluetoothSDKFramework.CipherOperationOnBlock, encryptionOperation: @escaping GGBluetoothSDKFramework.CipherOperationOnBlock) throws -> any GGBluetoothSDKFramework.CipherModeWorker
  var customBlockSize: Swift.Int? { get }
}
public protocol PaddingProtocol {
  func add(to: Swift.Array<Swift.UInt8>, blockSize: Swift.Int) -> Swift.Array<Swift.UInt8>
  func remove(from: Swift.Array<Swift.UInt8>, blockSize: Swift.Int?) -> Swift.Array<Swift.UInt8>
}
public enum Padding : GGBluetoothSDKFramework.PaddingProtocol {
  case noPadding
  public func add(to: Swift.Array<Swift.UInt8>, blockSize: Swift.Int) -> Swift.Array<Swift.UInt8>
  public func remove(from: Swift.Array<Swift.UInt8>, blockSize: Swift.Int?) -> Swift.Array<Swift.UInt8>
  public static func == (a: GGBluetoothSDKFramework.Padding, b: GGBluetoothSDKFramework.Padding) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public enum GGBluetoothAdapterState : Swift.Int, Swift.CustomStringConvertible {
  case GG_BLUETOOTH_ADAPTER_DISCONNECTED
  case GG_BLUETOOTH_ADAPTER_CONNECTING
  case GG_BLUETOOTH_ADAPTER_CONNECTED
  case GG_BLUETOOTH_ADAPTER_DISCONNECTING
  case GG_BLUETOOTH_ADAPTER_OFF
  case GG_BLUETOOTH_ADAPTER_TURNING_ON
  case GG_BLUETOOTH_ADAPTER_ON
  case GG_BLUETOOTH_ADAPTER_TURNING_OFF
  case GG_BLUETOOTH_ADAPTER_UNAUTHORIZED
  public static func fromValue(_ rawValue: Swift.Int) -> GGBluetoothSDKFramework.GGBluetoothAdapterState
  public var description: Swift.String {
    get
  }
  public func toString() -> Swift.String
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public let GG_TIME_OFFSET: Swift.UInt32
public enum PressureUnit : Swift.CustomStringConvertible {
  case Unknown
  case mmHg
  case kPa
  public var description: Swift.String {
    get
  }
  public func toString() -> Swift.String
  public static func == (a: GGBluetoothSDKFramework.PressureUnit, b: GGBluetoothSDKFramework.PressureUnit) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
extension CoreBluetooth.CBService {
  public func getUuid() -> Swift.String
}
public protocol GGIWeightInfo : GGBluetoothSDKFramework.GGIMeasurementInfo {
  func getWeight() -> GGBluetoothSDKFramework.float
  func getWeightInDouble() -> GGBluetoothSDKFramework.double
  func getWeightInLbs() -> GGBluetoothSDKFramework.float
  func getWeightInKg() -> GGBluetoothSDKFramework.float
  func getWeightInMg() -> GGBluetoothSDKFramework.float
  func getUnit() -> GGBluetoothSDKFramework.WeightUnit
  func reset()
}
extension Swift.Array {
  @inlinable internal init(reserveCapacity: Swift.Int) {
    self = Array<Element>()
    self.reserveCapacity(reserveCapacity)
  }
  @inlinable internal var slice: Swift.ArraySlice<Element> {
    get {
    self[self.startIndex ..< self.endIndex]
  }
  }
  @inlinable internal subscript(safe index: Swift.Array<Element>.Index) -> Element? {
    get {
    return indices.contains(index) ? self[index] : nil
  }
  }
}
extension Swift.Array where Element == Swift.UInt8 {
  public init(hex: Swift.String)
  public func toHexString() -> Swift.String
}
extension Swift.Array where Element == Swift.UInt8 {
  @available(*, deprecated)
  public func chunks(size chunksize: Swift.Int) -> Swift.Array<Swift.Array<Element>>
}
@usableFromInline
internal struct BatchedCollectionIndex<Base> where Base : Swift.Collection {
}
extension GGBluetoothSDKFramework.BatchedCollectionIndex : Swift.Comparable {
  @usableFromInline
  internal static func == <BaseCollection>(lhs: GGBluetoothSDKFramework.BatchedCollectionIndex<BaseCollection>, rhs: GGBluetoothSDKFramework.BatchedCollectionIndex<BaseCollection>) -> Swift.Bool where BaseCollection : Swift.Collection
  @usableFromInline
  internal static func < <BaseCollection>(lhs: GGBluetoothSDKFramework.BatchedCollectionIndex<BaseCollection>, rhs: GGBluetoothSDKFramework.BatchedCollectionIndex<BaseCollection>) -> Swift.Bool where BaseCollection : Swift.Collection
}
@usableFromInline
internal struct BatchedCollection<Base> : Swift.Collection where Base : Swift.Collection {
  @usableFromInline
  internal init(base: Base, size: Swift.Int)
  @usableFromInline
  internal typealias Index = GGBluetoothSDKFramework.BatchedCollectionIndex<Base>
  @usableFromInline
  internal var startIndex: GGBluetoothSDKFramework.BatchedCollection<Base>.Index {
    get
  }
  @usableFromInline
  internal var endIndex: GGBluetoothSDKFramework.BatchedCollection<Base>.Index {
    get
  }
  @usableFromInline
  internal func index(after idx: GGBluetoothSDKFramework.BatchedCollection<Base>.Index) -> GGBluetoothSDKFramework.BatchedCollection<Base>.Index
  @usableFromInline
  internal subscript(idx: GGBluetoothSDKFramework.BatchedCollection<Base>.Index) -> Base.SubSequence {
    get
  }
  @usableFromInline
  internal typealias Element = Base.SubSequence
  @usableFromInline
  internal typealias Indices = Swift.DefaultIndices<GGBluetoothSDKFramework.BatchedCollection<Base>>
  @usableFromInline
  internal typealias Iterator = Swift.IndexingIterator<GGBluetoothSDKFramework.BatchedCollection<Base>>
  @usableFromInline
  internal typealias SubSequence = Swift.Slice<GGBluetoothSDKFramework.BatchedCollection<Base>>
}
extension Swift.Collection {
  @inlinable internal func batched(by size: Swift.Int) -> GGBluetoothSDKFramework.BatchedCollection<Self> {
    BatchedCollection(base: self, size: size)
  }
}
public enum GGGoalType : Swift.Int {
  case GG_GOAL_UNSET
  case GG_GOAL_GAIN
  case GG_GOAL_LOSE
  case GG_GOAL_MAINTAIN
  public var description: Swift.String {
    get
  }
  public func toString() -> Swift.String
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@usableFromInline
final internal class BlockEncryptor : GGBluetoothSDKFramework.Cryptor, GGBluetoothSDKFramework.Updatable {
  public enum Error : Swift.Error {
    case unsupported
    public static func == (a: GGBluetoothSDKFramework.BlockEncryptor.Error, b: GGBluetoothSDKFramework.BlockEncryptor.Error) -> Swift.Bool
    public func hash(into hasher: inout Swift.Hasher)
    public var hashValue: Swift.Int {
      get
    }
  }
  @usableFromInline
  internal init(blockSize: Swift.Int, padding: GGBluetoothSDKFramework.Padding, _ worker: any GGBluetoothSDKFramework.CipherModeWorker) throws
  final public func update(withBytes bytes: Swift.ArraySlice<Swift.UInt8>, isLast: Swift.Bool) throws -> Swift.Array<Swift.UInt8>
  @usableFromInline
  final internal func seek(to: Swift.Int) throws
  @objc @usableFromInline
  deinit
}
extension Swift.FixedWidthInteger {
  @inlinable internal func bytes(totalBytes: Swift.Int = MemoryLayout<Self>.size) -> Swift.Array<Swift.UInt8> {
    arrayOfBytes(value: self.littleEndian, length: totalBytes)
     
     
     
  }
}
@_specialize(exported: false, kind: full, where T == Swift.Int)
@_specialize(exported: false, kind: full, where T == Swift.UInt)
@_specialize(exported: false, kind: full, where T == Swift.UInt8)
@_specialize(exported: false, kind: full, where T == Swift.UInt16)
@_specialize(exported: false, kind: full, where T == Swift.UInt32)
@_specialize(exported: false, kind: full, where T == Swift.UInt64)
@inlinable internal func arrayOfBytes<T>(value: T, length totalBytes: Swift.Int = MemoryLayout<T>.size) -> Swift.Array<Swift.UInt8> where T : Swift.FixedWidthInteger {
  let valuePointer = UnsafeMutablePointer<T>.allocate(capacity: 1)
  valuePointer.pointee = value

  let bytesPointer = UnsafeMutablePointer<UInt8>(OpaquePointer(valuePointer))
  var bytes = Array<UInt8>(repeating: 0, count: totalBytes)
  for j in 0..<min(MemoryLayout<T>.size, totalBytes) {
    bytes[totalBytes - 1 - j] = (bytesPointer + j).pointee
  }

  valuePointer.deinitialize(count: 1)
  valuePointer.deallocate()

  return bytes
}
@_hasMissingDesignatedInitializers public class GGIStub {
  public static func setLicenseKey(licenseKey: Swift.String) -> GGBluetoothSDKFramework.GGResultType
  public static func getHandler() -> any GGBluetoothSDKFramework.GGIBluetoothHandler
  @objc deinit
}
public protocol GGIFirmwareUpgradeInfo {
  func getStatus() -> GGBluetoothSDKFramework.GGFirmwareUpgradeStatus
}
public class ScanResult {
  public init(advertisementData: [Swift.String : Any], rssi: Foundation.NSNumber, id: Swift.String)
  public func getName() -> Swift.String
  public func getLocalName() -> Swift.String
  public func getManufacturerData() -> [GGBluetoothSDKFramework.byte]
  public func getRssi() -> GGBluetoothSDKFramework.int
  public func getPrimaryPhy() -> GGBluetoothSDKFramework.int
  public func getIdentifier() -> Swift.String
  public func getMACAddress() -> Swift.String
  public func getProtocolType() -> GGBluetoothSDKFramework.GGDeviceProtocolType
  public func getDeviceType() -> GGBluetoothSDKFramework.GGBLEDeviceType
  public func getCompanyId() -> Swift.String
  @objc deinit
}
public enum GGThermometerErrorCode : Swift.Int {
  case GG_THERMOMETER_ERROR_HI
  case GG_THERMOMETER_ERROR_LOW
  case GG_THERMOMETER_ERROR_OUT_OF_RANGE
  case GG_THERMOMETER_ERROR_LOW_BATTERY
  case GG_THERMOMETER_ERROR_UNSET
  public static func fromValue(_ rawValue: Swift.Int) -> GGBluetoothSDKFramework.GGThermometerErrorCode
  public var description: Swift.String {
    get
  }
  public func toString() -> Swift.String
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
public protocol GGIBLEDevice {
  func isConnected() -> GGBluetoothSDKFramework.boolean
  func getDeviceType() -> GGBluetoothSDKFramework.GGBLEDeviceType
  func getProtocolType() -> GGBluetoothSDKFramework.GGDeviceProtocolType
  func getDeviceInfo() -> (any GGBluetoothSDKFramework.GGIDeviceInfo)?
  func readBatteryLevel() -> GGBluetoothSDKFramework.GGResultType
  func readUnit() -> GGBluetoothSDKFramework.GGResultType
  func readTime() -> GGBluetoothSDKFramework.GGResultType
  func setTime(_ timeData: GGBluetoothSDKFramework.GGTimeData?) -> GGBluetoothSDKFramework.GGResultType
  func addAccount(_ accountInfo: GGBluetoothSDKFramework.GGAccountInfo, _ needPairingScreen: GGBluetoothSDKFramework.boolean) -> GGBluetoothSDKFramework.GGResultType
  func subscribeToLiveData() -> GGBluetoothSDKFramework.GGResultType
  func unsubscribeToLiveData() -> GGBluetoothSDKFramework.GGResultType
  func getHistoryData() -> GGBluetoothSDKFramework.GGResultType
  func syncData(_ accountID: [GGBluetoothSDKFramework.byte]) -> GGBluetoothSDKFramework.GGResultType
  func syncHistoryItems(_ count: Swift.Int) -> GGBluetoothSDKFramework.GGResultType
  func resetFactorySettings() -> GGBluetoothSDKFramework.GGResultType
  func onReceivedMeasurementData(_ status: GGBluetoothSDKFramework.GGResultType) -> GGBluetoothSDKFramework.GGResultType
  func onReceivedHistoryDataList(_ status: GGBluetoothSDKFramework.GGResultType) -> GGBluetoothSDKFramework.GGResultType
  func disconnect()
  func synchronizeLog() -> GGBluetoothSDKFramework.GGResultType
  func acknowledgeLogRawDataReceived() -> GGBluetoothSDKFramework.GGResultType
  func setWeightUnit(_ unit: GGBluetoothSDKFramework.WeightUnit) -> GGBluetoothSDKFramework.GGResultType
  func setBgmUnit(_ unit: GGBluetoothSDKFramework.BgmUnit) -> GGBluetoothSDKFramework.GGResultType
  func setTemperatureUnit(_ unit: GGBluetoothSDKFramework.TemperatureUnit) -> GGBluetoothSDKFramework.GGResultType
  func readMuteMode() -> GGBluetoothSDKFramework.GGResultType
  func setMuteMode(_ state: GGBluetoothSDKFramework.GGSwitchState) -> GGBluetoothSDKFramework.GGResultType
  func readHeartRateSwitch() -> GGBluetoothSDKFramework.GGResultType
  func setHeartRateSwitch(_ status: GGBluetoothSDKFramework.boolean) -> GGBluetoothSDKFramework.GGResultType
  func readImpedanceSwitch(_ onlyCurrentSession: GGBluetoothSDKFramework.boolean) -> GGBluetoothSDKFramework.GGResultType
  func setImpedanceSwitch(_ status: GGBluetoothSDKFramework.boolean, _ onlyCurrentSession: GGBluetoothSDKFramework.boolean) -> GGBluetoothSDKFramework.GGResultType
  func readAnimationPageSwitch() -> GGBluetoothSDKFramework.GGResultType
  func setAnimationPageSwitch(_ startAnimationStatus: GGBluetoothSDKFramework.boolean, _ shutdownAnimationStatus: GGBluetoothSDKFramework.boolean) -> GGBluetoothSDKFramework.GGResultType
  func readDisplayResultOrder() -> GGBluetoothSDKFramework.GGResultType
  func writeDisplayResultOrder(_ displayDetails: Swift.Array<GGBluetoothSDKFramework.GGWeighingScaleDisplayDetail>) -> GGBluetoothSDKFramework.GGResultType
  func getWifiSSID() -> GGBluetoothSDKFramework.GGResultType
  func readWifiMACAddress() -> GGBluetoothSDKFramework.GGResultType
  func readWifiSetupStatus() -> GGBluetoothSDKFramework.GGResultType
  func getWifiList() -> GGBluetoothSDKFramework.GGResultType
  func selectWifiInfo(_ wifiInfo: GGBluetoothSDKFramework.GGWifiInfo) -> GGBluetoothSDKFramework.GGResultType
  func cancelWifiInfo() -> GGBluetoothSDKFramework.GGResultType
  func acknowledgeWifiSSID() -> GGBluetoothSDKFramework.GGResultType
  func fetchAccountIDList(needImpedance: GGBluetoothSDKFramework.boolean) -> GGBluetoothSDKFramework.GGResultType
  func getAccountIDList() -> Swift.Array<GGBluetoothSDKFramework.GGAccountInfo>
  func addAccount(_ accountInfo: GGBluetoothSDKFramework.GGAccountInfo, _ userPreference: GGBluetoothSDKFramework.GGUserPreference, _ displayDetails: Swift.Array<GGBluetoothSDKFramework.GGWeighingScaleDisplayDetail>, _ needPairingScreen: GGBluetoothSDKFramework.boolean) -> GGBluetoothSDKFramework.GGResultType
  func deleteAccount(_ accountID: [GGBluetoothSDKFramework.byte]) -> GGBluetoothSDKFramework.GGResultType
  func addOnlineAccount() -> GGBluetoothSDKFramework.GGResultType
  func getAccountIDDistribution() -> GGBluetoothSDKFramework.GGResultType
  func selectAccountID(_ position: GGBluetoothSDKFramework.int) -> GGBluetoothSDKFramework.GGResultType
  func getDeviceMatchingAccountID() -> GGBluetoothSDKFramework.GGResultType
  func acknowledgeDeviceMatchingAccountIDReceived(_ resultType: GGBluetoothSDKFramework.GGResultType) -> GGBluetoothSDKFramework.GGResultType
  func clearAllData() -> GGBluetoothSDKFramework.GGResultType
  func clearUserAccountData() -> GGBluetoothSDKFramework.GGResultType
  func clearHistoryData() -> GGBluetoothSDKFramework.GGResultType
  func clearWifiNetworkData() -> GGBluetoothSDKFramework.GGResultType
  func clearSettingsData() -> GGBluetoothSDKFramework.GGResultType
  func deleteLatestEntry() -> GGBluetoothSDKFramework.GGResultType
  func tare() -> GGBluetoothSDKFramework.GGResultType
  func upgradeFirmware(_ scheduleTime: GGBluetoothSDKFramework.long) -> GGBluetoothSDKFramework.GGResultType
  func resetFirmware() -> GGBluetoothSDKFramework.GGResultType
}
@_hasMissingDesignatedInitializers public class Utils {
  public static func byteArrayToHexString(bytes: [Swift.UInt8]) -> Swift.String
  public static func hexStringToByteArray(_ string: Swift.String) -> [Swift.UInt8]
  public static func convertUUIDToBytes(_ uuid: CoreBluetooth.CBUUID) -> [Swift.UInt8]
  public static func convertBytesToUUID(_ bytes: [Swift.UInt8]) -> Foundation.UUID
  public static func addByteArray(_ array1: [Swift.UInt8], _ array2: [Swift.UInt8]) -> [Swift.UInt8]
  public static func floatFromByteArray(_ byteArray: [Swift.UInt8]) -> Swift.Float
  public static func reverseByteArray(array: inout [Swift.UInt8])
  public static func createVerificationCode(passwordBytes: [Swift.UInt8], random: [Swift.UInt8]) -> [Swift.UInt8]?
  public static func getTimeZoneInMillis() -> Swift.Int
  public static func getTimeZoneInSeconds() -> Swift.Int
  public static func getTimeZoneInMinutes() -> Swift.Int
  public static func getUTCTimeInMillis() -> Swift.Int64
  public static func getUTCTimeInSeconds() -> GGBluetoothSDKFramework.Long
  public static func getCurrentTime() -> GGBluetoothSDKFramework.Long
  public static func getTimeZoneFlag() -> GGBluetoothSDKFramework.int
  public static func round(_ value: Swift.Float, _ places: Swift.Int) -> Swift.Float
  public static func round(_ value: Swift.Double, _ places: Swift.Int) -> Swift.Double
  public static func convertTenthLbIntToKgFloat(_ tenthLbInt: Swift.Int) -> Swift.Float
  public static func convertKgIntToKgFloat(_ kgInt: Swift.Int, _ precision: Swift.Int = 2) -> GGBluetoothSDKFramework.float
  public static func convertKgIntToKgDouble(_ kgInt: Swift.Int, _ precision: Swift.Int = 2) -> GGBluetoothSDKFramework.double
  public static func convertMgIntToMgFloat(_ mgInt: Swift.Int) -> GGBluetoothSDKFramework.float
  public static func convertMgIntToMgDouble(_ mgInt: Swift.Int) -> GGBluetoothSDKFramework.double
  public static func convertLbIntToLbFloat(_ lbInt: Swift.Int) -> GGBluetoothSDKFramework.float
  public static func convertLbIntToLbDouble(_ lbInt: Swift.Int) -> GGBluetoothSDKFramework.double
  public static func convertKgIntToLbFloat(_ kgInt: Swift.Int) -> GGBluetoothSDKFramework.float
  public static func convertKgIntToLbDouble(_ kgInt: Swift.Int) -> GGBluetoothSDKFramework.double
  public static func convertLbIntToKgFloat(_ lbInt: Swift.Int) -> GGBluetoothSDKFramework.float
  public static func convertLbIntToKgDouble(_ lbInt: Swift.Int) -> GGBluetoothSDKFramework.double
  public static func convertMgIntToGramFloat(_ mg: Swift.Int) -> Swift.Float
  public static func convertMgIntToMlFloat(_ mg: Swift.Int) -> Swift.Float
  public static func convertMgIntToMlMilkFloat(_ mg: Swift.Int) -> Swift.Float
  public static func convertMgIntToFlOzFloat(_ mg: Swift.Int) -> Swift.Float
  public static func convertMgIntToFlOzMilkFloat(_ mg: Swift.Int) -> Swift.Float
  public static func convertOzToLbOz(ounces: Swift.Float) -> (lb: Swift.Int, oz: Swift.Float)
  public static func getR4TimeZone(_ itzOffset: Swift.Int) -> Swift.Int
  public static func trim(_ bytes: [GGBluetoothSDKFramework.byte]) -> [GGBluetoothSDKFramework.byte]
  public static func formatMACAddress(_ unformattedMAC: Swift.String, _ divisionChar: Swift.String) -> Swift.String
  public static func getByteArray<T>(_ value: T) -> [Swift.UInt8] where T : Swift.FixedWidthInteger
  @objc deinit
}
@_hasMissingDesignatedInitializers public class WellandUnitConversionUtils {
  public static func gToKg(_ g: Swift.Float, per: Swift.Int) -> Swift.Float
  public static func gToLb(_ g: Swift.Float, per: Swift.Int) -> Swift.Float
  @objc deinit
}
public enum GGSwitchState : Swift.UInt8 {
  case GG_SWITCH_STATE_UNSET
  case GG_SWITCH_STATE_OFF
  case GG_SWITCH_STATE_ON
  case GG_SWITCH_STATE_NOT_SET
  public var description: Swift.String {
    get
  }
  public func toString() -> Swift.String
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
public struct ECB : GGBluetoothSDKFramework.BlockMode {
  public let options: GGBluetoothSDKFramework.BlockModeOption
  public let customBlockSize: Swift.Int?
  public init()
  public func worker(blockSize: Swift.Int, cipherOperation: @escaping GGBluetoothSDKFramework.CipherOperationOnBlock, encryptionOperation: @escaping GGBluetoothSDKFramework.CipherOperationOnBlock) throws -> any GGBluetoothSDKFramework.CipherModeWorker
}
@usableFromInline
final internal class StreamDecryptor : GGBluetoothSDKFramework.Cryptor, GGBluetoothSDKFramework.Updatable {
  @usableFromInline
  internal enum Error : Swift.Error {
    case unsupported
    @usableFromInline
    internal static func == (a: GGBluetoothSDKFramework.StreamDecryptor.Error, b: GGBluetoothSDKFramework.StreamDecryptor.Error) -> Swift.Bool
    @usableFromInline
    internal func hash(into hasher: inout Swift.Hasher)
    @usableFromInline
    internal var hashValue: Swift.Int {
      @usableFromInline
      get
    }
  }
  @usableFromInline
  final internal let blockSize: Swift.Int
  @usableFromInline
  final internal var worker: any GGBluetoothSDKFramework.CipherModeWorker
  @usableFromInline
  final internal let padding: GGBluetoothSDKFramework.Padding
  @usableFromInline
  final internal var accumulated: [Swift.UInt8]
  @usableFromInline
  final internal var lastBlockRemainder: Swift.Int
  @usableFromInline
  internal init(blockSize: Swift.Int, padding: GGBluetoothSDKFramework.Padding, _ worker: any GGBluetoothSDKFramework.CipherModeWorker) throws
  @inlinable final public func update(withBytes bytes: Swift.ArraySlice<Swift.UInt8>, isLast: Swift.Bool) throws -> Swift.Array<Swift.UInt8> {
    self.accumulated += bytes

    let toProcess = self.accumulated.prefix(max(self.accumulated.count - self.worker.additionalBufferSize, 0))

    if var finalizingWorker = worker as? FinalizingDecryptModeWorker, isLast == true {
       
      try finalizingWorker.willDecryptLast(bytes: self.accumulated.slice)
    }

    var processedBytesCount = 0
    var plaintext = Array<UInt8>(reserveCapacity: bytes.count + self.worker.additionalBufferSize)
    for chunk in toProcess.batched(by: self.blockSize) {
      plaintext += self.worker.decrypt(block: chunk)
      processedBytesCount += chunk.count
    }

    if var finalizingWorker = worker as? FinalizingDecryptModeWorker, isLast == true {
      plaintext = Array(try finalizingWorker.didDecryptLast(bytes: plaintext.slice))
    }

     
    if self.padding != .noPadding {
      self.lastBlockRemainder = plaintext.count.quotientAndRemainder(dividingBy: self.blockSize).remainder
    }

    if isLast {
       
      plaintext = self.padding.remove(from: plaintext, blockSize: self.blockSize - self.lastBlockRemainder)
    }

    self.accumulated.removeFirst(processedBytesCount)  

    if var finalizingWorker = worker as? FinalizingDecryptModeWorker, isLast == true {
      plaintext = Array(try finalizingWorker.finalize(decrypt: plaintext.slice))
    }

    return plaintext
  }
  @inlinable final public func seek(to position: Swift.Int) throws {
    guard var worker = self.worker as? SeekableModeWorker else {
      throw Error.unsupported
    }

    try worker.seek(to: position)
    self.worker = worker
  }
  @objc @usableFromInline
  deinit
}
public enum GGSex : Swift.UInt8 {
  case GG_SEX_UNSET
  case GG_SEX_MALE
  case GG_SEX_FEMALE
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@_hasMissingDesignatedInitializers public class Logger {
  @objc deinit
}
@_hasMissingDesignatedInitializers open class GGScanDelegate : GGBluetoothSDKFramework.GGIScanDelegate {
  open func onDeviceFound(_ deviceInfo: any GGBluetoothSDKFramework.GGIDeviceInfo)
  open func onDeviceConnect(_ bleDevice: any GGBluetoothSDKFramework.GGIBLEDevice)
  open func onDeviceDisconnect(_ bleDevice: any GGBluetoothSDKFramework.GGIBLEDevice)
  open func onBLEAdapterStateChanged(_ state: GGBluetoothSDKFramework.GGBluetoothAdapterState)
  public func onConnectionFailed(_ bleDevice: any GGBluetoothSDKFramework.GGIDeviceInfo)
  @objc deinit
}
@objc @_inheritsConvenienceInitializers public class GGWifiInfo : ObjectiveC.NSObject, Swift.Identifiable {
  public var id: Foundation.UUID
  public func getMACAddress() -> Swift.String
  public func setMACAddress(_ macAddress: Swift.String)
  public func getSSID() -> Swift.String
  public func setSSID(_ ssid: Swift.String)
  public func getRSSI() -> Swift.Int
  public func setRSSI(_ rssi: Swift.Int)
  public func getPassword() -> Swift.String
  public func setPassword(_ password: Swift.String)
  @objc override dynamic public init()
  public func toString() -> Swift.String
  public typealias ID = Foundation.UUID
  @objc deinit
}
public enum GGBLEDeviceType : Swift.UInt8 {
  case GG_DEVICE_UNSET
  case GG_WEIGHING_SCALE_DEVICE
  case GG_BPM_DEVICE
  case GG_PULSE_OXIMETER_DEVICE
  case GG_BGM_DEVICE
  case GG_HEALTH_THERMOMETER_DEVICE
  case GG_SMART_KITCHEN_SCALE
  public func toString() -> Swift.String
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
public protocol GGIBodyMetricsInfo : GGBluetoothSDKFramework.GGIWeightInfo {
  func getBMI() -> GGBluetoothSDKFramework.float
  func getBodyFatPercent() -> GGBluetoothSDKFramework.float
  func getMusclePercent() -> GGBluetoothSDKFramework.float
  func getBodyWaterPercent() -> GGBluetoothSDKFramework.float
  func getBoneMass() -> GGBluetoothSDKFramework.float
  func getHeartRate() -> GGBluetoothSDKFramework.int
  func getVisceralFat() -> GGBluetoothSDKFramework.float
  func getSubcutaneousFat() -> GGBluetoothSDKFramework.float
  func getProtein() -> GGBluetoothSDKFramework.float
  func getSkeletalMuscle() -> GGBluetoothSDKFramework.float
  func getBMR() -> GGBluetoothSDKFramework.float
  func getMetabolicAge() -> GGBluetoothSDKFramework.int
  func getImpedance() -> GGBluetoothSDKFramework.int
  func getUserSelectionStatus() -> GGBluetoothSDKFramework.int
  func getDeviceMatchingAccountIDStatus() -> GGBluetoothSDKFramework.int
  func getDeviceMatchingAccountID() -> [GGBluetoothSDKFramework.byte]
}
extension Swift.UInt32 {
  @_specialize(exported: false, kind: full, where T == Swift.ArraySlice<Swift.UInt8>)
  @inlinable internal init<T>(bytes: T, fromIndex index: T.Index) where T : Swift.Collection, T.Element == Swift.UInt8, T.Index == Swift.Int {
    if bytes.isEmpty {
      self = 0
      return
    }

    let count = bytes.count

    let val0 = count > 0 ? UInt32(bytes[index.advanced(by: 0)]) << 24 : 0
    let val1 = count > 1 ? UInt32(bytes[index.advanced(by: 1)]) << 16 : 0
    let val2 = count > 2 ? UInt32(bytes[index.advanced(by: 2)]) << 8 : 0
    let val3 = count > 3 ? UInt32(bytes[index.advanced(by: 3)]) : 0

    self = val0 | val1 | val2 | val3
  }
}
public class BlockDecryptor : GGBluetoothSDKFramework.Cryptor, GGBluetoothSDKFramework.Updatable {
  public enum Error : Swift.Error {
    case unsupported
    public static func == (a: GGBluetoothSDKFramework.BlockDecryptor.Error, b: GGBluetoothSDKFramework.BlockDecryptor.Error) -> Swift.Bool
    public func hash(into hasher: inout Swift.Hasher)
    public var hashValue: Swift.Int {
      get
    }
  }
  @usableFromInline
  final internal let blockSize: Swift.Int
  @usableFromInline
  final internal let padding: GGBluetoothSDKFramework.Padding
  @usableFromInline
  internal var worker: any GGBluetoothSDKFramework.CipherModeWorker
  @usableFromInline
  internal var accumulated: [Swift.UInt8]
  @usableFromInline
  internal init(blockSize: Swift.Int, padding: GGBluetoothSDKFramework.Padding, _ worker: any GGBluetoothSDKFramework.CipherModeWorker) throws
  @inlinable public func update(withBytes bytes: Swift.ArraySlice<Swift.UInt8>, isLast: Swift.Bool = false) throws -> Swift.Array<Swift.UInt8> {
    self.accumulated += bytes

     
     
    if !isLast && self.accumulated.count < self.blockSize + self.worker.additionalBufferSize {
      return []
    }

    let accumulatedWithoutSuffix: Array<UInt8>
    if self.worker.additionalBufferSize > 0 {
       
      accumulatedWithoutSuffix = Array(self.accumulated.prefix(self.accumulated.count - self.worker.additionalBufferSize))
    } else {
      accumulatedWithoutSuffix = self.accumulated
    }

    var processedBytesCount = 0
    var plaintext = Array<UInt8>(reserveCapacity: accumulatedWithoutSuffix.count)
     
    for var chunk in accumulatedWithoutSuffix.batched(by: self.blockSize) {
      if isLast || (accumulatedWithoutSuffix.count - processedBytesCount) >= blockSize {
        let isLastChunk = processedBytesCount + chunk.count == accumulatedWithoutSuffix.count

        if isLast, isLastChunk, var finalizingWorker = worker as? FinalizingDecryptModeWorker {
          chunk = try finalizingWorker.willDecryptLast(bytes: chunk + accumulated.suffix(worker.additionalBufferSize))  
        }

        if !chunk.isEmpty {
          plaintext += worker.decrypt(block: chunk)
        }

        if isLast, isLastChunk, var finalizingWorker = worker as? FinalizingDecryptModeWorker {
          plaintext = Array(try finalizingWorker.didDecryptLast(bytes: plaintext.slice))
        }

        processedBytesCount += chunk.count
      }
    }
    accumulated.removeFirst(processedBytesCount)  

    if isLast {
      if accumulatedWithoutSuffix.isEmpty, var finalizingWorker = worker as? FinalizingDecryptModeWorker {
        try finalizingWorker.willDecryptLast(bytes: self.accumulated.suffix(self.worker.additionalBufferSize))
        plaintext = Array(try finalizingWorker.didDecryptLast(bytes: plaintext.slice))
      }
      plaintext = self.padding.remove(from: plaintext, blockSize: self.blockSize)
    }

    return plaintext
  }
  public func seek(to position: Swift.Int) throws
  @objc deinit
}
public protocol GGIBloodPressureInfo : GGBluetoothSDKFramework.GGIMeasurementInfo {
  func getSystolicValue() -> GGBluetoothSDKFramework.int
  func getDiastolicValue() -> GGBluetoothSDKFramework.int
  func getMeanPressureValue() -> GGBluetoothSDKFramework.int
  func getPulseRate() -> GGBluetoothSDKFramework.int
  func getUser() -> GGBluetoothSDKFramework.int
}
@_hasMissingDesignatedInitializers public class GGObject {
  @objc deinit
}
@_hasMissingDesignatedInitializers public class GGUserPreference {
  public func getWeightUnit() -> GGBluetoothSDKFramework.WeightUnit
  public func getWeightUnitInt(_ sku: GGBluetoothSDKFramework.int) -> GGBluetoothSDKFramework.int
  public func getPressureUnit() -> GGBluetoothSDKFramework.PressureUnit
  public func getPressureUnitInt(_ sku: GGBluetoothSDKFramework.int) -> GGBluetoothSDKFramework.int
  public func getTimeData() -> GGBluetoothSDKFramework.GGTimeData
  public func getMeasureImpedance() -> GGBluetoothSDKFramework.boolean
  public func getMeasureHeartRate() -> GGBluetoothSDKFramework.boolean
  public class Builder {
    public init()
    public func setWeightUnit(_ unit: GGBluetoothSDKFramework.WeightUnit) -> GGBluetoothSDKFramework.GGUserPreference.Builder
    public func setPressureUnit(_ unit: GGBluetoothSDKFramework.PressureUnit) -> GGBluetoothSDKFramework.GGUserPreference.Builder
    public func setTimeData(_ timeData: GGBluetoothSDKFramework.GGTimeData) -> GGBluetoothSDKFramework.GGUserPreference.Builder
    public func setMeasureImpedance(_ measureImpedance: GGBluetoothSDKFramework.boolean) -> GGBluetoothSDKFramework.GGUserPreference.Builder
    public func setMeasureHeartRate(_ measureHeartRate: GGBluetoothSDKFramework.boolean) -> GGBluetoothSDKFramework.GGUserPreference.Builder
    public func build() -> GGBluetoothSDKFramework.GGUserPreference
    @objc deinit
  }
  @objc deinit
}
public class BluetoothCentral {
  public init(withCentral central: CoreBluetooth.CBCentral)
  @objc deinit
}
public enum GGCallbackAcknowledgementType : Swift.UInt32 {
  case GG_CALLBACK_ACKNOWLEDGEMENT_UNSET
  case GG_CALLBACK_ACKNOWLEDGEMENT_DEVICE_CONNECTED
  case GG_CALLBACK_ACKNOWLEDGEMENT_DEVICE_CONNECTION_ERROR
  case GG_CALLBACK_ACKNOWLEDGEMENT_DEVICE_DISCONNECTION_SUCCESS
  case GG_CALLBACK_ACKNOWLEDGEMENT_DEVICE_DISCONNECTION_ERROR
  case GG_CALLBACK_ACKNOWLEDGEMENT_DEVICE_SERVICES_FOUND
  case GG_CALLBACK_ACKNOWLEDGEMENT_DEVICE_SERVICES_FAILED
  case GG_CALLBACK_ACKNOWLEDGEMENT_DEVICE_BINDING_FAIL
  case GG_CALLBACK_ACKNOWLEDGEMENT_DEVICE_BINDING_SUCCESS
  case GG_CALLBACK_ACKNOWLEDGEMENT_DEVICE_SETTING_UNIT_AND_UTC_FAIL
  case GG_CALLBACK_ACKNOWLEDGEMENT_DEVICE_SETTING_UNIT_AND_UTC_SUCCESS
  public init?(rawValue: Swift.UInt32)
  public typealias RawValue = Swift.UInt32
  public var rawValue: Swift.UInt32 {
    get
  }
}
@usableFromInline
final internal class StreamEncryptor : GGBluetoothSDKFramework.Cryptor, GGBluetoothSDKFramework.Updatable {
  @usableFromInline
  internal enum Error : Swift.Error {
    case unsupported
    @usableFromInline
    internal static func == (a: GGBluetoothSDKFramework.StreamEncryptor.Error, b: GGBluetoothSDKFramework.StreamEncryptor.Error) -> Swift.Bool
    @usableFromInline
    internal func hash(into hasher: inout Swift.Hasher)
    @usableFromInline
    internal var hashValue: Swift.Int {
      @usableFromInline
      get
    }
  }
  @usableFromInline
  final internal let blockSize: Swift.Int
  @usableFromInline
  final internal var worker: any GGBluetoothSDKFramework.CipherModeWorker
  @usableFromInline
  final internal let padding: GGBluetoothSDKFramework.Padding
  @usableFromInline
  final internal var lastBlockRemainder: Swift.Int
  @usableFromInline
  internal init(blockSize: Swift.Int, padding: GGBluetoothSDKFramework.Padding, _ worker: any GGBluetoothSDKFramework.CipherModeWorker) throws
  @inlinable final public func update(withBytes bytes: Swift.ArraySlice<Swift.UInt8>, isLast: Swift.Bool) throws -> Swift.Array<Swift.UInt8> {
    var accumulated = Array(bytes)
    if isLast {
       
      accumulated = self.padding.add(to: accumulated, blockSize: self.blockSize - self.lastBlockRemainder)
    }

    var encrypted = Array<UInt8>(reserveCapacity: bytes.count)
    for chunk in accumulated.batched(by: self.blockSize) {
      encrypted += self.worker.encrypt(block: chunk)
    }

     
    if self.padding != .noPadding {
      self.lastBlockRemainder = encrypted.count.quotientAndRemainder(dividingBy: self.blockSize).remainder
    }

    if var finalizingWorker = worker as? FinalizingEncryptModeWorker, isLast == true {
      encrypted = Array(try finalizingWorker.finalize(encrypt: encrypted.slice))
    }

    return encrypted
  }
  @usableFromInline
  final internal func seek(to: Swift.Int) throws
  @objc @usableFromInline
  deinit
}
extension GGBluetoothSDKFramework.GGBGErrorCode : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGBGErrorCode : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGBGErrorCode : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.GGBGStatus : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGBGStatus : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGBGStatus : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.GGResultType : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGResultType : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGResultType : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.AddressType : Swift.Equatable {}
extension GGBluetoothSDKFramework.AddressType : Swift.Hashable {}
extension GGBluetoothSDKFramework.AddressType : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.GGDeviceProtocolType : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGDeviceProtocolType : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGDeviceProtocolType : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.GGWeighingScaleDisplayDetail : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGWeighingScaleDisplayDetail : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGWeighingScaleDisplayDetail : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.GGMeasurementType : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGMeasurementType : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGMeasurementType : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.WeightUnit : Swift.Equatable {}
extension GGBluetoothSDKFramework.WeightUnit : Swift.Hashable {}
extension GGBluetoothSDKFramework.WeightUnit : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.GGGender : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGGender : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGGender : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.GGMealMark : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGMealMark : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGMealMark : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.GGUserType : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGUserType : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGUserType : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.TemperatureUnit : Swift.Equatable {}
extension GGBluetoothSDKFramework.TemperatureUnit : Swift.Hashable {}
extension GGBluetoothSDKFramework.TemperatureUnit : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.GGWifiState : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGWifiState : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGWifiState : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.GGTimeDisplayType : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGTimeDisplayType : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGTimeDisplayType : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.BinaryDataErrors : Swift.Equatable {}
extension GGBluetoothSDKFramework.BinaryDataErrors : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGFirmwareUpgradeStatus : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGFirmwareUpgradeStatus : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGFirmwareUpgradeStatus : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.GGOperationType : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGOperationType : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGOperationType : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.BgmUnit : Swift.Equatable {}
extension GGBluetoothSDKFramework.BgmUnit : Swift.Hashable {}
extension GGBluetoothSDKFramework.CipherError : Swift.Equatable {}
extension GGBluetoothSDKFramework.CipherError : Swift.Hashable {}
extension GGBluetoothSDKFramework.AES.Error : Swift.Equatable {}
extension GGBluetoothSDKFramework.AES.Error : Swift.Hashable {}
extension GGBluetoothSDKFramework.AES.Variant : Swift.Equatable {}
extension GGBluetoothSDKFramework.AES.Variant : Swift.Hashable {}
extension GGBluetoothSDKFramework.AES.Variant : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.Padding : Swift.Equatable {}
extension GGBluetoothSDKFramework.Padding : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGBluetoothAdapterState : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGBluetoothAdapterState : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGBluetoothAdapterState : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.PressureUnit : Swift.Equatable {}
extension GGBluetoothSDKFramework.PressureUnit : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGGoalType : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGGoalType : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGGoalType : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.BlockEncryptor.Error : Swift.Equatable {}
extension GGBluetoothSDKFramework.BlockEncryptor.Error : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGThermometerErrorCode : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGThermometerErrorCode : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGThermometerErrorCode : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.GGSwitchState : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGSwitchState : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGSwitchState : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.StreamDecryptor.Error : Swift.Equatable {}
extension GGBluetoothSDKFramework.StreamDecryptor.Error : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGSex : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGSex : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGSex : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.GGBLEDeviceType : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGBLEDeviceType : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGBLEDeviceType : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.BlockDecryptor.Error : Swift.Equatable {}
extension GGBluetoothSDKFramework.BlockDecryptor.Error : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGCallbackAcknowledgementType : Swift.Equatable {}
extension GGBluetoothSDKFramework.GGCallbackAcknowledgementType : Swift.Hashable {}
extension GGBluetoothSDKFramework.GGCallbackAcknowledgementType : Swift.RawRepresentable {}
extension GGBluetoothSDKFramework.StreamEncryptor.Error : Swift.Equatable {}
extension GGBluetoothSDKFramework.StreamEncryptor.Error : Swift.Hashable {}
