// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 70;
	objects = {

/* Begin PBXBuildFile section */
		1001E74F2C1FEE62001D85DE /* GGBluetoothPackageTestApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1001E74E2C1FEE62001D85DE /* GGBluetoothPackageTestApp.swift */; };
		48407B9C2D941B620047D8DF /* Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 48407B9B2D941B620047D8DF /* Launch Screen.storyboard */; };
		4895638C2D9BA8F400AA831E /* Alamofire in Frameworks */ = {isa = PBXBuildFile; productRef = 4898AAC42D93E05C00063FD4 /* Alamofire */; };
		489B23972DDDC1550066DBEA /* GGBluetoothSwiftPackage in Frameworks */ = {isa = PBXBuildFile; productRef = 489B23962DDDC1550066DBEA /* GGBluetoothSwiftPackage */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1001E74B2C1FEE62001D85DE /* GGBluetoothPackageTest.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = GGBluetoothPackageTest.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1001E74E2C1FEE62001D85DE /* GGBluetoothPackageTestApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GGBluetoothPackageTestApp.swift; sourceTree = "<group>"; };
		48407B9B2D941B620047D8DF /* Launch Screen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = "Launch Screen.storyboard"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		48CF168E2CF49D0500D4AE17 /* PBXFileSystemSynchronizedBuildFileExceptionSet */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 1001E74A2C1FEE62001D85DE /* GGBluetoothPackageTest */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		48CF16772CF49BE300D4AE17 /* Views */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Views; sourceTree = "<group>"; };
		48CF16842CF49C0500D4AE17 /* Shared */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Shared; sourceTree = "<group>"; };
		48CF16882CF49C2F00D4AE17 /* Data */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Data; sourceTree = "<group>"; };
		48CF168A2CF49C5A00D4AE17 /* Resources */ = {isa = PBXFileSystemSynchronizedRootGroup; exceptions = (48CF168E2CF49D0500D4AE17 /* PBXFileSystemSynchronizedBuildFileExceptionSet */, ); explicitFileTypes = {}; explicitFolders = (); path = Resources; sourceTree = "<group>"; };
		48CF169B2CF5813000D4AE17 /* Infrastructure */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Infrastructure; sourceTree = "<group>"; };
		48CF169F2CF581BA00D4AE17 /* Component */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Component; sourceTree = "<group>"; };
		48CF170E2CF6EF7400D4AE17 /* Application */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Application; sourceTree = "<group>"; };
		48CF17AD2CF710A500D4AE17 /* Model */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Model; sourceTree = "<group>"; };
		48CF17BB2CF718F500D4AE17 /* Routes */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Routes; sourceTree = "<group>"; };
		48CF19412CFD715400D4AE17 /* Domain */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Domain; sourceTree = "<group>"; };
		48D9E8EE2D2EA1D400902774 /* Theme */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Theme; sourceTree = "<group>"; };
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		1001E7482C1FEE62001D85DE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				489B23972DDDC1550066DBEA /* GGBluetoothSwiftPackage in Frameworks */,
				4895638C2D9BA8F400AA831E /* Alamofire in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1001E7422C1FEE62001D85DE = {
			isa = PBXGroup;
			children = (
				1001E74D2C1FEE62001D85DE /* GGBluetoothPackageTest */,
				4894CA752D8A8B8500EAD3B0 /* Frameworks */,
				1001E74C2C1FEE62001D85DE /* Products */,
			);
			sourceTree = "<group>";
		};
		1001E74C2C1FEE62001D85DE /* Products */ = {
			isa = PBXGroup;
			children = (
				1001E74B2C1FEE62001D85DE /* GGBluetoothPackageTest.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1001E74D2C1FEE62001D85DE /* GGBluetoothPackageTest */ = {
			isa = PBXGroup;
			children = (
				48CF19412CFD715400D4AE17 /* Domain */,
				48CF170E2CF6EF7400D4AE17 /* Application */,
				48CF169B2CF5813000D4AE17 /* Infrastructure */,
				48CF168A2CF49C5A00D4AE17 /* Resources */,
				48CF16882CF49C2F00D4AE17 /* Data */,
				48CF16842CF49C0500D4AE17 /* Shared */,
				1001E7822C202FE1001D85DE /* UI */,
				1001E74E2C1FEE62001D85DE /* GGBluetoothPackageTestApp.swift */,
				48407B9B2D941B620047D8DF /* Launch Screen.storyboard */,
			);
			path = GGBluetoothPackageTest;
			sourceTree = "<group>";
		};
		1001E7662C1FF12C001D85DE /* Shared */ = {
			isa = PBXGroup;
			children = (
				48CF17BB2CF718F500D4AE17 /* Routes */,
				48CF169F2CF581BA00D4AE17 /* Component */,
			);
			path = Shared;
			sourceTree = "<group>";
		};
		1001E7822C202FE1001D85DE /* UI */ = {
			isa = PBXGroup;
			children = (
				48D9E8EE2D2EA1D400902774 /* Theme */,
				48CF17AD2CF710A500D4AE17 /* Model */,
				48CF16772CF49BE300D4AE17 /* Views */,
				1001E7662C1FF12C001D85DE /* Shared */,
			);
			path = UI;
			sourceTree = "<group>";
		};
		4894CA752D8A8B8500EAD3B0 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1001E74A2C1FEE62001D85DE /* GGBluetoothPackageTest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1001E75E2C1FEE63001D85DE /* Build configuration list for PBXNativeTarget "GGBluetoothPackageTest" */;
			buildPhases = (
				1001E7472C1FEE62001D85DE /* Sources */,
				1001E7482C1FEE62001D85DE /* Frameworks */,
				1001E7492C1FEE62001D85DE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				48CF16772CF49BE300D4AE17 /* Views */,
				48CF16842CF49C0500D4AE17 /* Shared */,
				48CF16882CF49C2F00D4AE17 /* Data */,
				48CF168A2CF49C5A00D4AE17 /* Resources */,
				48CF169B2CF5813000D4AE17 /* Infrastructure */,
				48CF169F2CF581BA00D4AE17 /* Component */,
				48CF170E2CF6EF7400D4AE17 /* Application */,
				48CF17AD2CF710A500D4AE17 /* Model */,
				48CF17BB2CF718F500D4AE17 /* Routes */,
				48CF19412CFD715400D4AE17 /* Domain */,
				48D9E8EE2D2EA1D400902774 /* Theme */,
			);
			name = GGBluetoothPackageTest;
			packageProductDependencies = (
				4898AAC42D93E05C00063FD4 /* Alamofire */,
				489B23962DDDC1550066DBEA /* GGBluetoothSwiftPackage */,
			);
			productName = GGBluetoothPackageTest;
			productReference = 1001E74B2C1FEE62001D85DE /* GGBluetoothPackageTest.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1001E7432C1FEE62001D85DE /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1530;
				LastUpgradeCheck = 1530;
				TargetAttributes = {
					1001E74A2C1FEE62001D85DE = {
						CreatedOnToolsVersion = 15.3;
					};
				};
			};
			buildConfigurationList = 1001E7462C1FEE62001D85DE /* Build configuration list for PBXProject "GGBluetoothPackageTest" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1001E7422C1FEE62001D85DE;
			packageReferences = (
				4894CA7D2D8A8D8700EAD3B0 /* XCRemoteSwiftPackageReference "Alamofire" */,
				489B23952DDDC1550066DBEA /* XCLocalSwiftPackageReference "../GGBluetoothSwiftPackage" */,
			);
			productRefGroup = 1001E74C2C1FEE62001D85DE /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1001E74A2C1FEE62001D85DE /* GGBluetoothPackageTest */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1001E7492C1FEE62001D85DE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				48407B9C2D941B620047D8DF /* Launch Screen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1001E7472C1FEE62001D85DE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1001E74F2C1FEE62001D85DE /* GGBluetoothPackageTestApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1001E75C2C1FEE63001D85DE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1001E75D2C1FEE63001D85DE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1001E75F2C1FEE63001D85DE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = PX369MG78T;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = GGBluetoothPackageTest/Resources/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "GG Test App";
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "Test app uses bluetooth to pair and sync with bluetooth devices";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "Test app uses bluetooth to pair and sync with bluetooth devices";
				INFOPLIST_KEY_NSCameraUsageDescription = "Test app uses camera to sync with app sync scales";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "Test app uses location to pair with wifi scales";
				INFOPLIST_KEY_NSLocationAlwaysUsageDescription = "Test app uses location to pair with wifi scales";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "Test app uses location to pair with wifi scales";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = "Launch Screen";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.dmdbrands.GGBluetoothPackageTest;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		1001E7602C1FEE63001D85DE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = PX369MG78T;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = GGBluetoothPackageTest/Resources/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "GG Test App";
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "Test app uses bluetooth to pair and sync with bluetooth devices";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "Test app uses bluetooth to pair and sync with bluetooth devices";
				INFOPLIST_KEY_NSCameraUsageDescription = "Test app uses camera to sync with app sync scales";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "Test app uses location to pair with wifi scales";
				INFOPLIST_KEY_NSLocationAlwaysUsageDescription = "Test app uses location to pair with wifi scales";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "Test app uses location to pair with wifi scales";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = "Launch Screen";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.dmdbrands.GGBluetoothPackageTest;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1001E7462C1FEE62001D85DE /* Build configuration list for PBXProject "GGBluetoothPackageTest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1001E75C2C1FEE63001D85DE /* Debug */,
				1001E75D2C1FEE63001D85DE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1001E75E2C1FEE63001D85DE /* Build configuration list for PBXNativeTarget "GGBluetoothPackageTest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1001E75F2C1FEE63001D85DE /* Debug */,
				1001E7602C1FEE63001D85DE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		489B23952DDDC1550066DBEA /* XCLocalSwiftPackageReference "../GGBluetoothSwiftPackage" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = ../GGBluetoothSwiftPackage;
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCRemoteSwiftPackageReference section */
		4894CA7D2D8A8D8700EAD3B0 /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.10.2;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		4898AAC42D93E05C00063FD4 /* Alamofire */ = {
			isa = XCSwiftPackageProductDependency;
			package = 4894CA7D2D8A8D8700EAD3B0 /* XCRemoteSwiftPackageReference "Alamofire" */;
			productName = Alamofire;
		};
		489B23962DDDC1550066DBEA /* GGBluetoothSwiftPackage */ = {
			isa = XCSwiftPackageProductDependency;
			productName = GGBluetoothSwiftPackage;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 1001E7432C1FEE62001D85DE /* Project object */;
}
