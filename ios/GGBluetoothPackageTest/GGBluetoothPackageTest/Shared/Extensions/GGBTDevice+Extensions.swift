//
//  GGBTDevice+Extensions.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 16/01/25.
//

import Foundation
import GGBluetoothSwiftPackage

extension GGBTDevice {
    convenience init(from device: Device) {
        self.init(
            name: device.name ?? "",
            broadcastId: device.broadcastId ?? "",
            password: device.password,
            token: device.token,
            userNumber: Int(device.userNumber),
            preference:  nil,
            syncAllData: true,
            batteryLevel: Int(device.batteryLevel),
            protocolType: device.protocolType,
            macAddress: device.macAddress
        )
    }
}
