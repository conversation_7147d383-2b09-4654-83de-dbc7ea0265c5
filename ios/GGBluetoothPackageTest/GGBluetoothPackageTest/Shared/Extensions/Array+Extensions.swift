//
//  Array+Extensions.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/11/24.
//

import Foundation
extension Array {
    mutating func truncate(to index: Int) {
        guard index < self.count && index >= 0 else {
            return
        }
        self = Array(self[..<Swift.min(index + 1, self.count)])
    }
}

extension Array {
    func unique<T: Hashable>(by key: (Element) -> T) -> [Element] {
        var seen = Set<T>()
        return self.filter { seen.insert(key($0)).inserted }
    }
}
