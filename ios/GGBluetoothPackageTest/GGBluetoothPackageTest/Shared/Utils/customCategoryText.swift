//
//  customCategoryText.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 08/01/25.
//

import Foundation

func customCategoryText(for category: AppCategory?) -> String {
       switch category {
       case .balanceHealth:
           return "Balance Health"
       case .smartBaby:
           return "Smart Baby"
       case .weightGurus:
           return "Weight Gurus"
       case .sage:
           return "Sage"
       case .rpm:
           return "rpm"
       default:
           return  "All Devices"
       }
   }
