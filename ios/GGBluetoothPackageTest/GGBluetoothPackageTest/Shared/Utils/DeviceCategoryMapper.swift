//
//  DeviceCategoryMapper.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/12/24.
//

import Foundation

func deviceType(for category: AppCategory) -> DeviceCategory {
    switch category {
    case .weightGurus:
        return .weight
    case .balanceHealth, .smartBaby, .rpm:
        return .health
    case .sage:
        return .kitchen
    case .all:
        return .all
    }
}

func getDeviceType(forDeviceIdentifier identifier: String) -> DeviceCategory {
    if let device = devicesList.first(where: { $0.sku == identifier }) {
        return deviceType(for: device.app)
    } else {
        return .all
    }
}

