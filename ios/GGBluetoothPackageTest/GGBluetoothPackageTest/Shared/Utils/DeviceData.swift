//
//  DeviceData.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 24/12/24.
//

import Foundation

let devicesList: [DeviceInfo] = [
    DeviceInfo(
        name: "Kitchen Scale",
        sku: "---",
        image: AppAssets.kitchenScale,
        protocolType: "Welland",
        manufacturer: "Welland",
        app : .sage,
        errorcodes: CommonErrorCodes.basicErrors
    ),
    DeviceInfo(
        name: "Bluetooth smart scale",
        sku: "0375",
        image: AppAssets.wg0375,
        protocolType: "A3",
        manufacturer: "Welland",
        app : .weightGurus,
        errorcodes: CommonErrorCodes.WgA3ScaleErrors
    ),
    DeviceInfo(
        name: "Bluetooth smart scale",
        sku: "0376",
        image: AppAssets.wg0376,
        protocolType: "A3",
        manufacturer: "Welland",
        app : .weightGurus,
        errorcodes: CommonErrorCodes.WgA3ScaleErrors
    ),
    DeviceInfo(
        name: "Bluetooth smart scale",
        sku: "0380",
        image: AppAssets.wg0380,
        protocolType: "A3",
        manufacturer: "Welland",
        app : .weightGurus,
        errorcodes: CommonErrorCodes.WgA3ScaleErrors
    ),
    DeviceInfo(
        name: "Bluetooth smart scale",
        sku: "0382",
        image: AppAssets.wg0382,
        protocolType: "A3",
        manufacturer: "Welland",
        app : .weightGurus,
        errorcodes: CommonErrorCodes.WgA3ScaleErrors
    ),
    DeviceInfo(
        name: "Bluetooth smart scale",
        sku: "0378",
        image: AppAssets.wg0383,
        protocolType: "A6",
        manufacturer: "Welland",
        app : .weightGurus,
        errorcodes: CommonErrorCodes.WgA6ScaleErrors
    ),
    DeviceInfo(
        name: "Bluetooth smart scale",
        sku: "0383",
        image: AppAssets.wg0383,
        protocolType: "A6",
        manufacturer: "Welland",
        app : .weightGurus,
        errorcodes:  CommonErrorCodes.WgA6ScaleErrors
    ),
    DeviceInfo(
        name: "AccuCheck Verve Smart Scale",
        sku: "0412",
        image: AppAssets.wg0412,
        protocolType: "R4",
        manufacturer: "Unique",
        app : .weightGurus,
        errorcodes: CommonErrorCodes.R4ScaleErrors
    ),
    DeviceInfo(
        name: "Welland Bath Scale",
        sku: "0351/0344",
        image: AppAssets.wg0351,
        protocolType: "Welland Bath Scale",
        manufacturer: "Welland",
        app : .weightGurus,
        errorcodes: CommonErrorCodes.basicErrors
    ),
    DeviceInfo(
        name: "Smart Wrist Blood Pressure Monitor",
        sku: "0603",
        image: AppAssets.bh0603,
        protocolType: "A3",
        manufacturer: "Transtek",
        app : .balanceHealth,
        errorcodes: CommonErrorCodes.bhMonitorErrors
    ),
    DeviceInfo(
        name: "Smart Blood Pressure Monitor",
        sku: "0604",
        image: AppAssets.bh0604,
        protocolType: "A3",
        manufacturer: "Transtek",
        app : .balanceHealth,
        errorcodes: CommonErrorCodes.bhMonitorErrors
    ),
    DeviceInfo(
        name: "Smart Blood Pressure Monitor",
        sku: "0664",
        image: AppAssets.bh0604,
        protocolType: "A3",
        manufacturer: "Transtek",
        app : .balanceHealth,
        errorcodes: CommonErrorCodes.bhMonitorErrors
    ),
    DeviceInfo(
        name: "Smart Pro-Series Blood Pressure Monitor",
        sku: "0634",
        image: AppAssets.bh0634,
        protocolType: "A3",
        manufacturer: "Transtek",
        app : .balanceHealth,
        errorcodes: CommonErrorCodes.bhMonitorErrors
    ),
    DeviceInfo(
        name: "All-In-One Bluetooth Blood Pressure Monitor",
        sku: "0636",
        image: AppAssets.bh0636,
        protocolType: "A3",
        manufacturer: "Transtek",
        app : .balanceHealth,
        errorcodes: CommonErrorCodes.bhMonitorErrors
    ),
    DeviceInfo(
        name: "All-In-One Bluetooth Blood Pressure Monitor",
        sku: "0639",
        image: AppAssets.bh0636,
        protocolType: "A3",
        manufacturer: "Transtek",
        app : .balanceHealth,
        errorcodes: CommonErrorCodes.bhMonitorErrors
    ),
    DeviceInfo(
        name: "Smart Blood Pressure Monitor",
        sku: "0663",
        image: AppAssets.bh0663,
        protocolType: "A6",
        manufacturer: "Transtek",
        app : .balanceHealth,
        errorcodes: CommonErrorCodes.bhMonitorErrors
    ),
    DeviceInfo(
        name: "Smart Blood Pressure Monitor",
        sku: "0665",
        image: AppAssets.bh0663,
        protocolType: "A6",
        manufacturer: "Transtek",
        app : .balanceHealth,
        errorcodes: CommonErrorCodes.bhMonitorErrors
    ),
    DeviceInfo(
        name: "Smart Blood Pressure Monitor",
        sku: "0661",
        image: AppAssets.bh0604,
        protocolType: "A6",
        manufacturer: "Transtek",
        app : .balanceHealth,
        errorcodes: CommonErrorCodes.bhMonitorErrors
    ),
    DeviceInfo(
        name: "Pulse Oxi meter",
        sku: "0003",
        image: AppAssets.pulseOxiMeter,
        protocolType: "0003",
        manufacturer: "Transtek",
        app : .balanceHealth,
        errorcodes: CommonErrorCodes.pulseOximeterErrors
    ),
    DeviceInfo(
        name: "Blood Glucose Meter ",
        sku: "0005",
        image: AppAssets.bloodGlucoseMeter,
        protocolType: "0005",
        manufacturer: "Transtek",
        app : .balanceHealth,
        errorcodes: CommonErrorCodes.glucoseMeterErrors
    ),
    DeviceInfo(
        name: "Thermometer",
        sku: "0062",
        image: AppAssets.thermometer,
        protocolType: "Avita",
        manufacturer: "Avita",
        app : .rpm,
        errorcodes: CommonErrorCodes.thermometerErrors
    ),
    DeviceInfo(
        name: "Baby Scale",
        sku: "0222",
        image: AppAssets.bs0222,
        protocolType: "A6",
        manufacturer: "Lifesense",
        app : .smartBaby,
        errorcodes: CommonErrorCodes.basicErrors
    ),
    DeviceInfo(
        name: "Smart Baby Scale",
        sku: "0220",
        image: AppAssets.bs0220,
        protocolType: "A6",
        manufacturer: "Lifesense",
        app : .smartBaby,
        errorcodes: CommonErrorCodes.WgA6ScaleErrors
    ),
]
