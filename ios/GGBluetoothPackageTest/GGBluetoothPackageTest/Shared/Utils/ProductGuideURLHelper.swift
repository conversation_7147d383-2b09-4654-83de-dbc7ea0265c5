//
//  ProductGuideURLHelper.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 16/01/25.
//

import Foundation

struct ProductURLHelper {
    static func getProductGuideURL(forSKU sku: String) -> URL {
        let baseURL = "https://greatergoods.com/service/"
        if !sku.isEmpty {
            return URL(string: baseURL + sku) ?? URL(string: "https://greatergoods.com/not-found")!
        }
        return URL(string: "https://greatergoods.com/not-found")!
    }
}
