//
//  BloodPressureRanges.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 16/05/25.
//

import Foundation
import SwiftUI

func getColorFor(entry: BalanceHealthEntry?) -> Color {
    guard let entry = entry else { return .green }
    let values = (entry.systole, entry.diastole)

    switch values {
    case let (s, d) where s > 180 || d > 120:
        return .brown

    case let (s, d) where s >= 140 || d >= 90:
        return .darkOrange

    case let (s, d) where (130...139).contains(s) || (80...89).contains(d):
        return .lightOrange

    case let (s, d) where (120...129).contains(s) && d < 80:
        return .yellow

    case let (s, d) where s < 120 && d < 80:
        return .green

    default:
        return .green
    }
}



