//
//  AllowedSKU.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/03/25.
//

import Foundation
import SwiftUICore

let excludedSKUs: [String] = [
    "0351", "0344"
]

func getImage(for sku: String) -> Image? {
    let skuToImageMap: [String: Image] = [
        "0667": Image(AppAssets.bh0604),
        "0661": Image(AppAssets.bh0604),
        "0351": Image(AppAssets.wg0351),
        "0344": Image(AppAssets.wg0351)
    ]
    return skuToImageMap[sku]
}
