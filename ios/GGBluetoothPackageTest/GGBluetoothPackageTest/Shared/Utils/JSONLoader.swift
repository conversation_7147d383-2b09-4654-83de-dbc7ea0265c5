//
//  JSONLoader.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 05/12/24.
//

import Foundation

private let logger: AppLogger = AppLogger(category: String(describing: DeviceService.self))

func loadJSON<T: Decodable>(fromFile fileName: String, type: T.Type = T.self) -> T? {
    guard let url = Bundle.main.url(forResource: fileName, withExtension: "json") else {
        let error = NSError( domain: "", code: 404, userInfo: [NSLocalizedDescriptionKey: "Error: JSON file not found - \(fileName).json"])
        logger.error(error: error, function: #function, line: #line)
        return nil
    }
    
    do {
        let data = try Data(contentsOf: url)
        let decoder = JSONDecoder()
        let decodedData = try decoder.decode(T.self, from: data)
        return decodedData
    } catch {
        let error = NSError(domain: "", code: 500, userInfo: [NSLocalizedDescriptionKey: "Error: Failed to decode JSON data from file \(fileName).json - \(error)"])
        logger.error(error: error, function: #function, line: #line)
        return nil
    }
}


