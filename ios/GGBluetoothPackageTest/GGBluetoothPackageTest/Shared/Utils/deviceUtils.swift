//
//  deviceUtils.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 26/11/24.
//

import Foundation

func getSKU(_ deviceName: String) -> String{
    @Injector var deviceInfoManager : DeviceInfoManager
    
    switch deviceName{
    case "MY_SCALE":
        return "WellandKitchenScale"
    case "10376B", "0376B", "376B":
        return "0376"
    case "0202B", "1202B", "202B":
        return "0375"
    case "11251B", "1251B", "01251B":
        return "0380"
    case "1270B", "11270B", "01270B":
        return "0380"
    case "gG BS 0412":
        return "0412"
    case "LS212-B", "gG-RPM 0022":
        return "0383"
    case "gG PulseOx 0003":
        return "0003"
    case "gG-RPM 0040", "1490BT1":
        return "0604"
    case "gG BGM 0005":
        return "0005"
    case "0062":
        return "0062"
    case  "gG BS 0222":
        return "0222"
    case "gG BS 0220":
        return "0220"
    case "gG BPM 0667":
        return "0667"
    case "gG BPM 0661":
        return "0661"
    case "gG BS 0351":
        return "0351"
    case "gG BS 0344":
        return "0344"
    default:
        break
    }
    
    if let mappedSKU = deviceInfoManager.mapSKUFromBroadcastName(broadcastName: deviceName) {
        return mappedSKU
    }
    
    return "0375"
}
