//
//  AppCategoryMapper.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 05/12/24.
//
import Foundation
import GGBluetoothSwiftPackage

func getApp(forDevice broadcastId: String, deviceName: String) -> AppCategory {
    let deviceInfoManager = DeviceInfoManager()

    if deviceName.hasPrefix("gG BPM") || deviceName.contains("1490BT1") {
        return .balanceHealth
    } else if containsExcludedSKU(deviceName) || deviceName.contains("gG BS 0412") {
        return .weightGurus
    } else if deviceName.hasPrefix("gG BS") || deviceName.hasPrefix("BS") || deviceName.contains("BS1711-B") {
        return .smartBaby
    }

    let broadcastIdPattern = "^D0"
    if broadcastId.range(of: broadcastIdPattern, options: .regularExpression) != nil {
        return .sage
    }

    if broadcastId == "62FFECE7" {
        return .balanceHealth
    }

    let weightGurusIdentifiers = [
        "10376B", "0376B", "376B",
        "0202B", "1202B", "202B",
        "11251B", "1251B", "01251B",
        "1270B", "11270B", "01270B", "LS212-B"
    ]
    if weightGurusIdentifiers.contains(where: { deviceName.contains($0) }) {
        return .weightGurus
    }
    
    if deviceName.hasPrefix("gG-RPM") ||
       deviceName.hasPrefix("gG BGM") ||
       deviceName.contains("0062") ||
       deviceName.contains("LS212-B") ||
       deviceName.contains("gG PulseOx 0003") {
        return .rpm
    }

    if let sku = deviceInfoManager.mapSKUFromBroadcastName(broadcastName: deviceName) {
        switch sku {
        case "0375", "0376", "0380", "0382", "0378", "0383":
            return .weightGurus
        case "0603", "0604", "0634", "0636", "0663":
            return .balanceHealth
        default:
            return .sage
        }
    }
 
    return .weightGurus
}

func containsExcludedSKU(_ deviceName: String) -> Bool {
    for sku in excludedSKUs {
        if deviceName.contains("gG BS \(sku)") {
            return true
        }
    }
    return false
}

func appType(for category: AppCategory) -> GGAppType {
    switch category {
    case.weightGurus:
        return .WEIGHT_GURUS
    case .balanceHealth:
        return   .BALANCE_HEALTH
    case .smartBaby:
        return  .SMART_BABY
    case .sage:
        return   .SMART_KITCHEN
    case .rpm:
        return .RPM
    case .all:
        return .NONE
    }
}

func maximumUsers(for sku: String, in category: AppCategory) -> Int {
    let maxUsersMapping: [AppCategory: [String: Int]] = [
        .balanceHealth: [
            "0603": 2,
            "0604": 2,
            "0634": 2,
            "0636": 2,
            "0663": 2
        ],
        .weightGurus: [
            "0375": 8,
            "0376": 8,
            "0380": 8,
            "0382": 8,
            "0378": 6,
            "0383": 10
        ]
    ]
    
    guard let categoryMapping = maxUsersMapping[category],
          let maxUsers = categoryMapping[sku] else {
        switch category {
        case .balanceHealth:
            return 2
        case .weightGurus:
            return maxUsersMapping[.weightGurus]?.values.first(where: { $0 == 8 }) ?? 8
        default:
            return 8
        }
    }
    
    return maxUsers
}

func getAppCategoryUsingSKU(forSKU sku: String) -> AppCategory {
    let weightGurusSKUs = ["0375", "0376", "0380", "0382", "0378", "0379", "0383", "0412"]
    let balanceHealthSKUs = ["0603", "0604", "0634", "0636", "0663", "0665", "0667", "0661"]
    let smartBabySKUs = ["0220", "0222"]

    if weightGurusSKUs.contains(sku) {
        return .weightGurus
    } else if balanceHealthSKUs.contains(sku) {
        return .balanceHealth
    } else if smartBabySKUs.contains(sku) {
        return .smartBaby
    }

    return .weightGurus
}
