//
//  DeviceCategory.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 02/12/24.
//

import Foundation
import SwiftUICore

enum AppCategory: String, CaseIterable , Codable{
    case all
    case balanceHealth
    case rpm
    case sage
    case smartBaby
    case weightGurus
}

extension AppCategory {
    func image() -> Image {
        switch self {
        case .weightGurus:
            return Image("weightGurusAppLogo")
        case .balanceHealth:
            return Image("BalenceHeathAppLogo")
        case .smartBaby:
            return Image("smartBabyAppLogo")
        case .sage:
            return Image("sageAppLogo")
        case .rpm:
            return Image("rpmAppLogo")
        case .all:
            return Image("weightGurusAppLogo")
        }
    }
}

extension AppCategory {
    func apiUrl() -> String {
        switch self {
        case .weightGurus:
            return "https://api.weightgurus.com/v3/"
        case .smartBaby:
            return "https://api.smartbaby.greatergoods.com/v1/"
        case .balanceHealth:
            return "https://api.balance.greatergoods.com/bpm/v2/"
        case .sage, .rpm, .all:
            return "https://api.defaulturl.com/"
        }
    }
}

extension AppCategory {
    var loginURL: URL? {
        URL(string: apiUrl() + "account/login")
    }
    
    var scaleInfoURL: URL? {
        URL(string: apiUrl() + "account/scale?r=4")
    }
    
    var pairedScaleURL: URL? {
        URL(string: apiUrl() + "paired-scale/")
    }
    
    var postPreferenceURL: URL? {
        URL(string: apiUrl() + "scale-r4/preference")
    }
}
