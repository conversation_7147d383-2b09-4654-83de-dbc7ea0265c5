//
//  TileSizeType.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/05/25.
//

import SwiftUI

enum TileSizeType: Int, CaseIterable, Codable {
    case small = 1
    case medium = 2
    case large = 3
    
    var size: CGSize {
        let screenWidth = UIScreen.main.bounds.width - 40
        switch self {
        case .small: return CGSize(width: (screenWidth - 40) / 3, height: 120)
        case .medium: return CGSize(width: (screenWidth - 30) / 2, height: 120)
        case .large: return CGSize(width: screenWidth - 10 , height: 120)
        }
    }
    
    var columns: Double {
        switch self {
        case .small: return 1.0
        case .medium: return 2.0
        case .large: return 3.0
        }
    }
    
    var gridSpan: Int {
        switch self {
        case .small: return 1
        case .medium: return 2
        case .large: return 3
        }
    }
}
