//
//  MeasurementUnit.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 19/12/24.
//

import Foundation

enum MeasurementUnit: String, CaseIterable, Identifiable {
    
    case flOz = "fl’oz"
    case flOzm = "fl’oz-m"
    case g = "g"
    case kg = "kg"
    case lb = "lb"
    case lbOz = "lb:oz"
    case ml = "ml"
    case mlm = "ml-m"
    case oz = "oz"

    var id: String { self.rawValue }
    
    var displayValue: String {
        return self.rawValue
    }
    
    var unitName: String {
        switch self {
        case .flOz:
            return "fl_oz"
        case .flOzm:
            return "fl_oz_milk"
        case .g:
            return "gm"
        case .kg:
            return "kg"
        case .lb:
            return "lb"
        case .lbOz:
            return "lb_oz"
        case .ml:
            return "ml"
        case .mlm:
            return "ml_milk"
        case .oz:
            return "oz"
        }
    }
    
    static func from(unitName: String) -> MeasurementUnit? {
        return MeasurementUnit.allCases.first { $0.unitName == unitName }
    }
}
