//
//  SourceAndOpertaionType.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 23/01/25.
//

import SwiftUI

enum OperationType: String, Codable {
    case create = "create"
    case delete = "delete"
}

enum Source: String, Codable {
    case manual = "manual"
    case lcbt = "lcbt"
    case lcbtScale = "lcbt scale"
    case btWifiR4 = "btWifiR4"
    case bluetooth = "bluetooth scale"
    case appsync = "appsync scale"
    case wifi = "wifi"
    case wifiscale = "wifi scale"
    case pulseOxi = "pulse oxi"
    case bpm = "bpm"
    case bgm = "bgm"
    case thermometer = "thermometer"
}
