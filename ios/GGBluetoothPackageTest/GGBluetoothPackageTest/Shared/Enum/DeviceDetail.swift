//
//  DeviceDetail.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 04/02/25.
//

import SwiftUI

enum DeviceDetail: Hashable, Identifiable {
    case weightGurus(detail: WeightGurusAccountDetailsResponse)
    case balanceHealth(detail: Monitor)
    
    var id: String {
        switch self {
        case .weightGurus(let detail):
            return detail.id
        case .balanceHealth(let detail):
            return detail.id
        }
    }
}
