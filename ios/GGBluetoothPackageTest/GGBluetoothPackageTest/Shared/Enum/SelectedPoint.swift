//
//  SelectedPoint.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 12/05/25.
//

import Foundation

enum SelectedPoint {
    case weight(GraphWeightEntry)
    case balanceHealth(BalanceHealthEntry)
    case BGM(BGMData)

    var id: UUID {
        switch self {
        case .weight(let entry): return entry.id
        case .balanceHealth(let entry): return entry.id
        case .BGM(let data): return data.id
        }
    }

    var date: Date {
        switch self {
        case .weight(let entry): return entry.date
        case .balanceHealth(let entry): return entry.date
        case .BGM(let data):
            return data.date
        }
    }

    var weight: Double {
        switch self {
        case .weight(let entry): return entry.weight
        case .balanceHealth(let entry): return Double(entry.systole)
        case .BGM(let data): return data.glucose
        }
    }
}
