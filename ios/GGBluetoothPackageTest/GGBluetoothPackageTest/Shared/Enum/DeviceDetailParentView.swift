//
//  ParentView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 17/01/25.
//

import SwiftUI

enum DeviceDetailParentView: String {
    case scan
    case pairedDevice
    case Apps
}

enum HistoryPageParentView: String {
    case deviceDetail
    case historytab
}

enum UserDashBoardPairedDeviceParentView: String {
    case pairedDevice
    case apps
}

enum UserDashBoardHistoryParentView: String {
    case history
    case apps
}

enum LoginPageParentView {
    case apps
    case scan
}

enum EditUserNameParentView: String {
    case DuplicateUserModel
    case UsersTab
}

enum UserTabParentView {
    case deviceDetailView
    case deviceItemView
}

enum GraphParentViewType {
    case deviceDetail
    case history
}
