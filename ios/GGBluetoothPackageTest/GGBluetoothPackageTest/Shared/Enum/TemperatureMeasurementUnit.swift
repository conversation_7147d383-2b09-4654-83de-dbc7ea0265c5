//
//  TemperatureMeasurementUnit.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 25/02/25.
//

import Foundation

enum TemperatureMeasurementUnit: String, CaseIterable, Identifiable {
    case celsius = "°C"
    case farenheit  = "°F"
    var id: String { self.rawValue }
    var displayValue: String {
        return self.rawValue
    }
    var unitName: String{
        switch(self){
        case .celsius:
            return "°C"
        case .farenheit:
            return "°F"
        }
    }
    static func from(unitName: String) -> TemperatureMeasurementUnit? {
        return TemperatureMeasurementUnit.allCases.first { $0.unitName == unitName }
    }
}




