//
//  ResponseTypes.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 23/04/25.
//

import Foundation

 enum UserCreationResponseType: String, Codable {
    case creationCompleted = "CREATION_COMPLETED"
    case creationFailed = "CREATION_FAILED"
    case inputDataError = "INPUT_DATA_ERROR"
    case memoryFull = "MEMORY_FULL"
    case duplicateUserError = "DUPLICATE_USER_ERROR"
    case userSelectionInProgress = "USER_SELECTION_IN_PROGRESS"
    case differentUser = "DIFFERENT_USER"
    case notInPairingMode = "NOT_IN_PAIRING_MODE"
}

public enum UserDeletionResponseType: String, Codable {
    case success = "SUCCESS"
    case fail = "FAIL"
    case exceptionEncountered = "EXCEPTION_ENCOUNTERED"
}
