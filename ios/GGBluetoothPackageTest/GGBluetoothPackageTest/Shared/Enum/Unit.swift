//
//  Unit.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 13/03/25.
//

import Foundation

enum Unit: String, Codable {
    case celcius = "°C"
    case fahrenheit = "°F"
    case flOz = "fl_oz"
    case flOzm = "fl_oz_milk"
    case g = "gm"
    case kg = "kg"
    case lb = "lb"
    case lbOz = "lb_oz"
    case lbs = "lbs"
    case ml = "ml"
    case mlm = "ml-m"
    case oz = "oz"
    case unknown = "Unknown"
}
