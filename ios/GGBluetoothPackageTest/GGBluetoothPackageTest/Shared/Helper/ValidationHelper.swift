//
//  ValidationHelper.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 29/01/25.
//

import Foundation

struct ValidationHelper {
    static func validateName(_ name: String) -> String? {
        let nameRegex = "^[A-Za-z ]+$"
        let isValidName = NSPredicate(format: "SELF MATCHES %@", nameRegex).evaluate(with: name)
        
        if name.isEmpty {
            return "Name must not be empty."
        } else if !isValidName {
            return "Name must contain only alphabets."
        }
        return nil
    }
    
    static func validateEmail(_ email: String) -> String? {
        let emailRegex = "^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES[c] %@", emailRegex)
        
        if email.isEmpty {
            return "Email must not be empty."
        } else if !emailPredicate.evaluate(with: email) {
            return "Enter a valid email address."
        }
        return nil
    }
    
    static func validateHeight(_ height: Double) -> String? {
        if height == 0 {
            return "Height must not be empty."
        } else if height <= 0 || height > 299 {
            return "Height must be between 1 and 299 cm."
        }
        return nil
    }
    
    static func validateWeight(_ weight: Double) -> String? {
        if weight <= 0 {
            return "Weight must be greater than 0."
        } else if weight > 450 {
            return "Weight must be less than or equal to 450 kg."
        }
        return nil
    }
    
    static func validateGoalWeight(_ goalWeight: Double) -> String? {
        if goalWeight <= 0 {
            return "Goal Weight must be greater than 0."
        } else if goalWeight > 450 {
            return "Goal Weight must be less than or equal to 450 kg."
        }
        return nil
    }
    
    static func validatePassword(_ password: String) -> String? {
        if password.isEmpty {
            return "Password cannot be empty."
        } else if password.count < 6 {
            return "Password must be at least 6 characters long."
        }
        return nil
    }
}
