//
//  LoginHelper.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 06/02/25.
//

import Foundation

class LoginHelper {
    static let shared = LoginHelper()
    private let deviceService = DeviceService()
    private let loginService = LoginService()
    private let logger: AppLogger = AppLogger(category: String(describing: DeviceService.self))
    private init() {}
    
    func fetchLoginforAppCategory(for category: AppCategory, completion: @escaping (Bool, CombinedLoginData?) -> Void) {
        if let loginDetails = deviceService.fetchUserLogin(for: category) {
            let email = loginDetails.email ?? "N/A"
            let password = loginDetails.password ?? "N/A"
            
            loginService.login(for: category, email: email, password: password) { result in
                DispatchQueue.main.async {
                    switch result {
                    case .success(let combinedData):
                        completion(true, combinedData)
                        
                    case .failure(let error):
                        let nsError = NSError(domain: String(describing: DeviceService.self), code: 4, userInfo: [NSLocalizedDescriptionKey: "Login failed for category: \(category) with error: \(error)"])
                        self.logger.error(error: nsError, function: #function, line: #line)
                        completion(false, nil)
                    }
                }
            }
        } else {
            let nsError = NSError(domain: String(describing: DeviceService.self), code: 4, userInfo: [NSLocalizedDescriptionKey: "No login details found for category: \(category)"])
            logger.error(error: nsError, function: #function, line: #line)
            completion(false, nil)
        }
    }
    
    func convertToDate(_ dateString: String, for appCategory: AppCategory, format: String) -> String {
        let dateFormatter = DateFormatter()
        
        switch appCategory {
        case .weightGurus:
            dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
            if let date = dateFormatter.date(from: dateString) {
                dateFormatter.dateFormat = format
                return dateFormatter.string(from: date)
            }
        case .balanceHealth:
            if let timestamp = Double(dateString) {
                let date = Date(timeIntervalSince1970: timestamp / 1000)
                dateFormatter.dateFormat = format
                return dateFormatter.string(from: date)
            }
        default:
            dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
            if let date = dateFormatter.date(from: dateString) {
                dateFormatter.dateFormat = format
                return dateFormatter.string(from: date)
            }
        }
        
        let currentDate = Date()
        dateFormatter.dateFormat = format
        return dateFormatter.string(from: currentDate)
    }
    
    func filterMeasurements(_ measurements: [[String: Any]], for appCategory: AppCategory) -> [[String: Any]] {
        var deletedMeasurements: [String] = []

        for measurement in measurements {
            if appCategory == .balanceHealth {
                if let operation = measurement["operation"] as? String, operation == "delete" {
                    if let entryTimestamp = measurement["entryTimestamp"] as? String {
                        deletedMeasurements.append(entryTimestamp)
                    }
                } else if let operation = measurement["operation"] as? String, operation == "note" {
                    if let entryTimestamp = measurement["entryTimestamp"] as? String {
                        deletedMeasurements.append(entryTimestamp)
                    }
                }
            } else {
                if let operationType = measurement["operationType"] as? String, operationType == "delete" {
                    if let entryTimestamp = measurement["entryTimestamp"] as? String {
                        deletedMeasurements.append(entryTimestamp)
                    }
                }
            }
        }
        
        let filteredMeasurements = measurements.filter { measurement in
            if let entryTimestamp = measurement["entryTimestamp"] as? String {
                return !deletedMeasurements.contains(entryTimestamp)
            }
            
            if appCategory == .balanceHealth {
                if measurement["diastolic"] == nil && measurement["systolic"] == nil {
                    return false
                }
            }
            return true
        }
        return filteredMeasurements
    }
}
