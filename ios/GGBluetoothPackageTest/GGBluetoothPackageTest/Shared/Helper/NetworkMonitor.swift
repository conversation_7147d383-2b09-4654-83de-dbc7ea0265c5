//
//  NetworkMonitor.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 17/03/25.
//

import Foundation
import Network
import Combine

final class NetworkMonitor: ObservableObject {
    private let networkMonitor = NWPathMonitor()
    private let workerQueue = DispatchQueue(label: "Monitor")
    @Published var isConnected = false
    
    private var timer: Timer?
    
    init() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                guard let self = self else { return }
                let newStatus = path.status == .satisfied
                if newStatus != self.isConnected {
                    self.isConnected = newStatus
                    if !self.isConnected {
                        self.showToast()
                    }
                }
            }
        }
        
        DispatchQueue.main.async {
            self.networkMonitor.start(queue: self.workerQueue)
        }
    }

    private func showToast() {
        let message = "No Internet Connection"
        ToastService.shared.presentToast(with: message, style: .error)
    }
    
    deinit {
        networkMonitor.cancel()
    }
}
