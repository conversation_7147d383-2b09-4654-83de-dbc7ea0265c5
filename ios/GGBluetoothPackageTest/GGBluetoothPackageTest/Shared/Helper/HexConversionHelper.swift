//
//  HexConversionHelper.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 13/03/25.
//

import Foundation

struct HexConversionHelper {
    
    static func convertIntToHex(value: Int, protocolType: WeighingScaleProtocolType) -> String {
        var convertedValue: String
        
        switch protocolType {
        case .R4:
            convertedValue = String(format: "%012X", value)
        case .other:
            convertedValue = String(format: "%08X", value)
        }
        
        var reversedHex = reverseHexByteOrder(convertedValue)
        if protocolType == .other {
            reversedHex = String(reversedHex.prefix(8))
        }
        
        return reversedHex
    }
    
    static func convertHexToInt(value: String) -> Int? {
        guard value.count % 2 == 0 else {
            return nil
        }
        let reversedHex = reverseHexByteOrder(value)
        let intValue = Int(reversedHex, radix: 16)
        return intValue
    }
    
    private static func reverseHexByteOrder(_ hex: String) -> String {
        return stride(from: hex.count, to: 0, by: -2).map {
            let start = hex.index(hex.startIndex, offsetBy: $0 - 2)
            let end = hex.index(start, offsetBy: 2)
            return String(hex[start..<end])
        }.joined().uppercased()
    }
}
