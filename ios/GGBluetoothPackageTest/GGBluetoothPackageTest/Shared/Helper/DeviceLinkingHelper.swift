//
//  DeviceLinkingHelper.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 07/04/25.
//

import Foundation
import GGBluetoothSwiftPackage

class DeviceLinkingHelper {
    static let shared = DeviceLinkingHelper()
    @Injector var deviceService: DeviceService
    @Injector var loginService: LoginService
    
    func fetchUserLoginStatus(for category: AppCategory, device: GGBTDevice?, completion: @escaping (Bool) -> Void) {
        if let loginDetails = deviceService.fetchUserLogin(for: category) {
            if loginDetails.isLoggedIn {
                var deviceDetails: [String: Any] = [:]
                
                switch category {
                case .balanceHealth:
                    deviceDetails = [
                        "name": device?.name ?? "My Device",
                        "sku": getSKU(device?.name ?? "Device SKU"),
                        "type": "bluetooth",
                        "broadcastId": device?.broadcastId ?? "",
                        "peripheralIdentifier": device?.id.uuidString ?? "",
                        "mac": device?.macAddress ?? "",
                        "password": device?.password ?? "",
                        "userNumber": device?.userNumber ?? 0,
                        "nickname": device?.name ?? "My Device"
                    ]
                    
                case .weightGurus:
                    guard let broadcastIdString = device?.broadcastId,
                          let broadcastIdInt = HexConversionHelper.convertHexToInt(value: broadcastIdString) else {
                        completion(false)
                        return
                    }
                    
                    var passwordInt: Int = 0
                    if let passwordString = device?.password, !passwordString.isEmpty {
                        guard let convertedPasswordInt = HexConversionHelper.convertHexToInt(value: passwordString) else {
                            completion(false)
                            return
                        }
                        passwordInt = convertedPasswordInt
                    }
                    let fetchedDevice = deviceService.fetchDevice(by: device?.broadcastId ?? "")
                    let deviceType = device?.protocolType == ProtocolType.R4.rawValue ? DeviceType.btWifiR4.rawValue : DeviceType.bluetooth.rawValue

                    deviceDetails = [
                        "nickname": device?.name ?? "My Device",
                        "type": deviceType,
                        "userNumber": device?.userNumber ?? 0,
                        "scaleToken": fetchedDevice?.token ?? "",
                        "mac": device?.macAddress ?? "",
                        "broadcastId": broadcastIdInt,
                        "password": passwordInt,
                        "sku": getSKU(device?.name ?? "Device SKU"),
                        "name": device?.name ?? "My Device",
                        "peripheralIdentifier": device?.id.uuidString ?? ""
                    ]
                    
                default:
                    completion(false)
                    return
                }
                
                loginService.addDeviceToApp(for: category, email: loginDetails.email ?? "", password: loginDetails.password ?? "", deviceDetails: deviceDetails) { [self] success in
                    if success {
                        deviceService.linkDeviceToApp(by: device?.broadcastId ?? "", isLinked: true)
                        completion(true)
                    } else {
                        completion(false)
                    }
                }
            } else {
                completion(false)
            }
        } else {
            completion(false)
        }
    }
}
