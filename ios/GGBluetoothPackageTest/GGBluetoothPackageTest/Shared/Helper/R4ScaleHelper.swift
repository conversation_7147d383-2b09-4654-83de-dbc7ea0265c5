//
//  R4ScaleHelper.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 24/04/25.
//

import Foundation
@preconcurrency import GGBluetoothSwiftPackage
import SwiftUICore

class R4ScaleHelper {
    static let shared = R4ScaleHelper()
    @Injector var bluetoothService: BluetoothService
    @Injector var r4scaleService: R4ScaleService
    @Injector var userProfileService: UserProfileUpdationService
    @Injector var deviceService: DeviceService
    private let logger: AppLogger = AppLogger(category: String(describing: R4ScaleHelper.self))
    let toastLang = ToastMessageStrings.self
    let commonLang = CommonStrings.self
    
    func addR4ScaleUser(
        category: AppCategory,
        device: GGBTDevice,
        onDuplicateUser: @escaping (String) -> Void,
        onUserAdded: @escaping () -> Void,
        router: Router<DashboardRoutes>,
        showAlert: @escaping (String, String, String, String, @escaping () -> Void) -> Void,
        navigateToLogin: (() -> Void)?
    ) async -> (GGBTUser?, String) {
        let tokenResult = await r4scaleService.R4ScaleAccountCreation(for: category, router: router)
        
        if let token = tokenResult {
            device.token = token
            do{
                let updateResult = try await bluetoothService.updateAccount(device: device) ?? "UNKNOWN_ERROR"
                
                if let responseType = UserCreationResponseType(rawValue: updateResult) {
                    switch responseType {
                    case .duplicateUserError:
                        let existingUsers = await bluetoothService.fetchUsers(for: device)
                        let userName = userProfileService.fetchUserProfile()?.name
                        if let userName = userName,
                           let duplicateUser = existingUsers.first(where: { $0.name == userName }) {
                            onDuplicateUser(duplicateUser.name)
                            ToastService.shared.presentToast(with: toastLang.duplicateUserFound)
                            return (duplicateUser, responseType.rawValue)
                        }
                    case .creationCompleted:
                        handleUserAdded(device: device, onUserAdded: onUserAdded)
                        return (nil, responseType.rawValue)
                    default:
                        return (nil, responseType.rawValue)
                    }
                } else {
                    return (nil, updateResult)
                }
                
            }catch{
                let nsError = NSError(domain: String(describing: DeviceService.self), code: 1, userInfo: [NSLocalizedDescriptionKey: "Error:\(error.localizedDescription)"])
                self.logger.error(error: nsError, function: #function, line: #line)
            }
            
        } else {
            showAlert(
                toastLang.failedToAddUser,
                toastLang.noUserLoggedIn,
                commonLang.ok.uppercased(),
                commonLang.cancel,
                {
                    navigateToLogin?()
                }
            )
        }
        
        return (nil, UserCreationResponseType.creationFailed.rawValue)
    }
    
    private func handleUserAdded(device: GGBTDevice, onUserAdded: @escaping () -> Void) {
        deviceService.updateDeviceTokenAndUser(broadcastId: device.broadcastId, token: device.token, isUserAdded: true)
        DispatchQueue.main.async {
            onUserAdded()
        }
        ToastService.shared.presentToast(with: toastLang.userAddedSuccessfully)
    }
    
    func restoreUser(for device: GGBTDevice, duplicateUser: String) async throws {
        do {
            try await bluetoothService.restoreAccount(device: device, accountName: duplicateUser)
            handleUserAdded(device: device, onUserAdded: {
                ToastService.shared.presentToast(with: self.toastLang.userRestoredSuccessfully)
            })
        } catch {
            throw error
        }
    }
    
    func saveUserEdit(device: GGBTDevice, userName: String, router: Router<DashboardRoutes>) async throws -> String? {
        let profileDetails = userProfileService.fetchUserProfile()
        let metrics = DisplayMetricsHelper.extractMetricKeys().map { key in
                GGBTMetricConfig(id: key, label: key, isEnabled: true) 
            }
        let profile = GGBTUserProfile(
            name: userName,
            age: profileDetails?.birthday.map {
                Calendar.current.dateComponents([.year], from: $0, to: Date()).year
            } ?? 20,
            sex: profileDetails?.gender ?? Gender.male.rawValue,
            unit: profileDetails?.unitType ?? Unit.kg.rawValue,
            height: profileDetails?.height != nil ? Double(profileDetails?.height ?? Int16(170.0)) : 170.0,
            weight: {
                let weight = profileDetails?.weight ?? 70.0
                let isUsingKg = profileDetails?.unitType == Unit.kg.rawValue
                return isUsingKg ? convertKgToLb(weight) : weight
            }(),
            goalWeight: {
                let goalWeight = profileDetails?.goalWeight ?? 80.0
                let isUsingKg = profileDetails?.unitType == Unit.kg.rawValue
                return isUsingKg ? convertKgToLb(goalWeight) : goalWeight
            }(),
            isAthlete: profileDetails?.bodyType == BodyType.athelete.rawValue,
            goalType: profileDetails?.goalType ?? GoalType.lose.rawValue,
            metrics: metrics
        )
        
        try await bluetoothService.updateProfile(profile: profile)
        
        var updatedDevice = device
        
        var preference = device.preference ?? GGDevicePreference()
        
        preference.displayName = userName
        preference.displayMetrics = metrics.map { $0.id }
        preference.shouldMeasureImpedance = true
        preference.shouldMeasurePulse = true
        preference.timeFormat = TimeFormat.twelveHour.rawValue
        updatedDevice.preference = preference
        
        
        let scaleToken = await r4scaleService.R4ScaleAccountCreation(for: .weightGurus, router: router) ?? ""
        updatedDevice.token = scaleToken
        
        let result = try await bluetoothService.updateAccount(device: updatedDevice)
        if result == UserCreationResponseType.creationCompleted.rawValue {
            bluetoothService.syncDevices([device])
            handleUserAdded(device: device) {}
        }
        
        return result
    }
    
    private func convertKgToLb(_ weight: Double) -> Double {
        return (weight * 2.2046 * 10).rounded() / 10
    }
}
