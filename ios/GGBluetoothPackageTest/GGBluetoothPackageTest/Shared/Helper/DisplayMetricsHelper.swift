//
//  DisplayMetricsHelper.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 25/04/25.
//

import Foundation

struct DisplayMetricsHelper {
    
    static let labelToKeyMap: [String: String] = [
        "BMI": "bmi",
        "Body Fat": "bodyFatPercent",
        "Muscle Mass": "musclePercent",
        "Body Water": "bodyWaterPercent",
        "Bone Mass": "bonePercent",
        "Heart Rate": "heartRate",
        "Visceral Fat": "visceralFatLevel",
        "Subcutaneous Fat": "subcutaneousFatPercent",
        "Protein": "proteinPercent",
        "Skeletal Muscles": "skeletalMusclePercent",
        "BMR": "bmr",
        "Metabolic Age": "metabolicAge",
        "Goal Progress": "goalProgress",
        "Daily Average": "dailyAverage",
        "Weekly Average": "weeklyAverage",
        "Monthly Average": "monthlyAverage"
    ]
    
    static func extractDisplayMetrics(from metrics: [MetricItem], isEnabled: [String: Bool]) -> [String] {
        metrics
            .filter { isEnabled[$0.id] ?? false }
            .compactMap { labelToKeyMap[$0.label] }
    }
    
    static func extractMetricKeys() -> [String] {
            Array(labelToKeyMap.values)
        }
}
