//
//  GrowthChartDataHelper.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 14/05/25.
//

import Foundation

class GrowthChartDataHelper {
    private static let dataFiles: [String: WeightFileType] = [
        JsonFileNameStrings.boyWeightDecigrams: .weightData,
        JsonFileNameStrings.girlWeightDecigrams: .weightData,
        JsonFileNameStrings.boyWeightDecigramsLine: .percentileData,
        JsonFileNameStrings.girlWeightDecigramsLine: .percentileData
    ]
    
    private let logger: AppLogger = AppLogger(category: String(describing: GrowthChartDataHelper.self))
    
    func loadWeightData() {
        for (fileName, fileType) in GrowthChartDataHelper.dataFiles {
            switch fileType {
            case .percentileData:
                loadAndLog(fileName: fileName, dataType: [BabyWeightPercentileData].self)
            case .weightData:
                loadAndLog(fileName: fileName, dataType: [BabyWeightData].self)
            }
        }
    }

    func loadPercentileLineData(for gender: Gender) -> [BabyWeightPercentileData]? {
        let fileName: String
        
        switch gender {
        case .male:
            fileName = JsonFileNameStrings.boyWeightDecigramsLine
        case .female:
            fileName = JsonFileNameStrings.girlWeightDecigramsLine
        }
        
        guard let lineData: [BabyWeightPercentileData] = loadJSON(fromFile: fileName) else {
            logError(forFile: fileName)
            return nil
        }
        
        logSuccess(forFile: fileName)
        return lineData
    }

    private func loadAndLog<T: Decodable>(fileName: String, dataType: T.Type) {
        if let _: T = loadJSON(fromFile: fileName) {
            logSuccess(forFile: fileName)
        } else {
            logError(forFile: fileName)
        }
    }

    private func logError(forFile fileName: String) {
        let errorMessage = "Failed to load or decode \(fileName).json"
        let error = NSError(domain: "GrowthChartDataHelper", code: 0, userInfo: [NSLocalizedDescriptionKey: errorMessage])
        logger.error(error: error, function: #function, line: #line)
    }
    
    private func logSuccess(forFile fileName: String) {
        logger.info(info: "\(fileName) loaded successfully", function: #function, line: #line)
    }
}
