//
//  formatDateForOperations.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 14/03/25.
//

import Foundation

func formatDateForOperations(_ date: Date?, format: ApiOperationTimestampFormat) -> String {
    guard let date = date else { return "" }
    
    switch format {
    case .iso8601:
        let dateFormatter = ISO8601DateFormatter()
        return dateFormatter.string(from: date)
    case .posix:
        return String(format: "%.0f", date.timeIntervalSince1970 * 1000)
    }
}
