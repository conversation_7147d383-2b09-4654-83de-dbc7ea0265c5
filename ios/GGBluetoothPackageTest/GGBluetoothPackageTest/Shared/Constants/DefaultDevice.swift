//
//  DefaultDevice.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 22/04/25.
//

import Foundation
import GGBluetoothSwiftPackage

struct DefaultDevice {
    static let ggbtDevice = GGBTDevice(
        name: "",
        broadcastId: "",
        password: "",
        token: "",
        userNumber: 0,
        preference: nil,
        syncAllData: false,
        batteryLevel: 0,
        protocolType: "",
        macAddress: ""
    )
}
