//
//  AppStrings.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 25/11/24.
//

import Foundation

struct CommonStrings {
    static let scan = "Scan"
    static let pairedDevice = "Paired Device"
    static let history = "History"
    static let settings = "Settings"
    static let scanDevices = "Scan Devices"
    static let apps = "Apps"
    static let back = "Back"
    static let devices = "Devices"
    static let refresh = "Refresh"
    static let unlabeled = "Unlabeled"
    static let pairedDevicePlaceholder = "No paired devices found."
    static let scale = "Scale"
    static let deleteEntry = "Delete Entry"
    static let deleteEntryPlaceholder = "Are you sure you want to delete this entry?"
    static let delete = "Delete"
    static let deleteDevice = "Delete Device"
    static let connected = "Connected"
    static let disconnected = "Disconnected"
    static let ok = "OK"
    static let confirm = "Confirm"
    static let cancel = "Cancel"
    static let bluetooth = "Bluetooth"
    static let unknown = "Unknown"
    static let `protocol` = "Protocol"
    static let notAvailable = "Not available"
    static let wifiMacAddress = "Wi-fi MAC Address"
    static let wifi = "Wi-Fi"
    static let email = "email"
    static let password = "password"
    static let accessToken = "accessToken"
    static let token = "token"
    static let unit = "Unit"
    static let enabled = "Enabled"
    static let disabled = "Disabled"
    static let save = "Save"
    static let trash = "trash"
    static let addingUser = "Adding User"
    static let graphs = "Graphs"
    static let noEntriesAvailable = "No entries available."
    static let allDevices = "All Devices"
    static let all = "All"
    static let edit = "Edit"
    static let info = "Info"
    static let button = "Button"
    
    static func deleteDeviceConfirmation(deviceName: String?) -> String {
        return "Are you sure you want to delete \(deviceName ?? "this device")?"
    }
}

struct ScanViewStrings {
    static let noDevicesFound = "No devices found."
    static let noDevicesavailable = "No devices available."
    static let online = "Online"
    static let connecting = "Connecting..."
    static let connect = "Connect"
}

struct SettingsViewStrings {
    static let settings = "Settings"
    static let profile = "Profile"
    static let permissions = "Permissions"
    static let appSettings = "App Settings"
    static let noBroadcastIdPlaceholder = "No broadcast."
    
}

struct DeviceDetailStrings {
    static let basicDetail = "Basic Detail"
    static let user = "User"
    static let live = "Live"
    static let deviceSettings = "Device Settings"
    static let deviceLogs = "Device Logs"
    static let connection = "Connection"
    static let support = "Support"
    static let scaleName = "Scale Name"
    static let userNumber = "User Number"
    static let bluetooth = "Bluetooth"
    static let scaleType = "Scale Type"
    static let sku = "SKU"
    static let datePaired = "Date Paired"
    static let productGuide = "Product Guide"
    static let disconnect = "Disconnect"
    static let delete = "Delete"
    static let broadcastId = "Broadcast Id"
    static let connectedToApp = "Connected to App"
    static let linkToApp = "Link to App"
    static let confirm = "Confirm"
    static let confirmUnlink = "Confirm Unlink"
    static let connectToApp = "Connect to App"
    static let disconnectConfirmation = "Do you want to disconnect the device?"
    static let deleteConfirmation = "Do you want to delete the device?"
    static let connectToAppConfirmation = "Do you want to connect to the app?"
    static let unlinkConfirmation = "Do you want to unlink the device from the app?"
    static let setupIncomplete = "Setup Incomplete"
    static let setupWifi = "setup wi-fi"
    static let userDeleteAlertTitle = "Are you sure you want to delete?"
    static let loggedIn = "Logged in"
    static let logIn = "Log in"
    static let loginToAccount = "Login to your account"
    // New entries for R4ScaleUserView
    static let otherUsers = "Other Users"
    static let maxUserLimit = "Max: 10"
    static let lastActiveOn = "Last active on"
    static let deleteUser = "Delete User"
    static let userLimitExceeded = "User Limit Exceeded!"
    static let deleteInactiveUserPrompt = "Delete an inactive user to add yourself to the scale."
    
    static func deleteUserConfirmation(_ userName: String) -> String {
        return "Deleting \(userName) will remove them as a user of the scale and they'll need to reconnect. This action cannot be undone."
    }
}

struct ScaleMeasurementStrings {
    static let Tare = "Tare"
}

struct DevicesViewStrings {
    static let name = "Name"
    static let sku = "sku"
    static let errorCodes = "Error Codes"
    static let manufacturer = "Manufacturer"
    static let app = "App"
    static let deviceInformation = "Device Information"
    static let support = "Support"
    static let protocolType = "Protocol Type"
    static let productGuide = "Product Guide"
    static let modelNumber = "Model Number"
}

struct ThermometerLiveViewStrings{
    static let setMuteMode = "Mute Mode"
    static let startMeasurement = "Start Measurement"
    static let setUnit = "Set Unit"
}

struct ToastMessageStrings{
    static let scanningStarted = "Scanning Started"
    static let scanningStopped = "Scanning Stopped"
    static let deviceConnected = "Device Connected"
    static let deviceDisconnected = "Device Disconnected"
    static let deviceDeletedSuccessfully = "Device deleted successfully"
    static let entryAddedSuccessfully = "Entry added successfully"
    static let entryDeletedSuccessfully = "Entry deleted successfully"
    static let deviceLinkedSuccessfully = "Device linked successfully"
    static let deviceUnlinkedSuccessfully = "Device unlinked successfully"
    static let copiedToClipboard = "Copied to clipboard"
    static let preferencesSavedWithInfo = "Preferences saved successfully. These will be applied when the scale is connected."
    static let preferencesSavedShort = "Preferences saved successfully."
    static let unitUpdated = "Unit updated to %@."
    static let timeFormatUpdated = "Time format updated to %@."
    static let clearData = "Cleared %@ data."
    static let firmwareUpgradeSet = "Firmware upgrade option set to %@."
    static let firmwareUpgradeScheduled = "Scheduled firmware upgrade at %@."
    static let impedanceMeasurement = "Impedance Measurement %@."
    static let sessionImpedance = "Session Impedance %@."
    static let heartRateMeasurement = "Heart Rate Measurement %@."
    static let initialLogoAnimation = "Initial Logo Animation %@."
    static let finalLogoAnimation = "Final Logo Animation %@."
    static let firmwareRestored = "Firmware restored to default settings."
    static let factoryResetCompleted = "Factory reset completed."
    static let wifiSettingsIncomplete = "Please complete Wi-Fi settings to enable this option."
    static let firmwareUpgradeStartedNow = "Firmware upgrade started now."
    static let firmwareUpgradeScheduledNow = "Firmware upgrade scheduled."
    static let userAddedSuccessfully = "User added successfully"
    static let duplicateUserFound = "User with the same name already exists."
    static let connectedSuccessfully = "Connected successfully!"
    static let memoryFullMessage = "Device memory is full. Please delete an existing user."
    static let userRestoredSuccessfully = "User Restored Successfully"
    static let loggedInSuccessfully = "logged in successfully"
    static let selectAppCategory = "Please select an app category to view the graph"
    
    // Error Messages
    static let invalidAccountDetailsFormat = "Invalid account details format."
    static let deviceNotFoundInAccountDetails = "Device not found in account details."
    static let failedToRetrieveToken = "Failed to retrieve token."
    static let unsupportedCategory = "Unsupported category."
    static let noLoginDataFound = "No login data found. Please log in first."
    static let pleaseLogInFirstToConnect = "Please log in first to connect the app."
    static let failedToAddDeviceToApp = "Failed to add device to app."
    static let deviceNotFoundDeleted = "Device not found. The device has been deleted from the app."
    static let loginPrompt = "Please login to your account"
    static let authFailed = "Authentication failed. Please try again."
    static let invalidBroadcastId = "Invalid broadcast ID format"
    static let preferencesSaved = "Preferences saved successfully. These will be applied when the scale is connected."
    static let preferencesFailed = "Failed to save preferences."
    static let noPreferencesFound = "No preferences found for device with Broadcast ID:"
    static let invalidScheduledTime = "Error: Invalid scheduled time."
    static let failedToAddUser = "Failed to Add User"
    static let noUserLoggedIn = "No user login or user is not logged in. Please login to your account."
    static let failedToConnect = "Failed to connect."
    
    // Custom Error Messages
    static func deviceNotFoundinApi(for category: AppCategory) -> String {
        return "Device not found. The device has been deleted from the \(customCategoryText(for: category)) app."
    }
}

struct WifiSetupStrings {
    static let gatheringNetworks = "Gathering Networks..."
    static let wifiMacAddressInfo = "The Wi-Fi MAC Address of your scale is:"
    static let wifiMacAddressInstructions = """
    Record the address to share with your Wi-Fi network or IT department. Once the scale is whitelisted, return to scale setup to pair your scale.
    """
    static let copyMacAddress = "Copy MAC address"
    static let multipleNetworksInfo = "If you have multiple Wi-Fi networks, pick the 2.4 GHz network closest to your scale."
    static let exitAlertTitle = "Are you sure you want to exit?"
    static let exitAlertMessage = "Wi-Fi settings will not be updated."
    static let goBackButton = "GO BACK"
    static let exitButton = "EXIT"
    static let enterWifiPassword = "Enter Wi-Fi Password"
    static let password = "Password"
    static let noPasswordToggle = "Network has no password"
    static let back = "BACK"
    static let connect = "CONNECT"
    static let wifiSetupTitle = "Wi-Fi Setup"
    static let enterPassword = "Enter Password"
}

struct WifiCredentialsStrings {
    static let enterWifiPassword = "Enter Wi-Fi Password"
    static let password = "Password"
    static let enterPasswordPlaceholder = "Enter password"
    static let networkHasNoPassword = "Network has no password"
    static let back = "BACK"
    static let connect = "CONNECT"
    static let wifiSetup = "Wi-Fi Setup"
}


struct ErrorStrings{
    static let noDeviceFound = "No device found with Broadcast ID:"
    static let deviceLinkFailed = "Failed to link device with Broadcast ID:"
    static let noDeviceProvided = "No device provided."
    static let wifiConfigurationResult = "Wi-Fi configuration result:"
    static let savingUserEditError = "Error saving user edit: %@"
    static let errorRestoringUser = "Error restoring user: %@"
    static let errorDeletingUser = "Error deleting user:"
    static let unknownUnitReceived = "Unknown unit received: %@"
    
    static func noUpdateNeeded(_ broadcastId: String, _ isConfigured: Bool) -> String {
        return "No update needed. `isWifiConfigured` for device with Broadcast ID: \(broadcastId) is already \(isConfigured ? CommonStrings.enabled : CommonStrings.disabled)."
    }
    
    static func updateSuccess(_ broadcastId: String, _ status: String) -> String {
        return "Device with Broadcast ID: \(broadcastId) successfully updated `isWifiConfigured` to \(status)."
    }
    
    static func noDeviceFound(_ broadcastId: String) -> String {
        return "No device found with Broadcast ID: \(broadcastId)."
    }
    
    static func updateError(_ errorDescription: String) -> String {
        return "Error updating `isWifiConfigured` for broadcastId: \(errorDescription)"
    }
    
}

struct DisplayMetricsStrings {
    static let customizeSettingsTitle = "Customize settings"
    static let customiseSettingsSubtitle = "Customise your settings"
    static let updateSettingsSubtitle = "You can update settings at any time."
    static let dashboardMetricsTitle = "Body Metrics"
    static let dashboardMetricsSubtitle = "Customize which metrics you'll see on your app's dashboard."
    static let scaleMetricsTitle = "Customize the scale"
    static let scaleMetricsSubtitle = "Choose which metrics are seen and the order in which they'll appear."
    static let scaleModesTitle = "Scale Modes"
    static let scaleModesSubtitle = "Those with specific medical conditions may want to change modes."
    static let userNameTitle = "User Name"
    static let userNameSubtitle = "Change how your name appears on the scale."
    static let setupTitle = "AccuCheck Verve Scale Setup"
    static let otherMetrics = "Other Metrics"
    static let customizeAppDashboardTitle = "Customize App Dashboard"
    static let resetDashboardTitle = "Reset Dashboard"
    static let addTile = "Add tile"
    static let addTileHint = "Adds this metric tile back to the dashboard"
    static let removeTile = "Remove tile"
    static let removeTileHint = "Removes this metric tile from the dashboard"
    // Metric labels
    static let bmi = "BMI"
    static let bodyFat = "Body Fat"
    static let muscleMass = "Muscle Mass"
    static let bodyWater = "Body Water"
    static let boneMass = "Bone Mass"
    static let visceralFat = "Visceral Fat"
    static let subcutaneousFat = "Subcutaneous Fat"
    static let protein = "Protein"
    static let skeletalMuscles = "Skeletal Muscles"
    static let bmr = "BMR"
    static let metabolicAge = "Metabolic Age"
    static let heartRate = "Heart Rate"
    static let goalProgress = "Goal Progress"
    static let dailyAverage = "Daily Average"
    static let weeklyAverage = "Weekly Average"
    static let monthlyAverage = "Monthly Average"
    
    // ScaleMode
    static let changeScaleModeTitle = "Change the scale mode"
    static let changeScaleModeSubtitle = "This scale utilizes bioelectrical impedance analysis (BIA) to measure all body metrics beyond weight. Selecting Weight Only disables the scale's BIA function for all users."
    static let scaleModePickerLabel = "Scale Mode"
    static let allBodyMetrics = "All Body Metrics"
    static let weightOnly = "Weight Only"
    static let heartRateLabel = "Heart Rate"
    static let heartRateNote = "This metric takes additional time to collect. When off, the scale will only collect weight and body composition."
    static let weightOnlyModeIndicator = "indicates Weight only mode is on"
    static let weightOnlyNote = "NOTE: other users can temporarily enable all body metrics for one session via weight gurus"
}

struct APIKeyStrings {
    static let scaleId = "scaleId"
    static let tzOffset = "tzOffset"
    static let timeFormat = "timeFormat"
    static let displayName = "displayName"
    static let displayMetrics = "displayMetrics"
    static let shouldMeasurePulse = "shouldMeasurePulse"
    static let shouldMeasureImpedance = "shouldMeasureImpedance"
    static let shouldFactoryReset = "shouldFactoryReset"
    static let wifiFotaScheduleTime = "wifiFotaScheduleTime"
    static let broadcastId = "broadcastId"
    static let sku = "sku"
    static let id = "id"
}

struct ScaleConfigStrings {
    static let timeFormat = "Time Format"
    static let clearData = "Clear Data"
    static let firmwareUpgrade = "Firmware Upgrade"
    static let scheduleTime = "Schedule Time"
    static let pickDateTime = "Pick Date & Time"
    static let impedanceMeasurement = "Impedance Measurement"
    static let sessionImpedance = "Session Impedance"
    static let heartRateMeasurement = "Heart Rate Measurement"
    static let initialLogoAnimation = "Initial Logo Animation"
    static let finalLogoAnimation = "Final Logo Animation"
    static let startFirmwareUpgrade = "Start Firmware Upgrade"
    static let restoreFirmware = "Restore Firmware"
    static let restoreFactorySettings = "Restore Factory Settings"
    static let restoreDefaults = "Restore Defaults"
    static let selectDateTime = "Select Date and Time"
    static let unitKg = "Kg"
    static let unitLb = "lbs"
    static let timeFormat24H = "24H"
    static let timeFormat12H = "12H"
    static let clearSelect = "Select"
    static let clearAll = "All"
    static let clearUser = "User"
    static let clearHistory = "History"
    static let clearWiFi = "WiFi"
    static let clearSetting = "Setting"
    static let firmwareNow = "Now"
    static let firmwareSchedule = "Schedule"
    static let dateTimeFormat = "dd MMM hh:mm a"
}

struct DeviceLogsStrings {
    static let fetchLogsTitle = "Fetch Device Logs"
    static let fetchLogsButtonText = "Fetch Logs"
    static let fetchLogsIconName = "arrow.clockwise"
    static let fetchingLogs = "Fetching logs..."
    static let receivedLogsCount = "Received %d of %d logs"
    static let noLogsAvailable = "No logs available"
    static let exportLogsIconName = "square.and.arrow.down"
    static let navigationTitle = "Device Logs"
    static let deviceNotProvided = "Device not provided."
    static let failedToAccessDocumentsDirectory = "Failed to access Documents directory."
    static let logFileName = "DeviceLogs_%.0f.txt"
    static let failedToWriteLogs = "Failed to write logs to file: %@"
}

struct DuplicateUserText {
    static let title = "A user with your name already exists on the scale."
    static let subtitle = "Choose a new user name to proceed. Or, if this is you, restore the existing account."
    static let lastActivePrefix = "Last active on"
    static let errorMessage = "The scale can't have duplicate user names"
    static let editButtonTitle = "Edit name"
    static let restoreButtonTitle = "Restore"
}

struct AlertStrings {
    static let addUserTitle = "Add User"
    static let addUserMessage = "Are you sure you want to add this user?"
    static let loginFailed = "Login Failed"
}

struct EditUsernameStrings {
    static let duplicateUserMessage = "The scale can't have duplicate user names."
    static let usernamePlaceholder = "Username"
    static let editYourUsername = "Edit Your Username:"
    static let editUsername = "Edit User Name"
}

struct GraphStrings {
    static let timeFrame = "Time Frame"
    static let date = "Date"
    static let weight = "Weight"
    static let goal = "Goal"
    static let yGrid = "Y Grid"
    static let min = "Min"
    static let extendedMax = "ExtendedMax"
    static let percentageSymbol = "% of"
    static let mmHg = "mmHg"
    static let slash = "/"
    static let pulse = "Pulse"
    static let systolic = "Systolic"
    static let diastolic = "Diastolic"
    static let noDataAvailable = "No Data Available"
    static let typeFormat = "Type"
    static let valueFormat = "%.1f"
    static let fifth = "fifth"
    static let tenth = "tenth"
    static let twentyFifth = "twentyFifth"
    static let fiftieth = "fiftieth"
    static let seventyFifth = "seventyFifth"
    static let ninetieth = "ninetieth"
    static let ninetyFifth = "ninetyFifth"
    static let selectDevice = "Select device"
    static let glucose = "Glucose"
    static let bloodGlucoseMeter = "Blood Glucose Meter"
    static let rpmWeighingScale = "RPM Weighing Scale"
    static let rpmBabyScale = "RPM Baby Scale"
    static let thermometer = "Thermometer"
    static let pulseOximeter = "Pulse Oximeter"
    static let pulseOxi = "pulseOxi"
    static let bgm = "bgm"
    static let scale = "Scale"
    static let babyScale = "Baby Scale"
    static let startMeasuring = "Start measuring to see your progress here."
    
}

struct JsonFileNameStrings{
    static  let boyWeightDecigrams = "BoyWeightDecigrams"
    static let girlWeightDecigrams = "GirlWeightDecigrams"
    static let boyWeightDecigramsLine = "BoyWeightDecigramsLine"
    static let girlWeightDecigramsLine = "GirlWeightDecigramsLine"
}

struct HistoryViewStrings {
    static let meanPressure = "Mean Pressure"
    static let pulse = "Pulse"
    static let category = "Category"
    static let pulseAmplitudeIndex = "Pulse Amplitude Index"
    static let device = "Device"
    static let bmi = "BMI"
    static let bodyFat = "Body Fat"
    static let muscleMass = "Muscle Mass"
    static let bodyWater = "Body Water"
    static let unknownDevice = "Unknown Device"
    static let pulseOximeter = "Pulse Oximeter"
    static let thermometer = "Thermometer"
    static let bloodGlucoseMeter = "Blood Glucose Meter"
    static let babyScale = "RPM baby scale"
    static let weighingScale = "RPM weighing scale"
}

struct MeasurementUnitStrings {
    static let milligramsPerDeciliter = "mg/dL"
    static let millimetersOfMercury = "mmHg"
    static let percent = "%"
}

struct AppAssets{
    //Icons
    static let bluetooth = "bluetooth"
    static let profile = "profile"
    static let scaleIcon = "ScaleIcon"
    static let bmi = "bmiIcon"
    static let bodyFat = "bodyFatIcon"
    static let muscleMass = "muscleMassIcon"
    static let bodyWater = "bodyWaterIcon"
    static let heartRate = "heartRateIcon"
    static let bloodPressureMonitor = "bloodpressureMonitorIcon"
    static let thermometerIcon = "temperatureIcon"
    static let pulseOximeterIcon = "PulseOxiIcon"
    static let bgmIcon = "bgmIcon"
    static let metricEditIconBlue = "metric-edit-icon-blue"
    static let moreInfoIconBlue = "more-info-blue"
    static let scaleDisplayNameIcon = "scale-display-name-icon"
    static let weightOnlyModeDiscoveredIcon = "weight-only-mode-discovered"
    static let bmrIcon = "bmr-icon"
    static let boneMassIcon = "bone-mass-icon"
    static let metabolicAgeIcon = "metabolic-age-icon"
    static let proteinIcon = "protein-icon"
    static let pulseIcon = "pulse-icon"
    static let skeletalMuscleIcon = "skeletal-muscle-icon"
    static let subcutaneousFatIcon = "subcutaneous-fat-icon"
    static let visceralFatIcon = "visceral-fat-icon"
    static let metricEditIconWhite = "metric-edit-icon-white"
    static let moreInfoIconWhite = "more-info-white"
    
    //Images
    static let kitchenScale = "WellandKitchenScale"
    static let wg0375 =  "0375"
    static let wg0376 =  "0376"
    static let wg0380 =  "0380"
    static let wg0382 =  "0382"
    static let wg0383 =  "0383"
    static let wg0412 =  "0412"
    static let bh0604 =  "0604"
    static let bh0603 =  "0603"
    static let bh0634 =  "0634"
    static let bh0636 =  "0636"
    static let bh0663 =  "0663"
    static let pulseOxiMeter =  "0003"
    static let bloodGlucoseMeter =  "0005"
    static let thermometer =  "0062"
    static let bs0220 = "0220"
    static let bs0222 = "0222"
    static let wg0351 = "0351"
    static let scaleWeightMode = "weight-only-mode"
    static let scaleDisplayName = "scale-display-name"
}
