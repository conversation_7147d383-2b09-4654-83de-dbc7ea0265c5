//
//  GraphConstants.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 15/05/25.
//

import SwiftUI

struct GraphConstants {
    static let optimalSystole: Double = 120.0
    static let optimalDiastole: Double = 80.0
    static let percentileConversionFactor: Double = 10_000
    
    static func seriesColorMapping(
        systolicValue: Int,
        diastolicValue: Int,
        pulseValue: Int,
        exampleDate: Date
    ) -> KeyValuePairs<String, Color> {
        let balanceHealthEntry = BalanceHealthEntry(
            date: exampleDate,
            systole: systolicValue,
            diastole: diastolicValue,
            pulse: pulseValue
        )

        return KeyValuePairs(dictionaryLiteral:
            (GraphStrings.systolic, getColorFor(entry: balanceHealthEntry)),
            (GraphStrings.diastolic, getColorFor(entry: balanceHealthEntry)),
            (GraphStrings.pulse, .blue),
            (GraphStrings.weight, .purple),
            (GraphStrings.fifth, .gray.opacity(0.5)),
            (GraphStrings.tenth, .gray.opacity(0.5)),
            (GraphStrings.twentyFifth, .gray.opacity(0.5)),
            (GraphStrings.fiftieth, .gray.opacity(0.5)),
            (GraphStrings.seventyFifth, .gray.opacity(0.5)),
            (GraphStrings.ninetieth, .gray.opacity(0.5)),
            (GraphStrings.ninetyFifth, .gray.opacity(0.5))
        )
    }
}

