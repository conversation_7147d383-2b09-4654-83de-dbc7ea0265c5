//
//  RPMDeviceSKUs.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 20/05/25.
//

import Foundation

struct RPMDeviceSKUs {
    static let babyScale = "0222"
    static let weighingScale = "0383"
    static let bloodGlucoseMeter = "0005"
    static let thermometer = "0062"
    static let pulseOximeter = "0003"
}

let rpmDevices: [(name: String, sku: String)] = [
    (GraphStrings.bloodGlucoseMeter, RPMDeviceSKUs.bloodGlucoseMeter),
    (GraphStrings.rpmWeighingScale, RPMDeviceSKUs.weighingScale),
    (GraphStrings.rpmBabyScale, RPMDeviceSKUs.babyScale),
    (GraphStrings.thermometer, RPMDeviceSKUs.thermometer),
    (GraphStrings.pulseOximeter, RPMDeviceSKUs.pulseOximeter),
    (CommonStrings.allDevices,"")
]

let rpmDeviceShortForms: [String: String] = [
    RPMDeviceSKUs.bloodGlucoseMeter: GraphStrings.bgm.uppercased(),
    RPMDeviceSKUs.weighingScale: GraphStrings.scale.uppercased(),
    RPMDeviceSKUs.babyScale: GraphStrings.babyScale.uppercased(),
    RPMDeviceSKUs.thermometer: GraphStrings.thermometer.uppercased(),
    RPMDeviceSKUs.pulseOximeter: GraphStrings.pulseOxi.uppercased(),
    "": CommonStrings.all.uppercased()
]
