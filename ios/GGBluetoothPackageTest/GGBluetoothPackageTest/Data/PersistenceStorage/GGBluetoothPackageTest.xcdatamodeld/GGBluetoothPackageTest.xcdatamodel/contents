<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="23605" systemVersion="24D81" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="Device" representedClassName="Device" syncable="YES" codeGenerationType="class">
        <attribute name="appCategory" optional="YES" attributeType="String"/>
        <attribute name="batteryLevel" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="broadcastId" optional="YES" attributeType="String"/>
        <attribute name="datePaired" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="deviceType" optional="YES" attributeType="String"/>
        <attribute name="isDeviceLinkedToApp" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isUserAdded" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isWifiConfigured" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="macAddress" optional="YES" attributeType="String"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="password" optional="YES" attributeType="String"/>
        <attribute name="protocolType" optional="YES" attributeType="String"/>
        <attribute name="synAllData" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="token" optional="YES" attributeType="String"/>
        <attribute name="userNumber" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="preference" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Preference" inverseName="device" inverseEntity="Preference"/>
    </entity>
    <entity name="MeasurementEntry" representedClassName="MeasurementEntry" syncable="YES" codeGenerationType="class">
        <attribute name="appCategory" optional="YES" attributeType="String"/>
        <attribute name="bgm" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="bmi" optional="YES" attributeType="Float" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="bmr" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="bodyFat" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="boneMass" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="broadcastID" optional="YES" attributeType="String"/>
        <attribute name="broadcastIDString" optional="YES" attributeType="String"/>
        <attribute name="deviceSKU" optional="YES" attributeType="String"/>
        <attribute name="diastolic" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="entryTimestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" optional="YES" attributeType="String"/>
        <attribute name="impedance" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="meanPressure" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="metabolicAge" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="muscleMass" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="proteinPercent" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="protocolType" optional="YES" attributeType="String"/>
        <attribute name="pulse" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="pulseAmplitudeIndex" optional="YES" attributeType="Float" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="skeletalMusclePercent" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="source" optional="YES" attributeType="String"/>
        <attribute name="spo" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="subcutaneousFatPercent" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="systolic" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="temperature" optional="YES" attributeType="Float" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="unit" optional="YES" attributeType="String"/>
        <attribute name="visceralFatLevel" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="water" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="weight" optional="YES" attributeType="Float" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="weightInKg" optional="YES" attributeType="Float" defaultValueString="0.0" usesScalarValueType="YES"/>
    </entity>
    <entity name="MetricsEntity" representedClassName="MetricsEntity" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="String"/>
        <attribute name="isEnabled" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="label" optional="YES" attributeType="String"/>
        <attribute name="order" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
    </entity>
    <entity name="Preference" representedClassName="Preference" syncable="YES" codeGenerationType="class">
        <attribute name="displayMetrics" optional="YES" attributeType="Transformable"/>
        <attribute name="displayName" optional="YES" attributeType="String"/>
        <attribute name="shouldMeasureImpedance" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="shouldMeasurePulse" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="timeFormat" optional="YES" attributeType="String"/>
        <relationship name="device" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Device" inverseName="preference" inverseEntity="Device"/>
    </entity>
    <entity name="SelectedAppCategory" representedClassName="SelectedAppCategory" syncable="YES" codeGenerationType="class">
        <attribute name="selectedCategory" optional="YES" attributeType="String"/>
    </entity>
    <entity name="UserLogin" representedClassName="UserLogin" syncable="YES" codeGenerationType="class">
        <attribute name="appCategory" optional="YES" attributeType="String"/>
        <attribute name="email" optional="YES" attributeType="String"/>
        <attribute name="isLoggedIn" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="password" optional="YES" attributeType="String"/>
    </entity>
    <entity name="UserProfile" representedClassName="UserProfile" syncable="YES" codeGenerationType="class">
        <attribute name="age" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="birthday" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="bodyType" optional="YES" attributeType="String"/>
        <attribute name="email" optional="YES" attributeType="String"/>
        <attribute name="gender" optional="YES" attributeType="String"/>
        <attribute name="goalType" optional="YES" attributeType="String"/>
        <attribute name="goalWeight" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="height" optional="YES" attributeType="Integer 16" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="unitType" optional="YES" attributeType="String"/>
        <attribute name="weight" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
    </entity>
</model>