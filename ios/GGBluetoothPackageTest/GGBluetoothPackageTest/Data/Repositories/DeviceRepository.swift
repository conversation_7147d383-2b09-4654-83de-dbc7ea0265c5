//
//  DeviceRepository.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 02/12/24.
//

import Foundation
import CoreData
import GGBluetoothSwiftPackage

class DeviceRepository : DeviceRepositoryProtocol{
    
    private let viewContext: NSManagedObjectContext
    private let logger: AppLogger = AppLogger(category: String(describing: DeviceRepository.self))
    
    init(viewContext: NSManagedObjectContext) {
        self.viewContext = viewContext
    }
    
    internal func fetchDevice(by broadcastId: String) -> Device? {
        let request = NSFetchRequest<Device>(entityName: "Device")
        request.predicate = NSPredicate(format: "broadcastId == %@", broadcastId)
        
        do {
            return try viewContext.fetch(request).first
        } catch let error {
            let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 2, userInfo: [NSLocalizedDescriptionKey: "Error fetching device by broadcastId: \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
            return nil
        }
    }
    
    internal func fetchAllDevices() -> [Device]? {
        let request = NSFetchRequest<Device>(entityName: "Device")
        
        do {
            return try viewContext.fetch(request)
        } catch let error {
            let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 5, userInfo: [NSLocalizedDescriptionKey: "Error fetching all devices: \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
            return nil
        }
    }
    internal func fetchAllGGBTDevices() -> [GGBTDevice] {
        let request = NSFetchRequest<Device>(entityName: "Device")
        
        do {
            let devices = try viewContext.fetch(request)
            return devices.map { device in
                return GGBTDevice(
                    name: device.name ?? "",
                    broadcastId: device.broadcastId ?? "",
                    password: device.password ?? "",
                    token: device.token ?? "",
                    userNumber: Int(device.userNumber),
                    preference: nil,
                    syncAllData: true,
                    batteryLevel: Int(device.batteryLevel),
                    protocolType: device.protocolType ?? "",
                    macAddress: device.macAddress ?? ""
                )
            }
        } catch let error {
            let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 5, userInfo: [NSLocalizedDescriptionKey: "Error fetching all GGBT devices: \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
            return []
        }
    }
    
    internal func saveDevice(
        appCategory: String,
        name: String,
        broadcastId: String,
        userNumber: Int?,
        batteryLevel: Int?,
        macAddress: String?,
        password: String?,
        protocolType: String?,
        token: String?,
        deviceType: String?,
        preference: Preference?,
        isDeviceLinkedToApp: Bool = false,
        isUserAdded: Bool = false,
        isWifiConfigured : Bool = false
    ) {
        
        if appCategory == AppCategory.balanceHealth.rawValue || appCategory == AppCategory.weightGurus.rawValue {
            let fetchRequest: NSFetchRequest<Device> = Device.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "broadcastId == %@ AND userNumber == %d", broadcastId, userNumber ?? 0)
            
            do {
                let existingDevices = try viewContext.fetch(fetchRequest)
                if !existingDevices.isEmpty {
                    ToastService.shared.presentToast(with: "Error: Device or user already exists.")
                    return
                }
            } catch let error {
                let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 5, userInfo: [NSLocalizedDescriptionKey: "Error checking existing devices: \(error.localizedDescription)"])
                logger.error(error: nsError, function: #function, line: #line)
                return
            }
        }
        
        // Create and save the new device
        let newDevice = Device(context: viewContext)
        newDevice.appCategory = appCategory
        newDevice.name = name
        newDevice.broadcastId = broadcastId
        newDevice.userNumber = Int32(userNumber ?? 0)
        newDevice.datePaired = Date()
        newDevice.batteryLevel = Int16(batteryLevel ?? 0)
        newDevice.macAddress = macAddress
        newDevice.password = password
        newDevice.protocolType = protocolType
        newDevice.token = token
        newDevice.deviceType = deviceType
        newDevice.preference = preference
        newDevice.isDeviceLinkedToApp = isDeviceLinkedToApp
        newDevice.isWifiConfigured = isWifiConfigured
        
        do {
            try viewContext.save()
            logger.info(info: """
                      Device saved successfully:
                      appCategory: \(appCategory)
                      Name: \(name)
                      Broadcast ID: \(broadcastId)
                      User Number: \(String(describing: userNumber))
                      Battery Level: \(batteryLevel ?? 0)
                      MAC Address: \(macAddress ?? "")
                      Password: \(password ?? "")
                      Protocol Type: \(protocolType ?? "" )
                      Token: \(token ?? "")
                      Device Type: \(deviceType ?? "")
                      Preference: \(String(describing: preference))
                      Date Paired: \(newDevice.datePaired!)
                      Is Device Linked to App: \(isDeviceLinkedToApp)
                      """, function: #function, line: #line)
            
        } catch let error {
            let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 3, userInfo: [NSLocalizedDescriptionKey: "Error saving device: \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
        }
    }
    
    internal func deleteDevice(by broadcastId: String) {
        let request = NSFetchRequest<Device>(entityName: "Device")
        request.predicate = NSPredicate(format: "broadcastId == %@", broadcastId)
        
        do {
            let devices = try viewContext.fetch(request)
            if let deviceToDelete = devices.first {
                viewContext.delete(deviceToDelete)
                try viewContext.save()
                logger.info(info: "Device with Broadcast ID: \(broadcastId) deleted successfully.", function: #function, line: #line)
            } else {
                logger.error(error: NSError(
                    domain: String(describing: DeviceRepository.self),
                    code: 6,
                    userInfo: [NSLocalizedDescriptionKey: "No device found with Broadcast ID: \(broadcastId)."]
                ), function: #function, line: #line)
                
            }
        } catch let error {
            let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 4, userInfo: [NSLocalizedDescriptionKey: "Error deleting device by broadcastId: \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
        }
    }
    
    
    func saveSelectedCategory(_ category: String) {
        let request = NSFetchRequest<SelectedAppCategory>(entityName: "SelectedAppCategory")
        
        do {
            let existingCategories = try viewContext.fetch(request)
            let selectedAppCategory: SelectedAppCategory
            
            if let existing = existingCategories.first {
                selectedAppCategory = existing
            } else {
                selectedAppCategory = SelectedAppCategory(context: viewContext)
                selectedAppCategory.selectedCategory = "all"
            }
            selectedAppCategory.selectedCategory = category
            try viewContext.save()
        } catch let error {
            let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 3, userInfo: [NSLocalizedDescriptionKey: "Failed to save selected category: \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
        }
    }
    
    func fetchSelectedCategory() -> String? {
        let request = NSFetchRequest<SelectedAppCategory>(entityName: "SelectedAppCategory")
        
        do {
            let categories = try viewContext.fetch(request)
            return categories.first?.selectedCategory
        } catch _ {
            return nil
        }
    }
    
    
    func saveUserLogin(category: AppCategory, email: String, password: String, isLoggedIn: Bool) {
        let request = NSFetchRequest<UserLogin>(entityName: "UserLogin")
        request.predicate = NSPredicate(format: "appCategory == %@", category.rawValue)
        
        do {
            let existingLogins = try viewContext.fetch(request)
            let userLogin: UserLogin
            
            if let existing = existingLogins.first {
                userLogin = existing
            } else {
                userLogin = UserLogin(context: viewContext)
            }
            
            userLogin.email = email
            userLogin.password = password
            userLogin.isLoggedIn = isLoggedIn
            userLogin.appCategory = category.rawValue
            
            try viewContext.save()
            logger.info(info: "User login saved successfully for category \(category.rawValue): \(email)", function: #function, line: #line)
        } catch let error {
            let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 7, userInfo: [NSLocalizedDescriptionKey: "Failed to save user login: \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
        }
    }
    
    func fetchUserLogin(for category: AppCategory) -> (email: String?, password: String?, isLoggedIn: Bool)? {
        let request = NSFetchRequest<UserLogin>(entityName: "UserLogin")
        request.predicate = NSPredicate(format: "appCategory == %@", category.rawValue)
        
        do {
            if let userLogin = try viewContext.fetch(request).first {
                return (userLogin.email, userLogin.password, userLogin.isLoggedIn)
            } else {
                return nil
            }
        } catch let error {
            let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 9, userInfo: [NSLocalizedDescriptionKey: "Error fetching user login for category \(category.rawValue): \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
            return nil
        }
    }
    
    func deleteUserLogin(for category: AppCategory) {
        let request = NSFetchRequest<UserLogin>(entityName: "UserLogin")
        request.predicate = NSPredicate(format: "appCategory == %@", category.rawValue)
        
        do {
            let logins = try viewContext.fetch(request)
            if let loginToDelete = logins.first {
                viewContext.delete(loginToDelete)
                try viewContext.save()
            } else {
                logger.info(info: "No user login found for category \(category.rawValue) to delete.", function: #function, line: #line)
            }
        } catch let error {
            let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 10, userInfo: [NSLocalizedDescriptionKey: "Error deleting user login for category \(category.rawValue): \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
        }
    }
    
    func fetchAllUserLogins() -> [(UserLogin, AppCategory)]? {
        let request = NSFetchRequest<UserLogin>(entityName: "UserLogin")
        
        do {
            let userLogins = try viewContext.fetch(request)
            let result = userLogins.compactMap { userLogin -> (UserLogin, AppCategory)? in
                if let categoryString = userLogin.appCategory,
                   let category = AppCategory(rawValue: categoryString) {
                    return (userLogin, category)
                }
                return nil
            }
            logger.info(info: "Fetched all user logins successfully", function: #function, line: #line)
            return result
        } catch let error {
            let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 11, userInfo: [NSLocalizedDescriptionKey: "Error fetching all user logins: \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
            return nil
        }
    }
    
    internal func isDeviceLinkedToApp(by broadcastId: String) -> Bool? {
        let request = NSFetchRequest<Device>(entityName: "Device")
        request.predicate = NSPredicate(format: "broadcastId == %@", broadcastId)
        
        do {
            let devices = try viewContext.fetch(request)
            return devices.first?.isDeviceLinkedToApp
        } catch let error {
            let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 12, userInfo: [NSLocalizedDescriptionKey: "Error fetching device linked status by broadcastId: \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
            return nil
        }
    }
    
    internal func setDeviceLinkedToApp(by broadcastId: String, isLinked: Bool) {
        let request = NSFetchRequest<Device>(entityName: "Device")
        request.predicate = NSPredicate(format: "broadcastId == %@", broadcastId)
        
        do {
            let devices = try viewContext.fetch(request)
            if let device = devices.first {
                device.isDeviceLinkedToApp = isLinked
                try viewContext.save()
                let status = isLinked ? "linked" : "unlinked"
                logger.info(info: "Device with Broadcast ID: \(broadcastId) successfully \(status) to app.", function: #function, line: #line)
            } else {
                logger.error(error: NSError(
                    domain: String(describing: DeviceRepository.self),
                    code: 13,
                    userInfo: [NSLocalizedDescriptionKey: "No device found with Broadcast ID: \(broadcastId)."]
                ), function: #function, line: #line)
            }
        } catch let error {
            let nsError = NSError(
                domain: String(describing: DeviceRepository.self),
                code: 14,
                userInfo: [NSLocalizedDescriptionKey: "Error updating device link status by broadcastId: \(error.localizedDescription)"]
            )
            logger.error(error: nsError, function: #function, line: #line)
        }
    }
    
    func updateDevice(
        broadcastId: String,
        token: String?,
        isUserAdded: Bool) {
            
            let fetchRequest: NSFetchRequest<Device> = Device.fetchRequest()
            fetchRequest.predicate = NSPredicate(format: "broadcastId == %@", broadcastId)
            
            do {
                let devices = try viewContext.fetch(fetchRequest)
                
                if let device = devices.first {
                    if let token = token {
                        device.token = token
                    }
                    device.isUserAdded = isUserAdded
                    try viewContext.save()
                    logger.info(info: "Device with Broadcast ID: \(broadcastId) updated successfully.", function: #function, line: #line)
                } else {
                    logger.error(error: NSError(
                        domain: String(describing: DeviceRepository.self),
                        code: 15,
                        userInfo: [NSLocalizedDescriptionKey: "No device found with Broadcast ID: \(broadcastId)."]
                    ), function: #function, line: #line)
                }
            } catch let error {
                let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 16, userInfo: [NSLocalizedDescriptionKey: "Error updating device: \(error.localizedDescription)"])
                logger.error(error: nsError, function: #function, line: #line)
            }
        }
    
    func isUserAdded(for broadcastId: String) -> Bool? {
        let fetchRequest: NSFetchRequest<Device> = Device.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "broadcastId == %@", broadcastId)
        
        do {
            let devices = try viewContext.fetch(fetchRequest)
            return devices.first?.isUserAdded
        } catch let error {
            let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 17, userInfo: [NSLocalizedDescriptionKey: "Error fetching isUserAdded for broadcastId: \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
            return nil
        }
    }
    
    func savePreference(
        for broadcastId: String,
        displayMetrics: [String],
        displayName: String,
        shouldMeasureImpedance: Bool,
        shouldMeasurePulse: Bool,
        timeFormat: String
    ) {
        let fetchRequest: NSFetchRequest<Device> = Device.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "broadcastId == %@", broadcastId)
        
        do {
            if let device = try viewContext.fetch(fetchRequest).first {
                let preference: Preference
                
                // Check if the device already has a preference
                if let existingPreference = device.preference {
                    preference = existingPreference
                } else {
                    // Create a new Preference object
                    preference = Preference(context: viewContext)
                    device.preference = preference
                }
                
                // Update the preference attributes
                preference.displayMetrics = displayMetrics as NSArray
                preference.displayName = displayName
                preference.shouldMeasureImpedance = shouldMeasureImpedance
                preference.shouldMeasurePulse = shouldMeasurePulse
                preference.timeFormat = timeFormat
                
                // Save the context
                try viewContext.save()
                
                logger.info(info: """
                    Preference saved successfully for device with Broadcast ID: \(broadcastId):
                    Display Name: \(displayName)
                    Display Metrics: \(displayMetrics)
                    Should Measure Impedance: \(shouldMeasureImpedance)
                    Should Measure Pulse: \(shouldMeasurePulse)
                    Time Format: \(timeFormat)
                """, function: #function, line: #line)
            } else {
                logger.error(error: NSError(
                    domain: String(describing: DeviceRepository.self),
                    code: 18,
                    userInfo: [NSLocalizedDescriptionKey: "No device found with Broadcast ID: \(broadcastId)."]
                ), function: #function, line: #line)
            }
        } catch let error {
            let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 19, userInfo: [NSLocalizedDescriptionKey: "Error saving preference for device: \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
        }
    }
    
    func fetchPreference(by broadcastId: String) -> Preference? {
        let fetchRequest: NSFetchRequest<Device> = Device.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "broadcastId == %@", broadcastId)
        
        do {
            if let device = try viewContext.fetch(fetchRequest).first {
                return device.preference
            } else {
                logger.error(error: NSError(
                    domain: String(describing: DeviceRepository.self),
                    code: 20,
                    userInfo: [NSLocalizedDescriptionKey: "No device found with Broadcast ID: \(broadcastId)"]
                ), function: #function, line: #line)
                return nil
            }
        } catch let error {
            let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 21, userInfo: [NSLocalizedDescriptionKey: "Error fetching preference for device: \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
            return nil
        }
    }
    
    func getIsWifiConfigured(for broadcastId: String) -> Bool? {
        let fetchRequest: NSFetchRequest<Device> = Device.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "broadcastId == %@", broadcastId)
        
        do {
            let devices = try viewContext.fetch(fetchRequest)
            if let device = devices.first {
                return device.isWifiConfigured
            } else {
                logger.error(error: NSError(
                    domain: String(describing: DeviceRepository.self),
                    code: 22,
                    userInfo: [NSLocalizedDescriptionKey: "No device found with Broadcast ID: \(broadcastId)."]
                ), function: #function, line: #line)
                return nil
            }
        } catch let error {
            let nsError = NSError(domain: String(describing: DeviceRepository.self), code: 23, userInfo: [NSLocalizedDescriptionKey: "Error fetching `isWifiConfigured` for broadcastId: \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
            return nil
        }
    }
    
    func setIsWifiConfigured(for broadcastId: String, isConfigured: Bool) {
        let fetchRequest: NSFetchRequest<Device> = Device.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "broadcastId == %@", broadcastId)
        
        do {
            let devices = try viewContext.fetch(fetchRequest)
            if let device = devices.first {
                if device.isWifiConfigured == isConfigured {
                    logger.info(info: ErrorStrings.noUpdateNeeded(broadcastId, isConfigured), function: #function, line: #line)
                    return
                }
                device.isWifiConfigured = isConfigured
                try viewContext.save()
                let status = isConfigured ? CommonStrings.enabled : CommonStrings.disabled
                logger.info(info: ErrorStrings.updateSuccess(broadcastId, status), function: #function, line: #line)
            } else {
                logger.error(error: NSError(
                    domain: String(describing: DeviceRepository.self),
                    code: 24,
                    userInfo: [NSLocalizedDescriptionKey: ErrorStrings.noDeviceFound(broadcastId)]
                ), function: #function, line: #line)
            }
        } catch let error {
            let nsError = NSError(
                domain: String(describing: DeviceRepository.self),
                code: 25,
                userInfo: [NSLocalizedDescriptionKey: ErrorStrings.updateError(error.localizedDescription)]
            )
            logger.error(error: nsError, function: #function, line: #line)
        }
    }
    
}
