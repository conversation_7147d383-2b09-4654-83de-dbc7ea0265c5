//
//  UserProfileRepository.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 06/01/25.
//

import Foundation
import CoreData

class UserProfileRepository: UserProfileRepositoryProtocol {
    private let viewContext: NSManagedObjectContext
    private let logger: AppLogger = AppLogger(category: String(describing: UserProfileRepository.self))
    
    init(viewContext: NSManagedObjectContext) {
        self.viewContext = viewContext
    }
    
    internal func saveUserProfile(
        name: String,
        email: String,
        birthday: Date,
        height: Int16,
        weight: Double,
        gender: String,
        bodyType: String,
        goalType: String,
        goalWeight: Double,
        unitType: String
    ) {
        let fetchRequest: NSFetchRequest<UserProfile> = UserProfile.fetchRequest()
        
        do {
            let existingProfiles = try viewContext.fetch(fetchRequest)
            
            if let existingProfile = existingProfiles.first {
                existingProfile.name = name
                existingProfile.email = email
                existingProfile.birthday = birthday
                existingProfile.height = height
                existingProfile.weight = weight
                existingProfile.gender = gender
                existingProfile.bodyType = bodyType
                existingProfile.goalType = goalType
                existingProfile.goalWeight = goalWeight
                existingProfile.unitType = unitType
            } else {
                let newUserProfile = UserProfile(context: viewContext)
                newUserProfile.name = name
                newUserProfile.email = email
                newUserProfile.birthday = birthday
                newUserProfile.height = height
                newUserProfile.weight = weight
                newUserProfile.gender = gender
                newUserProfile.bodyType = bodyType
                newUserProfile.goalType = goalType
                newUserProfile.goalWeight = goalWeight
                newUserProfile.unitType = unitType
            }
            
            try viewContext.save()
            logger.info(info: "User profile saved/updated successfully: \(name), Email: \(email)", function: #function, line: #line)
        } catch let error {
            let nsError = NSError(domain: String(describing: UserProfileRepository.self), code: 3, userInfo: [NSLocalizedDescriptionKey: "Error saving/updating user profile: \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
        }
    }
    
    internal func saveMetricsEntities(metricsEntities: [MetricsEntityModel]) {
        let fetchRequest: NSFetchRequest<MetricsEntity> = MetricsEntity.fetchRequest()
        
        do {
            let existingEntities = try viewContext.fetch(fetchRequest)
            
            for metricsEntityModel in metricsEntities {
                if let existingEntity = existingEntities.first(where: { $0.id == metricsEntityModel.id }) {
                    // Update only isEnabled and order for existing entities
                    existingEntity.isEnabled = metricsEntityModel.isEnabled
                    existingEntity.order = metricsEntityModel.order
                } else {
                    // Create new entity for first-time saves
                    let newMetricsEntity = MetricsEntity(context: viewContext)
                    newMetricsEntity.id = metricsEntityModel.id
                    newMetricsEntity.isEnabled = metricsEntityModel.isEnabled
                    newMetricsEntity.label = metricsEntityModel.label
                    newMetricsEntity.order = metricsEntityModel.order
                }
            }
            
            try viewContext.save()
            logger.info(info: """
                Metrics entities saved/updated successfully. Details:
                \(metricsEntities.map { "id: \($0.id), isEnabled: \($0.isEnabled), label: \($0.label), order: \($0.order)" }.joined(separator: "; "))
            """, function: #function, line: #line)
            DispatchQueue.main.async{
                ToastService.shared.presentToast(with:"Metrics entities saved successfully.")
            }
            
        } catch let error {
            let nsError = NSError(domain: String(describing: UserProfileRepository.self), code: 3, userInfo: [NSLocalizedDescriptionKey: "Error saving/updating metrics entities: \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
        }
    }

    internal func fetchMetricsEntities() -> [MetricsEntityModel] {
        let fetchRequest: NSFetchRequest<MetricsEntity> = MetricsEntity.fetchRequest()
        
        do {
            let fetchedEntities = try viewContext.fetch(fetchRequest)
            
            // Map fetched entities to MetricsEntityModel
            let metricsEntityModels = fetchedEntities.map { entity in
                MetricsEntityModel(
                    id: entity.id ?? "",
                    isEnabled: entity.isEnabled,
                    label: entity.label ?? "",
                    order: entity.order
                )
            }            
            logger.info(info: """
                Successfully fetched and mapped MetricsEntity records. Details:
                \(metricsEntityModels.map { "id: \($0.id), isEnabled: \($0.isEnabled), label: \($0.label), order: \($0.order)" }.joined(separator: "; "))
            """, function: #function, line: #line)
            
            return metricsEntityModels
        } catch let error {
            let nsError = NSError(domain: String(describing: UserProfileRepository.self), code: 4, userInfo: [NSLocalizedDescriptionKey: "Error fetching metrics entities: \(error.localizedDescription)"])
            logger.error(error: nsError, function: #function, line: #line)
            return []
        }
    }
}
