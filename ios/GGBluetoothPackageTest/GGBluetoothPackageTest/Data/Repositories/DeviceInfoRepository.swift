//
//  DeviceInfoRepository.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 03/12/24.
//

import Foundation

class DeviceInfoRepository: DeviceInfoProtocol {
    private var shared = DeviceInfoRepository.self
    private let logger: AppLogger = AppLogger(category: String(describing: DeviceRepository.self))
    
    init(){}
    
    internal func readScaleDetails() -> [Scale]? {
        return loadJSON(fromFile: "weightGurusScales", type: [Scale].self)
    }
    
    internal  func readMonitorDetails() -> [MonitorType]? {
        return loadJSON(fromFile: "balanceHealthDevices", type: [MonitorType].self)
    }
}
