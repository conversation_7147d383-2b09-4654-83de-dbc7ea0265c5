//
//  MeasurementEntryRepository.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 23/01/25.
//

import CoreData
import Combine

class MeasurementEntryRepository: MeasurementEntryProtocol {
    private let viewContext: NSManagedObjectContext
    private let logger: AppLogger = AppLogger(category: String(describing: MeasurementEntryRepository.self))
    
    init(viewContext: NSManagedObjectContext) {
        self.viewContext = viewContext
    }
    
    func fetchAllMeasurementEntries() -> [MeasurementEntry] {
        let fetchRequest: NSFetchRequest<MeasurementEntry> = MeasurementEntry.fetchRequest()
        fetchRequest.sortDescriptors = [NSSortDescriptor(keyPath: \MeasurementEntry.entryTimestamp, ascending: false)]
        
        do {
            let entries = try viewContext.fetch(fetchRequest)
            return entries
        } catch {
            logger.error(error: NSError(domain: String(describing: MeasurementEntryRepository.self), code: 1, userInfo: [NSLocalizedDescriptionKey: "Failed to fetch measurement entries: \(error.localizedDescription)"]), function: #function, line: #line)
            return []
        }
    }
    
    func saveMeasurementEntry(_ operation: Operation, appCategory: String?, broadcastId: String, deviceSKU: String?) -> Bool {
        
        if appCategory == AppCategory.balanceHealth.rawValue {
            guard (operation.systolic ?? 0 > 0) || (operation.diastolic ?? 0 > 0) else {
                return false
            }
        } else {
            guard (appCategory == AppCategory.sage.rawValue && (operation.weight ?? 0) > 0) ||
                    (appCategory == AppCategory.weightGurus.rawValue && (operation.weight ?? 0) > 0) ||
                    (appCategory == AppCategory.smartBaby.rawValue && (operation.weight ?? 0) > 0) ||
                    (appCategory == AppCategory.rpm.rawValue && (
                        (operation.temperature ?? 0) > 0 ||
                        ((operation.pulse ?? 0) > 0 || (operation.bgm ?? 0) > 0) ||
                        (operation.spo ?? 0) > 0 || (operation.weight ?? 0) > 0
                    )) else {
                return false
            }
        }
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        let formattedTimestamp = dateFormatter.date(from: dateFormatter.string(from: operation.entryTimestamp))!
        let lowerBound = Calendar.current.date(byAdding: .second, value: -10, to: formattedTimestamp)!
        let fetchRequest: NSFetchRequest<MeasurementEntry> = MeasurementEntry.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "broadcastID == %@ AND entryTimestamp >= %@ AND entryTimestamp <= %@",
                                             broadcastId,
                                             lowerBound as NSDate,
                                             formattedTimestamp as NSDate)
        
        do {
            let existingEntries = try viewContext.fetch(fetchRequest)
            if !existingEntries.isEmpty {
                return false
            }
        } catch {
            logger.error(error: NSError(domain: String(describing: MeasurementEntryRepository.self),
                                        code: 3,
                                        userInfo: [NSLocalizedDescriptionKey: "Failed to check existing entries: \(error.localizedDescription)"]),
                         function: #function,
                         line: #line)
            return false
        }
        
        let entry = MeasurementEntry(context: viewContext)
        entry.id = operation.id.uuidString
        entry.weight = operation.weight ?? 0
        entry.entryTimestamp = operation.entryTimestamp
        entry.bodyFat = Int32(operation.bodyFat ?? 0)
        entry.muscleMass = Int32(operation.muscleMass ?? 0)
        entry.boneMass = Int32(operation.boneMass ?? 0)
        entry.water = Int32(operation.water ?? 0)
        entry.bmi = Float(operation.bmi ?? 0)
        entry.unit = operation.measurementUnit?.rawValue ?? "Unknown"
        entry.impedance = Int32(operation.impedance ?? 0)
        entry.pulse = Int32(operation.pulse ?? 0)
        entry.visceralFatLevel = Int32(operation.visceralFatLevel ?? 0)
        entry.subcutaneousFatPercent = Int32(operation.subcutaneousFatPercent ?? 0)
        entry.proteinPercent = Int32(operation.proteinPercent ?? 0)
        entry.skeletalMusclePercent = Int32(operation.skeletalMusclePercent ?? 0)
        entry.bmr = Int32(operation.bmr ?? 0)
        entry.metabolicAge = Int32(operation.metabolicAge ?? 0)
        entry.diastolic = Int32(operation.diastolic ?? 0)
        entry.spo = Int32(operation.spo ?? 0)
        entry.systolic = Int32(operation.systolic ?? 0)
        entry.meanPressure = Int32(operation.meanPressure ?? 0)
        entry.bgm = Int32(operation.bgm ?? 0)
        entry.temperature = Float(operation.temperature ?? 0)
        entry.source = operation.source?.rawValue
        entry.appCategory = appCategory ?? AppCategory.all.rawValue
        entry.broadcastID = broadcastId
        entry.broadcastIDString = operation.broadcastIdString
        entry.protocolType = operation.protocolType
        entry.weightInKg = Float(operation.weightInKg ?? 0)
        entry.deviceSKU = operation.deviceSKU
        entry.pulseAmplitudeIndex = operation.pulseAmplitudeIndex ?? 0.0
        
        do {
            try viewContext.save()
            logger.info(info: "Measurement entry saved successfully", function: #function, line: #line)
            return true
        } catch {
            logger.error(error: NSError(domain: String(describing: MeasurementEntryRepository.self), code: 2, userInfo: [NSLocalizedDescriptionKey: "Failed to save measurement entry: \(error.localizedDescription)"]), function: #function, line: #line)
            return false
        }
    }
    
    func deleteMeasurementEntry(entryTimestamp: Date) {
        let context = viewContext
        let fetchRequest: NSFetchRequest<MeasurementEntry> = MeasurementEntry.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "entryTimestamp == %@", entryTimestamp as NSDate)
        
        do {
            let entries = try context.fetch(fetchRequest)
            if let entryToDelete = entries.first {
                context.delete(entryToDelete)
                try context.save()
                logger.info(info: "Successfully deleted entry with timestamp \(entryTimestamp).", function: #function, line: #line)
            } else {
                logger.info(info: "No entry found with timestamp \(entryTimestamp).", function: #function, line: #line)
            }
        } catch {
            logger.error(error: NSError(domain: String(describing: MeasurementEntryRepository.self), code: 3, userInfo: [NSLocalizedDescriptionKey: "Error deleting entry: \(error.localizedDescription)"]), function: #function, line: #line)
        }
    }
    
    func fetchMeasurementEntriesWithBroadcastId(forBroadcastId broadcastId: String) -> [MeasurementEntry] {
        let fetchRequest: NSFetchRequest<MeasurementEntry> = MeasurementEntry.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "broadcastID == %@", broadcastId)
        fetchRequest.sortDescriptors = [NSSortDescriptor(keyPath: \MeasurementEntry.entryTimestamp, ascending: false)]
        
        do {
            let entries = try viewContext.fetch(fetchRequest)
            return entries
        } catch {
            logger.error(error: NSError(domain: String(describing: MeasurementEntryRepository.self), code: 5, userInfo: [NSLocalizedDescriptionKey: "Failed to fetch measurement entries for broadcast ID \(broadcastId): \(error.localizedDescription)"]), function: #function, line: #line)
            return []
        }
    }
}
