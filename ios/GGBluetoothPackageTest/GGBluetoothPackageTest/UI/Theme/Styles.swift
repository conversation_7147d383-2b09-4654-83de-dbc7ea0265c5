//
//  Styles.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 08/01/25.
//

import SwiftUI

struct ToastModifier: ViewModifier {
  
  @Binding var toast: Toast?
  @State private var workItem: DispatchWorkItem?
  
  func body(content: Content) -> some View {
    content
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .overlay(
        ZStack {
          mainToastView()
            .offset(y: 32)
        }.animation(.spring(), value: toast)
      )
      .onChange(of: toast) { value in
        showToast()
      }
  }
  
  @ViewBuilder func mainToastView() -> some View {
    if let toast = toast {
      VStack {
        ToastView(
          style: toast.style,
          message: toast.message,
          width: toast.width
        ) {
          dismissToast()
        }
        Spacer()
      }
    }
  }
  
  private func showToast() {
    guard let toast = toast else { return }
    
    UIImpactFeedbackGenerator(style: .light)
      .impactOccurred()
    
    if toast.duration > 0 {
      workItem?.cancel()
      
      let task = DispatchWorkItem {
        dismissToast()
      }
      
      workItem = task
      DispatchQueue.main.asyncAfter(deadline: .now() + toast.duration, execute: task)
    }
  }
  
  private func dismissToast() {
    withAnimation {
      toast = nil
    }
    
    workItem?.cancel()
    workItem = nil
  }
}

/// A custom `Shape` that allows rounding only specific corners of a rectangle.
/// Use this when the default `cornerRadius` is insufficient for layout or design needs.

struct RoundedCorner: Shape {
  var radius: CGFloat = .infinity
  var corners: UIRectCorner = .allCorners
   
  func path(in rect: CGRect) -> Path {
    let path = UIBezierPath(
      roundedRect: rect,
      byRoundingCorners: corners,
      cornerRadii: CGSize(width: radius, height: radius)
    )
    return Path(path.cgPath)
  }
}

struct WiggleIfNeeded: ViewModifier {
    let shouldWiggle: Bool
    /// Conditionally applies the wiggle animation modifier based on `shouldWiggle`.
    /// - Parameter shouldWiggle: If true, applies the `.wiggling()` modifier.
    func body(content: Content) -> some View {
        if shouldWiggle {
            content.wiggling()
        } else {
            content
        }
    }
}

struct WiggleModifier: ViewModifier {
    @State private var isWiggling = false

    /// Produces a randomized interval for the wiggle animation, adding slight variance to the timing.
    /// - Parameters:
    ///   - interval: Base interval.
    ///   - variance: Amount of variance to add/subtract.
    /// - Returns: A `TimeInterval` with variance applied.
    private static func randomize(interval: TimeInterval, withVariance variance: Double) -> TimeInterval {
        let random = (Double(arc4random_uniform(1000)) - 500.0) / 500.0
        return interval + variance * random
    }
    
    /// Animation for the rotation (wiggle) effect.
    private let rotateAnimation = Animation
        .easeInOut(
            duration: WiggleModifier.randomize(
                interval: 0.14,
                withVariance: 0.025
            )
        )
        .repeatForever(autoreverses: true)
    
    /// Animation for the vertical bounce effect.
    private let bounceAnimation = Animation
        .easeInOut(
            duration: WiggleModifier.randomize(
                interval: 0.18,
                withVariance: 0.025
            )
        )
        .repeatForever(autoreverses: true)
    
    func body(content: Content) -> some View {
        content
            .rotationEffect(.degrees(isWiggling ? 2.0 : 0))
            .animation(rotateAnimation, value: isWiggling)
            .offset(x: 0, y: isWiggling ? 2.0 : 0)
            .animation(bounceAnimation, value: isWiggling)
            .onAppear { isWiggling = true }
            .onDisappear { isWiggling = false }
    }
}

extension View {
   
    /// Adds a toast overlay using the given `Toast?` binding.
    /// - Parameter toast: Binding to an optional toast.
    /// - Returns: View with toast applied.
    func toastView(toast: Binding<Toast?>) -> some View {
        self.modifier(ToastModifier(toast: toast))
    }

    /// Rounds specific corners of a view.
    /// - Parameters:
    ///   - radius: Corner radius value.
    ///   - corners: Corners to apply rounding to.
    /// - Returns: View with selected corners rounded.
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
    
    /// Applies a wiggle animation to the view.
    /// - Returns: A view that wiggles (rotates and bounces) indefinitely.
    func wiggling() -> some View {
        modifier(WiggleModifier())
    }
}
