//
//  Color.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 22/04/25.
//

import SwiftUI

extension Color {
    static let lightBlue = Color(red: 0.85, green: 0.91, blue: 0.98)
    static let lightOrange = Color(red: 235 / 255, green: 153 / 255, blue: 39 / 255) 
    static let darkOrange = Color(red: 226 / 255, green: 98 / 255, blue: 3 / 255)
    static let brown = Color(red: 132 / 255, green: 0 / 255, blue: 10 / 255)
}
