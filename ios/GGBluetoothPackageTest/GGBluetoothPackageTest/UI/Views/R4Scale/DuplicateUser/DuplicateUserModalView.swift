//
//  DuplicateUserModalView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 21/04/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct DuplicateUserModalView: View {
    let user: GGBTUser
    let onEditName: () -> Void
    let onRestore: () -> Void

    var body: some View {
        VStack{
            Spacer()
                .frame(maxHeight: 30)
            ModalContainer {
                VStack(alignment: .leading, spacing: 15) {
                    Text(DuplicateUserText.title)
                        .fontWeight(.bold)
                        .font(.title3)
                    
                    Text(DuplicateUserText.subtitle)
                        .font(.subheadline)
                        .fontWeight(.light)
                        .padding(.top, 2)
                    
                    userInfoSection
                    
                    actionButtons
                }
            }
            Spacer()
        }
    }

    private var userInfoSection: some View {
        VStack(alignment: .leading, spacing: 5) {
            Text(user.name)
                .font(.subheadline)
                .fontWeight(.semibold)

            Text("\(DuplicateUserText.lastActivePrefix) \(formatLastActiveTimestamp(user.lastActive))")
                .font(.caption)

            Text(DuplicateUserText.errorMessage)
                .foregroundColor(.red)
                .font(.caption)
                .padding(.top, 5)
        }
        .padding(.vertical)
        .padding(.trailing,60)
        .padding(.leading,10)
        .frame(maxWidth: .infinity)
        .background(Color(.systemGray6))
        .cornerRadius(5)
    }

    private var actionButtons: some View {
        HStack {
            PrimaryRoundedButtonView(title: DuplicateUserText.editButtonTitle, action: onEditName)
            Spacer()
            PrimaryRoundedButtonView(title: DuplicateUserText.restoreButtonTitle, action: onRestore)
        }
        .padding(.vertical)
    }
}

