//
//  ScaleConfigView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 15/04/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct ScaleConfigView: View {
    let device: GGBTDevice
    @StateObject private var viewModel: ScaleConfigViewModel
    
    init(device: GGBTDevice) {
        self.device = device
        _viewModel = StateObject(wrappedValue: ScaleConfigViewModel(device: device))
    }
    
    var body: some View {
        VStack {
            List {
                LabelValuePairView(
                    key: Text(CommonStrings.unit.capitalized),
                    value: Picker("", selection: $viewModel.unitSelection) {
                        ForEach(viewModel.unitOptions, id: \.self) { Text($0).tag($0) }
                    }
                        .pickerStyle(MenuPickerStyle())
                        .onChange(of: viewModel.unitSelection) { _ in
                            viewModel.updateUnitSelectionManuallyIfNeeded()
                        }
                )
                
                LabelValuePairView(
                    key: Text(ScaleConfigStrings.timeFormat),
                    value: Picker("", selection: $viewModel.timeFormatSelection) {
                        ForEach(viewModel.timeFormatOptions, id: \.self) { Text($0).tag($0) }
                    }.pickerStyle(MenuPickerStyle())
                )
                
                LabelValuePairView(
                    key: Text(ScaleConfigStrings.clearData),
                    value: Picker("", selection: $viewModel.clearDataSelection) {
                        ForEach(viewModel.clearDataOptions, id: \.self) { Text($0).tag($0) }
                    }.pickerStyle(MenuPickerStyle())
                )
                
                LabelValuePairView(
                    key: Text(ScaleConfigStrings.firmwareUpgrade),
                    value: Picker("", selection: $viewModel.firmwareUpgradeSelection) {
                        ForEach(viewModel.firmwareUpgradeOptions, id: \.self) { Text($0).tag($0) }
                    }
                        .pickerStyle(MenuPickerStyle())
                        .onChange(of: viewModel.firmwareUpgradeSelection, perform: viewModel.handleFirmwareUpgradeSelection)
                )
                .disabled(!viewModel.isWifiConfigured)
                .onTapGesture {
                    if !viewModel.isWifiConfigured {
                        viewModel.showWifiSettingsToast()
                    }
                }
                
                if viewModel.firmwareUpgradeSelection == "Schedule" {
                    LabelValuePairView(
                        key: Text(ScaleConfigStrings.scheduleTime),
                        value: Button(viewModel.scheduledTimeString.isEmpty ? ScaleConfigStrings.pickDateTime : viewModel.scheduledTimeString) {
                            viewModel.showDateTimePicker = true
                        }
                            .padding(5)
                            .foregroundColor(Color.blue)
                    )
                }
                
                Toggle(ScaleConfigStrings.impedanceMeasurement, isOn: $viewModel.impedanceMeasurement)
                    .tint(.blue)
                
                Toggle(ScaleConfigStrings.sessionImpedance, isOn: $viewModel.sessionImpedance)
                    .tint(.blue)
                    .disabled(!viewModel.impedanceMeasurement)
                
                Toggle(ScaleConfigStrings.heartRateMeasurement, isOn: $viewModel.heartRateMeasurement)
                    .tint(.blue)
                
                Toggle(ScaleConfigStrings.initialLogoAnimation, isOn: $viewModel.initialLogoAnimation)
                    .tint(.blue)
                
                Toggle(ScaleConfigStrings.finalLogoAnimation, isOn: $viewModel.finalLogoAnimation)
                    .tint(.blue)
                
                Button(action: {
                    if viewModel.isWifiConfigured {
                        viewModel.firmwareUpgrade(device: device)
                    }
                }) {
                    LabelValuePairView(
                        key: Text(ScaleConfigStrings.startFirmwareUpgrade).foregroundColor(viewModel.isWifiConfigured ? .black : .gray),
                        value: Image(systemName: "arrow.clockwise").foregroundColor(viewModel.isWifiConfigured ? .blue : .gray)
                    )
                }
                .disabled(!viewModel.isWifiConfigured)
                .onTapGesture {
                    if !viewModel.isWifiConfigured {
                        viewModel.showWifiSettingsToast()
                    }
                }
                
                Button(action: {
                    if viewModel.isWifiConfigured {
                        viewModel.restoreFirmware(device: device)
                    }
                }) {
                    LabelValuePairView(
                        key: Text(ScaleConfigStrings.restoreFirmware).foregroundColor(viewModel.isWifiConfigured ? .black : .gray),
                        value: Image(systemName: "arrow.clockwise").foregroundColor(viewModel.isWifiConfigured ? .blue : .gray)
                    )
                }
                .disabled(!viewModel.isWifiConfigured)
                .onTapGesture {
                    if !viewModel.isWifiConfigured {
                        viewModel.showWifiSettingsToast()
                    }
                }
                
                Button(action: {
                    viewModel.factoryReset(device: device)
                }) {
                    LabelValuePairView(
                        key: Text(ScaleConfigStrings.restoreFactorySettings),
                        value: Image(systemName: "arrow.clockwise")
                            .foregroundColor(.blue)
                    )
                }
            }
            .listStyle(.plain)
            .contentMargins(.init(rawValue: 0), 0, for: .scrollContent)
            .listStyle(.grouped)
        }
        .onAppear{
            viewModel.loadDeviceSettings()
        }
        .bottomSheet(isPresented: $viewModel.showDateTimePicker) {
            VStack {
                Text(ScaleConfigStrings.selectDateTime)
                    .font(.headline)
                    .padding()
                
                DatePicker(ScaleConfigStrings.scheduleTime, selection: $viewModel.selectedDateTime, displayedComponents: [.date, .hourAndMinute])
                    .datePickerStyle(.compact)
                    .labelsHidden()
                    .padding()
                
                Button(CommonStrings.confirm) {
                    viewModel.confirmScheduleTime()
                }
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .padding()
        }
    }
}

struct ScaleConfigView_Previews: PreviewProvider {
    static var previews: some View {
        ScaleConfigView(device: GGBTDevice(name: "", broadcastId: "", password: "", token: "", userNumber: 0, preference: nil, syncAllData: false, batteryLevel: 0, protocolType: "", macAddress: ""))
    }
}
