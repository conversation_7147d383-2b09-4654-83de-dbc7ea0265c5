//
//  ScaleConfigViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 16/04/25.
//

import SwiftUI
@preconcurrency import GGBluetoothSwiftPackage
import Combine

class ScaleConfigViewModel: ObservableObject {
    let device: GGBTDevice
    private var isUpdatingFromLiveMeasurement = false
    private let logger: AppLogger = AppLogger(category: String(describing: ScaleConfigViewModel.self))
    var commonLang = CommonStrings.self
    var toastLang = ToastMessageStrings.self
    init(device: GGBTDevice) {
        self.device = device
    }
    @Published var isWifiConfigured: Bool = false
    @Published var unitSelection = ScaleConfigStrings.unitKg {
        didSet {
            if unitSelection != oldValue {
                updateUnitSetting()
                ToastService.shared.presentToast(with: String(format: toastLang.unitUpdated, unitSelection))
            }
        }
    }
    
    @Published var timeFormatSelection = ScaleConfigStrings.timeFormat24H {
        didSet {
            if timeFormatSelection != oldValue {
                bluetoothService.updateDeviceSetting(device, key: .TIME_FORMAT, value: .string(timeFormatSelection))
                ToastService.shared.presentToast(with: String(format: toastLang.timeFormatUpdated, timeFormatSelection))
            }
        }
    }
    
    @Published var clearDataSelection = ScaleConfigStrings.clearSelect {
        didSet {
            let key: GGBTSettingType?
            
            switch clearDataSelection {
            case ScaleConfigStrings.clearAll:
                key = .CLEAR_ALL_DATA
            case ScaleConfigStrings.clearUser:
                key = .CLEAR_USER_ACCOUNT_DATA
            case ScaleConfigStrings.clearHistory:
                key = .CLEAR_HISTORY_DATA
            case ScaleConfigStrings.clearWiFi:
                key = .CLEAR_WIFI_NETWORK_DATA
            case ScaleConfigStrings.clearSetting:
                key = .CLEAR_SETTINGS_DATA
            default:
                key = nil
            }
            
            if let key = key {
                bluetoothService.updateDeviceSetting(device, key: key, value: .bool(true))
                ToastService.shared.presentToast(with: String(format: toastLang.clearData, clearDataSelection.lowercased()))
                
            }
        }
    }
    
    @Published var firmwareUpgradeSelection = ScaleConfigStrings.firmwareNow {
        didSet {
            if firmwareUpgradeSelection != oldValue {
                handleFirmwareUpgradeSelection(firmwareUpgradeSelection)
                ToastService.shared.presentToast(with: String(format: toastLang.firmwareUpgradeSet, firmwareUpgradeSelection))
            }
        }
    }
    
    @Published var impedanceMeasurement = false {
        didSet {
            if impedanceMeasurement != oldValue {
                bluetoothService.updateDeviceSetting(device, key: .IMPEDANCE, value: .bool(impedanceMeasurement))
                ToastService.shared.presentToast(with: String(format: toastLang.impedanceMeasurement, impedanceMeasurement ? commonLang.enabled : commonLang.disabled))
            }
        }
    }
    
    @Published var sessionImpedance = false {
        didSet {
            if sessionImpedance != oldValue {
                bluetoothService.updateDeviceSetting(device, key: .SESSION_IMPEDANCE, value: .bool(sessionImpedance))
                ToastService.shared.presentToast(with: String(format: toastLang.sessionImpedance, sessionImpedance ? commonLang.enabled : commonLang.disabled))
            }
        }
    }
    
    @Published var heartRateMeasurement = false {
        didSet {
            if heartRateMeasurement != oldValue {
                bluetoothService.updateDeviceSetting(device, key: .HEART_RATE, value: .bool(heartRateMeasurement))
                ToastService.shared.presentToast(with: String(format: toastLang.heartRateMeasurement, heartRateMeasurement ? commonLang.enabled : commonLang.disabled))
            }        }
    }
    
    @Published var initialLogoAnimation = false {
        didSet {
            if initialLogoAnimation != oldValue {
                bluetoothService.updateDeviceSetting(device, key: .INITIAL_LOGO_ANIM, value: .bool(initialLogoAnimation))
                ToastService.shared.presentToast(with: String(format: toastLang.initialLogoAnimation, initialLogoAnimation ? commonLang.enabled : commonLang.disabled))
            }
        }
    }
    
    @Published var finalLogoAnimation = false {
        didSet {
            if finalLogoAnimation != oldValue {
                bluetoothService.updateDeviceSetting(device, key: .FINAL_LOGO_ANIM, value: .bool(finalLogoAnimation))
                ToastService.shared.presentToast(with: String(format: toastLang.finalLogoAnimation, finalLogoAnimation ? commonLang.enabled : commonLang.disabled))
            }
        }
    }
    
    @Published var showDateTimePicker = false
    @Published var selectedDateTime = Date()
    @Published var scheduledTimeString: String = ""
    private var cancellables = Set<AnyCancellable>()
    @Injector var bluetoothService: BluetoothService
    
    let unitOptions = [ScaleConfigStrings.unitKg, ScaleConfigStrings.unitLb]
    let timeFormatOptions = [ScaleConfigStrings.timeFormat12H, ScaleConfigStrings.timeFormat24H]
    let clearDataOptions = [ScaleConfigStrings.clearSelect, ScaleConfigStrings.clearAll, ScaleConfigStrings.clearUser, ScaleConfigStrings.clearHistory, ScaleConfigStrings.clearWiFi, ScaleConfigStrings.clearSetting]
    let firmwareUpgradeOptions = [ScaleConfigStrings.firmwareNow, ScaleConfigStrings.firmwareSchedule]
    
    func loadDeviceSettings() {
        Task {
            let details = await bluetoothService.getDeviceInfo(device)
            DispatchQueue.main.async {
                self.impedanceMeasurement = details.impedanceSwitchState ?? false
                self.sessionImpedance = details.sessionImpedanceSwitchState ?? false
                self.heartRateMeasurement = details.heartRateState ?? false
                self.initialLogoAnimation = details.startAnimationState ?? false
                self.finalLogoAnimation = details.endAnimationState ?? false
                self.isWifiConfigured = details.isWifiConfigured ?? false
                self.sinkLiveMeasurementSubject()
                
            }
        }
    }
    
    func sinkLiveMeasurementSubject() {
        bluetoothService.liveMeasurementSubject
            .first()
            .sink { [weak self] liveEntry in
                guard let self = self else { return }
                DispatchQueue.main.async {
                    self.isUpdatingFromLiveMeasurement = true
                    defer { self.isUpdatingFromLiveMeasurement = false }
                    
                    if let unit = liveEntry.unit {
                        switch unit.lowercased() {
                        case MeasurementUnit.kg.rawValue:
                            self.unitSelection = ScaleConfigStrings.unitKg
                        case MeasurementUnit.lb.rawValue:
                            self.unitSelection = ScaleConfigStrings.unitLb
                        default:
                            let nsError = NSError(domain: String(describing: DeviceService.self), code: 1, userInfo: [NSLocalizedDescriptionKey: ErrorStrings.unknownUnitReceived])
                            self.logger.error(error: nsError, function: #function, line: #line)
                        }
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    func updateUnitSelectionManuallyIfNeeded() {
        if !isUpdatingFromLiveMeasurement {
            updateUnitSetting()
        }
    }
    
    private func updateUnitSetting() {
        let measurementUnit: MeasurementUnit = (unitSelection == ScaleConfigStrings.unitKg) ? .kg : .lb
        bluetoothService.updateUnit(device, unit: measurementUnit)
    }
    
    func handleFirmwareUpgradeSelection(_ newValue: String) {
        if newValue == ScaleConfigStrings.firmwareSchedule {
            showDateTimePicker = true
        }
    }
    
    func confirmScheduleTime() {
        let formatter = DateFormatter()
        formatter.dateFormat = ScaleConfigStrings.dateTimeFormat
        scheduledTimeString = formatter.string(from: selectedDateTime)
        showDateTimePicker = false
        ToastService.shared.presentToast(with: String(format: toastLang.firmwareUpgradeScheduled, scheduledTimeString))    }
    
    func firmwareUpgrade(device: GGBTDevice) {
        var timestamp: Int64
        
        if firmwareUpgradeSelection == ScaleConfigStrings.firmwareNow {
            timestamp = Int64(Date().timeIntervalSince1970)
            ToastService.shared.presentToast(with: toastLang.firmwareUpgradeStartedNow)
        } else if let scheduledTimestamp = scheduledTimestamp() {
            timestamp = scheduledTimestamp
            ToastService.shared.presentToast(with: toastLang.firmwareUpgradeScheduledNow)
        } else {
            ToastService.shared.presentToast(with: toastLang.invalidScheduledTime)
            return
        }
        bluetoothService.startFirmwareUpdate(device, timestamp)
    }
    
    private func scheduledTimestamp() -> Int64? {
        return Int64(selectedDateTime.timeIntervalSince1970)
    }
    
    func restoreFirmware(device: GGBTDevice) {
        bluetoothService.updateDeviceSetting(device, key: .RESET_FIRMWARE, value: .bool(true))
        ToastService.shared.presentToast(with: toastLang.firmwareRestored)
    }
    
    func factoryReset(device: GGBTDevice) {
        bluetoothService.updateDeviceSetting(device, key: .RESTORE_FACTORY, value: .bool(true))
        ToastService.shared.presentToast(with: toastLang.factoryResetCompleted)
    }
    
    func showWifiSettingsToast() {
        ToastService.shared.presentToast(with: toastLang.wifiSettingsIncomplete)
    }
    
}
