//
//  WifiCredentialsFormView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 08/04/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct WifiCredentialsFormView: View {
    @ObservedObject var viewModel: WifiCredentialsViewModel
    @Binding var showLoader: Bo<PERSON>
    @Binding var showSuccess: Bool
    @Binding var showFailure: Bool
    @Binding var errorCode: String
    
    let selectedSSID: String
    let device: GGBTDevice
    let category: AppCategory
    var onBack: (() -> Void)?
    let lang = WifiSetupStrings.self
    
    var body: some View {
        VStack {
            Spacer()
                .frame(height: 50)
            
            VStack(alignment: .leading, spacing: 20) {
                Text(lang.enterWifiPassword)
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.black)
                    .padding(.top)
                    .padding(.horizontal)
                
                HStack {
                    Image(systemName: "wifi")
                        .foregroundColor(.blue)
                    Text(selectedSSID)
                        .font(.headline)
                        .foregroundColor(.black)
                }
                .padding(10)
                .frame(maxWidth: .infinity)
                .background(Color.gray.opacity(0.3))
                .cornerRadius(8)
                .padding(.horizontal, 30)
                
                Text(lang.password)
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .padding(.horizontal)
                
                HStack {
                    if viewModel.isPasswordVisible {
                        TextField(lang.enterPassword, text: $viewModel.password)
                            .disabled(viewModel.isNoPassword)
                            .opacity(viewModel.isNoPassword ? 0.5 : 1.0)
                    } else {
                        SecureField(lang.enterPassword, text: $viewModel.password)
                            .disabled(viewModel.isNoPassword)
                            .opacity(viewModel.isNoPassword ? 0.5 : 1.0)
                    }
                    
                    Button {
                        viewModel.isPasswordVisible.toggle()
                    } label: {
                        Image(systemName: viewModel.isPasswordVisible ? "eye.slash.fill" : "eye.fill")
                            .foregroundColor(.blue)
                    }
                    .disabled(viewModel.isNoPassword)
                    .opacity(viewModel.isNoPassword ? 0.5 : 1.0)
                }
                .padding(.horizontal)
                .padding(.bottom, 5)
                
                HStack {
                    Text(lang.noPasswordToggle)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                    Spacer()
                    Toggle("", isOn: $viewModel.isNoPassword)
                }
                .padding(.horizontal)
                
                HStack {
                    Button(lang.back) {
                        onBack?()
                    }
                    .padding()
                    .foregroundColor(.blue)
                    
                    Spacer()
                    
                    Button(lang.connect) {
                        showLoader = true
                        viewModel.connectToWifi(device: device, ssid: selectedSSID, category: category) { success, code in
                            if success {
                                showSuccess = true
                                showFailure = false
                            } else {
                                errorCode = code ?? "Unknown"
                                showFailure = true
                                showSuccess = false
                            }
                        }
                    }
                    .padding(5)
                    .padding(.horizontal)
                    .foregroundColor(.white)
                    .background(Color.blue)
                    .cornerRadius(30)
                }
            }
            .padding()
            .padding(.bottom , 20)
            .background(Color.white)
            .cornerRadius(15)
            .shadow(radius: 5)
            .padding(.horizontal, 20)
            
            Spacer()
        }
        .onTapGesture {
            hideKeyboard()
        }
    }
}
