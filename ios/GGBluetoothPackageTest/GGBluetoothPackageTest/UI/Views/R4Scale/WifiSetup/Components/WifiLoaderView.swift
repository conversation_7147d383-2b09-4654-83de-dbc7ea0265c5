//
//  WifiLoaderView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 04/04/25.
//

import SwiftUI

struct WifiLoaderView: View {
    @State private var isAnimating = false
    let lang = WifiSetupStrings.self
    var body: some View {
        VStack {
            Text(lang.gatheringNetworks)
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.black)
                .padding(.bottom, 30)
            
            ZStack {
                Circle()
                    .fill(Color.lightBlue)
                    .frame(width: isAnimating ? 140 : 110, height: isAnimating ? 140 : 110)
                    .animation(Animation.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: isAnimating)
                
                Circle()
                    .fill(Color.blue)
                    .frame(width: 80, height: 80)
                
                VStack(spacing: 4) {
                    Image(systemName: "wifi")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 50, height: 50)
                        .foregroundColor(.white)
                        .opacity(isAnimating ? 1 : 0.5)
                        .animation(Animation.easeInOut(duration: 0.8).repeatForever(autoreverses: true).delay(0), value: isAnimating)
                }
            }
        }
        .onAppear {
            isAnimating = true
        }
    }
}

#Preview {
    WifiLoaderView()
}
