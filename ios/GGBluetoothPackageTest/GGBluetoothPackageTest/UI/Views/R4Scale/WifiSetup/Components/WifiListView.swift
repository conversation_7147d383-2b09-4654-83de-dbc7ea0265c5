//
//  WifiListView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 04/04/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct WifiListView: View {
    @EnvironmentObject var router: Router<DashboardRoutes>
    let wifiList: [String]
    let onSelect: (String) -> Void
    let onRefresh: () -> Void
    let device: GGBTDevice
    let category: AppCategory
    let lang = WifiSetupStrings.self
    let commonLang = CommonStrings.self
    
    var body: some View {
        VStack {
            Text(lang.multipleNetworksInfo)
                .font(.subheadline)
                .foregroundColor(.gray)
                .multilineTextAlignment(.leading)
                .padding(.horizontal)
                .padding(.top)
            
            List {
                ForEach(Array(wifiList.enumerated()), id: \.offset) { index, wifiName in
                    VStack {
                        HStack {
                            Image(systemName: "wifi")
                                .foregroundColor(.blue)
                            Text(wifiName)
                                .font(.body)
                            Spacer()
                            Button(action: {
                                onSelect(wifiName)
                            }) {
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.blue)
                            }
                        }
                        .padding(.vertical, 2)
                        .contentShape(Rectangle())
                        .onTapGesture {
                            router.navigate(to: .wifiCredentials(device, selectedSSID: wifiName, category))
                        }
                        Divider()
                    }
                }
                .listRowSeparator(.hidden)
            }
            .listStyle(PlainListStyle())
            
            Spacer().frame(maxHeight: 10)
            
            Button(action: onRefresh) {
                HStack {
                    Image(systemName: "arrow.clockwise")
                    Text(commonLang.refresh.uppercased())
                        .font(.body)
                }
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.blue)
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(15)
        .shadow(radius: 5)
        .padding(.horizontal, 20)
        .navigationTitle(commonLang.wifi)
        .navigationBarTitleDisplayMode(.inline)
        .environmentObject(router)
    }
}

#Preview {
    NavigationView {
        WifiListView(
            wifiList: [
                "GG_2G",
                "3diq2",
                "3diqwifi_1",
                "3diqAdmin",
                "SW_Workspace",
                "SW_Ground Floor",
                "Smart_users",
                "SW_Bharati Vilas",
                "SW_3F",
                "SW_1st Floor",
                "SW_2nd Floor",
                "SW_3rd Floor",
                "SW_TECH"
            ],
            onSelect: { wifi in
                print("Selected: \(wifi)")
            },
            onRefresh: {
                print("Refreshed")
            },
            device: DefaultDevice.ggbtDevice, category: .weightGurus
        )
        .environmentObject(Router<DashboardRoutes>())
    }
}
