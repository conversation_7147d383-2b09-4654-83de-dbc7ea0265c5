//
//  StatusIndicationLoaderViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 07/04/25.
//

import SwiftUI
import Combine

class StatusIndicationLoaderViewModel: ObservableObject {
    @Published var connectionState: ConnectionState = .loading
    @Published var dotScales = Array(repeating: 0.5, count: 5)
    @Published var isAnimating = false
    
    var dotColor: Color {
        switch connectionState {
        case .loading: return .blue
        case .success: return .green
        case .failure: return .red
        }
    }
    
    var shouldAnimate: Bool {
        connectionState == .loading
    }
    
    var connectionStateTitle: String {
        switch connectionState {
        case .loading: return "Connecting to Wi-Fi"
        case .success: return "Connected"
        case .failure: return "Connection Error"
        }
    }
    
    func onAppear() {
        if connectionState == .loading {
            startLoadingAnimation()
        }
    }
    
    func startLoadingAnimation() {
        guard !isAnimating else { return }
        isAnimating = true
        Timer.scheduledTimer(withTimeInterval: 0.6, repeats: true) { [weak self] timer in
            guard let self = self, self.connectionState == .loading else {
                timer.invalidate()
                return
            }
            withAnimation {
                for i in 0..<self.dotScales.count {
                    self.dotScales[i] = self.dotScales[i] == 1.2 ? 0.8 : 1.2
                }
            }
        }
    }
    
    func stopAnimation() {
        isAnimating = false
    }
    
    func connectionSuccessful() {
        stopAnimation()
        withAnimation {
            connectionState = .success
        }
    }
    
    func connectionFailed() {
        stopAnimation()
        withAnimation {
            connectionState = .failure
        }
    }
    
    func retryConnection() {
        withAnimation {
            connectionState = .loading
        }
        startLoadingAnimation()
    }
}

