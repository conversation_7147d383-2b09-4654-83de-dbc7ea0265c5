//
//  WifiCredentialsView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 04/04/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct WifiCredentialsView: View {
    @EnvironmentObject var router: Router<DashboardRoutes>
    @StateObject private var viewModel = WifiCredentialsViewModel()
    @State private var showLoader = false
    @State private var showSuccess = false
    @State private var showFailure = false
    @State private var errorCode = ""
    
    let selectedSSID: String
    let device: GGBTDevice
    let category: AppCategory
    let lang = WifiCredentialsStrings.self
    
    var body: some View {
        VStack {
            if showLoader {
                StatusIndicationLoaderView(
                    showSuccess: showSuccess,
                    showFailure: showFailure,
                    errorCode: errorCode,
                    tryAgainAction: {
                        showSuccess = false
                        showFailure = false
                        
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                            showLoader = false
                        }
                    }
                )
            } else {
                WifiCredentialsFormView(
                    viewModel: viewModel,
                    showLoader: $showLoader,
                    showSuccess: $showSuccess,
                    showFailure: $showFailure,
                    errorCode: $errorCode,
                    selectedSSID: selectedSSID,
                    device: device,
                    category: category,
                    onBack: {
                        router.navigateBack()
                    }
                )
            }
        }
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    showLoader ? router.navigateToRoot() : router.navigateBack(1)
                    
                    
                }) {
                    Image(systemName: "xmark")
                        .foregroundColor(.blue)
                }
            }
        }
        .navigationTitle(lang.wifiSetup)
        .navigationBarTitleDisplayMode(.inline)
    }
    
}

#Preview {
    WifiCredentialsView(selectedSSID: "3diqwifi_1", device: DefaultDevice.ggbtDevice, category: .weightGurus)
}
