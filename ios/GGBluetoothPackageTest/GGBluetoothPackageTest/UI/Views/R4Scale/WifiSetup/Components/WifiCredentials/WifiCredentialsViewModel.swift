//  WifiCredentialsViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 07/04/25.
//

import Foundation
import GGBluetoothSwiftPackage

class WifiCredentialsViewModel: ObservableObject {
    @Published var password = ""
    @Published var isPasswordVisible = false
    @Published var isNoPassword = false
    @Published var connectionState: ConnectionState = .loading
        
    @Injector var bluetoothService: BluetoothService
    @Injector var deviceService: DeviceService
    private let logger: AppLogger = AppLogger(category: String(describing: WifiCredentialsViewModel.self))
    let lang = ErrorStrings.self
    
    func connectToWifi(device: GGBTDevice, ssid: String, category: AppCategory, completion: @escaping (Bool, String?) -> Void)  {
        DispatchQueue.main.async {
            self.connectionState = .loading
        }
        
        DeviceLinkingHelper.shared.fetchUserLoginStatus(for: category, device: device) { [weak self] success in
            guard success, let self = self else {
                DispatchQueue.main.async {
                    self?.connectionState = .failure
                }
                completion(false, nil)
                return
            }
            
            if let fetchedDevice = self.deviceService.fetchDevice(by: device.broadcastId) {
                var updatedDevice = device
                updatedDevice.password = fetchedDevice.password ?? ""
                updatedDevice.token = fetchedDevice.token ?? ""
                
                let wifiPassword = self.isNoPassword ? "" : self.password
                Task {
                    let result = await self.bluetoothService.configureDeviceWifi(device: updatedDevice, ssid: ssid, password: wifiPassword)
                    await MainActor.run {
                        self.handleWifiConfigurationResponse(result, device: device, completion: completion)
                        
                    }
                }
            } else {
                DispatchQueue.main.async {
                    self.connectionState = .failure
                }
                completion(false, nil)
            }
        }
    }
    
    private func handleWifiConfigurationResponse(_ response: GGWifiSetupResponse, device: GGBTDevice, completion: @escaping (Bool, String?) -> Void) {
        DispatchQueue.main.async {
            if response.wifiState == GGWifiState.connected.rawValue {
                self.connectionState = .success
                self.deviceService.setIsWifiConfigured(for: device.broadcastId, isConfigured: true)
                completion(true, nil)
            } else {
                self.connectionState = .failure
                self.deviceService.setIsWifiConfigured(for: device.broadcastId, isConfigured: false)
                completion(false, response.errorCode)
            }
        }
    }
}

