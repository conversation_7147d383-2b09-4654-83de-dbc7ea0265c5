//
//  WifiMacAddressSheetView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 04/04/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct WifiMacAddressView: View {
    @EnvironmentObject var router: Router<DashboardRoutes>
    @StateObject var viewModel = WifiSetupViewModel()
    let device: GGBTDevice?
    let wifiMacAddress: String
    let lang = WifiSetupStrings.self
    let commonLang = CommonStrings.self
    let ToastLang = ToastMessageStrings.self
    
    var body: some View {
        VStack {
            Spacer().frame(height: 20)
            
            VStack(spacing: 20) {
                
                Text(lang.wifiMacAddressInfo)
                    .font(.title3)
                    .foregroundColor(.black)
                    .multilineTextAlignment(.leading)
                    .fontWeight(.bold)
                    .padding(.top, 30)
                
                Text(wifiMacAddress)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.black)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.gray.opacity(0.3))
                    .cornerRadius(8)
                    .padding(.horizontal, 30)
                
                But<PERSON>(action: {
                    UIPasteboard.general.string = wifiMacAddress
                    ToastService.shared.presentToast(with: ToastLang.copiedToClipboard, style: .info)
                }) {
                    Text(lang.copyMacAddress)
                        .font(.headline)
                        .foregroundColor(.blue)
                }
                .padding(.top, 10)
                
                Text(lang.wifiMacAddressInstructions)
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.leading)
                    .padding(.horizontal, 30)
                    .padding(.top, 20)
                    .padding(.bottom, 20)
                
            }
            .padding()
            .background(Color.white)
            .cornerRadius(15)
            .shadow(radius: 10)
            .padding(.horizontal, 20)
            
            Spacer()
            
        }
        .navigationBarBackButtonHidden(true)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .principal) {
                Text(commonLang.wifiMacAddress)
                    .font(.headline)
            }
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    router.navigateBack(1)
                }) {
                    Image(systemName: "xmark")
                        .foregroundColor(.blue)
                }
            }
        }
    }
}

#Preview {
    WifiMacAddressView(device: DefaultDevice.ggbtDevice, wifiMacAddress: "E0:E0:E0:E0:09:00")
        .environmentObject(ToastService.shared)
}
