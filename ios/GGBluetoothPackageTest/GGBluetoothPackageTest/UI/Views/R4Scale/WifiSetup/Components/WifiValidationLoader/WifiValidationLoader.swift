//
//  WifiValidationLoader.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 07/04/25.
//

import SwiftUI

struct StatusIndicationLoaderView: View {
    @StateObject private var viewModel = StatusIndicationLoaderViewModel()
    @EnvironmentObject var router: Router<DashboardRoutes>
    var showSuccess: Bool
    var showFailure: Bool
    var errorCode: String
    var tryAgainAction: (() -> Void)?
    var connectionStateTitle: String?
    var loaderImage: String?

    init(
        showSuccess: Bool = false,
        showFailure: Bool = false,
        errorCode: String = "",
        tryAgainAction: (() -> Void)? = nil,
        customTitle: String? = nil,
        loaderImage: String = "wifi"
    ) {
        self.showSuccess = showSuccess
        self.showFailure = showFailure
        self.errorCode = errorCode
        self.tryAgainAction = tryAgainAction
        self.connectionStateTitle = customTitle
        self.loaderImage = loaderImage
    }
    
    var body: some View {
        VStack(spacing: 20) {
            Text(connectionStateTitle ?? viewModel.connectionStateTitle)
                .font(.title)
                .fontWeight(.bold)
            
            if viewModel.connectionState == .failure {
                Text("Error code: \(errorCode)")
                    .font(.subheadline)
            }
            
            Image(AppAssets.wg0412)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 200, height: 200)
            
            VStack(spacing: 15) {
                ForEach(0..<5, id: \.self) { index in
                    if viewModel.connectionState == .success && index == 2 {
                        Image(systemName: "checkmark.circle.fill")
                            .resizable()
                            .frame(width: 30, height: 30)
                            .foregroundColor(.green)
                    } else if viewModel.connectionState == .failure && index == 2 {
                        Image(systemName: "xmark.circle.fill")
                            .resizable()
                            .frame(width: 30, height: 30)
                            .foregroundColor(.red)
                    } else {
                        Circle()
                            .fill(viewModel.dotColor)
                            .frame(width: 10, height: 10)
                            .scaleEffect(viewModel.connectionState == .loading ? viewModel.dotScales[index] : 1.0)
                            .animation(viewModel.shouldAnimate ?
                                .easeInOut(duration: 0.6).repeatForever(autoreverses: true).delay(Double(index) * 0.15) : .default,
                                       value: viewModel.dotScales[index])
                    }
                }
            }
            
            ZStack {
                Circle()
                    .fill(viewModel.connectionState == .failure ? Color.red : Color.blue)
                    .frame(width: 80, height: 80)
                
                Image(systemName: loaderImage ?? "wifi")
                    .resizable()
                    .frame(width: 50, height: loaderImage == "wifi" ? 35 : 50)
                    .foregroundColor(.white)
                    .opacity(viewModel.connectionState == .loading ? (viewModel.isAnimating ? 1 : 0.5) : 1.0)
                    .animation(viewModel.shouldAnimate ?
                        .easeInOut(duration: 0.8).repeatForever(autoreverses: true) : .default,
                               value: viewModel.isAnimating)
            }
            
            if viewModel.connectionState == .failure {
                Button("Try Again".uppercased()) {
                    viewModel.retryConnection()
                    tryAgainAction?()
                }
                .padding(5)
                .fontWeight(.semibold)
                .padding(.horizontal, 30)
                .foregroundColor(.white)
                .background(Color.blue)
                .cornerRadius(30)
                .padding(.top, 30)
            }
        }
        .onAppear {
            viewModel.onAppear()
        }
        .onChange(of: showSuccess) { newValue in
            if newValue {
                viewModel.connectionSuccessful()
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    router.navigateToRoot()
                }
            }
        }
        .onChange(of: showFailure) { newValue in
            if newValue {
                viewModel.connectionFailed()
            }
        }
    }
}

struct StatusIndicationLoaderView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            StatusIndicationLoaderView(
                showSuccess: false,
                showFailure: false,
                tryAgainAction: {},
                loaderImage: "person.fill.badge.plus"
            )
            .previewDisplayName("Loading")
            
            StatusIndicationLoaderView(
                showSuccess: true,
                showFailure: false,
                tryAgainAction: {}
            )
            .previewDisplayName("Success")
            
            StatusIndicationLoaderView(
                showSuccess: false,
                showFailure: true,
                errorCode: "E1005",
                tryAgainAction: {}
            )
            .previewDisplayName("Failure")
        }
    }
}
