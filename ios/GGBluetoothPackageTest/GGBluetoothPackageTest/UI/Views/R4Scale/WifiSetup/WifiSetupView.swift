//
//  WifiSetupView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 04/04/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct WifiSetupView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var router: Router<DashboardRoutes>
    @StateObject var viewModel = WifiSetupViewModel()
    @State private var showAlert = false
    let lang = WifiSetupStrings.self
    let commonLang = CommonStrings.self
    let device: GGBTDevice
    let category : AppCategory?
    
    var body: some View {
        VStack {
            if viewModel.isLoading {
                WifiLoaderView()
            } else {
                let wifiList = viewModel.wifiList.map { $0.ssid ?? "Unknown SSID" }
                let device = self.device
                WifiListView(
                    wifiList: wifiList,
                    onSelect: { wifi in
                        router.navigate(to: .wifiCredentials(device, selectedSSID: wifi, category ?? .weightGurus))
                    },
                    onRefresh: {
                        Task {
                             viewModel.getWifiList(device: device)
                        }
                    },
                    device: device,
                    category: category ?? .weightGurus
                )
            }
        }
        .navigationBarBackButtonHidden(true)
        .environmentObject(router)
        .onAppear {
            Task {
                viewModel.getWifiList(device: device)
            }
        }
        .toolbar {
            ToolbarItem(placement: .principal) {
                Text(commonLang.wifi)
                    .font(.headline)
            }
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    router.navigateBack()
                }) {
                    Image(systemName: "xmark")
                        .foregroundColor(.blue)
                }
            }
        }
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text(lang.exitAlertTitle),
                message: Text(lang.exitAlertMessage),
                primaryButton: .default(Text(lang.goBackButton)),
                secondaryButton: .destructive(Text(lang.exitButton)) {
                    presentationMode.wrappedValue.dismiss()
                }
            )
        }
    }
}

#Preview {
    WifiSetupView(device: DefaultDevice.ggbtDevice, category: .weightGurus)
        .environmentObject(Router<DashboardRoutes>())
}
