//
//  WifiSetupViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 04/04/25.
//

import GGBluetoothSwiftPackage
import Foundation

class WifiSetupViewModel: ObservableObject  {
    @Injector var bluetoothService: BluetoothService
    private let logger: AppLogger = AppLogger(category: String(describing: WifiSetupViewModel.self))
    
    @Published var isLoading = false
    @Published var wifiList: [GGWifiDetails] = []

func getWifiList(device: GGBTDevice) {
    
    Task {
        DispatchQueue.main.async {
            self.isLoading = true
        }

        do {
            let result = try await bluetoothService.getWifiList(device)

            DispatchQueue.main.async {
                if let wifiResponse = result as? GGWifiResponse<GGWifiDetails> {
                    self.wifiList = wifiResponse.wifi
                }
                self.isLoading = false
            }

        } catch {
            let nsError = NSError(domain: String(describing: WifiSetupViewModel.self),
                                  code: 2,
                                  userInfo: [NSLocalizedDescriptionKey: error.localizedDescription])
            self.logger.error(error: nsError, function: #function, line: #line)

            DispatchQueue.main.async {
                self.isLoading = false
            }
        }
    }
}

}

