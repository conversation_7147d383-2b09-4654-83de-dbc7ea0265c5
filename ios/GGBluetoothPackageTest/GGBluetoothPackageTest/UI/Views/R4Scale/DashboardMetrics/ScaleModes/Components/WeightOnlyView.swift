//
//  WeightOnlyView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 10/04/25.
//

import SwiftUI

struct WeightOnlyView: View {
    var body: some View {
        VStack(alignment: .center, spacing: 15) {
            
            HStack{
                Image(AppAssets.weightOnlyModeDiscoveredIcon)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 25, height: 25)
                
                Text(DisplayMetricsStrings.weightOnlyModeIndicator)
                    .font(.subheadline)
                    .foregroundColor(.black)
            }
            
            Image(AppAssets.scaleWeightMode)
                .resizable()
                .scaledToFit()
                .frame(width: 200, height: 200)
            
            Text(DisplayMetricsStrings.weightOnlyNote)
                .font(.subheadline)
                .multilineTextAlignment(.leading)
                .lineLimit(nil)
                .fixedSize(horizontal: false, vertical: true)
                .padding()
                .frame(maxWidth: .infinity)
                .background(Color(.systemGray6))
                .cornerRadius(10)

        }
        .padding(.horizontal)
        .padding(.top)
    }
}

#Preview {
    WeightOnlyView()
}

