//
//  ScaleModesViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 10/04/25.
//

import Foundation
import GGBluetoothSwiftPackage

class ScaleModesViewModel: ObservableObject {
    @Published var selectedMode: String = ScaleModes.allBodyMetrics.rawValue
    @Published var isHeartRateEnabled: Bool = false
    @Published var displayMetrics: [String] = []
    
    @Injector var userProfileUpdationService: UserProfileUpdationService
    @Injector var bluetoothService: BluetoothService
    @Injector var deviceService: DeviceService
    @Injector var r4ScaleService: R4ScaleService
    private let logger: AppLogger = AppLogger(category: String(describing: ScaleModesViewModel.self))
    var toastLang = ToastMessageStrings.self
    var defaultUser : String = "Default User"
    
    func fetchPreferences(for broadcastId: String) {
        if let preference = deviceService.fetchPreference(for: broadcastId) {
            DispatchQueue.main.async {
                self.selectedMode = preference.shouldMeasureImpedance ? ScaleModes.allBodyMetrics.rawValue : ScaleModes.weightOnly.rawValue
                self.isHeartRateEnabled = preference.shouldMeasurePulse
                self.displayMetrics = preference.displayMetrics as? [String] ?? []
            }
        } else {
            DispatchQueue.main.async {
                let nsError = NSError(domain: String(describing: ScaleModesViewModel.self), code: 1, userInfo: [NSLocalizedDescriptionKey: "\(self.toastLang.noPreferencesFound) \(broadcastId)"])
                self.logger.error(error: nsError, function: #function, line: #line)
            }
        }
    }
    
    func saveSettings(for device: GGBTDevice, category: AppCategory, isDeviceConnected: Bool) {
        let shouldMeasureImpedance = selectedMode != ScaleModes.weightOnly.rawValue
        let shouldMeasurePulse = isHeartRateEnabled
        let userName = device.preference?.displayName
        
        deviceService.savePreference(
            for: device.broadcastId,
            displayMetrics: displayMetrics,
            displayName: userName ?? "Unknown Device",
            shouldMeasureImpedance: shouldMeasureImpedance,
            shouldMeasurePulse: shouldMeasurePulse,
            timeFormat: TimeFormat.twelveHour.rawValue
        )
        
        if isDeviceConnected {
            guard let storedDevice = deviceService.fetchDevice(by: device.broadcastId) else {
                return
            }
            
            let updatedPreference = GGDevicePreference(
                displayName: userName,
                displayMetrics: displayMetrics,
                shouldMeasureImpedance: shouldMeasureImpedance,
                shouldMeasurePulse: shouldMeasurePulse,
                timeFormat: storedDevice.preference?.timeFormat ?? TimeFormat.twentyFourHour.rawValue
            )
            
            let updatedDevice = GGBTDevice(
                name: storedDevice.name ?? device.name,
                broadcastId: storedDevice.broadcastId ?? device.broadcastId,
                password: storedDevice.password ?? device.password,
                token: storedDevice.token ?? device.token,
                userNumber: Int(storedDevice.userNumber),
                preference: updatedPreference,
                syncAllData: true,
                batteryLevel: Int(storedDevice.batteryLevel),
                protocolType: storedDevice.protocolType ?? device.protocolType,
                macAddress: storedDevice.macAddress ?? device.macAddress
            )
            
            Task{
                do{
                    try await  bluetoothService.updateAccount(device: updatedDevice)
                } catch{
                    let nsError = NSError(domain: String(describing: DeviceService.self), code: 1, userInfo: [NSLocalizedDescriptionKey: "Error updating:  account \(error.localizedDescription)"])
                            self.logger.error(error: nsError, function: #function, line: #line)
                }
            }
        }
        Task{
            await storeR4ScalePreferenceForDevice(
                broadcastId: device.broadcastId,
                category: category,
                shouldMeasureImpedance: shouldMeasureImpedance,
                shouldMeasurePulse: shouldMeasurePulse
            )
        }
    }
    
    private func storeR4ScalePreferenceForDevice(broadcastId: String, category: AppCategory, shouldMeasureImpedance: Bool, shouldMeasurePulse: Bool) async {
        guard let loginDetails = deviceService.fetchUserLogin(for: category),
              loginDetails.isLoggedIn,
              let email = loginDetails.email,
              let password = loginDetails.password else {
            DispatchQueue.main.async {
                ToastService.shared.presentToast(with: self.toastLang.noLoginDataFound)
            }
            return
        }
        
        guard let accessToken = await r4ScaleService.authenticate(email: email, password: password, for: category) else {
            DispatchQueue.main.async {
                ToastService.shared.presentToast(with: self.toastLang.authFailed)
            }
            return
        }
        
        let displayName = userProfileUpdationService.fetchUserProfile()?.name ?? defaultUser
        
        let preference = R4ScalePreference(
            tzOffset: 330,
            timeFormat: "12",
            displayName: displayName,
            displayMetrics: displayMetrics,
            shouldMeasurePulse: shouldMeasurePulse,
            shouldMeasureImpedance: shouldMeasureImpedance,
            shouldFactoryReset: false,
            wifiFotaScheduleTime: Int(Date().timeIntervalSince1970)
        )
        
        guard let broadcastIdInt = HexConversionHelper.convertHexToInt(value: broadcastId) else {
            DispatchQueue.main.async {
                ToastService.shared.presentToast(with: ToastMessageStrings.invalidBroadcastId)
            }
            return
        }
        
        let success = await r4ScaleService.storeR4ScalePreference(
            broadcastId: String(broadcastIdInt),
            preference: preference,
            for: category
        )
        let isConnected = DeviceConnectionState.shared.getDeviceConnectionState(deviceId: broadcastId)
        
        DispatchQueue.main.async {
            if success {
                let message = isConnected
                ? self.toastLang.preferencesSavedShort
                : self.toastLang.preferencesSavedWithInfo
                ToastService.shared.presentToast(with: message)
            } else {
                ToastService.shared.presentToast(with: self.toastLang.preferencesFailed)
            }
        }
    }
}
