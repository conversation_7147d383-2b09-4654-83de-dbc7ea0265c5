//
//  AllBodyMetricsView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 10/04/25.
//

import SwiftUI

struct AllBodyMetricsView: View {
    @Binding var isHeartRateEnabled: Bool
    
    var body: some View {
        VStack(alignment: .leading) {
            VStack {
                HStack {
                    Image(systemName: "heart.fill")
                        .foregroundColor(isHeartRateEnabled ? .blue : .gray)
                    Text("\(DisplayMetricsStrings.heartRateLabel): \(isHeartRateEnabled ? "ON" : "OFF")")
                        .font(.headline)
                        .foregroundColor(.black)
                    Spacer()
                    Toggle("", isOn: $isHeartRateEnabled)
                        .tint(.blue)
                }
                .padding()
                
                Text(DisplayMetricsStrings.heartRateNote)
                    .font(.caption)
                    .foregroundColor(.black)
                    .padding(.horizontal)
            }
            .padding(.bottom)
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(Color.gray.opacity(0.5), lineWidth: 1)
            )
            .padding(.horizontal)
            .padding(.top)
        }
    }
}

#Preview {
    AllBodyMetricsView(isHeartRateEnabled: .constant(true))
}

