//
//  MetricsSettingsRow.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 08/04/25.
//

import SwiftUI

struct MetricsSettingsRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let action: () -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 15) {
                HStack {
                    Image(icon)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 25, height: 25)
                        .foregroundColor(.blue)
                        .padding(.trailing, 15)
                    
                    Text(title)
                        .font(.body)
                        .fontWeight(.semibold)
                        .foregroundColor(.black)
                }
                Text(subtitle)
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .lineLimit(nil)
            }
            
            Spacer()
            
            Button(action:{
                action()
            }, label: {
                Image(systemName: "chevron.right")
                    .foregroundColor(.blue)
            })
            
        }
        .onTapGesture {
            action()
        }
        .padding()
        .background(Color.white)
        .cornerRadius(10)
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(Color.gray.opacity(0.5), lineWidth: 1)
        )
    }
}

#Preview {
    MetricsSettingsRow(icon: AppAssets.weightOnlyModeDiscoveredIcon, title: "Dashboard Metrics", subtitle: "Customize which metrics you'll see on your app's dashboard", action: {})
        .padding()
}

