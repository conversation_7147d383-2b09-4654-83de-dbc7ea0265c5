//
//  DashBoardMetricsMainView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 08/04/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct DashBoardMetricsMainView: View {
    @EnvironmentObject var router: Router<DashboardRoutes>
    @EnvironmentObject var deviceConnectionState: DeviceConnectionState
    let device: GGBTDevice
    let category: AppCategory
    var isDeviceConnected: Bool {
        deviceConnectionState.getDeviceConnectionState(deviceId: device.broadcastId)
    }
    var body: some View {
        VStack(alignment: .center, spacing: 20) {
            VStack(alignment: .leading, spacing: 10) {
                Text(DisplayMetricsStrings.customiseSettingsSubtitle)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.black)
                Text(DisplayMetricsStrings.updateSettingsSubtitle)
                    .font(.subheadline)
                    .foregroundColor(.gray)
            }
            .padding(.horizontal)
            .padding(.top)
            .padding(.bottom)
            
            VStack(spacing: 25) {
                
                MetricsSettingsRow(
                    icon: AppAssets.metricEditIconBlue,
                    title: DisplayMetricsStrings.dashboardMetricsTitle,
                    subtitle: DisplayMetricsStrings.dashboardMetricsSubtitle, action: {
                        router.navigate(to: .customizeAppDashboard)
                    }
                )
                
                MetricsSettingsRow(
                    icon: AppAssets.moreInfoIconBlue,
                    title: DisplayMetricsStrings.scaleMetricsTitle,
                    subtitle: DisplayMetricsStrings.scaleMetricsSubtitle, action: {
                        router.navigate(to: .displayMetricsView(device, category))
                    }
                )
                
                MetricsSettingsRow(
                    icon: AppAssets.weightOnlyModeDiscoveredIcon,
                    title: DisplayMetricsStrings.scaleModesTitle,
                    subtitle: DisplayMetricsStrings.scaleModesSubtitle, action: {
                        router.navigate(to: .scaleModesView(device, category))
                    }
                )
                
                if isDeviceConnected {
                    MetricsSettingsRow(
                        icon: AppAssets.scaleDisplayNameIcon,
                        title: DisplayMetricsStrings.userNameTitle,
                        subtitle: DisplayMetricsStrings.userNameSubtitle, action: {
                            router.navigate(to: .r4ScaleUserView(device: device, selectedCategory: category, ParentViewType: .deviceDetailView))
                        }
                    )
                }
            }
            .padding(.horizontal)
            
            Spacer()
            
        }
        .background(Color.gray.opacity(0.1))
        .navigationBarBackButtonHidden(true)
        .navigationTitle(DisplayMetricsStrings.setupTitle)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    router.navigateBack(1)
                }) {
                    Image(systemName: "xmark")
                        .foregroundColor(.blue)
                }
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {}) {
                    Image(systemName: "questionmark.circle")
                        .foregroundColor(.blue)
                }
            }
        }
    }
}

#Preview {
    DashBoardMetricsMainView(device: GGBTDevice(name: "", broadcastId: "", password: "", token: "", userNumber: 0, preference: nil, syncAllData: false, batteryLevel: 0, protocolType: "", macAddress: ""), category: .weightGurus)
}

