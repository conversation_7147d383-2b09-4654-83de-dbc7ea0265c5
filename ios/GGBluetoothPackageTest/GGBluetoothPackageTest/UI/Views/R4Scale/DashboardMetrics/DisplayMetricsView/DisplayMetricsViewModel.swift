//
//  DisplayMetricsViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 09/04/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

final class DisplayMetricsViewModel: ObservableObject {
    @Published var metricsData: [MetricItem] = MetricsDataStore.bodyMetrics
    @Published var otherMetrics: [MetricItem] = MetricsDataStore.otherMetrics
    @Published var isMetricEnabled: [String: Bool] = {
        var initial = [String: Bool]()
        MetricsDataStore.bodyMetrics.forEach { initial[$0.id] = true }
        MetricsDataStore.otherMetrics.forEach { initial[$0.id] = true }
        return initial
    }()
    @Injector var bluetoothService: BluetoothService
    @Injector var deviceService: DeviceService
    @Injector var userProfileUpdationService: UserProfileUpdationService
    @Injector var r4ScaleService: R4ScaleService
    var toastLang = ToastMessageStrings.self
    
    func moveBodyMetrics(fromOffsets indices: IndexSet, toOffset newOffset: Int) {
        metricsData.move(fromOffsets: indices, toOffset: newOffset)
    }
    
    func moveOtherMetrics(fromOffsets indices: IndexSet, toOffset newOffset: Int) {
        otherMetrics.move(fromOffsets: indices, toOffset: newOffset)
    }
    
    func saveMetrics(device: GGBTDevice, category: AppCategory, isDeviceConnected: Bool) {
        saveMetricsEntitiesToUserProfile()
        
        if isDeviceConnected {
            guard let storedDevice = deviceService.fetchDevice(by: device.broadcastId) else { return }
            
            let userName = userProfileUpdationService.fetchUserProfile()?.name ?? "Default User"
            let allMetrics = metricsData + otherMetrics
            let displayMetrics = DisplayMetricsHelper.extractDisplayMetrics(from: allMetrics, isEnabled: isMetricEnabled)
            
            let updatedPreference = GGDevicePreference(
                displayName: userName,
                displayMetrics: displayMetrics,
                shouldMeasureImpedance: storedDevice.preference?.shouldMeasureImpedance ?? true,
                shouldMeasurePulse: storedDevice.preference?.shouldMeasurePulse ?? true,
                timeFormat: storedDevice.preference?.timeFormat ?? TimeFormat.twentyFourHour.rawValue
            )
            
            let updatedDevice = GGBTDevice(
                name: storedDevice.name ?? device.name,
                broadcastId: storedDevice.broadcastId ?? device.broadcastId,
                password: storedDevice.password ?? device.password,
                token: storedDevice.token ?? device.token,
                userNumber: Int(storedDevice.userNumber),
                preference: updatedPreference,
                syncAllData: true,
                batteryLevel: Int(storedDevice.batteryLevel),
                protocolType: storedDevice.protocolType ?? device.protocolType,
                macAddress: storedDevice.macAddress ?? device.macAddress
            )
            
            Task{
                try await bluetoothService.updateAccount(device: updatedDevice)
            }
        }
        Task{
            await storeR4ScalePreferenceForDevice(
                broadcastId: device.broadcastId,
                tzOffset: 330,
                timeFormat: TimeFormat.twelveHour.rawValue,
                category: category
            )
        }
    }
    
    func saveMetricsEntitiesToUserProfile() {
        let allMetrics = metricsData + otherMetrics
        let metricsEntities = allMetrics.map { metric in
            MetricsEntityModel(
                id: metric.id,
                isEnabled: isMetricEnabled[metric.id] ?? false,
                label: metric.label,
                order: Int16(allMetrics.firstIndex(where: { $0.id == metric.id }) ?? 0)
            )
        }
        
        userProfileUpdationService.saveMetricsEntities(metricsEntities: metricsEntities)
    }
    
    func fetchAndApplyMetricsFromUserProfile() {
        let metricsEntities = userProfileUpdationService.fetchMetricsEntities()
        
        guard !metricsEntities.isEmpty else {
            return
        }
        
        let storedBodyMetrics = metricsEntities.filter { entity in
            MetricsDataStore.bodyMetrics.contains(where: { $0.id == entity.id })
        }
        
        let storedOtherMetrics = metricsEntities.filter { entity in
            MetricsDataStore.otherMetrics.contains(where: { $0.id == entity.id })
        }
        
        metricsData = storedBodyMetrics.sorted(by: { $0.order < $1.order }).compactMap { entity in
            MetricsDataStore.bodyMetrics.first(where: { $0.id == entity.id }).map {
                MetricItem(id: $0.id, label: $0.label, icon: $0.icon, order: entity.order)
            }
        }
        
        otherMetrics = storedOtherMetrics.sorted(by: { $0.order < $1.order }).compactMap { entity in
            MetricsDataStore.otherMetrics.first(where: { $0.id == entity.id }).map {
                MetricItem(id: $0.id, label: $0.label, icon: $0.icon, order: entity.order)
            }
        }
        metricsEntities.forEach { entity in
            isMetricEnabled[entity.id] = entity.isEnabled
        }
        
    }
    
    func storeR4ScalePreferenceForDevice(broadcastId: String, tzOffset: Int, timeFormat: String, category: AppCategory) async {
        
        guard let loginDetails = deviceService.fetchUserLogin(for: category),
              loginDetails.isLoggedIn,
              let email = loginDetails.email,
              let password = loginDetails.password else {
            DispatchQueue.main.async {
                ToastService.shared.presentToast(with: self.toastLang.noLoginDataFound)
            }
            return
        }
        
        guard let accessToken = await r4ScaleService.authenticate(email: email, password: password, for: category) else {
            DispatchQueue.main.async {
                ToastService.shared.presentToast(with: self.toastLang.authFailed)
            }
            return
        }
        
        let allMetrics = metricsData + otherMetrics
        let displayMetrics = DisplayMetricsHelper.extractDisplayMetrics(from: allMetrics, isEnabled: isMetricEnabled)
        
        let displayName = userProfileUpdationService.fetchUserProfile()?.name ?? "Default User"
        let storedDevice = deviceService.fetchDevice(by: broadcastId)
        let existingPreference = storedDevice?.preference
        let preference = R4ScalePreference(
            tzOffset: tzOffset,
            timeFormat: timeFormat,
            displayName: displayName,
            displayMetrics: displayMetrics,
            shouldMeasurePulse: existingPreference?.shouldMeasurePulse ?? true,
            shouldMeasureImpedance: existingPreference?.shouldMeasureImpedance ?? true,
            shouldFactoryReset:  false,
            wifiFotaScheduleTime: Int(Date().timeIntervalSince1970)
        )
        
        guard let broadcastIdInt = HexConversionHelper.convertHexToInt(value: broadcastId) else {
            DispatchQueue.main.async {
                ToastService.shared.presentToast(with: self.toastLang.invalidBroadcastId)
            }
            return
        }
        
        let success = await r4ScaleService.storeR4ScalePreference(
            broadcastId: String(broadcastIdInt),
            preference: preference,
            for: category
        )
        let isConnected = DeviceConnectionState.shared.getDeviceConnectionState(deviceId: broadcastId)
        
        DispatchQueue.main.async {
            if success {
                let message = isConnected
                ? self.toastLang.preferencesSavedShort
                : self.toastLang.preferencesSavedWithInfo
                ToastService.shared.presentToast(with: message)
            } else {
                ToastService.shared.presentToast(with: self.toastLang.preferencesFailed)
            }
        }
    }
}
