//
//  DisplayMetricsView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 09/04/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct DisplayMetricsView: View {
    @StateObject private var viewModel = DisplayMetricsViewModel()
    @EnvironmentObject var router: Router<DashboardRoutes>
    @EnvironmentObject var deviceConnectionState: DeviceConnectionState

    let device: GGBTDevice
    let category: AppCategory

    var body: some View {
        ScrollView {
            VStack {
                VStack(alignment: .leading) {
                    VStack(alignment: .leading, spacing: 10) {
                        Text(DisplayMetricsStrings.scaleMetricsTitle)
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.black)
                        
                        Text(DisplayMetricsStrings.scaleMetricsSubtitle)
                            .font(.subheadline)
                            .foregroundColor(.gray)
                        
                        Text(DisplayMetricsStrings.dashboardMetricsTitle)
                            .font(.title3)
                            .fontWeight(.semibold)
                            .foregroundColor(.black)
                            .padding(.horizontal)
                        
                        Divider().padding(.bottom)
                    }
                    .padding([.horizontal, .top])
                    
                    VStack(alignment: .leading, spacing: 15) {
                        List {
                            ForEach(Array(viewModel.metricsData.enumerated()), id: \.1.id) { index, _ in
                                metricRow(for: $viewModel.metricsData[index])
                            }
                            .onMove { indices, newOffset in
                                viewModel.moveBodyMetrics(fromOffsets: indices, toOffset: newOffset)
                            }
                            .listRowSeparator(.hidden)
                        }
                        .listStyle(.plain)
                        .scrollIndicators(.hidden)
                        .environment(\.editMode, .constant(.active))
                        .frame(height: CGFloat(viewModel.metricsData.count * 60))
                    }
                    .padding(.horizontal)
                    
                    VStack(alignment: .leading, spacing: 15) {
                        Text(DisplayMetricsStrings.otherMetrics)
                            .font(.title3)
                            .fontWeight(.semibold)
                            .foregroundColor(.black)
                            .padding(.horizontal)
                            .padding(.top)
                        
                        List {
                            ForEach($viewModel.otherMetrics, id: \.id) { item in
                                metricRow(for: item)
                            }
                            .onMove { indices, newOffset in
                                viewModel.moveOtherMetrics(fromOffsets: indices, toOffset: newOffset)
                            }
                            .listRowSeparator(.hidden)
                        }
                        .listStyle(.plain)
                        .scrollIndicators(.hidden)
                        .environment(\.editMode, .constant(.active))
                        .frame(height: CGFloat(viewModel.otherMetrics.count * 60))
                        
                    }
                    .padding(.horizontal)
                }
            }
            .onAppear {
                viewModel.fetchAndApplyMetricsFromUserProfile()
            }
            .background(Color.white)
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(Color.gray.opacity(0.5), lineWidth: 1)
            )
            .padding(.top, 20)
            .padding(.horizontal)
        }
        .navigationBarBackButtonHidden(true)
        .navigationTitle(DisplayMetricsStrings.setupTitle)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    router.navigateBack(1)
                }) {
                    Image(systemName: "xmark")
                        .foregroundColor(.blue)
                }
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                         viewModel.saveMetrics(
                            device: device,
                            category: category,
                            isDeviceConnected: deviceConnectionState.getDeviceConnectionState(deviceId: device.broadcastId)
                        )
                }) {
                    Text(CommonStrings.save.capitalized)
                        .foregroundColor(.blue)
                        .fontWeight(.semibold)
                }
            }
        }
    }

    private func metricRow(for item: Binding<MetricItem>) -> some View {
        let binding = Binding(
            get: { viewModel.isMetricEnabled[item.wrappedValue.id] ?? true },
            set: { viewModel.isMetricEnabled[item.wrappedValue.id] = $0 }
        )
        return MetricRow(item: item.wrappedValue, isEnabled: binding)
    }
}

#Preview {
    DisplayMetricsView(device: GGBTDevice(name: "", broadcastId: "", password: "", token: "", userNumber: 0, preference: nil, syncAllData: false, batteryLevel: 0, protocolType: "", macAddress: ""), category: .weightGurus)
        .environmentObject(DeviceConnectionState.shared)
}
