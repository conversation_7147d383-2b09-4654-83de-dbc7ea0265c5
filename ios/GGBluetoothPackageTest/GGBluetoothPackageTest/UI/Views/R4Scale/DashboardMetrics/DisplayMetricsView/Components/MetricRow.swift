//
//  MetricRow.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 09/04/25.
//

import SwiftUI

struct MetricRow: View {
    let item: MetricItem
    @Binding var isEnabled: Bool
    
    var body: some View {
        HStack {
            if !item.icon.isEmpty {
                Image(item.icon)
                    .resizable()
                    .scaledToFit()
                    .frame(width: 20, height: 20)
            }
            
            Text(item.label)
                .font(.subheadline)
                .foregroundColor(.gray)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            Spacer()
            
            Toggle("", isOn: $isEnabled)
                .labelsHidden()
                .tint(.blue)
                .padding(.trailing)
        }
        .padding(.vertical, 3)
    }
}

#Preview {
    MetricRow(item: MetricItem(id: "BF", label: "Body Fat", icon: AppAssets.bodyFat, order: 0), isEnabled: .constant(true))
        .padding(.horizontal)
}

