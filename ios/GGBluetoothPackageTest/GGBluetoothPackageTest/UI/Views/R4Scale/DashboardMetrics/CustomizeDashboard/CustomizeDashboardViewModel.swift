//
//  CustomizeDashboardViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/05/25.
//

import Foundation
import SwiftUI

class CustomizeDashboardViewModel: ObservableObject {
    @Published var selectedMetrics: Set<String> = []
    @Published var showMenuForMetricId: String? = nil
    @Published var menuPosition: CGPoint = .zero
    @Published var isEditMode: Bool = false
    @Published var bodyMetrics: [MetricItem] = MetricsDataStore.bodyMetrics
    @Published var dropHoverId: String? = nil
    @Published var gridLayoutId = UUID()
    @Published var isResizing = false

    var animationKey: String {
        "\(gridLayoutId.uuidString)__" +
        bodyMetrics.map { "\($0.id)_\($0.tileSizeType.rawValue)_\($0.isRemoved)" }.joined(separator: "|")
    }
    
    init() {
        selectedMetrics = Set(MetricsDataStore.bodyMetrics.map { $0.id })
    }

    func groupedRows(from items: [MetricItem]) -> [[MetricItem]] {
        let activeItems = items.filter { !$0.isRemoved }
        let removedItems = items.filter { $0.isRemoved }
        var rows: [[MetricItem]] = []
        rows.append(contentsOf: groupedRowsByGridRules(from: activeItems))
        rows.append(contentsOf: groupedRowsByGridRules(from: removedItems))
        return rows
    }

    private func groupedRowsByGridRules(from items: [MetricItem]) -> [[MetricItem]] {
        let largeTiles = items.filter { $0.tileSizeType == .large }
        let mediumTiles = items.filter { $0.tileSizeType == .medium }
        let smallTiles = items.filter { $0.tileSizeType == .small }
        var rows: [[MetricItem]] = []
        
        for tile in largeTiles {
            rows.append([tile])
        }
        var mediumIndex = 0
        while mediumIndex < mediumTiles.count {
            let end = min(mediumIndex + 2, mediumTiles.count)
            let row = Array(mediumTiles[mediumIndex..<end])
            rows.append(row)
            mediumIndex += 2
        }
        var smallIndex = 0
        while smallIndex < smallTiles.count {
            let end = min(smallIndex + 3, smallTiles.count)
            let row = Array(smallTiles[smallIndex..<end])
            rows.append(row)
            smallIndex += 3
        }
        return rows
    }

    func binding(for item: MetricItem) -> Binding<MetricItem> {
        Binding(
            get: {
                self.bodyMetrics.first(where: { $0.id == item.id }) ?? item
            },
            set: { newValue in
                if let index = self.bodyMetrics.firstIndex(where: { $0.id == newValue.id }) {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        self.bodyMetrics[index] = newValue
                        self.gridLayoutId = UUID()
                    }
                }
            }
        )
    }

    func isRemoved(_ item: MetricItem) -> Bool {
        bodyMetrics.first(where: { $0.id == item.id })?.isRemoved ?? item.isRemoved
    }

    func handleRemoveRestoreTile(id: String, remove: Bool) {
        guard let idx = bodyMetrics.firstIndex(where: { $0.id == id }) else { return }
        bodyMetrics[idx].isRemoved = remove
        let tile = bodyMetrics.remove(at: idx)
        if remove {
            bodyMetrics.append(tile)
        } else {
            if let firstRemoved = bodyMetrics.firstIndex(where: { $0.isRemoved }) {
                bodyMetrics.insert(tile, at: firstRemoved)
            } else {
                bodyMetrics.append(tile)
            }
        }
        gridLayoutId = UUID()
    }

    func handleDrop(draggedId: String, targetId: String) -> Bool {
        guard let fromIndex = bodyMetrics.firstIndex(where: { $0.id == draggedId }),
              let toIndex = bodyMetrics.firstIndex(where: { $0.id == targetId }),
              fromIndex != toIndex else { return false }
        let fromTile = bodyMetrics[fromIndex]
        let toTile = bodyMetrics[toIndex]
        if fromTile.tileSizeType == toTile.tileSizeType {
            bodyMetrics.move(fromOffsets: IndexSet(integer: fromIndex),
                             toOffset: toIndex > fromIndex ? toIndex + 1 : toIndex)
        } else {
            bodyMetrics[fromIndex].label = toTile.label
            bodyMetrics[fromIndex].icon = toTile.icon
            bodyMetrics[toIndex].label = fromTile.label
            bodyMetrics[toIndex].icon = fromTile.icon
        }
        gridLayoutId = UUID()
        return true
    }

    func toggleSelection(for id: String) {
        if selectedMetrics.contains(id) {
            selectedMetrics.remove(id)
        } else {
            selectedMetrics.insert(id)
        }
    }
        
    func isFirstSmallInRow(menuId: String?) -> Bool {
        guard let menuId = menuId else { return false }
        let rows = groupedRows(from: bodyMetrics)
        for row in rows {
            if row.count == 3,
               row.allSatisfy({ $0.tileSizeType == .small }),
               row.first?.id == menuId {
                return true
            }
        }
        return false
    }
    
   func clampedPopupX(
        originalX: CGFloat,
        popupWidth: CGFloat = 140,
        horizontalPadding: CGFloat = 16,
        extraLeftPadding: CGFloat = 40,
        isFirstSmall: Bool = false
    ) -> CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        let minX = (isFirstSmall ? extraLeftPadding : horizontalPadding) + popupWidth / 2
        let maxX = screenWidth - horizontalPadding - popupWidth / 2
        return min(max(originalX, minX), maxX)
    }
}
