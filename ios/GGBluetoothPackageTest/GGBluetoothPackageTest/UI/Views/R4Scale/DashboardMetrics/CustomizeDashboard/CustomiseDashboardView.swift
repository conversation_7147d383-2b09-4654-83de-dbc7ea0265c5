//
//  CustomiseDashboardView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 08/04/25.
//

import SwiftUI

struct CustomizeAppDashboardView: View {
    @EnvironmentObject var router: Router<DashboardRoutes>
    @State private var selectedMetrics: Set<String> = []
    @State private var showMenuForMetricId: String? = nil
    @State private var menuPosition: CGPoint = .zero
    @State private var isEditMode: Bool = false
    @State private var bodyMetrics = MetricsDataStore.bodyMetrics
    @State private var draggedMetricId: String? = nil
    @State private var dropHoverId: String? = nil
    @State private var gridLayoutId = UUID() // Force grid recalculation
    private let columns = Array(repeating: GridItem(.flexible()), count: 3)
    private let popupVerticalOffset: CGFloat = 55
    var tileSizeType: TileSizeType = .small

    var body: some View {
        ScrollView {
            ZStack {
                VStack {
                    headerView
                    metricsGrid
                    Spacer()
                    if isEditMode { dashboardEditActionsView }
                }
                .onTapGesture { if showMenuForMetricId == nil { withAnimation { isEditMode = false } } }
                .padding(.horizontal)
                .navigationBarBackButtonHidden(true)
                .navigationTitle(DisplayMetricsStrings.setupTitle)
                .navigationBarTitleDisplayMode(.inline)
                .toolbar { leadingToolbarButton; trailingToolbarButton }
                .onAppear {
                    selectedMetrics = Set(MetricsDataStore.bodyMetrics.map { $0.id })
                }

                if let id = showMenuForMetricId,
                   MetricsDataStore.bodyMetrics.contains(where: { $0.id == id }),
                   !isEditMode {
                    menuOverlay
                }
            }
        }
        .background(Color.gray.opacity(0.1))
    }

    private var headerView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(DisplayMetricsStrings.customizeAppDashboardTitle)
                .font(.title2).fontWeight(.semibold).foregroundColor(.black)
            Text(DisplayMetricsStrings.dashboardMetricsSubtitle)
                .font(.subheadline).foregroundColor(.gray)
        }
        .padding([.horizontal, .top])
    }

//    private var metricsGrid: some View {
//        VStack(alignment: .leading, spacing: 10) {
//            ForEach(groupedRows(from: bodyMetrics).indices, id: \.self) { index in
//                let row = groupedRows(from: bodyMetrics)[index]
//                metricRow(row: row)
//            }
//        }
//        .padding()
//        .background(Color.white)
//        .cornerRadius(10)
//        .overlay(RoundedRectangle(cornerRadius: 10).stroke(Color.gray.opacity(0.5)))
//        .padding(.top, 20)
//    }

    private func metricRow(row: [MetricItem]) -> some View {
        HStack(alignment: .top, spacing: 10) {
            ForEach(row, id: \.id) { item in
                metricTileView(for: Binding(
                    get: { item },
                    set: { newValue in
                        if let index = bodyMetrics.firstIndex(where: { $0.id == newValue.id }) {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                bodyMetrics[index] = newValue
                            }
                        }
                    }
                ))
                .frame(width: item.tileSizeType.size.width, height: item.tileSizeType.size.height)
                .transition(.asymmetric(
                    insertion: .scale.combined(with: .opacity),
                    removal: .scale.combined(with: .opacity)
                ))
            }

            // Add spacer to push items to leading edge
            if !row.isEmpty {
                Spacer(minLength: 0)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .animation(.easeInOut(duration: 0.3), value: row.map { $0.tileSizeType })
    }


    @ViewBuilder
    private func metricTileView(for item: Binding<MetricItem>) -> some View {
        DashboardMetricTile(
            id: item.wrappedValue.id,
            label: item.wrappedValue.label,
            icon: item.wrappedValue.icon,
            onTap: {
                if selectedMetrics.contains(item.wrappedValue.id) {
                    selectedMetrics.remove(item.wrappedValue.id)
                } else {
                    selectedMetrics.insert(item.wrappedValue.id)
                }
            },
            onLongPress: { tileFrame in
                if !isEditMode {
                    showMenuForMetricId = item.wrappedValue.id
                    menuPosition = tileFrame
                }
            },
            isEditMode: isEditMode,
            isDropTarget: dropHoverId == item.wrappedValue.id,
            isRemoved: Binding(
                get: { item.wrappedValue.isRemoved },
                set: { newValue in
                    var updated = item.wrappedValue
                    updated.isRemoved = newValue
                    item.wrappedValue = updated

                    // Trigger grid recalculation when items are removed/added
                    DispatchQueue.main.async {
                        gridLayoutId = UUID()
                    }
                }
            ),
            tileSizeType: Binding(
                get: { item.wrappedValue.tileSizeType },
                set: { newValue in
                    var updated = item.wrappedValue
                    updated.tileSizeType = newValue
                    item.wrappedValue = updated

                    // Trigger grid recalculation
                    DispatchQueue.main.async {
                        gridLayoutId = UUID()
                    }
                }
            )

        )
        .draggable(item.wrappedValue.id) {
            ZStack {
                RoundedRectangle(cornerRadius: 0)
                    .fill(Color.gray.opacity(0.5))
                VStack(spacing: 10) {
                    Image(item.wrappedValue.icon)
                        .font(.title)
                        .foregroundColor(.black)
                    Text(item.wrappedValue.label)
                        .font(.subheadline)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 5)
                }
                .padding(.vertical, 10)
            }
            .frame(width: 100, height: 100)
            .clipShape(RoundedRectangle(cornerRadius: 0))
        }
        .dropDestination(for: String.self) { droppedIds, location in
            guard let draggedId = droppedIds.first,
                  let fromIndex = bodyMetrics.firstIndex(where: { $0.id == draggedId }),
                  let toIndex = bodyMetrics.firstIndex(where: { $0.id == item.wrappedValue.id }),
                  fromIndex != toIndex else { return false }
            withAnimation {
                bodyMetrics.move(fromOffsets: IndexSet(integer: fromIndex),
                                 toOffset: toIndex > fromIndex ? toIndex + 1 : toIndex)
            }
            dropHoverId = nil

            // Trigger grid recalculation after drag and drop
            DispatchQueue.main.async {
                gridLayoutId = UUID()
            }

            return true
        } isTargeted: { isTargeted in
            dropHoverId = isTargeted ? item.wrappedValue.id : nil
        }
    }

    private var dashboardEditActionsView: some View {
        VStack(spacing: 12) {
            Button(action: {}) {
                Text(CommonStrings.save.uppercased())
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 10)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .font(.headline)
                    .cornerRadius(30)
            }
            Button(action: { withAnimation { isEditMode = false } }) {
                Text(DisplayMetricsStrings.resetDashboardTitle.uppercased())
                    .frame(maxWidth: .infinity)
                    .foregroundColor(.blue)
                    .font(.headline)
            }
        }
        .padding()
        .transition(.move(edge: .bottom).combined(with: .opacity))
    }

    private var leadingToolbarButton: some ToolbarContent {
        ToolbarItem(placement: .navigationBarLeading) {
            Button(action: { router.navigateBack(1) }) {
                Image(systemName: "xmark").foregroundColor(.blue)
            }
        }
    }

    private var trailingToolbarButton: some ToolbarContent {
        ToolbarItem(placement: .navigationBarTrailing) {
            Button(action: {}) {
                Image(systemName: "questionmark.circle").foregroundColor(.blue)
            }
        }
    }

    private var menuOverlay: some View {
        Group {
            TouchableOverlay { withAnimation { showMenuForMetricId = nil } }
                .zIndex(9998)
            PopupMenuView(
                onInfo: { withAnimation { showMenuForMetricId = nil } },
                onEdit: {
                    withAnimation {
                        showMenuForMetricId = nil
                        isEditMode = true
                    }
                }
            )
            .position(x: menuPosition.x, y: menuPosition.y - popupVerticalOffset)
            .transition(.move(edge: .bottom).combined(with: .opacity))
            .zIndex(9999)
        }
    }

    private func groupedRows(from items: [MetricItem]) -> [[MetricItem]] {
        var rows: [[MetricItem]] = []
        let activeItems = items.filter { !$0.isRemoved }
        let maxColumns = 3

        var currentRow: [MetricItem] = []
        var currentRowColumns = 0

        for item in activeItems {
            let itemColumns = item.tileSizeType.gridSpan

            // If adding this item would exceed the row capacity
            if currentRowColumns + itemColumns > maxColumns {
                // Save current row if it has items
                if !currentRow.isEmpty {
                    rows.append(currentRow)
                    currentRow = []
                    currentRowColumns = 0
                }

                // If the item is larger than max columns, force it into its own row
                if itemColumns > maxColumns {
                    rows.append([item])
                    continue
                }
            }

            // Add item to current row
            currentRow.append(item)
            currentRowColumns += itemColumns

            // If row is exactly filled or this is a large item, start new row
            if currentRowColumns >= maxColumns {
                rows.append(currentRow)
                currentRow = []
                currentRowColumns = 0
            }
        }

        // Add any remaining items
        if !currentRow.isEmpty {
            rows.append(currentRow)
        }

        return optimizeRowLayout(rows)
    }

    // Optimize the layout by trying to fill gaps in rows
    private func optimizeRowLayout(_ rows: [[MetricItem]]) -> [[MetricItem]] {
        var optimizedRows: [[MetricItem]] = []
        let maxColumns = 3

        for row in rows {
            var currentRow = row
            let currentColumns = currentRow.reduce(0) { $0 + $1.tileSizeType.gridSpan }

            // If row has space, try to fit small items from subsequent rows
            if currentColumns < maxColumns {
                let remainingSpace = maxColumns - currentColumns

                // Look for small items that can fit in the remaining space
                for (rowIndex, subsequentRow) in rows.enumerated() {
                    if rowIndex <= optimizedRows.count { continue }

                    for (itemIndex, item) in subsequentRow.enumerated() {
                        if item.tileSizeType.gridSpan <= remainingSpace {
                            // Move this item to current row
                            currentRow.append(item)
                            break
                        }
                    }
                }
            }

            optimizedRows.append(currentRow)
        }

        return optimizedRows
    }

    private var metricsGrid: some View {
        let rows = groupedRows(from: bodyMetrics)

        return VStack(alignment: .leading, spacing: 10) {
            ForEach(rows.indices, id: \.self) { index in
                let row = rows[index]
                metricRow(row: row)
                    .id("row_\(index)_\(row.map { $0.id }.joined(separator: "_"))_\(gridLayoutId)")
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(10)
        .overlay(RoundedRectangle(cornerRadius: 10).stroke(Color.gray.opacity(0.5)))
        .padding(.top, 20)
        .id(gridLayoutId)
        .animation(.easeInOut(duration: 0.3), value: gridLayoutId)
        .animation(.easeInOut(duration: 0.3), value: bodyMetrics.map { "\($0.id)_\($0.tileSizeType.rawValue)_\($0.isRemoved)" })
    }



}

#Preview {
    CustomizeAppDashboardView( tileSizeType: .small)
        .environmentObject(Router<DashboardRoutes>())
}


