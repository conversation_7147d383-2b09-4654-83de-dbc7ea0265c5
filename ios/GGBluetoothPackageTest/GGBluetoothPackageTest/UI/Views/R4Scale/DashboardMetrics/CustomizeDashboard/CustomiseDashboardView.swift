//
//  CustomiseDashboardView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 08/04/25.
//

import SwiftUI

struct CustomizeAppDashboardView: View {
    @EnvironmentObject var router: Router<DashboardRoutes>
    @StateObject private var viewModel = CustomizeDashboardViewModel()
    private let columns = Array(repeating: GridItem(.flexible()), count: 3)
    private let popupVerticalOffset: CGFloat = 55
    var tileSizeType: TileSizeType = .small
    @State private var menuFrame: CGRect = .zero
    @State private var showParticles = false
    var body: some View {
        ScrollView {
            ZStack {
                VStack {
                    headerView
                    metricsGrid
                    Spacer()
                    if viewModel.isEditMode { dashboardEditActionsView }
                }
                .onTapGesture { if viewModel.showMenuForMetricId == nil { withAnimation { viewModel.isEditMode = false } } }
                .padding(.horizontal)
                .navigationBarBackButtonHidden(true)
                .navigationTitle(DisplayMetricsStrings.setupTitle)
                .navigationBarTitleDisplayMode(.inline)
                .toolbar { leadingToolbarButton; trailingToolbarButton }
                .onAppear {
                    viewModel.selectedMetrics = Set(MetricsDataStore.bodyMetrics.map { $0.id })
                }

                if let id = viewModel.showMenuForMetricId,
                   MetricsDataStore.bodyMetrics.contains(where: { $0.id == id }),
                   !viewModel.isEditMode {
                    menuOverlay
                }
            }
        }
        .background(Color.gray.opacity(0.1))
    }

    private var headerView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(DisplayMetricsStrings.customizeAppDashboardTitle)
                .font(.title2).fontWeight(.semibold).foregroundColor(.black)
            Text(DisplayMetricsStrings.dashboardMetricsSubtitle)
                .font(.subheadline).foregroundColor(.gray)
        }
        .padding([.horizontal, .top])
    }

    private func metricRow(row: [MetricItem]) -> some View {
        HStack(alignment: .top, spacing: 10) {
            ForEach(row, id: \.id) { item in
                metricTileView(for: viewModel.binding(for: item))
                    .frame(width: item.tileSizeType.size.width, height: item.tileSizeType.size.height)
                    .transition(.asymmetric(
                        insertion: .scale.combined(with: .opacity),
                        removal: .scale.combined(with: .opacity)
                    ))
                    .id("\(item.id)_\(item.tileSizeType.rawValue)")
            }
            if !row.isEmpty {
                Spacer(minLength: 0)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .animation(.easeInOut(duration: 0.3), value: row.map { "\($0.id)_\($0.tileSizeType.rawValue)" })
    }

    @ViewBuilder
    private func metricTileView(for item: Binding<MetricItem>) -> some View {
        let isTileRemoved = item.wrappedValue.isRemoved
        let tile = DashboardMetricTileView(
            id: item.wrappedValue.id,
            label: item.wrappedValue.label,
            icon: item.wrappedValue.icon,
            onTap: {
                viewModel.toggleSelection(for: item.wrappedValue.id)
            },
            onLongPress: { tileFrame in
                if !viewModel.isEditMode {
                    viewModel.showMenuForMetricId = item.wrappedValue.id
                    viewModel.menuPosition = tileFrame
                }
            },
            isEditMode: viewModel.isEditMode,
            isDropTarget: viewModel.dropHoverId == item.wrappedValue.id,
            isRemoved: Binding(
                get: { viewModel.isRemoved(item.wrappedValue) },
                set: { newValue in
                    viewModel.handleRemoveRestoreTile(id: item.wrappedValue.id, remove: newValue)
                }
            ),
            tileSizeType: Binding(
                get: { item.wrappedValue.tileSizeType },
                set: { newValue in
                    var updated = item.wrappedValue
                    updated.tileSizeType = newValue
                    item.wrappedValue = updated
                    DispatchQueue.main.async {
                        viewModel.gridLayoutId = UUID()
                    }
                }
            )
        )
        .opacity(isTileRemoved ? 0.75 : 1.0)

        if isTileRemoved {
            tile
        } else {
            tile
                .draggable(item.wrappedValue.id) {
                    ZStack {
                        RoundedRectangle(cornerRadius: 0)
                            .fill(Color.gray.opacity(0.5))
                        VStack(spacing: 10) {
                            Image(item.wrappedValue.icon)
                                .font(.title)
                                .foregroundColor(.black)
                            Text(item.wrappedValue.label)
                                .font(.subheadline)
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 5)
                        }
                        .padding(.vertical, 10)
                    }
                    .frame(width: 100, height: 100)
                    .clipShape(RoundedRectangle(cornerRadius: 0))
                }
                .dropDestination(for: String.self) { droppedIds, location in
                    guard let draggedId = droppedIds.first else { return false }
                    let targetId = item.wrappedValue.id
                    let result = viewModel.handleDrop(draggedId: draggedId, targetId: targetId)
                    viewModel.dropHoverId = nil
                    return result
                } isTargeted: { isTargeted in
                    viewModel.dropHoverId = isTargeted ? item.wrappedValue.id : nil
                }
        }
    }

    private var dashboardEditActionsView: some View {
        VStack(spacing: 12) {
            Button(action: {}) {
                Text(CommonStrings.save.uppercased())
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 10)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .font(.headline)
                    .cornerRadius(30)
            }
            Button(action: { withAnimation { viewModel.isEditMode = false } }) {
                Text(DisplayMetricsStrings.resetDashboardTitle.uppercased())
                    .frame(maxWidth: .infinity)
                    .foregroundColor(.blue)
                    .font(.headline)
            }
        }
        .padding()
        .transition(.move(edge: .bottom).combined(with: .opacity))
    }

    private var leadingToolbarButton: some ToolbarContent {
        ToolbarItem(placement: .navigationBarLeading) {
            Button(action: { router.navigateBack(1) }) {
                Image(systemName: "xmark").foregroundColor(.blue)
            }
        }
    }

    private var trailingToolbarButton: some ToolbarContent {
        ToolbarItem(placement: .navigationBarTrailing) {
            Button(action: {}) {
                Image(systemName: "questionmark.circle").foregroundColor(.blue)
            }
        }
    }

    private var menuOverlay: some View {
        Group {
            TouchableOverlay { withAnimation { viewModel.showMenuForMetricId = nil } }
                .zIndex(9998)
            PopupMenuView(
                onInfo: { withAnimation { viewModel.showMenuForMetricId = nil } },
                onEdit: {
                    withAnimation {
                        viewModel.showMenuForMetricId = nil
                        viewModel.isEditMode = true
                    }
                }
            )
            .background(
                            GeometryReader { proxy in
                                Color.clear
                                    .preference(key: FramePreferenceKey.self, value: proxy.frame(in: .global))
                            }
                        )
                        .onPreferenceChange(FramePreferenceKey.self) { frame in
                            menuFrame = frame
                        }
            .position(
                x: viewModel.clampedPopupX(
                    originalX: viewModel.menuPosition.x,
                    isFirstSmall: viewModel.isFirstSmallInRow(menuId: viewModel.showMenuForMetricId)
                ),
                y: viewModel.menuPosition.y - popupVerticalOffset
            )
            .transition(.particleExplosion(isActive: $showParticles, popupSize: menuFrame.size))
            .zIndex(9999)

        }
    }

    private var metricsGrid: some View {
        let rows = viewModel.groupedRows(from: viewModel.bodyMetrics)
        return VStack(alignment: .center, spacing: 10) {
            ForEach(rows.indices, id: \.self) { index in
                let row = rows[index]
                metricRow(row: row)
                    .id("row_\(index)_\(row.map { $0.id }.joined(separator: "_"))_\(viewModel.gridLayoutId)")
            }
        }
        .frame(width: UIScreen.main.bounds.width * 0.9)
        .padding(10)
        .background(Color.white)
        .cornerRadius(10)
        .overlay(RoundedRectangle(cornerRadius: 10).stroke(Color.gray.opacity(0.5)))
        .padding(.top, 20)
        .padding(.horizontal,10)
        .id(viewModel.gridLayoutId)
        .animation(
            .easeInOut(duration: 0.3),
            value: viewModel.animationKey
        )
    }
}

#Preview {
    CustomizeAppDashboardView(tileSizeType: .small)
        .environmentObject(Router<DashboardRoutes>())
}

import SwiftUI

struct ParticleExplosionTransition: ViewModifier {
    @Binding var isActive: Bool
    let particleCount: Int
    let popupSize: CGSize

    @State private var particles: [Particle] = []

    struct Particle: Identifiable {
        let id = UUID()
        var position: CGPoint
        var offset: CGSize = .zero
        var opacity: Double = 1
        var scale: CGFloat = 1
        var color: Color
    }

    func body(content: Content) -> some View {
        ZStack {
            content
                .opacity(isActive ? 1 : 0)
            if !isActive {
                ForEach(particles) { particle in
                    Circle()
                        .fill(particle.color)
                        .frame(width: 6 + CGFloat.random(in: 0...8), height: 6 + CGFloat.random(in: 0...8))
                        .position(particle.position)
                        .offset(particle.offset)
                        .opacity(particle.opacity)
                        .scaleEffect(particle.scale)
                        .onAppear {
                            withAnimation(Animation.easeOut(duration: 0.8)) {
                                // Random direction
                                let angle = Double.random(in: 0..<2 * .pi)
                                let distance = CGFloat.random(in: 50...140)
                                let dx = cos(angle) * distance
                                let dy = sin(angle) * distance
                                if let idx = particles.firstIndex(where: { $0.id == particle.id }) {
                                    particles[idx].offset = CGSize(width: dx, height: dy)
                                    particles[idx].opacity = 0
                                    particles[idx].scale = 0.3
                                }
                            }
                        }
                }
            }
        }
        .onChange(of: isActive) { newValue in
            if !newValue {
                // Generate particles when dismissing
                let colors: [Color] = [.white, .blue, .gray, .cyan, .black]
                particles = (0..<particleCount).map { _ in
                    let x = CGFloat.random(in: 0...popupSize.width)
                    let y = CGFloat.random(in: 0...popupSize.height)
                    return Particle(
                        position: CGPoint(x: x, y: y),
                        color: colors.randomElement() ?? .white
                    )
                }
            } else {
                particles = []
            }
        }
    }
}

// Helper extension to use as transition
extension AnyTransition {
    static func particleExplosion(isActive: Binding<Bool>, popupSize: CGSize, particleCount: Int = 40) -> AnyTransition {
        .modifier(
            active: ParticleExplosionTransition(isActive: isActive, particleCount: particleCount, popupSize: popupSize),
            identity: ParticleExplosionTransition(isActive: .constant(true), particleCount: particleCount, popupSize: popupSize)
        )
    }
}

struct FramePreferenceKey: PreferenceKey {
    static var defaultValue: CGRect = .zero
    static func reduce(value: inout CGRect, nextValue: () -> CGRect) {
        value = nextValue()
    }
}
