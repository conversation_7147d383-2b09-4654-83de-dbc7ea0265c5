//
//  DashboardMetricTileViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/05/25.
//

import SwiftUI

class DashboardMetricTileViewModel: ObservableObject {
    @Published var tileSize: CGSize
    @Published var label: String
    @Published var icon: String
    @Published var isRemoved: Bool
    @Published var tileSizeType: TileSizeType
    let lang = DisplayMetricsStrings.self
    let id: String
    var iconName: String { isRemoved ? "plus.circle" : "minus.circle" }
    var iconColor: Color { isRemoved ? .blue : .gray }
    var iconLabel: String { isRemoved ? lang.addTile : lang.removeTile }
    var iconHint: String { isRemoved ? lang.addTileHint : lang.removeTileHint }
    
    init(id: String, label: String, icon: String, initialTileSizeType: TileSizeType, isRemoved: Bool = false) {
        self.id = id
        self.label = label
        self.icon = icon
        self.tileSizeType = initialTileSizeType
        self.isRemoved = isRemoved
        self.tileSize = initialTileSizeType.size
    }
    
    func determineSizeType(for size: CGSize) -> TileSizeType {
        let screenWidth = UIScreen.main.bounds.width - 40
        let smallWidth = (screenWidth - 40) / 3
        let mediumWidth = (screenWidth - 30) / 2
        let largeWidth = screenWidth
        
        if size.width <= smallWidth + (mediumWidth - smallWidth) / 2 {
            return .small
        } else if size.width <= mediumWidth + (largeWidth - mediumWidth) / 2 {
            return .medium
        } else {
            return .large
        }
    }
}
