//
//  DashboardMetricTile.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 09/04/25.
//

import SwiftUI

struct DashboardMetricTile: View {
    @ObservedObject private var viewModel: DashboardMetricTileViewModel

    let onTap: () -> Void
    let onLongPress: (CGPoint) -> Void
    let isEditMode: Bool
    let lang = DisplayMetricsStrings.self
    let isDropTarget: Bool
    @Binding var isRemoved: Bool
    @Binding var tileSizeType: TileSizeType

    @GestureState private var dragOffset: CGSize = .zero

    init(
        id: String,
        label: String,
        icon: String,
        onTap: @escaping () -> Void,
        onLongPress: @escaping (CGPoint) -> Void,
        isEditMode: Bool,
        isDropTarget: Bool,
        isRemoved: Binding<Bool>,
        tileSizeType: Binding<TileSizeType>
    ) {
        self._viewModel = ObservedObject(
            wrappedValue: DashboardMetricTileViewModel(
                id: id,
                label: label,
                icon: icon,
                initialTileSizeType: tileSizeType.wrappedValue
            )
        )
        self.onTap = onTap
        self.onLongPress = onLongPress
        self.isEditMode = isEditMode
        self.isDropTarget = isDropTarget
        self._isRemoved = isRemoved
        self._tileSizeType = tileSizeType
    }

    var shouldWiggle: Bool {
        isEditMode && !isRemoved && !isDropTarget
    }

    var body: some View {
        GeometryReader { geo in
            ZStack {
                VStack {
                    ZStack(alignment: .topTrailing) {
                        RoundedRectangle(cornerRadius: 30)
                            .fill(Color(.systemGray6).opacity(0.7))
                            .frame(width: viewModel.tileSize.width, height: viewModel.tileSize.height)
                            .overlay(
                                RoundedRectangle(cornerRadius: 30)
                                    .strokeBorder(style: StrokeStyle(lineWidth: 2, dash: [6]))
                                    .foregroundColor(isDropTarget ? .gray : .clear)
                            )
                            .overlay(
                                Group {
                                    if isEditMode {
                                        BottomRightCorner(size: 20)
                                            .fill(Color(.systemGray6).opacity(0.6))
                                            .frame(width: 30, height: 30)
                                            .offset(x: -10, y: -8)
                                            .gesture(
                                                DragGesture()
                                                    .updating($dragOffset) { value, state, _ in
                                                        state = value.translation
                                                        let newSize = CGSize(
                                                            width: max(60, tileSizeType.size.width + value.translation.width),
                                                            height: max(60, tileSizeType.size.height + value.translation.height)
                                                        )
                                                        viewModel.tileSize = newSize
                                                    }
                                                    .onEnded { value in
                                                        let newSize = CGSize(
                                                            width: max(60, viewModel.tileSize.width + value.translation.width),
                                                            height: max(60, viewModel.tileSize.height + value.translation.height)
                                                        )
                                                        let nearestSizeType = viewModel.determineSizeType(for: newSize)
                                                        withAnimation(.easeInOut(duration: 0.3)) {
                                                            viewModel.tileSize = nearestSizeType.size
                                                            tileSizeType = nearestSizeType
                                                        }
                                                    }
                                            )
                                    }
                                },
                                alignment: .bottomTrailing
                            )

                        if isEditMode && !isDropTarget {
                            Image(systemName: isRemoved ? "plus.circle" : "minus.circle")
                                .foregroundColor(isRemoved ? .blue : .gray)
                                .font(.title3)
                                .offset(x: 5, y: -5)
                                .onTapGesture {
                                    isRemoved.toggle()
                                }
                                .accessibilityLabel(isRemoved ? lang.addTile : lang.removeTile)
                                .accessibilityHint(isRemoved ? lang.addTileHint : lang.removeTileHint)
                        }
                    }
                }
                VStack(spacing: 10) {
                    Image(viewModel.icon)
                        .font(.title)
                        .foregroundColor(.black)
                    Text(viewModel.label)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 5)
                }
                .padding(.vertical, 10)
                .opacity(isDropTarget ? 0.3 : 1.0)
            }
            .frame(width: tileSizeType.size.width, height: tileSizeType.size.height)
            .modifier(WiggleIfNeeded(shouldWiggle: shouldWiggle))
            .contentShape(Rectangle())
            .onTapGesture {
                if !isRemoved { onTap() }
            }
            .simultaneousGesture(
                LongPressGesture(minimumDuration: 0.1)
                    .onEnded { _ in
                        if !isRemoved {
                            let frame = geo.frame(in: .global)
                            let center = CGPoint(x: frame.midX, y: frame.maxY)
                            onLongPress(center)
                        }
                    }
            )
            .disabled(false)
        }
        .frame(width: viewModel.tileSize.width, height: viewModel.tileSize.height)
        .onAppear {
            viewModel.tileSize = tileSizeType.size
        }
        .onChange(of: tileSizeType) {
            withAnimation(.easeInOut(duration: 0.3)) {
                viewModel.tileSize = tileSizeType.size
            }
        }
    }
}
struct DashboardMetricTile_Previews: PreviewProvider {
    static var previews: some View {
        DashboardMetricTile(
            id: "Muscle Mass",
            label: "Muscle Mass",
            icon: AppAssets.muscleMass,
            onTap: {},
            onLongPress: {_ in },
            isEditMode: true,
            isDropTarget: false,
            isRemoved: .constant(false),
            tileSizeType: .constant(.small)
        )
        .frame(width: 120, height: 150)
        .previewLayout(.sizeThatFits)
        .padding()
    }
}

import SwiftUI

class DashboardMetricTileViewModel: ObservableObject {
    @Published var tileSize: CGSize
    
    let id: String
    @Published var label: String
    @Published var icon: String
    @Published var isRemoved: Bool
    @Published var tileSizeType: TileSizeType
    let lang = DisplayMetricsStrings.self
    
    init(id: String, label: String, icon: String, initialTileSizeType: TileSizeType, isRemoved: Bool = false) {
        self.id = id
        self.label = label
        self.icon = icon
        self.tileSizeType = initialTileSizeType
        self.isRemoved = isRemoved
        self.tileSize = initialTileSizeType.size
    }
    
    func determineSizeType(for size: CGSize) -> TileSizeType {
        let screenWidth = UIScreen.main.bounds.width - 40
        let smallWidth = (screenWidth - 40) / 3
        let mediumWidth = (screenWidth - 30) / 2
        let largeWidth = screenWidth
        
        if size.width <= smallWidth + (mediumWidth - smallWidth) / 2 {
            return .small
        } else if size.width <= mediumWidth + (largeWidth - mediumWidth) / 2 {
            return .medium
        } else {
            return .large
        }
    }
    
    // MARK: - Modularized helpers for the View
    
    var iconName: String { isRemoved ? "plus.circle" : "minus.circle" }
    var iconColor: Color { isRemoved ? .blue : .gray }
    var iconLabel: String { isRemoved ? lang.addTile : lang.removeTile }
    var iconHint: String { isRemoved ? lang.addTileHint : lang.removeTileHint }
    
    func handleResizeGesture() -> some Gesture {
        DragGesture()
            .onChanged { value in
                let newSize = CGSize(
                    width: max(60, self.tileSizeType.size.width + value.translation.width),
                    height: max(60, self.tileSizeType.size.height + value.translation.height)
                )
                self.tileSize = newSize
            }
            .onEnded { value in
                let newSize = CGSize(
                    width: max(60, self.tileSize.width + value.translation.width),
                    height: max(60, self.tileSize.height + value.translation.height)
                )
                let nearestSizeType = self.determineSizeType(for: newSize)
                withAnimation(.easeInOut(duration: 0.3)) {
                    self.tileSize = nearestSizeType.size
                    self.tileSizeType = nearestSizeType
                }
            }
    }
}
