//
//  DashboardMetricTile.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 09/04/25.
//

import SwiftUI

enum TileSizeType: Int, CaseIterable, Codable {
    case small = 1
    case medium = 2
    case large = 3

    var size: CGSize {
        let screenWidth = UIScreen.main.bounds.width - 40 // Account for padding
        switch self {
        case .small: return CGSize(width: (screenWidth - 40) / 3, height: 120) // 3 items with spacing
        case .medium: return CGSize(width: (screenWidth - 30) / 2, height: 120) // 2 items with spacing
        case .large: return CGSize(width: screenWidth, height: 120)
        }
    }

    var columns: Double {
        switch self {
        case .small: return 1.0
        case .medium: return 2.0
        case .large: return 3.0
        }
    }

    var gridSpan: Int {
        switch self {
        case .small: return 1
        case .medium: return 2
        case .large: return 3
        }
    }
}


struct DashboardMetricTile: View {
    let id: String
    let label: String
    let icon: String
    let onTap: () -> Void
    let onLongPress: (CGPoint) -> Void
    let isEditMode: Bool
    let lang = DisplayMetricsStrings.self
    let isDropTarget: Bool
    var shouldWiggle: Bool {
        isEditMode && !isRemoved && !isDropTarget
    }
    @Binding var isRemoved: Bool

    @State private var tileSize: CGSize = CGSize(width: 120, height: 120)
    @GestureState private var dragOffset: CGSize = .zero
    @Binding var tileSizeType: TileSizeType
   // @State private var tileSize: CGSize

    init(
        id: String,
        label: String,
        icon: String,
        onTap: @escaping () -> Void,
        onLongPress: @escaping (CGPoint) -> Void,
        isEditMode: Bool,
        isDropTarget: Bool,
        isRemoved: Binding<Bool>,
        tileSizeType: Binding<TileSizeType>
    ) {
        self.id = id
        self.label = label
        self.icon = icon
        self.onTap = onTap
        self.onLongPress = onLongPress
        self.isEditMode = isEditMode
        self.isDropTarget = isDropTarget
        self._isRemoved = isRemoved
        self._tileSizeType = tileSizeType
        self._tileSize = State(initialValue: tileSizeType.wrappedValue.size)
    }


    var body: some View {
        GeometryReader { geo in
            Button(action: {
                if !isRemoved {
                    onTap()
                }
            }) {
                ZStack {
                    VStack {
                        ZStack(alignment: .topTrailing) {
                            RoundedRectangle(cornerRadius: 30)
                                .fill(Color(.systemGray6).opacity(0.7))
                                .frame(width: tileSize.width, height: tileSize.height)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 30)
                                        .strokeBorder(style: StrokeStyle(lineWidth: 2, dash: [6]))
                                        .foregroundColor(isDropTarget ? .gray : .clear)
                                )
                                .overlay(
                                    Group {
                                        if isEditMode {
                                            BottomRightCorner(size: 20)
                                                .fill(Color(.systemGray6).opacity(0.6))
                                                .frame(width: 30, height: 30)
                                                .offset(x: -10, y: -8)
                                                .gesture(
                                                    DragGesture()
                                                        .updating($dragOffset) { value, state, _ in
                                                            state = value.translation

                                                            // Real-time preview of size change
                                                            let newSize = CGSize(
                                                                width: max(60, tileSizeType.size.width + value.translation.width),
                                                                height: max(60, tileSizeType.size.height + value.translation.height)
                                                            )
                                                            tileSize = newSize
                                                        }
                                                        .onEnded { value in
                                                            let newSize = CGSize(
                                                                width: max(60, tileSize.width + value.translation.width),
                                                                height: max(60, tileSize.height + value.translation.height)
                                                            )

                                                            // Find the nearest size type based on width primarily
                                                            let nearestSizeType = determineSizeType(for: newSize)

                                                            // Update both local state and binding with animation
                                                            withAnimation(.easeInOut(duration: 0.3)) {
                                                                tileSize = nearestSizeType.size
                                                                tileSizeType = nearestSizeType
                                                            }
                                                        }
                                                )
                                        }
                                    },
                                    alignment: .bottomTrailing
                                )

                            if isEditMode && !isDropTarget {
                                Image(systemName: isRemoved ? "plus.circle" : "minus.circle")
                                    .foregroundColor(isRemoved ? .blue : .gray)
                                    .font(.title3)
                                    .offset(x: 5, y: -5)
                                    .onTapGesture {
                                        if isRemoved { return }
                                        isRemoved.toggle()
                                    }
                                    .accessibilityLabel(isRemoved ? lang.addTile : lang.removeTile)
                                    .accessibilityHint(isRemoved ? lang.addTileHint : lang.removeTileHint)
                            }

                        }
                    }

                    VStack(spacing: 10) {
                        Image(icon)
                            .font(.title)
                            .foregroundColor(.black)
                        Text(label)
                            .font(.subheadline)
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 5)
                    }
                    .padding(.vertical, 10)
                    .opacity(isDropTarget ? 0.3 : 1.0)
                }
                .frame(width: tileSizeType.size.width, height: tileSizeType.size.height)
                .modifier(WiggleIfNeeded(shouldWiggle: shouldWiggle))
            }
            .buttonStyle(PlainButtonStyle())
            .simultaneousGesture(
                LongPressGesture(minimumDuration: 0.1)
                    .onEnded { _ in
                        if !isRemoved {
                            let frame = geo.frame(in: .global)
                            let center = CGPoint(x: frame.midX, y: frame.maxY)
                            onLongPress(center)
                        }
                    }
            )
            .disabled(isRemoved)
        }
        .frame(width: tileSize.width, height: tileSize.height)
        .onAppear {
            // Sync tileSize with tileSizeType on appear
            tileSize = tileSizeType.size
        }
        .onChange(of: tileSizeType) { newSizeType in
            // Update tileSize when tileSizeType changes
            withAnimation(.easeInOut(duration: 0.3)) {
                tileSize = newSizeType.size
            }
        }
    }

    // Determine the appropriate size type based on the new size
    private func determineSizeType(for size: CGSize) -> TileSizeType {
        let screenWidth = UIScreen.main.bounds.width - 40
        let smallWidth = (screenWidth - 40) / 3
        let mediumWidth = (screenWidth - 30) / 2
        let largeWidth = screenWidth

        // Determine based on width thresholds
        if size.width <= smallWidth + (mediumWidth - smallWidth) / 2 {
            return .small
        } else if size.width <= mediumWidth + (largeWidth - mediumWidth) / 2 {
            return .medium
        } else {
            return .large
        }
    }
}

struct DashboardMetricTile_Previews: PreviewProvider {
    static var previews: some View {
        DashboardMetricTile(
            id: "Muscle Mass",
            label: "Muscle Mass",
            icon: AppAssets.muscleMass,
            onTap: {},
            onLongPress: {_ in },
            isEditMode: true,
            isDropTarget: false,
            isRemoved: .constant(false), tileSizeType: .constant(.small)
        )
        .frame(width: 120, height: 150)
        .previewLayout(.sizeThatFits)
        .padding()
    }
}
