//
//  BottomRightCorner.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 26/05/25.
//

import SwiftUI

struct BottomRightCorner: Shape {
    let size: CGFloat
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: CGPoint(x: rect.maxX - size, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY - size))
        path.closeSubpath()
        return path
    }
}
