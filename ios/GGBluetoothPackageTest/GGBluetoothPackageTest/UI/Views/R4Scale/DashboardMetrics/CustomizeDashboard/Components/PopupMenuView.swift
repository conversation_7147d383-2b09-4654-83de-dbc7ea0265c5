//
//  PopupMenuView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 22/05/25.
//

import SwiftUI

struct PopupMenuView: View {
    let onInfo: () -> Void
    let onEdit: () -> Void
    let commonLang = CommonStrings.self
    var body: some View {
        VStack(spacing: 1) {
            menuButton(
                title: commonLang.info,
                icon: AppAssets.moreInfoIconWhite,
                corners: [.topLeft, .topRight],
                action: onInfo
            )
            menuButton(
                title: commonLang.edit,
                icon: AppAssets.metricEditIconWhite,
                corners: [.bottomLeft, .bottomRight],
                action: onEdit
            )
        }
    }
    
    @ViewBuilder
    private func menuButton(title: String, icon: String, corners: UIRectCorner, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                Spacer()
                Image(icon)
                    .font(.subheadline)
                    .foregroundColor(.white)
            }
            .padding(.horizontal, 15)
            .frame(height: 40)
            .frame(width: 140)
        }
        .background(Color.blue)
        .cornerRadius(40, corners: corners)
        .accessibilityElement()
        .accessibilityLabel(Text("\(title) \(commonLang.button)"))
    }
}

struct PopupMenuView_Previews: PreviewProvider {
    static var previews: some View {
        PopupMenuView(
            onInfo: { print("Info tapped") },
            onEdit: { print("Edit tapped") }
        )
        .padding()
        .background(Color.gray.opacity(0.1))
        .previewLayout(.sizeThatFits)
    }
}
