//
//  ScaleModesView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 10/04/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct ScaleModesView: View {
    @StateObject private var viewModel = ScaleModesViewModel()
    @EnvironmentObject var router: Router<DashboardRoutes>
    @EnvironmentObject var deviceConnectionState: DeviceConnectionState
    let device: GGBTDevice
    let category: AppCategory
    
    init(device: GGBTDevice, category: AppCategory) {
        self.device = device
        self.category = category
    }
    
    var body: some View {
        VStack {
            VStack(alignment: .leading) {
                VStack(alignment: .leading, spacing: 15) {
                    Text(DisplayMetricsStrings.changeScaleModeTitle)
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.black)
                    
                    Text(DisplayMetricsStrings.changeScaleModeSubtitle)
                        .font(.subheadline)
                        .foregroundColor(.black)
                }
                .padding(.horizontal)
                .padding(.top)
                
                Picker(DisplayMetricsStrings.scaleModePickerLabel, selection: $viewModel.selectedMode) {
                    Text(DisplayMetricsStrings.allBodyMetrics).tag(DisplayMetricsStrings.allBodyMetrics)
                    Text(DisplayMetricsStrings.weightOnly).tag(DisplayMetricsStrings.weightOnly)
                }
                
                .tint(.blue)
                .pickerStyle(.segmented)
                .padding(.horizontal)
                .padding(.top)
                
                if viewModel.selectedMode == "All Body Metrics" {
                    AllBodyMetricsView(isHeartRateEnabled: $viewModel.isHeartRateEnabled)
                } else {
                    WeightOnlyView()
                }
            }
            
            Spacer()
        }
        .padding(.horizontal)
        .onAppear {
            viewModel.fetchPreferences(for: device.broadcastId)
        }
        .navigationBarBackButtonHidden()
        .navigationTitle(DisplayMetricsStrings.setupTitle)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    router.navigateBack(1)
                }) {
                    Image(systemName: "xmark")
                        .foregroundColor(.blue)
                }
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                       viewModel.saveSettings(for: device, category: category, isDeviceConnected: deviceConnectionState.getDeviceConnectionState(deviceId: device.broadcastId))
                }) {
                    Text(CommonStrings.save.uppercased())                        .foregroundColor(.blue)
                        .fontWeight(.semibold)
                }
            }
        }
    }
}


#Preview {
    ScaleModesView(device: GGBTDevice(name: "", broadcastId: "", password: "", token: "", userNumber: 0, preference: nil, syncAllData: false, batteryLevel: 0, protocolType: "", macAddress: ""), category: .weightGurus)
}

