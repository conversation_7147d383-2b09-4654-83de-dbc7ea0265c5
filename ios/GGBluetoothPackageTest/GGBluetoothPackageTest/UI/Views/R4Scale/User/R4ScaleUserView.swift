//
//  R4ScaleUserView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/03/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct R4ScaleUserView: View {
    @StateObject private var viewModel = R4ScaleUserViewModel()
    let device: GGBTDevice
    let selectedCategory: AppCategory?
    let parentView: UserTabParentView
    @EnvironmentObject var router: Router<DashboardRoutes>
    @State private var showDeleteAlert = false
    @State private var selectedUser: GGBTUser?
    @State private var showEditAlert = false
    @State private var newUserName: String = ""
    @State private var isLoadingAddUser = false
    var lang = DeviceDetailStrings.self
    var commonLang = CommonStrings.self
    var errorLang = ErrorStrings.self
    
    var body: some View {
        ZStack {
            VStack(spacing: 16) {
                if parentView == .deviceDetailView {
                    userHeader(userName: viewModel.userName, isBodyMetricsEnabled: device.preference?.shouldMeasureImpedance ?? false, icon: "applepencil.gen1", iconColor: .blue, iconAction: {
                        newUserName = viewModel.userName
                        showEditAlert = true
                    })
                    .padding(.horizontal, 10)
                    
                    infoRow(title: lang.otherUsers, value: "\(lang.maxUserLimit)")
                        .padding(10)
                        .background(Color.gray.opacity(0.2))
                }else{
                    UserLimitExceededView(lang: lang)
                }
                
                ScrollView {
                    if viewModel.isLoading || viewModel.isDeleting {
                        ProgressView()
                            .scaleEffect(2.0)
                            .padding(.vertical, 50)
                            .font(.title)
                            .frame(maxWidth: .infinity)
                    } else {
                        ForEach(Array(viewModel.users.enumerated()), id: \.element.token) { index, user in
                            VStack {
                                userItem(user: user) {
                                    selectedUser = user
                                    showDeleteAlert = true
                                }
                                .padding(.horizontal, 10)
                                .swipeActions {
                                    Button(role: .destructive) {
                                        selectedUser = user
                                        showDeleteAlert = true
                                    } label: {
                                        Label(commonLang.delete, systemImage: commonLang.trash)
                                    }
                                }
                                
                                if !(parentView == .deviceItemView && index == viewModel.users.count - 1) {
                                    Divider()
                                        .padding(.horizontal, parentView == .deviceItemView ? 10 : 0)
                                }
                            }
                        }
                    }
                }
                
                if parentView == .deviceDetailView {
                    Spacer()
                }
            }
            .background(parentView == .deviceItemView ? Color.white : Color.clear)
            .cornerRadius(parentView == .deviceItemView ? 10 : 0)
            .shadow(radius: parentView == .deviceItemView ? 5 : 0)
            .padding(.horizontal, parentView == .deviceItemView ? 20 : 0)
            .padding(.top, parentView == .deviceItemView ? 20 : 20)
            .padding(.bottom, parentView == .deviceItemView ? 40 : 0)
            .blur(radius: showEditAlert ? 5 : 0)
            .onAppear {
                viewModel.fetchUserAndUserList(device: device)
            }
            .navigationBarItems(trailing:
                                    Button(action: {
                viewModel.fetchR4scaleUsers(device: device)
            }) {
                Text(commonLang.refresh.uppercased())
                    .fontWeight(.semibold)
            }
            )
            .alert(
                lang.userDeleteAlertTitle,
                isPresented: $showDeleteAlert,
                presenting: selectedUser
            ) { user in
                Button(commonLang.cancel, role: .cancel) { }
                Button(commonLang.ok, role: .destructive) {
                    viewModel.deleteUserForR4Scale(device: device, userName: user.name, parentView: parentView, router: router)
                }
            } message: { user in
                Text(lang.deleteUserConfirmation(user.name))
            }
            
            if showEditAlert {
                Color.clear.opacity(0.2)
                    .edgesIgnoringSafeArea(.all)
                EditUsernameView(
                    isPresented: $showEditAlert,
                    newUserName: $newUserName,
                    onSave: {
                        Task {
                            viewModel.isLoading = true
                            viewModel.updateUserName(newName: newUserName, device: device)
                            viewModel.fetchR4scaleUsers(device: device)
                            viewModel.isLoading = false
                            showEditAlert = false
                        }
                    },
                    onCancel: {},
                    parentView: .UsersTab
                )
            }
            
            if viewModel.isLoadingAddUser {
                ZStack {
                    Color.white
                        .ignoresSafeArea()
                    
                    StatusIndicationLoaderView(
                        customTitle: commonLang.addingUser,
                        loaderImage: "person.fill.badge.plus"
                    )
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.white)
                    .cornerRadius(20)
                    .ignoresSafeArea()
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                    .zIndex(1)
                    .animation(.easeInOut(duration: 0.4), value: viewModel.isLoadingAddUser)
                }
                .transition(.opacity)
                .animation(.easeInOut, value: viewModel.isLoadingAddUser)
            }
        }
    }
}

extension R4ScaleUserView {
    private var defaultDevice: GGBTDevice {
        GGBTDevice(name: "", broadcastId: "", password: "", token: "", userNumber: 1, preference: nil, syncAllData: false, batteryLevel: 0, protocolType: "", macAddress: "")
    }
    
    @ViewBuilder
    private func userHeader(userName: String, isBodyMetricsEnabled: Bool, icon: String, iconColor: Color, iconAction: @escaping () -> Void) -> some View {
        HStack {
            Text(userName.capitalized)
                .font(.title)
                .fontWeight(.semibold)
                .foregroundColor(.secondary)
            
            if isBodyMetricsEnabled == false {
                WeightOnlyModeIconView(width: 30, height: 30)
            }
            
            Spacer()
            
            Button(action: iconAction) {
                Image(systemName: icon)
                    .padding(.trailing, 10)
                    .font(.title)
                    .fontWeight(.semibold)
                    .foregroundColor(iconColor)
            }
        }
    }
    
    @ViewBuilder
    private func infoRow(title: String, value: String) -> some View {
        HStack {
            Text(title.capitalized)
            Spacer()
            Text(value)
                .foregroundColor(.gray)
        }
    }
    
    @ViewBuilder
    private func userItem(user: GGBTUser, onDelete: @escaping () -> Void) -> some View {
        HStack {
            VStack(alignment: .leading) {
                HStack{
                    Text(user.name)
                        .font(.headline)
                    
                    if user.isBodyMetricsEnabled == false {
                        WeightOnlyModeIconView()
                    }
                }
                Text("\(lang.lastActiveOn) \(formatLastActiveTimestamp(user.lastActive))")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            Spacer()
            Button(action: onDelete) {
                Image(systemName: commonLang.trash)
                    .foregroundColor(.red)
            }
        }
        .padding(.vertical, 3)
    }
    
    @ViewBuilder
    private func UserLimitExceededView(lang: DeviceDetailStrings.Type) -> some View {
        VStack(alignment: .leading) {
            Text(lang.userLimitExceeded)
                .fontWeight(.bold)
                .font(.title)
            
            Text(lang.deleteInactiveUserPrompt)
                .font(.subheadline)
        }
        .padding(.vertical)
    }
    
    @ViewBuilder
    func WeightOnlyModeIconView(width: CGFloat = 15, height: CGFloat = 15) -> some View {
        Image(AppAssets.weightOnlyModeDiscoveredIcon)
            .resizable()
            .scaledToFit()
            .frame(width: width, height: height)
    }
    
}

#Preview {
    R4ScaleUserView(
        device: GGBTDevice(name: "", broadcastId: "", password: "", token: "", userNumber: 1, preference: nil, syncAllData: false, batteryLevel: 1, protocolType: "", macAddress: ""),
        selectedCategory: .weightGurus,
        parentView: .deviceDetailView
    )
}
