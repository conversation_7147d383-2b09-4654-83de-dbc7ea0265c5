//
//  R4ScaleUserViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/03/25.
//

import Foundation
import SwiftUI
@preconcurrency import GGBluetoothSwiftPackage

class R4ScaleUserViewModel: ObservableObject {
    @Injector var bluetoothService: BluetoothService
    @Injector var userProfileService: UserProfileUpdationService
    @Injector var deviceService: DeviceService
    @Published var users: [GGBTUser] = []
    @Published var isLoading = false
    @Published var isDeleting = false
    @Published var userName: String = "Unknown"
    @Published var isLoadingAddUser = false
    private let logger: AppLogger = AppLogger(category: String(describing: DeviceService.self))
    private let r4Helper = R4ScaleHelper()
    let toastLang = ToastMessageStrings.self
        
    func fetchR4scaleUsers(device: GGBTDevice) {
        DispatchQueue.main.async {
            self.isLoading = true
        }
        Task {
            let fetchedUsers = await bluetoothService.fetchUsers(for: device)
            DispatchQueue.main.async {
                self.users = fetchedUsers.filter { $0.name != self.userName }
                self.isLoading = false
            }
        }
    }
        
    func deleteUserForR4Scale(device: GGBTDevice, userName: String, parentView: UserTabParentView, router: Router<DashboardRoutes>) {
        DispatchQueue.main.async {
            self.isDeleting = true
        }

        Task {
            let users = await bluetoothService.fetchUsers(for: device)

            guard let user = users.first(where: { $0.name == userName }) else {
                DispatchQueue.main.async {
                    self.isDeleting = false
                }
                return
            }

            let updatedDevice = GGBTDevice(
                name: user.name,
                broadcastId: device.broadcastId,
                password: device.password,
                token: user.token,
                userNumber: device.userNumber,
                preference: device.preference,
                syncAllData: device.syncAllData,
                batteryLevel: device.batteryLevel,
                protocolType: device.protocolType,
                macAddress: device.macAddress
            )
            
            await bluetoothService.deleteUser(device: updatedDevice, canDisconnect: false)

            if parentView == .deviceItemView {
                addUser(for: device, router: router, completion: {
                    router.navigateToRoot()
                })
            } else {
                fetchR4scaleUsers(device: device)
            }

            DispatchQueue.main.async {
                self.isDeleting = false
            }
        }
    }
    
    func fetchUserAndUserList(device: GGBTDevice) {
        DispatchQueue.main.async {
            self.isLoading = true
        }
        
        Task {
            guard let deviceToken = deviceService.fetchDevice(by: device.broadcastId)?.token else {
                DispatchQueue.main.async {
                    self.userName = "Unknown"
                    self.users = []
                    self.isLoading = false
                }
                return
            }
            
            let fetchedUsers = await bluetoothService.fetchUsers(for: device)
            let matchingUser = fetchedUsers.first(where: { $0.token == deviceToken })
            
            DispatchQueue.main.async {
                if let user = matchingUser {
                    self.userName = user.name
                } else if let displayName = device.preference?.displayName {
                    self.userName = displayName
                } else {
                    self.userName = "Unknown"
                }
                self.users = fetchedUsers.filter { $0.name != self.userName }
                self.isLoading = false
            }
        }
    }
    
    func updateUserName(newName: String, device: GGBTDevice) {
        guard let storedDevice = deviceService.fetchDevice(by: device.broadcastId) else {
            return
        }
        let token = storedDevice.token
        userProfileService.updateUserName(newName: newName)
        let updatedDevice = GGBTDevice(
            name: newName,
            broadcastId: device.broadcastId,
            password: device.password,
            token: token,
            userNumber: device.userNumber,
            preference: device.preference,
            syncAllData: device.syncAllData,
            batteryLevel: device.batteryLevel,
            protocolType: device.protocolType,
            macAddress: device.macAddress
        )
        do{
            Task{
                try await  bluetoothService.updateAccount(device: updatedDevice)
            }
        } catch{
            let nsError = NSError(domain: String(describing: DeviceService.self), code: 1, userInfo: [NSLocalizedDescriptionKey: "Error while updating user name. \(error.localizedDescription)"])
            self.logger.error(error: nsError, function: #function, line: #line)
            
        }
        self.userName = newName
    }
    
    func addUser(for device: GGBTDevice,router: Router<DashboardRoutes>, completion: @escaping () -> Void) {
        DispatchQueue.main.async {
            self.isLoadingAddUser = true
        }

        Task {
            let _ = await r4Helper.addR4ScaleUser(
                category: .weightGurus,
                device: device,
                onDuplicateUser: { duplicateUser in
                    ToastService.shared.presentToast(with: self.toastLang.duplicateUserFound)
                    DispatchQueue.main.async {
                        self.isLoadingAddUser = false
                    }
                },
                onUserAdded: {
                    ToastService.shared.presentToast(with: self.toastLang.userAddedSuccessfully)
                    DispatchQueue.main.async {
                        self.isLoadingAddUser = false
                        completion()
                    }
                }, router: router,
                showAlert: { title, message, _, _, _ in
                    ToastService.shared.presentToast(with: "\(title): \(message)")
                },
                navigateToLogin: nil
            )
        }
    }
}
