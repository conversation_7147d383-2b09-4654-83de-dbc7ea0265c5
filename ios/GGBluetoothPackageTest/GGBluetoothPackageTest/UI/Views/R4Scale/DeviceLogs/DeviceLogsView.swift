//
//  deviceLogsView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 24/02/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct DeviceLogsView: View {
    @StateObject var viewModel = DeviceLogsViewModel()
    let device: GGBTDevice
    let lang = DeviceLogsStrings.self
    
    var body: some View {
        VStack {
            SettingsOptionView(
                title: lang.fetchLogsTitle,
                buttonText: lang.fetchLogsButtonText,
                iconName: lang.fetchLogsIconName,
                buttonAction: {
                    viewModel.fetchDeviceLogs(for: device)
                }
            )
            
            if viewModel.isLoading {
                ProgressView(value: Double(viewModel.receivedLogsCount), total: Double(viewModel.totalLogsCount)) {
                    Text(lang.fetchingLogs)
                        .font(.headline)
                } currentValueLabel: {
                    Text(String(format: lang.receivedLogsCount, viewModel.receivedLogsCount, viewModel.totalLogsCount))
                        .font(.subheadline)
                }
                .padding(16)
                Spacer()
            } else {
                if viewModel.logs.isEmpty {
                    Text(lang.noLogsAvailable)
                        .font(.title3)
                        .foregroundColor(.gray)
                        .padding()
                    Spacer()
                } else {
                    List(viewModel.logs, id: \.self) { log in
                        Text(log)
                            .font(.system(size: 14, weight: .regular, design: .monospaced))
                            .padding(.vertical, 4)
                    }
                    .listStyle(PlainListStyle())
                }
            }
        }
        .toolbar {
            ToolbarItem(placement: .topBarTrailing) {
                Button(action: {
                    viewModel.exportLogs()
                }, label: {
                    Image(systemName: lang.exportLogsIconName)
                })
            }
        }
        .sheet(isPresented: Binding(
            get: { viewModel.showShareSheet && viewModel.logFileURL != nil },
            set: { newValue in
                viewModel.showShareSheet = newValue
                if !newValue {
                    viewModel.logFileURL = nil
                }
            })
        ) {
            if let logFileURL = viewModel.logFileURL {
                ShareSheet(activityItems: [logFileURL])
                    .presentationDetents([.fraction(0.5)])
                    .presentationDragIndicator(.visible)
            }
        }
        .navigationTitle(lang.navigationTitle)
        .navigationBarTitleDisplayMode(.inline)
    }
}
