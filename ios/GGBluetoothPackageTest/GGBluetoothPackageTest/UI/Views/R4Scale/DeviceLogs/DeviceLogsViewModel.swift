//
//  DeviceLogsViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 16/04/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

class DeviceLogsViewModel: ObservableObject {
    @Published var logs: [String] = []
    @Published var isLoading: Bool = false
    @Published var receivedLogsCount: Int = 0
    @Published var totalLogsCount: Int = 1
    @Published var isLogsFetched: Bool = false
    @Published var showShareSheet: Bool = false
    @Published var logFileURL: URL?
    private let logger: AppLogger = AppLogger(category: String(describing: DeviceLogsViewModel.self))
    private let btService = BluetoothService.shared

    func fetchDeviceLogs(for device: GGBTDevice?) {
        guard let device = device else {
            Task {
                await MainActor.run {
                    logs = [DeviceLogsStrings.deviceNotProvided]
                }
            }
            return
        }

        Task {
            await MainActor.run {
                isLoading = true
                logs = []
            }

            let result = await btService.getDeviceLogs(device: device)

            await MainActor.run {
                totalLogsCount = result.logs.count
            }

            for (index, logChunk) in result.logs.enumerated() {
                await MainActor.run {
                    receivedLogsCount = index + 1
                    logs.append(logChunk.log ?? "")
                }
                try? await Task.sleep(nanoseconds: 500_000_000)
            }

            await MainActor.run {
                isLogsFetched = true
                isLoading = false
            }
        }
    }


    func exportLogs() {
        let logsContent = logs.joined(separator: "\n")
        let fileManager = FileManager.default
        guard let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first else {
            let nsError = NSError(
                domain: String(describing: DeviceLogsViewModel.self),
                code: 1,
                userInfo: [NSLocalizedDescriptionKey: DeviceLogsStrings.failedToAccessDocumentsDirectory]
            )
            self.logger.error(error: nsError, function: #function, line: #line)
            return
        }
        let logFileName = String(format: DeviceLogsStrings.logFileName, Date().timeIntervalSince1970)
        let logFileURL = documentsDirectory.appendingPathComponent(logFileName)

        do {
            try logsContent.write(to: logFileURL, atomically: true, encoding: .utf8)
            Task{
                await MainActor.run {
                    self.logFileURL = logFileURL
                    showShareSheet = true
                }
            }
        } catch {
            let nsError = NSError(
                domain: String(describing: DeviceLogsViewModel.self),
                code: 1,
                userInfo: [NSLocalizedDescriptionKey: "Error writing logs to file: \(error.localizedDescription)"]
            )
            self.logger.error(error: nsError, function: #function, line: #line)
        }
    }
}
