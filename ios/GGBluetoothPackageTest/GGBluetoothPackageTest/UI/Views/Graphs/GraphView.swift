//
//  GraphView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 30/04/25.
//

import SwiftUI
import Charts
import GGBluetoothSwiftPackage

struct GraphView: View {
    @StateObject private var viewModel : GraphViewModel
    var lang = GraphStrings.self
    @State var parentView: GraphParentViewType
    let appCategory: AppCategory
    let device: GGBTDevice
    @Binding var selectedSKU: String
    
    init(parentView: GraphParentViewType, device: GGBTDevice, appCategory: AppCategory, selectedSKU: Binding<String>) {
        self._viewModel = StateObject(wrappedValue: GraphViewModel(appCategory: appCategory))
        self.parentView = parentView
        self.device = device
        self.appCategory = appCategory
        self._selectedSKU = selectedSKU
    }
    
    var body: some View {
        VStack(alignment: .leading) {
            if viewModel.isGraphDataEmpty {
                EmptyPlaceholderView()
            } else {
                if parentView == .history {
                    renderCategoryContent()
                }
                
                graphChartViewForAppCategory()
                
                Picker(lang.timeFrame, selection: $viewModel.selectedTimeFrame) {
                    ForEach(TimeFrame.allCases, id: \.self) {
                        Text($0.rawValue.capitalized).tag($0)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()
                
                if parentView != .history {
                    renderCategoryContent()
                }
            }
        }
        .onChange(of: selectedSKU) {
            viewModel.selectedSKU = selectedSKU
            viewModel.fetchDataBasedOnParentView(device: device)
            viewModel.selectedPoint = nil
            if selectedSKU !=  RPMDeviceSKUs.bloodGlucoseMeter {
                viewModel.displayedWeight = viewModel.calculateAverageWeight()
            }
        }
        .onAppear {
            viewModel.selectedSKU = selectedSKU
            viewModel.setParentView(parentView)
            viewModel.fetchDataBasedOnParentView(device: device)
            viewModel.displayedWeight = viewModel.calculateAverageWeight()
        }
        .padding()
    }
    
    @ViewBuilder
    func EmptyPlaceholderView() -> some View {
        VStack {
            Spacer()
            VStack(spacing: 16) {
                Image(systemName: "tray")
                    .font(.system(size: 48))
                    .foregroundColor(.gray)
                Text(lang.noDataAvailable)
                    .font(.headline)
                    .foregroundColor(.gray)
                Text(lang.startMeasuring)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity)
            Spacer()
        }
    }

    @ViewBuilder
    func graphMetricText(
        text: String,
        fontSize: CGFloat,
        fontWeight: Font.Weight,
        color: Color
    ) -> some View {
        Text(text)
            .font(.system(size: fontSize, weight: fontWeight))
            .foregroundColor(color)
    }
    
    @ViewBuilder
    private func renderCategoryContent() -> some View {
        switch appCategory {
        case .weightGurus:
            if let weight = viewModel.displayedWeight ?? viewModel.calculateAverageWeight() {
                WeightDisplayView(weight: weight)
                    .padding(.top, 0)
            }
            
        case .balanceHealth:
            let selectedEntry: BalanceHealthEntry? = {
                if case let .balanceHealth(entry) = viewModel.selectedPoint {
                    return entry
                }
                return nil
            }()
            
            let bpColor = getColorFor(entry: selectedEntry)

            HStack {
                GraphMetricSection(
                    title: lang.mmHg,
                    content: HStack(spacing: 0) {
                        graphMetricText(
                            text: "\(viewModel.getSystole())",
                            fontSize: 30,
                            fontWeight: .semibold,
                            color: bpColor
                        )
                        graphMetricText(
                            text: lang.slash,
                            fontSize: 30,
                            fontWeight: .regular,
                            color: .primary
                        )
                        graphMetricText(
                            text: "\(viewModel.getDiastole())",
                            fontSize: 30,
                            fontWeight: .semibold,
                            color: bpColor
                        )
                    }
                )
                Spacer()
                GraphMetricSection(
                    title: lang.pulse,
                    content: graphMetricText(
                        text: "\(viewModel.getPulse())",
                        fontSize: 30,
                        fontWeight: .semibold,
                        color: .blue
                    )
                )
            }
            .padding(.horizontal)

        case .rpm where viewModel.selectedSKU == RPMDeviceSKUs.bloodGlucoseMeter:
            GraphMetricSection(
                title: lang.bgm.uppercased(),
                content: graphMetricText(
                    text: viewModel.getBGMValueText(),
                    fontSize: 40,
                    fontWeight: .semibold,
                    color: .blue
                )
            )
            .padding(.horizontal)
            
        default:
            if let weight = viewModel.displayedWeight ?? viewModel.calculateAverageWeight() {
                WeightDisplayView(weight: weight)
                    .padding(.top, 0)
            }
        }
    }
    
    @ViewBuilder
    private func graphChartViewForAppCategory() -> some View {
        let graphDataResult = viewModel.getGraphData(for: appCategory)
        GraphChartView(
            data: graphDataResult.graphWeights,
            balanceHealthdata: graphDataResult.healthEntries,
            goalWeight: graphDataResult.goalWeight,
            selectedPoint: $viewModel.selectedPoint,
            displayedWeight: $viewModel.displayedWeight,
            selectedSKU: $viewModel.selectedSKU,
            fullData: graphDataResult.rawWeights,
            yAxisTicks: viewModel.yAxisTicks,
            extendedMaxY: viewModel.extendedMaxY,
            parentView: parentView,
            selectedTimeFrame: viewModel.selectedTimeFrame,
            appCategory: appCategory,
            percentileData: graphDataResult.babyPercentiles,
            BGMData: graphDataResult.bgmEntries
        )
    }
}

#Preview {
    GraphView(parentView: .deviceDetail, device: DefaultDevice.ggbtDevice, appCategory: .weightGurus, selectedSKU: .constant(""))
}
