//
//  GraphViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 30/04/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

class GraphViewModel: ObservableObject {
    @Published var selectedTimeFrame: TimeFrame = .week
    @Published var displayedWeight: Double?
    @Published var selectedPoint: SelectedPoint?
    @Published var weightData: [GraphWeightEntry] = []
    @Published var balanceHealthData: [BalanceHealthEntry] = []
    @Published var BGMData: [BGMData] = []
    @Published var goalWeight: Double = 63.0
    @Published var selectedSKU: String = RPMDeviceSKUs.bloodGlucoseMeter
    @Published var rpmBloodGlucoseData: [BGMData] = []
    @Published var rpmWeightData: [GraphWeightEntry] = []
    @Published var rpmBabyWeightData: [GraphWeightEntry] = []
    @Injector var measurementService: MeasurementEntryService
    @Injector var userProfileService: UserProfileUpdationService
    @Injector var percentileService: PercentileService
    private var parentView: GraphParentViewType?
    let appCategory : AppCategory
    var yAxisTicks: [Double] {
        let values: [Double]
        switch appCategory {
        case .weightGurus:
            values = filteredData(for: selectedTimeFrame, data: weightData, dateKeyPath: \.date, valueKeyPath: \.weight).map(\.weight)
            
        case .balanceHealth:
            let filteredSystole = filteredData(for: selectedTimeFrame, data: balanceHealthData, dateKeyPath: \.date, valueKeyPath: \.systole).map { Double($0.systole) }
            let filteredDiastole = filteredData(for: selectedTimeFrame, data: balanceHealthData, dateKeyPath: \.date, valueKeyPath: \.diastole).map { Double($0.diastole) }
            let filteredPulse = filteredData(for: selectedTimeFrame, data: balanceHealthData, dateKeyPath: \.date, valueKeyPath: \.pulse).map { Double($0.pulse) }
            values = filteredSystole + filteredDiastole + filteredPulse
            
        case .rpm:
            switch selectedSKU {
            case RPMDeviceSKUs.bloodGlucoseMeter:
                values = filteredData(for: selectedTimeFrame, data: BGMData, dateKeyPath: \.date, valueKeyPath: \.glucose).map(\.glucose)
            case RPMDeviceSKUs.weighingScale:
                values = filteredData(for: selectedTimeFrame, data: rpmWeightData, dateKeyPath: \.date, valueKeyPath: \.weight).map(\.weight)
            case RPMDeviceSKUs.babyScale:
                values = filteredData(for: selectedTimeFrame, data: rpmBabyWeightData, dateKeyPath: \.date, valueKeyPath: \.weight).map(\.weight)
            default:
                values = filteredData(for: selectedTimeFrame, data: weightData, dateKeyPath: \.date, valueKeyPath: \.weight).map(\.weight)
            }
            
        default:
            values = filteredData(for: selectedTimeFrame, data: weightData, dateKeyPath: \.date, valueKeyPath: \.weight).map(\.weight)
        }
        
        return generateYAxisTicks(from: values)
    }
    
    var extendedMaxY: Double {
        guard let max = yAxisTicks.last, let min = yAxisTicks.first else { return 0 }
        let range = max - min
        
        guard let parentView = parentView else {
            return max + (max * 0.2)
        }
        
        switch parentView {
        case .history:
            return   0
            
        case .deviceDetail:
            switch range {
            case ...10:
                return max + (max * 0.025)
            case ...20:
                return max + (max * 0.15)
            default:
                return max + (max * 0.15)
            }
        }
    }
    
    var goalWeightIfInRange: Double? {
        let weights: [GraphWeightEntry] = filteredData(for: selectedTimeFrame, data: weightData, dateKeyPath: \.date, valueKeyPath: \.weight)
        guard let min = weights.map({ $0.weight }).min(), let max = weights.map({ $0.weight }).max() else { return nil }
        return (goalWeight >= min && goalWeight <= max) ? goalWeight : nil
    }
    
    var isGraphDataEmpty: Bool {
        switch appCategory {
        case .weightGurus:
            return weightData.isEmpty
        case .balanceHealth:
            return balanceHealthData.isEmpty
        case .rpm:
            switch selectedSKU {
            case RPMDeviceSKUs.bloodGlucoseMeter:
                return BGMData.isEmpty
            case RPMDeviceSKUs.weighingScale:
                return rpmWeightData.isEmpty
            case RPMDeviceSKUs.babyScale:
                return rpmBabyWeightData.isEmpty
            default:
                return weightData.isEmpty
            }
        default:
            return weightData.isEmpty
        }
    }
        
    init(appCategory: AppCategory) {
        self.appCategory = appCategory
        self.displayedWeight = calculateAverageWeight()
    }
    
    func setParentView(_ parentView: GraphParentViewType) {
        self.parentView = parentView
    }
    
    func fetchDataBasedOnParentView(device: GGBTDevice) {
        guard let parentView = parentView else { return }

        switch parentView {
        case .deviceDetail:
            let broadcastId = device.broadcastId
            let entries = measurementService.fetchMeasurementEntriesWithBroadcastId(forBroadcastId: broadcastId)
            let operations = entries.map { entry in
                Operation(
                    appCategory: AppCategory(rawValue: entry.appCategory ?? AppCategory.all.rawValue) ?? .all,
                    bgm: Int(entry.bgm),
                    bmi: Float(entry.bmi),
                    bodyFat: Int(entry.bodyFat),
                    boneMass: Int(entry.boneMass),
                    broadcastId: entry.broadcastID ?? "",
                    broadcastIdString: entry.broadcastIDString ?? "",
                    bmr: Int(entry.bmr),
                    deviceSKU: entry.deviceSKU ?? CommonStrings.unknown,
                    diastolic: Int(entry.diastolic),
                    entryTimestamp: entry.entryTimestamp ?? Date(),
                    id: UUID(),
                    impedance: Int(entry.impedance),
                    meanPressure: Int(entry.meanPressure),
                    metabolicAge: Int(entry.metabolicAge),
                    muscleMass: Int(entry.muscleMass),
                    operationType: .create,
                    proteinPercent: Int(entry.proteinPercent),
                    pulse: Int(entry.pulse),
                    protocolType: entry.protocolType,
                    serverTimestamp: nil,
                    skeletalMusclePercent: Int(entry.skeletalMusclePercent),
                    source: Source(rawValue: entry.source ?? CommonStrings.unknown) ?? .manual,
                    spo: Int(entry.spo),
                    subcutaneousFatPercent: Int(entry.subcutaneousFatPercent),
                    systolic: Int(entry.systolic),
                    temperature: Float(entry.temperature),
                    visceralFatLevel: Int(entry.visceralFatLevel),
                    water: Int(entry.water),
                    weight: Float(entry.weight),
                    weightInKg: Float(entry.weightInKg),
                    measurementUnit: Unit(rawValue: entry.unit ?? CommonStrings.unknown) ?? .unknown,
                    pulseAmplitudeIndex: Float(entry.pulseAmplitudeIndex)
                )
            }
            if appCategory == .rpm{
                selectedSKU = getSKU(device.name)
            }
            populateData(from: operations, appCategory: appCategory)

        case .history:
            let appCategory = appCategory
            let allEntries = measurementService.getAllMeasurementEntries()
            let filtered = allEntries.filter { $0.appCategory.rawValue == appCategory.rawValue }
            populateData(from: filtered, appCategory: appCategory)
        }

        fetchAndSetGoalWeight()
    }

    private func populateData(from entries: [Operation], appCategory: AppCategory?) {
        switch appCategory {
        case .weightGurus:
            weightData = entries.map {
                GraphWeightEntry(
                    date: $0.entryTimestamp,
                    weight: Double($0.weightInKg ?? 0.0),
                    sku: $0.deviceSKU
                )
            }

        case .balanceHealth:
            balanceHealthData = entries.compactMap {
                BalanceHealthEntry(
                    date: $0.entryTimestamp,
                    systole: $0.systolic ?? 0,
                    diastole: $0.diastolic ?? 0,
                    pulse: $0.pulse ?? 0
                )
            }

        case .rpm:
            populateRPMData(from: entries)

        default:
            weightData = entries.map {
                GraphWeightEntry(
                    date: $0.entryTimestamp,
                    weight: Double($0.weightInKg ?? 0.0),
                    sku: $0.deviceSKU
                )
            }
        }
    }

    private func populateRPMData(from entries: [Operation]) {
        BGMData = entries.compactMap {
            guard $0.deviceSKU == RPMDeviceSKUs.bloodGlucoseMeter, let glucose = $0.bgm else { return nil }
            return GGBluetoothPackageTest.BGMData(
                date: $0.entryTimestamp,
                glucose: Double(glucose),
                unit: $0.measurementUnit?.rawValue ?? ""
            )
        }

        rpmWeightData = entries.compactMap {
            guard $0.deviceSKU == RPMDeviceSKUs.weighingScale, let weight = $0.weightInKg else { return nil }
            return GGBluetoothPackageTest.GraphWeightEntry(
                date: $0.entryTimestamp,
                weight: Double(weight),
                sku: $0.deviceSKU
            )
        }

        rpmBabyWeightData = entries.compactMap {
            guard $0.deviceSKU == RPMDeviceSKUs.babyScale, let weight = $0.weightInKg else { return nil }
            return GGBluetoothPackageTest.GraphWeightEntry(
                date: $0.entryTimestamp,
                weight: Double(weight),
                sku: $0.deviceSKU
            )
        }
    }


    
    func filteredData<T, Value: Numeric & Comparable>(
        for timeFrame: TimeFrame,
        data: [T],
        dateKeyPath: KeyPath<T, Date>,
        valueKeyPath: KeyPath<T, Value>
    ) -> [T] {
        guard !data.isEmpty else { return [] }
        let calendar = Calendar.current
        
        switch timeFrame {
        case .week:
            let sorted = data.sorted { $0[keyPath: dateKeyPath] < $1[keyPath: dateKeyPath] }
            guard let latest = sorted.last?[keyPath: dateKeyPath] else { return [] }
            let weekAgo = calendar.date(byAdding: .day, value: -7, to: latest)!
            let recentEntries = sorted.filter { $0[keyPath: dateKeyPath] >= weekAgo }
            let grouped = Dictionary(grouping: recentEntries) { calendar.startOfDay(for: $0[keyPath: dateKeyPath]) }
            return grouped.compactMap { _, vals in vals.max(by: { $0[keyPath: dateKeyPath] < $1[keyPath: dateKeyPath] }) }
                .sorted { $0[keyPath: dateKeyPath] < $1[keyPath: dateKeyPath] }
            
        case .month:
            let today = Date()
            let recentStart = calendar.date(byAdding: .day, value: -30, to: today)!
            var recent = data.filter { $0[keyPath: dateKeyPath] >= recentStart }
            if recent.isEmpty, let latestDate = data.map({ $0[keyPath: dateKeyPath] }).max() {
                let fallbackStart = calendar.date(byAdding: .day, value: -30, to: latestDate)!
                recent = data.filter { $0[keyPath: dateKeyPath] >= fallbackStart && $0[keyPath: dateKeyPath] <= latestDate }
            }
            let grouped = Dictionary(grouping: recent) { calendar.startOfDay(for: $0[keyPath: dateKeyPath]) }
            return grouped.compactMap { _, values in
                values.max(by: { $0[keyPath: dateKeyPath] < $1[keyPath: dateKeyPath] })
            }.sorted(by: { $0[keyPath: dateKeyPath] < $1[keyPath: dateKeyPath] })
            
        case .year, .total:
            let start = timeFrame == .year
            ? calendar.date(byAdding: .month, value: -12, to: Date())!
            : Date.distantPast
            
            let relevant = data.filter { $0[keyPath: dateKeyPath] >= start }
            let grouped = Dictionary(grouping: relevant) {
                calendar.dateComponents([.year, .month], from: $0[keyPath: dateKeyPath])
            }
            return grouped.compactMap { comps, vals in
                guard let date = calendar.date(from: comps) else { return nil }
                let avg = vals.reduce(0) { $0 + Double(truncating: $1[keyPath: valueKeyPath] as? NSNumber ?? 0) } / Double(vals.count)
                return vals.first
            }.sorted { $0[keyPath: dateKeyPath] < $1[keyPath: dateKeyPath] }
        }
    }
    
    func calculateAverageWeight() -> Double? {
        let weights: [Double]? = {
            switch appCategory {
            case .rpm:
                switch selectedSKU {
                case RPMDeviceSKUs.weighingScale:
                    return rpmWeightData.map(\.weight)
                case RPMDeviceSKUs.babyScale:
                    return rpmBabyWeightData.map(\.weight)
                default:
                    return weightData.map(\.weight)
                }
            default:
                return weightData.map(\.weight)
            }
        }()
        guard let values = weights, !values.isEmpty else { return nil }
        return values.reduce(0, +) / Double(values.count)
    }
    
    func updateDisplayedWeight(with selectedPoint: GraphWeightEntry?) {
        displayedWeight = selectedPoint?.weight ?? calculateAverageWeight()
    }
    
    func fetchAndSetGoalWeight() {
        if let profile = userProfileService.fetchUserProfile() {
            goalWeight = profile.goalWeight
        }
    }
    
    func calculateGoalWeightPercentage(for weight: Double) -> Int {
        return Int((weight / goalWeight) * 100)
    }
    
    private func generateYAxisTicks(from values: [Double]) -> [Double] {
        guard let minValue = values.min(), let maxValue = values.max() else {
            return []
        }
        
        let range = maxValue - minValue
        let interval: Double = {
            switch range {
            case ...10: return 1
            case ...50: return 5
            case ...100: return 10
            case ...1000: return 100
            default: return 1000
            }
        }()
        
        let start = floor(minValue / interval) * interval
        let end = ceil(maxValue / interval) * interval
        return stride(from: start, through: end, by: interval).map { $0 }
    }
    
    private func getValue<T>(_ keyPath: KeyPath<BalanceHealthEntry, T>) -> T where T: AdditiveArithmetic & Comparable & BinaryInteger {
        if let selected = selectedPoint, case .balanceHealth(let entry) = selected {
            return entry[keyPath: keyPath]
        } else if balanceHealthData.count == 1 {
            return balanceHealthData.first?[keyPath: keyPath] ?? .zero
        } else if balanceHealthData.count > 1 {
            let total = balanceHealthData.map { $0[keyPath: keyPath] }.reduce(.zero, +)
            return total / T(balanceHealthData.count)
        } else {
            return .zero
        }
    }
    
    func getSystole() -> Int {
        getValue(\.systole)
    }
    
    func getDiastole() -> Int {
        getValue(\.diastole)
    }
    
    func getPulse() -> Int {
        getValue(\.pulse)
    }
    
    func getBGMValueText(filteredBGMData: [BGMData]? = nil) -> String {
        if let selected = selectedPoint, case let .BGM(entry) = selected {
            return String(format: "%.1f", entry.glucose)
        }
        let bgmData = filteredBGMData ?? BGMData
        let glucoseValues = bgmData.compactMap { $0.glucose }
        if !glucoseValues.isEmpty {
            let avg = glucoseValues.reduce(0, +) / Double(glucoseValues.count)
            return String(format: "%.1f", avg)
        }
        return "0"
    }
    
    func getGraphData(for appCategory: AppCategory) -> GraphDataResult {
        func filteredWeights(from data: [GraphWeightEntry]) -> [GraphWeightEntry] {
            filteredData(
                for: selectedTimeFrame,
                data: data,
                dateKeyPath: \GraphWeightEntry.date,
                valueKeyPath: \GraphWeightEntry.weight
            )
        }

        switch appCategory {
        case .weightGurus:
            let filteredWeights = filteredWeights(from: weightData)
            return GraphDataResult(
                graphWeights: filteredWeights,
                healthEntries: [],
                goalWeight: goalWeight,
                rawWeights: weightData,
                babyPercentiles: [],
                bgmEntries: []
            )

        case .balanceHealth:
            let convertedHealthData = balanceHealthData.map { entry in
                BalanceHealthEntry(
                    date: entry.date,
                    systole: entry.systole,
                    diastole: entry.diastole,
                    pulse: entry.pulse
                )
            }
            let filteredHealthData = filteredData(
                for: selectedTimeFrame,
                data: convertedHealthData,
                dateKeyPath: \BalanceHealthEntry.date,
                valueKeyPath: \BalanceHealthEntry.systole
            )
            return GraphDataResult(
                graphWeights: [],
                healthEntries: filteredHealthData,
                goalWeight: goalWeight,
                rawWeights: [],
                babyPercentiles: [],
                bgmEntries: []
            )

        case .smartBaby:
            let percentileFilteredData = percentileService.filteredData(for: selectedTimeFrame)
            let filteredWeights = filteredWeights(from: weightData)
            return GraphDataResult(
                graphWeights: filteredWeights,
                healthEntries: [],
                goalWeight: goalWeight,
                rawWeights: weightData,
                babyPercentiles: percentileFilteredData,
                bgmEntries: []
            )

        case .rpm:
            switch selectedSKU {
            case RPMDeviceSKUs.bloodGlucoseMeter:
                let filteredBGMData = filteredData(
                    for: selectedTimeFrame,
                    data: BGMData,
                    dateKeyPath: \.date,
                    valueKeyPath: \.glucose
                )
                return GraphDataResult(
                    graphWeights: [],
                    healthEntries: [],
                    goalWeight: goalWeight,
                    rawWeights: [],
                    babyPercentiles: [],
                    bgmEntries: filteredBGMData
                )

            case RPMDeviceSKUs.weighingScale:
                let filteredWeights = filteredWeights(from: rpmWeightData)
                return GraphDataResult(
                    graphWeights: filteredWeights,
                    healthEntries: [],
                    goalWeight: goalWeight,
                    rawWeights: rpmWeightData,
                    babyPercentiles: [],
                    bgmEntries: []
                )

            case RPMDeviceSKUs.babyScale:
                let filteredWeights = filteredWeights(from: rpmBabyWeightData)
                return GraphDataResult(
                    graphWeights: filteredWeights,
                    healthEntries: [],
                    goalWeight: goalWeight,
                    rawWeights: rpmBabyWeightData,
                    babyPercentiles: [],
                    bgmEntries: []
                )

            default:
                let filteredWeights = filteredWeights(from: weightData)
                return GraphDataResult(
                    graphWeights: filteredWeights,
                    healthEntries: [],
                    goalWeight: goalWeight,
                    rawWeights: weightData,
                    babyPercentiles: [],
                    bgmEntries: []
                )
            }
            
        default:
            let filteredWeights = filteredWeights(from: weightData)
            return GraphDataResult(
                graphWeights: filteredWeights,
                healthEntries: [],
                goalWeight: goalWeight,
                rawWeights: weightData,
                babyPercentiles: [],
                bgmEntries: []
            )
        }
    }
}
