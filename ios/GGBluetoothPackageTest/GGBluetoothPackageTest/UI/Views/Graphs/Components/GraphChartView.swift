//
//  GraphChartView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 30/04/25.
//

import SwiftUI
import Charts

struct GraphChartView: View {
    let data: [GraphWeightEntry]
    let balanceHealthdata: [BalanceHealthEntry]
    let goalWeight: Double?
    @Binding var selectedPoint: SelectedPoint?
    @Binding var displayedWeight: Double?
    @Binding var selectedSKU: String
    let fullData: [GraphWeightEntry]
    let lang = GraphStrings.self
    let yAxisTicks: [Double]
    let extendedMaxY: Double
    let parentView: GraphParentViewType
    let selectedTimeFrame: TimeFrame
    let appCategory: AppCategory
    let percentileData: [BabyWeightPercentileData]
    let BGMData: [BGMData]
    @State private var selectedDevice: String = GraphStrings.selectDevice
    private var yAxisDomain: ClosedRange<Double> {
        switch appCategory {
        case .smartBaby:
            guard let minValue = SeriesData.map(\.value).min(),
                  let maxValue = SeriesData.map(\.value).max() else {
                return 0...1
            }
            return minValue...maxValue
            
        default:
            guard let firstTick = yAxisTicks.first, let lastTick = yAxisTicks.last else {
                return 0...100
            }
            return firstTick...lastTick
        }
    }
    
    private var xAxisDomain: ClosedRange<Date> {
        func paddedDomain(firstDate: Date?, lastDate: Date?, padding: TimeInterval = 24 * 60 * 50) -> ClosedRange<Date> {
            guard let firstDate, let lastDate else { return Date()...Date() }
            if firstDate == lastDate {
                let adjustedStart = firstDate.addingTimeInterval(-padding)
                let adjustedEnd = firstDate.addingTimeInterval(padding)
                return adjustedStart...adjustedEnd
            }
            return firstDate.addingTimeInterval(-padding)...lastDate.addingTimeInterval(padding)
        }
        
        switch appCategory {
        case .smartBaby, .balanceHealth:
            let dates = SeriesData.map(\.date)
            return paddedDomain(firstDate: dates.min(), lastDate: dates.max())
            
        case .rpm where selectedSKU == RPMDeviceSKUs.bloodGlucoseMeter:
            return paddedDomain(firstDate: BGMData.first?.date, lastDate: BGMData.last?.date)
            
        default:
            return paddedDomain(firstDate: data.first?.date, lastDate: data.last?.date)
        }
    }
    
    private var goalWeightIsVisible: Bool {
        yAxisDomain.contains(goalWeight ?? 0)
    }
    
    private var isFirstSelectedPoint: Bool {
        guard let selected = selectedPoint else { return false }
        switch selected {
        case .weight(let entry):
            return data.first?.id == entry.id
        case .balanceHealth(let entry):
            return data.first?.id == entry.id
        case .BGM(let bgm):
            return data.first?.id == bgm.id
        }
    }
    
    private var percentageTowardsGoal: String? {
        guard let current = displayedWeight else { return nil }
        guard goalWeight ?? 0 > 0 else { return nil }
        let percentage = (current / (goalWeight ?? 0)) * 100
        return "\(Int(percentage))\(lang.percentageSymbol) \(lang.goal)"
    }
    
    var SeriesData: [GraphSeries] {
        var result: [GraphSeries] = []
        
        switch appCategory {
        case .balanceHealth:
            for entry in balanceHealthdata {
                result.append(GraphSeries(date: entry.date, value: Double(entry.systole), series: lang.systolic))
                result.append(GraphSeries(date: entry.date, value: Double(entry.pulse), series: lang.pulse))
                result.append(GraphSeries(date: entry.date, value: Double(entry.diastole), series: lang.diastolic))
            }
            
        case .smartBaby:
            for entry in data {
                if entry.date <= Date() {
                    result.append(GraphSeries(date: entry.date, value: entry.weight, series: lang.weight))
                }
            }
            
            if selectedTimeFrame == .total || selectedTimeFrame == .year {
                for percentile in percentileData {
                    if let seriesDate = dateFromDay(percentile.day) {
                        if seriesDate <= Date() {
                            let percentiles = [
                                (value: percentile.fifth, series: lang.fifth),
                                (value: percentile.tenth, series: lang.tenth),
                                (value: percentile.twentyFifth, series: lang.twentyFifth),
                                (value: percentile.fiftieth, series: lang.fiftieth),
                                (value: percentile.seventyFifth, series: lang.seventyFifth),
                                (value: percentile.ninetieth, series: lang.ninetieth),
                                (value: percentile.ninetyFifth, series: lang.ninetyFifth)
                            ]
                            for percentileData in percentiles {
                                result.append(
                                    GraphSeries(
                                        date: seriesDate,
                                        value: Double(percentileData.value) / GraphConstants.percentileConversionFactor,
                                        series: percentileData.series
                                    )
                                )
                            }
                        }
                    }
                }
            }
            
        default:
            result = []
        }
        
        return result
    }
    
    private var colorMapping: KeyValuePairs<String, Color> {
        if let selectedPoint = selectedPoint, case let .balanceHealth(entry) = selectedPoint {
            let systolicValue = entry.systole
            let diastolicValue = entry.diastole
            let pulseValue = entry.pulse
            let date = entry.date
            return GraphConstants.seriesColorMapping(
                systolicValue: systolicValue,
                diastolicValue: diastolicValue,
                pulseValue: pulseValue,
                exampleDate: date
            )
        } else {
            return GraphConstants.seriesColorMapping(
                systolicValue: 120,
                diastolicValue: 80,
                pulseValue: 70,
                exampleDate: Date()
            )
        }
    }
    
    var body: some View {
        Chart {
            renderChart(
                appCategory: appCategory,
                balanceHealthSeriesData: SeriesData,
                balanceHealthdata: balanceHealthdata,
                data: data,
                selectedPoint: selectedPoint,
                lang: lang
            )
            
            if appCategory != .smartBaby && appCategory != .rpm {
                renderGoalMarks(
                    parentView: parentView,
                    appCategory: appCategory,
                    goalWeightIsVisible: goalWeightIsVisible,
                    isFirstSelectedPoint: isFirstSelectedPoint,
                    percentageTowardsGoal: percentageTowardsGoal
                )
            }
            
            if let selected = selectedPoint {
                RectangleMark(
                    xStart: .value(lang.date, selected.date.addingTimeInterval(-60)),
                    xEnd: .value(lang.date, selected.date.addingTimeInterval(60)),
                    yStart: .value(
                        lang.min,
                        parentView == .history ? (yAxisTicks.first ?? 0) - extendedMaxY : (yAxisTicks.first ?? 0)
                    ),
                    yEnd: .value(
                        lang.extendedMax,
                        parentView == .history ? selected.weight : extendedMaxY
                    )
                )
                .foregroundStyle(Color.black)
                .zIndex(1)
                .annotation(position: parentView == .history ? .bottom : .top, alignment: .center) {
                    GeometryReader { geo in
                        let screenWidth = UIScreen.main.bounds.width
                        let annotationWidth: CGFloat = 100
                        let positionX = geo.frame(in: .global).midX
                        
                        Text(
                            selectedTimeFrame == .total || selectedTimeFrame == .year
                            ? "\(selected.date.formatted(.dateTime.month(.abbreviated))), \(selected.date.formatted(.dateTime.year()))"
                            : "\(selected.date.formatted(.dateTime.month(.abbreviated).day())), \(selected.date.formatted(.dateTime.year()))"
                        )
                        .font(.caption)
                        .foregroundColor(.gray)
                        .padding(4)
                        .background(Color.white)
                        .cornerRadius(4)
                        .offset(x: {
                            if positionX + annotationWidth / 2 > screenWidth {
                                return -annotationWidth / 2
                            } else if positionX - annotationWidth / 2 < 0 {
                                return annotationWidth / 2
                            } else {
                                return 0
                            }
                        }())
                    }
                    .frame(width: 100, height: 20)
                }
            }
            
            ForEach(yAxisTicks, id: \.self) { tick in
                RuleMark(y: .value(lang.yGrid, tick))
                    .lineStyle(StrokeStyle(lineWidth: 1, dash: [1, 10]))
                    .foregroundStyle(Color.gray.opacity(0.5))
                    .zIndex(-2)
            }
        }
        .frame(width: UIScreen.main.bounds.width * 0.9, height: 240)
        .padding(.bottom)
        .chartForegroundStyleScale(colorMapping)
        .chartLegend(.hidden)
        .chartYScale(domain: yAxisDomain)
        .chartXScale(domain: xAxisDomain)
        .chartYAxis {
            AxisMarks(position: .leading, values: yAxisTicks) { value in
                AxisTick()
                AxisValueLabel()
            }
        }
        .chartXAxis {
            AxisMarks(values: .automatic) { _ in
                AxisTick()
            }
        }
        .chartOverlay { proxy in
            GeometryReader { geo in
                Rectangle().fill(Color.clear).contentShape(Rectangle())
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { value in
                                if let plotFrame = proxy.plotFrame {
                                    let frame = geo[plotFrame]
                                    let locationX = value.location.x - frame.origin.x
                                    let clampedX = min(max(0, locationX), frame.size.width)
                                    updateSelectedPoint(
                                        appCategory: appCategory,
                                        clampedX: clampedX,
                                        proxy: proxy,
                                        balanceHealthdata: balanceHealthdata,
                                        data: data
                                    )
                                }
                            }
                    )
            }
        }
        .onAppear {
            if data.count == 1, let onlyEntry = data.first {
                selectedPoint = .weight(onlyEntry)
                displayedWeight = onlyEntry.weight
            }
        }
    }
    
    @ChartContentBuilder
    private func renderChart(
        appCategory: AppCategory,
        balanceHealthSeriesData: [GraphSeries],
        balanceHealthdata: [BalanceHealthEntry],
        data: [GraphWeightEntry],
        selectedPoint: SelectedPoint?,
        lang: GraphStrings.Type
    ) -> some ChartContent {
        switch appCategory {
        case .balanceHealth:
            let order: [String] = [lang.systolic, lang.pulse, lang.diastolic]
            ForEach(order, id: \.self) { type in
                ForEach(balanceHealthSeriesData.filter { $0.series == type }) { series in
                    LineMark(
                        x: .value(lang.date, series.date),
                        y: .value(series.series, series.value)
                    )
                    .foregroundStyle(by: .value(lang.typeFormat, series.series))
                    .interpolationMethod(.catmullRom)
                    
                    PointMark(
                        x: .value(lang.date, series.date),
                        y: .value(series.series, series.value)
                    )
                    .symbolSize(selectedPoint?.id == balanceHealthdata.first(where: { $0.date == series.date })?.id ? 50 : 20)
                    .foregroundStyle(by: .value(lang.typeFormat, series.series))
                }
            }
     
        case .smartBaby:
            ForEach(SeriesData) { series in
                LineMark(
                    x: .value(lang.date, series.date),
                    y: .value(series.series, series.value)
                )
                .foregroundStyle(by: .value(lang.typeFormat, series.series))
                .interpolationMethod(.catmullRom)

                if series.series == lang.weight {
                    PointMark(
                        x: .value(lang.date, series.date),
                        y: .value(series.series, series.value)
                    )
                    .symbolSize(selectedPoint?.id == series.id ? 50 : 20)
                    .foregroundStyle(by: .value(lang.typeFormat, series.series))
                }
            }
            
        case .rpm where selectedSKU == RPMDeviceSKUs.bloodGlucoseMeter:
            ForEach(BGMData) { entry in
                LineMark(
                    x: .value(lang.date, entry.date),
                    y: .value(lang.glucose, entry.glucose)
                )
                .foregroundStyle(Color.red)
                .interpolationMethod(.cardinal)
                
                PointMark(
                    x: .value(lang.date, entry.date),
                    y: .value(lang.glucose, entry.glucose)
                )
                .foregroundStyle(Color.red)
                .symbolSize(selectedPoint?.id == entry.id ? 50 : 20)
            }
            
        default:
            ForEach(data) { entry in
                LineMark(
                    x: .value(lang.date, entry.date),
                    y: .value(lang.weight, entry.weight)
                )
                .foregroundStyle(Color.blue)
                .interpolationMethod(.cardinal)
                
                PointMark(
                    x: .value(lang.date, entry.date),
                    y: .value(lang.weight, entry.weight)
                )
                .symbolSize(selectedPoint?.id == entry.id ? 50 : 20)
            }
        }
    }
    
    @ChartContentBuilder
    private func renderGoalMarks(
        parentView: GraphParentViewType,
        appCategory: AppCategory,
        goalWeightIsVisible: Bool,
        isFirstSelectedPoint: Bool,
        percentageTowardsGoal: String?
    ) -> some ChartContent {
        switch parentView {
        case .history:
            switch appCategory {
            case .balanceHealth:
                let yValues = [GraphConstants.optimalSystole, GraphConstants.optimalDiastole]
                ForEach(yValues, id: \.self) { yValue in
                    goalOrPercentageRuleMark(
                        yValue: yValue,
                        label: "",
                        trailingPadding: goalWeightIsVisible ? (isFirstSelectedPoint ? 330 : 350) : (isFirstSelectedPoint ? 300 : 310),
                        isGoalVisible: goalWeightIsVisible,
                        isSettingsView: true,
                        percentageTowardsGoal: percentageTowardsGoal,
                        color: .gray
                    )
                }
            default:
                goalOrPercentageRuleMark(
                    yValue: (goalWeightIsVisible ? goalWeight : (data.map(\.weight).max() ?? 0)) ?? 0,
                    label: goalWeightIsVisible ? "\(goalWeight ?? 0)" : (percentageTowardsGoal ?? lang.goal),
                    trailingPadding: goalWeightIsVisible ? (isFirstSelectedPoint ? 330 : 350) : (isFirstSelectedPoint ? 300 : 310),
                    isGoalVisible: goalWeightIsVisible,
                    isSettingsView: true,
                    percentageTowardsGoal: percentageTowardsGoal
                )
            }
        case .deviceDetail:
            switch appCategory {
            case .balanceHealth:
                let yValues = [GraphConstants.optimalSystole, GraphConstants.optimalDiastole]
                ForEach(yValues, id: \.self) { yValue in
                    goalOrPercentageRuleMark(
                        yValue: yValue,
                        label: "",
                        trailingPadding: 300,
                        isGoalVisible: false,
                        isSettingsView: false,
                        percentageTowardsGoal: nil
                    )
                }
            default:
                if goalWeightIsVisible {
                    goalOrPercentageRuleMark(
                        yValue: goalWeight ?? 0,
                        label: lang.goal,
                        trailingPadding: isFirstSelectedPoint ? 330 : 350,
                        isGoalVisible: true,
                        isSettingsView: false,
                        percentageTowardsGoal: nil
                    )
                } else if let percentage = percentageTowardsGoal {
                    goalOrPercentageRuleMark(
                        yValue: data.map(\.weight).max() ?? 0,
                        label: percentage,
                        trailingPadding: isFirstSelectedPoint ? 300 : 310,
                        isGoalVisible: false,
                        isSettingsView: false,
                        percentageTowardsGoal: nil
                    )
                }
            }
        }
    }
    
    @ChartContentBuilder
    func goalOrPercentageRuleMark(
        yValue: Double,
        label: String,
        trailingPadding: CGFloat = 0,
        isGoalVisible: Bool,
        isSettingsView: Bool,
        percentageTowardsGoal: String?,
        color: Color? = nil
    ) -> some ChartContent {
        let position: AnnotationPosition = isGoalVisible ? .leading : .top
        let lineStyle = isSettingsView
        ? StrokeStyle(lineWidth: 1)
        : StrokeStyle(lineWidth: 1, dash: [5])
        let ruleColor = color ?? (isSettingsView ? Color.green : Color.gray)
        
        RuleMark(y: .value(lang.goal, yValue))
            .lineStyle(lineStyle)
            .foregroundStyle(ruleColor)
            .annotation(position: position) {
                if isSettingsView {
                    if isGoalVisible {
                        styledGreenText {
                            Text("\(yValue, specifier: lang.valueFormat)")
                        }
                    } else if let percentage = percentageTowardsGoal {
                        styledGreenText {
                            Text(percentage)
                        }
                        .padding(.trailing, trailingPadding)
                    }
                } else {
                    Text(label)
                        .font(.caption)
                        .foregroundColor(.gray)
                        .padding(.trailing, trailingPadding)
                }
            }
    }
    
    @ViewBuilder
    func styledGreenText<Content: View>(@ViewBuilder content: () -> Content) -> some View {
        content()
            .font(.caption)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .padding(.horizontal, 5)
            .padding(.vertical, 1)
            .background(Color.green)
            .cornerRadius(10)
    }
    
    private func selectedPointAlignment(for point: GraphWeightEntry) -> Alignment {
        guard let index = data.firstIndex(where: { $0.id == point.id }) else {
            return .center
        }
        
        if index == 0 {
            return .leading
        } else if index == data.count - 1 || index == data.count - 2 {
            return .trailing
        } else {
            return .center
        }
    }
    
    private func updateSelectedPoint(
        appCategory: AppCategory,
        clampedX: CGFloat,
        proxy: ChartProxy,
        balanceHealthdata: [BalanceHealthEntry],
        data: [GraphWeightEntry]
    ) {
        switch appCategory {
        case .balanceHealth:
            let xPositions: [(BalanceHealthEntry, CGFloat)] = balanceHealthdata.compactMap { entry in
                if let dateX: CGFloat = proxy.position(forX: entry.date) {
                    return (entry, dateX)
                } else {
                    return nil
                }
            }
            if let (closestEntry, _) = xPositions.min(by: { abs($0.1 - clampedX) < abs($1.1 - clampedX) }) {
                selectedPoint = .balanceHealth(closestEntry)
            }
            
        case .rpm where selectedSKU == RPMDeviceSKUs.bloodGlucoseMeter:
            
            let xPositions: [(BGMData, CGFloat)] = BGMData.compactMap { entry in
                if let dateX: CGFloat = proxy.position(forX: entry.date) {
                    return (entry, dateX)
                }
                return nil
            }
            if let (closestEntry, _) = xPositions.min(by: { abs($0.1 - clampedX) < abs($1.1 - clampedX) }) {
                selectedPoint = .BGM(closestEntry)
                displayedWeight = closestEntry.glucose
            }

        default:
            let xPositions: [(GraphWeightEntry, CGFloat)] = data.compactMap { entry in
                if let dateX: CGFloat = proxy.position(forX: entry.date) {
                    return (entry, dateX)
                } else {
                    return nil
                }
            }
            if let (closestEntry, _) = xPositions.min(by: { abs($0.1 - clampedX) < abs($1.1 - clampedX) }) {
                selectedPoint = .weight(closestEntry)
                displayedWeight = closestEntry.weight
            }
        }
    }
    
    private func dateFromDay(_ day: Int) -> Date? {
        @Injector var userProfileUpdationService: UserProfileUpdationService
        guard let profile = userProfileUpdationService.fetchUserProfile(),
              let birthDate = profile.birthday else {
            return nil
        }
        return Calendar.current.date(byAdding: .day, value: day, to: birthDate)
    }
}

