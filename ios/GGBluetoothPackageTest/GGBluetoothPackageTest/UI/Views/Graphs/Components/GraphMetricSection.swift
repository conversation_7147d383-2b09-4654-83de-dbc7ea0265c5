//
//  GraphMetricSection.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 12/05/25.
//

import SwiftUI

struct GraphMetricSection<Content: View>: View {
    let title: String
    let content: Content
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Text(title)
                .foregroundColor(.gray)
                .padding(.bottom, 0)
            content
        }
    }
}
