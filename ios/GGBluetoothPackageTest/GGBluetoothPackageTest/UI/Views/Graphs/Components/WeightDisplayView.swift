//
//  WeightDisplayView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 30/04/25.
//

import SwiftUI

struct WeightDisplayView: View {
    let weight: Double
    var body: some View {
        HStack(alignment: .firstTextBaseline, spacing: 15) {
            Text("\(weight, specifier: "%.1f")")
                .font(.system(size: 50, weight: .semibold))
            Text(MeasurementUnit.kg.displayValue)
                .font(.system(size: 30))
        }
        .padding(.top)
        .transition(.opacity)
        .animation(.easeInOut, value: weight)
    }
}
