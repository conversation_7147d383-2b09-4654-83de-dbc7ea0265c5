//
//  UserDashboardViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 04/02/25.
//

import Foundation

class UserDashboardViewModel: ObservableObject {
    @Published var loginData: CombinedLoginData?
    
    func decodeCombinedLoginData(_ combinedData: CombinedLoginData, appCategory: AppCategory) -> (loginResponse: WrappedLoginResponse, accountDetails: [Any], measurements: [[String: Any]]) {
        let loginResponse = combinedData.loginResponse
        let accountDetails = combinedData.accountDetails
        let measurements = combinedData.measurements ?? []
        return (loginResponse, accountDetails, measurements)
    }

    func getFilteredMeasurements(_ measurements: [[String: Any]], for appCategory: AppCategory) -> [[String: Any]] {
        return LoginHelper.shared.filterMeasurements(measurements, for: appCategory)
    }
}
