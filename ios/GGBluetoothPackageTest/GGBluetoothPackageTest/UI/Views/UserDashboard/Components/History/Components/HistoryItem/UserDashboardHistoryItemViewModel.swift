//
//  UserDashboardHistoryItemViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 04/02/25.
//

import Foundation

class UserDashboardHistoryItemViewModel: ObservableObject {
    @Published var measurement: [String: Any]
    let appCategory: AppCategory
    @Published var isExpanded = false
    
    init(measurement: [String: Any], appCategory: AppCategory) {
        self.measurement = measurement
        self.appCategory = appCategory
    }
    
    func convertToDate(_ dateString: String, format: String) -> String {
        return LoginHelper.shared.convertToDate(dateString, for: appCategory, format: format)
    }
    
    func formatMetric(_ value: Double) -> String {
        return String(format: "%.1f", value / 10)
    }
    
    func convertMillimetersToInches(_ millimeters: Int) -> Double {
            return Double(millimeters) / 25.4
        }
    
    func convertDecigramsToPounds(_ decigrams: Int) -> Double {
        return Double(decigrams) * 0.0002204623
    }
}
