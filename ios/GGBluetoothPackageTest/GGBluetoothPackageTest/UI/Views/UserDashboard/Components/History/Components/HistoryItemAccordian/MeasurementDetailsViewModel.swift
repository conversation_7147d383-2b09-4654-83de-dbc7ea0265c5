//
//  MeasurementDetailsViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 14/02/25.
//

import SwiftUI

class MeasurementDetailsViewModel: ObservableObject {
    let measurement: [String: Any]
    let appCategory: AppCategory
    
    init(measurement: [String: Any], appCategory: AppCategory) {
        self.measurement = measurement
        self.appCategory = appCategory
    }
    
    var details: [(String, String, String)] {
        getDetails()
    }
    
    private func getDetails() -> [(String, String, String)] {
        switch appCategory {
        case .balanceHealth:
            return [
                ("Mean Pressure", formatValue(measurement["meanArterial"] as? Double), "waveform.path.ecg"),
                ("Pulse", formatValue(measurement["pulse"] as? Int, suffix: " BPM"), "heart.fill"),
                ("Note", measurement["note"] as? String ?? "", "note.text"),
                ("Type", measurement["type"] as? String ?? "", "doc.text")
            ].filter { !$0.1.isEmpty }
            
        case .smartBaby:
            return [
                ("Entry Type", measurement["entryType"] as? String, "text.justify.left"),
                ("Entry Note", measurement["entryNote"] as? String, "pencil"),
                ("Diaper Type", measurement["diaperType"] as? String, "drop"),
                ("Feeding Time Left", formatValue(measurement["feedingTimeSecondsLeft"] as? Int, suffix: " sec"), "timer"),
                ("Feeding Time Right", formatValue(measurement["feedingTimeSecondsRight"] as? Int, suffix: " sec"), "timer"),
                ("Baby Length", formatValue(measurement["babyLengthMillimeters"] as? Int, suffix: " mm"), "ruler"),
                ("Feeding Milliliters", formatValue(measurement["feedingMilliliters"] as? Int, suffix: " ml"), "bottle.fill"),
                ("Sleep Time", formatValue(measurement["sleepTimeMinutes"] as? Int, suffix: " min"), "bed.double"),
                ("Photo", measurement["photo"] as? String, "camera"),
                ("Source", measurement["source"] as? String, "doc.text.magnifyingglass")
            ]
            .compactMap { name, value, icon in
                guard let value = value, !value.isEmpty else { return nil }
                return (name, value, icon)
            }
            
        default:
            return [
                ("BMI", formatValue(measurement["bmi"] as? Double), AppAssets.bmi),
                ("Body Fat", formatValue(measurement["bodyFat"] as? Double, suffix: " %"), AppAssets.bodyFat),
                ("Body Water", formatValue(measurement["water"] as? Double, suffix: " %"), AppAssets.bodyWater),
                ("Muscle Mass", formatValue(measurement["muscleMass"] as? Double, suffix: " %"), AppAssets.muscleMass),
                ("Source", measurement["source"] as? String ?? "", "doc.text.magnifyingglass")
            ].filter { !$0.1.isEmpty }
        }
    }
    
    private func formatValue(_ value: Double?, suffix: String = "") -> String {
        guard let value = value else { return "" }
        return String(format: "%.1f%@", value / 10, suffix)
    }
    
    private func formatValue(_ value: Int?, suffix: String = "") -> String {
        guard let value = value else { return "" }
        return "\(value)\(suffix)"
    }
}
