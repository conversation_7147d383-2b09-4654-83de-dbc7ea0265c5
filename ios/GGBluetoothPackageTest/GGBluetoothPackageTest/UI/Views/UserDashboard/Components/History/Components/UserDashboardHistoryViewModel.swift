//
//  UserDashboardHistoryViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 14/03/25.
//

import Foundation

class UserDashboardHistoryViewModel: ObservableObject {
    @Published var measurements: [[String: Any]]
    let appCategory: AppCategory
    @Published var entryToDelete: [String: Any]?
    @Published var showDeleteConfirmation = false
    private let logger = AppLogger(category: "UserDashboardHistoryViewModel")
    @Injector var deviceService: DeviceService
    @Injector var loginService: LoginService
    init(measurements: [[String: Any]], appCategory: AppCategory) {
        self.measurements = measurements
        self.appCategory = appCategory
    }

    var indices: [Int] {
        if appCategory == .weightGurus {
            return Array(measurements.indices).reversed()
        } else if appCategory == .balanceHealth {
            return measurements.enumerated()
                .sorted {
                    guard let firstTimestamp = $0.element["entryTimestamp"] as? Int,
                          let secondTimestamp = $1.element["entryTimestamp"] as? Int else {
                        return false
                    }
                    return firstTimestamp > secondTimestamp
                }
                .map { $0.offset }
        } else {
            return Array(measurements.indices)
        }
    }

    func prepareToDelete(_ measurement: [String: Any]) {
        entryToDelete = measurement
        showDeleteConfirmation = true
    }

    func confirmDelete() {
        if let entry = entryToDelete {
            deleteEntry(entry)
        }
    }

    private func deleteEntry(_ entry: [String: Any]) {
        guard let entryTimestamp = entry["entryTimestamp"] as? String else {
            logger.error(error: NSError(domain: "InvalidEntry", code: 0, userInfo: [NSLocalizedDescriptionKey: "Failed to get entryTimestamp from entry: \(entry)"]), function: #function, line: #line)
            return
        }

        if let loginDetails = deviceService.fetchUserLogin(for: appCategory) {
            let email = loginDetails.email ?? ""
            let password = loginDetails.password ?? ""

            loginService.deleteOperationFromApp(
                for: appCategory,
                email: email,
                password: password,
                entryTimestamp: entryTimestamp
            ) { success in
                if success {
                    self.logger.info(info: "Operation deleted successfully", function: #function, line: #line)
                    ToastService.shared.presentToast(with: ToastMessageStrings.entryDeletedSuccessfully)
                } else {
                    self.logger.error(error: NSError(domain: "DeleteOperation", code: 1, userInfo: [NSLocalizedDescriptionKey: "Failed to delete operation"]), function: #function, line: #line)
                }
            }
        } else {
            logger.error(error: NSError(domain: "FetchLoginDetails", code: 2, userInfo: [NSLocalizedDescriptionKey: "Failed to fetch login details for category: \(appCategory)"]), function: #function, line: #line)
        }
    }
}
