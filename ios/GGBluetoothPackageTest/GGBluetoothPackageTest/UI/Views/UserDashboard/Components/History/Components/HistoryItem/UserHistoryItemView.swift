//
//  UserDashboardHistoryItemView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 29/01/25.
//

import SwiftUI

struct UserDashboardHistoryItemView: View {
    @StateObject private var viewModel: UserDashboardHistoryItemViewModel
    
    init(measurement: [String: Any], appCategory: AppCategory) {
        _viewModel = StateObject(wrappedValue: UserDashboardHistoryItemViewModel(measurement: measurement, appCategory: appCategory))
    }
    
    var body: some View {
        VStack {
            HStack {
                VStack(alignment: .center, spacing: 5) {
                    if let entryTimestamp = viewModel.measurement["entryTimestamp"] as? String {
                        Text(viewModel.convertToDate(entryTimestamp, format: "MMM dd, yyyy"))
                            .fontWeight(.bold)
                            .font(.subheadline)
                        
                        Text(viewModel.convertToDate(entryTimestamp, format: "h:mm a"))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.trailing, 15)
                
                Image(viewModel.appCategory == .balanceHealth ? AppAssets.bloodPressureMonitor : AppAssets.scaleIcon)
                    .resizable()
                    .frame(width: 25, height: 25)
                
                Spacer()
                
                switch viewModel.appCategory {
                case .balanceHealth:
                    if let systole = viewModel.measurement["systolic"] as? Double,
                       let diastole = viewModel.measurement["diastolic"] as? Double {
                        measurementDisplay(value: "\(Int(systole))/\(Int(diastole))", unit: "mmHg")
                    }
                case .smartBaby:
                    if let babyWeightDecigrams = viewModel.measurement["babyWeightDecigrams"] as? Int {
                        let babyWeightPounds = viewModel.convertDecigramsToPounds(babyWeightDecigrams)
                        measurementDisplay(value: String(format: "%.2f", babyWeightPounds), unit: "lbs")
                    }
                    if let babyLengthMillimeters = viewModel.measurement["babyLengthMillimeters"] as? Int {
                        let babyLengthInches = viewModel.convertMillimetersToInches(babyLengthMillimeters)
                        measurementDisplay(value: String(format: "%.2f", babyLengthInches), unit: "in")
                    }
                default:
                    if let weight = viewModel.measurement["weight"] as? Double {
                        measurementDisplay(value: viewModel.formatMetric(weight), unit: "lbs")
                    }
                }
                
                Button(action: {
                    withAnimation {
                        viewModel.isExpanded.toggle()
                    }
                }, label: {
                    Image(systemName: viewModel.isExpanded ? "chevron.down" : "chevron.right")
                        .foregroundColor(.blue)
                        .padding(.leading, 20)
                        .frame(width: 25, height: 25)
                })
            }
            
            if viewModel.isExpanded {
                MeasurementDetailsAccordionView(measurement: viewModel.measurement, appCategory: viewModel.appCategory)
                    .padding(.bottom,10)
            }
        }
    }
    
    func measurementDisplay(value: String, unit: String) -> some View {
        HStack(alignment: .bottom) {
            Text(value)
                .font(.title3)
            Text(unit)
                .foregroundColor(.gray)
                .font(.caption)
        }
    }
}

struct UserHistoryItemView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            UserDashboardHistoryItemView(
                measurement: [
                    "entryTimestamp": "2023-01-01T12:00:00.000Z",
                    "weight": 150.0,
                    "source": "Scale",
                    "bmi": 22.5,
                    "bodyFat": 18.5,
                    "muscleMass": 40.0,
                    "water": 55.0
                ], appCategory: .weightGurus
            )
            UserDashboardHistoryItemView(
                measurement: [
                    "opTimestamp": "1723549407552",
                    "type": "device",
                    "pulse": 71,
                    "systolic": 105,
                    "diastolic": 74,
                    "meanArterial": NSNull(),
                    "userId": "f6b726a0-3547-4722-a2ef-67fb0412fe6e",
                    "entryTimestamp": 1723549405643,
                    "note": NSNull(),
                    "operation": "create"
                ], appCategory: .balanceHealth
            )
        }
        .padding(.horizontal)
    }
}
