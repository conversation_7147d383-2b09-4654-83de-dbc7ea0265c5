//
//  UserDashboardHistoryView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 29/01/25.
//

import SwiftUI

struct UserDashboardHistoryView: View {
    @StateObject private var viewModel: UserDashboardHistoryViewModel
    var parentView: UserDashBoardHistoryParentView
    
    init(measurements: [[String: Any]], appCategory: AppCategory, parentView: UserDashBoardHistoryParentView) {
        _viewModel = StateObject(wrappedValue: UserDashboardHistoryViewModel(measurements: measurements, appCategory: appCategory))
        self.parentView = parentView
    }
    
    var body: some View {
        VStack(alignment: .leading) {
            if viewModel.measurements.isEmpty {
                
                (parentView == .history ? AnyView(FourDotsLoaderView()) : AnyView(Text("No history available").foregroundColor(.gray).padding()))
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
                
            } else {
                List(viewModel.indices, id: \.self) { index in
                    let measurement = viewModel.measurements[index]
                    UserDashboardHistoryItemView(measurement: measurement, appCategory: viewModel.appCategory)
                        .swipeActions {
                            Button(role: .destructive) {
                                viewModel.prepareToDelete(measurement)
                            } label: {
                                Label("", systemImage: "trash")
                            }
                        }
                }
            }
        }
        .alert(isPresented: $viewModel.showDeleteConfirmation) {
            Alert(
                title: Text(CommonStrings.deleteEntry),
                message: Text(CommonStrings.deleteEntryPlaceholder),
                primaryButton: .destructive(Text(CommonStrings.delete)) {
                    viewModel.confirmDelete()
                },
                secondaryButton: .cancel()
            )
        }
    }
}

struct UserDashboardHistoryView_Previews: PreviewProvider {
    static var previews: some View {
        UserDashboardHistoryView(measurements: [
            // BalanceHealth measurements
            ["operation": "create", "meanArterial": NSNull(), "systolic": 107, "userId": "f6b726a0-3547-4722-a2ef-67fb0412fe6e", "diastolic": 88, "opTimestamp": 1723613055477, "entryTimestamp": 1723613052940, "type": "device", "pulse": 71, "note": NSNull()],
            ["operation": "create", "userId": "f6b726a0-3547-4722-a2ef-67fb0412fe6e", "pulse": 61, "diastolic": 71, "type": "device", "opTimestamp": 1733478339462, "meanArterial": NSNull(), "entryTimestamp": 1388514637033, "systolic": 115, "note": NSNull()],
            // WeightGurus measurements
            ["water": NSNull(), "operationType": "create", "source": "btWifiR4", "weight": 1622, "serverTimestamp": "2024-08-20T07:00:56.125Z", "entryTimestamp": "2024-08-20T07:00:49.000Z", "bmi": 270, "bodyFat": NSNull(), "muscleMass": NSNull()],
            ["bodyFat": NSNull(), "weight": 1623, "muscleMass": NSNull(), "bmi": 270, "source": "btWifiR4", "operationType": "create", "entryTimestamp": "2024-08-20T07:04:29.000Z", "water": NSNull(), "serverTimestamp": "2024-08-20T07:04:36.547Z"],
        ], appCategory: .all, parentView: .apps)
    }
}
