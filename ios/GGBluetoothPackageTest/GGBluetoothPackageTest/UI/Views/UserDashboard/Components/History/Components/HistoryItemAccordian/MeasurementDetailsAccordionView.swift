//
//  MeasurementDetailsAccordionView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 04/02/25.
//

import SwiftUI

struct MeasurementDetailsAccordionView: View {
    @StateObject private var viewModel: MeasurementDetailsViewModel

    init(measurement: [String: Any], appCategory: AppCategory) {
        _viewModel = StateObject(wrappedValue: MeasurementDetailsViewModel(measurement: measurement, appCategory: appCategory))
    }

    var body: some View {
        VStack(alignment: .leading) {
            ForEach(viewModel.details.indices, id: \.self) { index in
                DetailView(
                    detail: viewModel.details[index],
                    isEven: index.isMultiple(of: 2),
                    symbols: systemSymbols
                )
            }
        }
    }

    private let systemSymbols: [String] = [
        "waveform.path.ecg", "heart.fill", "note.text", "doc.text",
        "doc.text.magnifyingglass", "tag", "number", "text.justify.left",
        "arrow.uturn.right", "calendar", "clock", "pencil", "drop",
        "timer", "ruler", "bottle.fill", "bed.double", "camera"
    ]
}

#Preview {
    MeasurementDetailsAccordionView(
        measurement: [
            "meanArterial": 85.0,
            "pulse": 72,
            "note": "Feeling good",
            "type": "Regular Checkup",
            "source": "Monitor",
            "bmi": 22.5,
            "bodyFat": 18.5,
            "muscleMass": 40.0,
            "water": 55.0,
            "systolic": 120.0,
            "diastolic": 80.0
        ],
        appCategory: .balanceHealth
    )
}
