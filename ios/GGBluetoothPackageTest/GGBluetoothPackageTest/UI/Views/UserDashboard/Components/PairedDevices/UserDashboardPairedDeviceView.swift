//
//  UserDashboardPairedDeviceView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 29/01/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct UserDashboardPairedDeviceView: View {
    var accountDetails: [Any]
    var category: AppCategory
    @EnvironmentObject var router: Router<DashboardRoutes>
    @EnvironmentObject var deviceConnectionState: DeviceConnectionState
    @StateObject private var viewModel = UserDashboardPairedDeviceViewModel()
    @State private var showDeleteConfirmation = false
    @State private var deviceToDelete: GGBTDevice?
    var parentView: UserDashBoardPairedDeviceParentView
    
    var body: some View {
        VStack(alignment: .leading) {
            if accountDetails.isEmpty {
                (parentView == .pairedDevice ? AnyView(FourDotsLoaderView()) : AnyView(Text("No account details available").foregroundColor(.gray).padding()))
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
            }
            else {
                List {
                    ForEach(viewModel.getDeviceDetails(from: accountDetails, category: category), id: \.self) { deviceDetail in
                        
                        let device = viewModel.convertDeviceDetailToGGBTDevice(deviceDetail: deviceDetail)
                        let sku = viewModel.getSKU(from: deviceDetail)
                        let deviceCategory = parentView == .pairedDevice ? viewModel.getAppCategory(for: sku) : category
                        
                        PairedDeviceItemView(
                            device: device,
                            sku: sku,
                            isConnected: .constant(deviceConnectionState.getDeviceConnectionState(deviceId: {
                                if category == .weightGurus, let deviceIdInt = Int(device.broadcastId) {
                                    return HexConversionHelper.convertIntToHex(value: deviceIdInt, protocolType: .other)
                                } else {
                                    return device.broadcastId
                                }
                            }())),
                            category: deviceCategory,
                            parentView: .Apps
                        )
                        .swipeActions {
                            Button(role: .destructive) {
                                deviceToDelete = device
                                showDeleteConfirmation = true
                            } label: {
                                Label("", systemImage: "trash")
                            }
                        }
                        .listRowSeparator(.hidden)
                        .padding(.trailing, 5)
                        .cornerRadius(8)
                        .shadow(color: .gray.opacity(0.3), radius: 5, x: 0, y: 2)
                    }
                    .overlay(
                        CustomDividerView().offset(y: 10), alignment: .bottom
                    )
                }
                .listStyle(.plain)
                .contentMargins(.init(rawValue: 0), 0, for: .scrollContent)
                .listStyle(.grouped)
                .scrollIndicators(.hidden)
            }
        }
        .alert(isPresented: $showDeleteConfirmation) {
            Alert(
                title: Text(CommonStrings.deleteDevice),
                message: Text(CommonStrings.deleteDeviceConfirmation(deviceName: deviceToDelete?.name)),
                primaryButton: .destructive(Text(CommonStrings.delete)) {
                    if let device = deviceToDelete {
                        viewModel.deleteDevice(category: category, deviceId: device.broadcastId) { success in
                            if success {
                                viewModel.fetchLoginData(for: category) { _, _ in}
                            }
                        }
                    }
                    deviceToDelete = nil
                },
                secondaryButton: .cancel {
                    deviceToDelete = nil
                }
            )
        }
    }
}

struct UserDashboardPairedDeviceView_Previews: PreviewProvider {
    static var previews: some View {
        UserDashboardPairedDeviceView(
            accountDetails: [
                WeightGurusAccountDetailsResponse(
                    id: "1",
                    nickname: "Home Scale",
                    type: "Scale",
                    createdAt: "2023-01-01",
                    userNumber: 1,
                    mac: "00:11:22:33:44:55",
                    broadcastId: 12345,
                    password: nil,
                    sku: "WG-1234",
                    name: "Weight Gurus Scale",
                    peripheralIdentifier: "Peripheral 1",
                    scaleToken: nil,
                    preference: WeightGurusAccountDetailsResponse.Preference(
                        tzOffset: 0,
                        timeFormat: "24h",
                        displayName: "Home",
                        displayMetrics: [],
                        shouldMeasurePulse: true,
                        shouldMeasureImpedance: true,
                        shouldFactoryReset: false,
                        wifiFotaScheduleTime: nil
                    ),
                    latestVersion: "1.0.0"
                ),
                Monitor(
                    id: "2",
                    name: "Blood Pressure Monitor",
                    sku: "BH-5678",
                    type: "Monitor",
                    broadcastId: "67890",
                    peripheralIdentifier: "Peripheral 2",
                    mac: "00:11:22:33:44:66",
                    password: "password",
                    userNumber: 2,
                    nickname: "",
                    createdAt: "2023-02-02"
                )
            ],
            category: .all,
            parentView: .apps
        )
    }
}
