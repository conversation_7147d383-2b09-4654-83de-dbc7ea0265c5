//
//  UserDashboardPairedDeviceViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 12/03/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

class UserDashboardPairedDeviceViewModel: ObservableObject {
    @Injector var deviceService: DeviceService
    @Injector var loginService: LoginService
    func convertDeviceDetailToGGBTDevice(deviceDetail: DeviceDetail) -> GGBTDevice {
        switch deviceDetail {
        case .weightGurus(let wgDetail):
            return GGBTDevice(
                name: wgDetail.name,
                broadcastId: String(wgDetail.broadcastId),
                password: wgDetail.password.map { String($0) },
                token: nil,
                userNumber: wgDetail.userNumber,
                preference: nil,
                syncAllData: true,
                batteryLevel: nil,
                protocolType: nil,
                macAddress: wgDetail.mac
            )
        case .balanceHealth(let bhDetail):
            return GGBTDevice(
                name: bhDetail.name,
                broadcastId: bhDetail.broadcastId,
                password: bhDetail.password,
                token: nil,
                userNumber: bhDetail.userNumber,
                preference: nil,
                syncAllData: true,
                batteryLevel: nil,
                protocolType: nil,
                macAddress: bhDetail.mac
            )
        }
    }
    
    func getDeviceDetails(from details: [Any], category: AppCategory) -> [DeviceDetail] {
        var deviceDetails: [DeviceDetail] = []
        
        for detail in details {
            switch category {
            case .weightGurus:
                if let wgDetail = detail as? WeightGurusAccountDetailsResponse {
                    deviceDetails.append(.weightGurus(detail: wgDetail))
                }
            case .balanceHealth:
                if let bhDetail = detail as? Monitor {
                    deviceDetails.append(.balanceHealth(detail: bhDetail))
                }
            case .all:
                if let wgDetail = detail as? WeightGurusAccountDetailsResponse {
                    deviceDetails.append(.weightGurus(detail: wgDetail))
                }
                if let bhDetail = detail as? Monitor {
                    deviceDetails.append(.balanceHealth(detail: bhDetail))
                }
            default:
                break
            }
        }
        
        return deviceDetails
    }
    
    func getSKU(from deviceDetail: DeviceDetail) -> String {
        switch deviceDetail {
        case .weightGurus(let wgDetail):
            return wgDetail.sku
        case .balanceHealth(let bhDetail):
            return bhDetail.sku
        }
    }
    
    func deleteDevice(category: AppCategory, deviceId: String, completion: @escaping (Bool) -> Void) {
        if let loginDetails = deviceService.fetchUserLogin(for: category), loginDetails.isLoggedIn {
            let email = loginDetails.email ?? "N/A"
            let password = loginDetails.password ?? "N/A"
            
            loginService.deleteDeviceFromApp(for: category, email: email, password: password, deviceId: deviceId) { success in
                if success {
                    
                    var actualDeviceId = deviceId
                    if category == .weightGurus, let deviceIdInt = Int(deviceId) {
                        actualDeviceId = HexConversionHelper.convertIntToHex(value: deviceIdInt, protocolType: .other)
                    }
                    
                    if let device = self.deviceService.fetchDevice(by: actualDeviceId) {
                        self.deviceService.linkDeviceToApp(by: actualDeviceId, isLinked: false)
                    }
                    ToastService.shared.presentToast(with: ToastMessageStrings.deviceDeletedSuccessfully)
                    completion(true)
                } else {
                    completion(false)
                }
            }
        } else {
            completion(false)
        }
    }
    
    func fetchLoginData(for category: AppCategory, completion: @escaping (Bool, CombinedLoginData?) -> Void) {
        LoginHelper.shared.fetchLoginforAppCategory(for: category) { success, combinedData in
            DispatchQueue.main.async {
                completion(success, combinedData)
            }
        }
    }
    
    func getAppCategory(for sku: String) -> AppCategory {
        return getAppCategoryUsingSKU(forSKU: sku )
    }
    
}
