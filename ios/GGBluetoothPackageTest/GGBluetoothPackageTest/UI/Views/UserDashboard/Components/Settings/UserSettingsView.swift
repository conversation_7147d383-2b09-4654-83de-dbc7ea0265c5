//
//  UserSettingsView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 29/01/25.
//

import SwiftUI

struct UserSettingsView: View {
    var loginResponse: WrappedLoginResponse
    var category: AppCategory
    @EnvironmentObject var router: Router<DashboardRoutes>
    @State private var showLogoutAlert = false
    @StateObject private var deviceService = DeviceService()
    
    var body: some View {
        VStack {
            List {
                if category == .weightGurus {
                    if let weightGurusResponse = loginResponse.wrapped as? WeightGurusLoginResponse {
                        ProfileHeaderView(
                            name: "\(weightGurusResponse.account.firstName) \(weightGurusResponse.account.lastName)",
                            email: weightGurusResponse.account.email,
                            profileImage: AppAssets.profile
                        )
                        .frame(maxWidth: .infinity, alignment: .center)
                        
                        Section(header: Text("Personal Information")) {
                            LabelValuePairView(key: Text("Gender"), value: Text(weightGurusResponse.account.gender.uppercased()))
                            LabelValuePairView(key: Text("Date of Birth"), value: Text(formatDate(weightGurusResponse.account.dob)))
                            LabelValuePairView(key: Text("Zipcode"), value: Text(weightGurusResponse.account.zipcode))
                        }
                        
                        Section(header: Text("Settings")) {
                            LabelValuePairView(key: Text("Weight Unit"), value: Text(weightGurusResponse.account.weightUnit))
                            LabelValuePairView(key: Text("Is Weightless On"), value: Text(weightGurusResponse.account.isWeightlessOn ? "Yes" : "No"))
                            LabelValuePairView(key: Text("Is Streak On"), value: Text(weightGurusResponse.account.isStreakOn ? "Yes" : "No"))
                            LabelValuePairView(key: Text("Should Send Entry Notifications"), value: Text(weightGurusResponse.account.shouldSendEntryNotifications ? "Yes" : "No"))
                            LabelValuePairView(key: Text("Should Send Weight In Entry Notifications"), value: Text(weightGurusResponse.account.shouldSendWeightInEntryNotifications ? "Yes" : "No"))
                        }
                        
                        Section(header: Text("Body Metrics")) {
                            LabelValuePairView(key: Text("Height"), value: Text("\(weightGurusResponse.account.height) cm"))
                            LabelValuePairView(key: Text("Weightless Body Fat"), value: Text(weightGurusResponse.account.weightlessBodyFat != nil ? "\(weightGurusResponse.account.weightlessBodyFat!)" : "N/A"))
                            LabelValuePairView(key: Text("Weightless Muscle"), value: Text(weightGurusResponse.account.weightlessMuscle != nil ? "\(weightGurusResponse.account.weightlessMuscle!)" : "N/A"))
                            LabelValuePairView(key: Text("Weightless Weight"), value: Text(weightGurusResponse.account.weightlessWeight != nil ? "\(weightGurusResponse.account.weightlessWeight!)" : "N/A"))
                        }
                        
                        Section(header: Text("Activity and Goal")) {
                            LabelValuePairView(key: Text("Activity Level"), value: Text(weightGurusResponse.account.activityLevel))
                            LabelValuePairView(key: Text("Goal Type"), value: Text(weightGurusResponse.account.goalType))
                            LabelValuePairView(key: Text("Goal Weight"), value: Text("\(weightGurusResponse.account.goalWeight / 10) lbs"))
                            LabelValuePairView(key: Text("Initial Weight"), value: Text("\(weightGurusResponse.account.initialWeight) lbs"))
                        }
                        
                        
                        if weightGurusResponse.account.preferredInputMethod != nil || weightGurusResponse.account.weightlessTimestamp != nil {
                            Section(header: Text("Other Information")) {
                                if let preferredInputMethod = weightGurusResponse.account.preferredInputMethod {
                                    LabelValuePairView(key: Text("Preferred Input Method"), value: Text(preferredInputMethod))
                                }
                                
                                if let weightlessTimestamp = weightGurusResponse.account.weightlessTimestamp {
                                    LabelValuePairView(key: Text("Timestamp"), value: Text(weightlessTimestamp))
                                }
                            }
                        }
                    } else {
                        Text("Unsupported login response type for WeightGurus")
                    }
                } else if category == .balanceHealth {
                    if let balanceHealthResponse = loginResponse.wrapped as? BalanceHealthLoginResponse {
                        ProfileHeaderView(
                            name: "\(balanceHealthResponse.user.firstName) \(balanceHealthResponse.user.lastName)",
                            email: balanceHealthResponse.user.email,
                            profileImage: AppAssets.profile
                        )
                        .frame(maxWidth: .infinity, alignment: .center)
                        
                        Section(header: Text("Personal Information")) {
                            LabelValuePairView(key: Text("Gender"), value: Text(balanceHealthResponse.user.gender.uppercased()))
                            LabelValuePairView(key: Text("Date of Birth"), value: Text(formatDate(balanceHealthResponse.user.dob)))
                            LabelValuePairView(key: Text("Zipcode"), value: Text(balanceHealthResponse.user.zipcode))
                        }
                    } else {
                        Text("Unsupported login response type for BalanceHealth")
                    }
                }else if category == .smartBaby {
                    if let smartBabyResponse = loginResponse.wrapped as? SmartBabyLoginResponse {
                        ProfileHeaderView(
                            name: "\(smartBabyResponse.firstName) \(smartBabyResponse.lastName)",
                             email: smartBabyResponse.email,
                           profileImage: AppAssets.profile
                        )
                        .frame(maxWidth: .infinity, alignment: .center)
                        
                        Section(header: Text("Other Information")) {
                            LabelValuePairView(key: Text("Zipcode"), value: Text(smartBabyResponse.zipcode))
                            LabelValuePairView(key: Text("Measurement Unit"), value: Text(smartBabyResponse.measurementUnits))
                        }
                        
                    }
                } else {
                    Text("Unsupported app category")
                }
                
                Section(header: Text("Log Out")) {
                    Button(action: {
                        showLogoutAlert = true
                    }, label: {
                        LabelValuePairView(key: Text("Log Out"), value: Text(""))
                            .foregroundColor(.red)
                    })
                }
            }
        }
        .environmentObject(router)
        .alert("Log Out", isPresented: $showLogoutAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Log Out", role: .destructive) {
                self.deviceService.deleteUserLogin(for: category)
                router.navigateToRoot()
            }
        } message: {
            Text("Are you sure you want to log out?")
        }
    }
    private func formatDate(_ dateString: String) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        if let date = dateFormatter.date(from: dateString) {
            dateFormatter.dateFormat = "MM/dd/yyyy"
            return dateFormatter.string(from: date)
        } else {
            return dateString
        }
    }
}
