//
//  UserDashboardView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 29/01/25.
//

import SwiftUI

struct UserDashboardView: View {
    @State private var selectedTab: Int = 0
    @State private var showTabBar: Bool = true
    let appCategory: AppCategory
    let loginData: CombinedLoginData?

    @EnvironmentObject var router: Router<DashboardRoutes>
    @StateObject private var viewModel = UserDashboardViewModel()

    private let tabItems: [TabItem] = [
        TabItem(title: CommonStrings.pairedDevice, icon: Image(AppAssets.bluetooth)),
        TabItem(title: CommonStrings.history, icon: Image(systemName: "note.text")),
        TabItem(title: CommonStrings.settings, icon: Image(systemName: "gearshape"))
    ]
    
    var body: some View {
        VStack {
            switch selectedTab {
            case 0:
                if appCategory != .smartBaby {
                    pairedDeviceContent()
                } else {
                    historyContent()
                }

            case 1:
                if appCategory != .smartBaby {
                    historyContent()
                } else {
                    settingsContent()
                }

            case 2:
                if appCategory != .smartBaby {
                    settingsContent()
                } else {
                    Text("No content available")
                }

            default:
                Text("No content available")
            }

            Spacer()

            if showTabBar {
                CustomTabBarView(selectedTab: $selectedTab, showTabBar: $showTabBar, tabItems: getFilteredTabItems())
            }
        }
        .navigationBarBackButtonHidden(true)
        .navigationBarTitleDisplayMode(.inline)
        .environmentObject(router)
        .onAppear {
            tabBarStateSubject.send(false)
            if let loginData = loginData {
                viewModel.loginData = loginData
            }
        }
        .onDisappear {
            tabBarStateSubject.send(true)
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    router.navigateToRoot()
                }) {
                    HStack {
                        Image(systemName: "chevron.backward")
                        Text("Back")
                    }
                }
            }
        }
    }

    private func getFilteredTabItems() -> [TabItem] {
        if appCategory == .smartBaby {
            return tabItems.filter { $0.title != CommonStrings.pairedDevice }
        } else {
            return tabItems
        }
    }

    @ViewBuilder
    private func pairedDeviceContent() -> some View {
        ContentWrapperView(title: .text(CommonStrings.pairedDevice)) {
            if let loginData = viewModel.loginData {
                let (_, accountDetails, _) = viewModel.decodeCombinedLoginData(loginData, appCategory: appCategory)
                UserDashboardPairedDeviceView(accountDetails: accountDetails, category: appCategory, parentView: .apps)
            } else {
                VStack {
                    Text("No account details available")
                    Spacer()
                }
            }
        }
    }

    @ViewBuilder
    private func historyContent() -> some View {
        ContentWrapperView(title: .text(CommonStrings.history)) {
            if let loginData = viewModel.loginData {
                let (_, _, measurements) = viewModel.decodeCombinedLoginData(loginData, appCategory: appCategory)
                let filteredMeasurements = viewModel.getFilteredMeasurements(measurements, for: appCategory)
                UserDashboardHistoryView(measurements: filteredMeasurements, appCategory: appCategory, parentView: .apps)
            } else {
                Text("No measurements available")
            }
        }
    }

    @ViewBuilder
    private func settingsContent() -> some View {
        ContentWrapperView(title: .text(CommonStrings.settings)) {
            if let loginData = viewModel.loginData {
                let (loginResponse, _, _) = viewModel.decodeCombinedLoginData(loginData, appCategory: appCategory)
                UserSettingsView(loginResponse: loginResponse, category: appCategory)
            } else {
                Text("No login data available")
            }
        }
    }
}

#Preview {
    UserDashboardView(appCategory: .weightGurus, loginData: nil)
}
