//
//  LocalPairedDeviceViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 05/02/25.
//

import SwiftUI
import Foundation
import GGBluetoothSwiftPackage

final class LocalPairedDeviceViewModel: ObservableObject {
    private let deviceService = DeviceService()
    @Published var selectedCategory: AppCategory? = .all
    @Published var pairedDevices: [GGBTDevice] = []
    @Injector var loginService: LoginService
    
    var filteredDevices: [GGBTDevice] {
        guard let category = selectedCategory else { return pairedDevices }
        if category == .all {
            return pairedDevices
        }
        return pairedDevices.filter { device in
            getApp(forDevice: device.broadcastId, deviceName: device.name) == category
        }
    }
    
    private func getAllStoredBroadcastIds() -> [String] {
        guard let storedDevices = deviceService.fetchAllDevices() else { return [] }
        return storedDevices.compactMap { $0.broadcastId }
    }
    
    func fetchPairedDevices() {
        let storedBroadcastIds = getAllStoredBroadcastIds()
        
        var uniqueDevices: [GGBTDevice] = []
        
        for broadcastId in storedBroadcastIds {
            if let device = deviceService.fetchDevice(by: broadcastId) {
                let newDevice = GGBTDevice(
                    name: device.name ?? "",
                    broadcastId: device.broadcastId ?? "",
                    password: device.password,
                    token: device.token,
                    userNumber: Int(device.userNumber),
                    preference: nil,
                    syncAllData: true,
                    batteryLevel: Int(device.batteryLevel),
                    protocolType:device.protocolType,
                    macAddress: device.macAddress
                )
                
                if !uniqueDevices.contains(where: { $0.broadcastId == newDevice.broadcastId }) {
                    uniqueDevices.append(newDevice)
                }
            }
        }
        pairedDevices = uniqueDevices
    }
    
    func getAppCategory(forDevice device: GGBTDevice) -> AppCategory {
        return getApp(forDevice: device.broadcastId, deviceName: device.name)
    }
    
    func deleteDevice(at offsets: IndexSet) {
        for index in offsets {
            let device = pairedDevices[index]
            let category = getApp(forDevice: device.broadcastId, deviceName: device.name)
            let isSingleDeviceInCategory = pairedDevices.filter { getApp(forDevice: $0.broadcastId, deviceName: $0.name) == category }.count == 1
            
            if isSingleDeviceInCategory, [.smartBaby, .weightGurus, .balanceHealth].contains(category) {
                deviceService.deleteUserLogin(for: category)
            }
            
            Task {
                await deviceService.deleteDevice(by: device) 
            }
            pairedDevices.remove(at: index)
            ToastService.shared.presentToast(with: ToastMessageStrings.deviceDeletedSuccessfully)
        }
    }
}
