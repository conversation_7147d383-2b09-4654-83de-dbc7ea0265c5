//
//  LocalPairedDevicesView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 05/02/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct LocalPairedDevicesView: View {
    @ObservedObject var pairedDevicesModel: LocalPairedDeviceViewModel
    @EnvironmentObject var deviceConnectionState: DeviceConnectionState
    @EnvironmentObject var router: Router<DashboardRoutes>
    @State private var showDeleteConfirmation = false
    @State private var deviceToDelete: GGBTDevice?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            if pairedDevicesModel.filteredDevices.isEmpty {
                Text(CommonStrings.pairedDevicePlaceholder)
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
            } else {
                List {
                    ForEach(pairedDevicesModel.filteredDevices, id: \.broadcastId) { device in
                        let appCategory = getApp(forDevice: device.broadcastId, deviceName: device.name)
                        PairedDeviceItemView(
                            device: device, sku: getSKU(device.name),
                            isConnected: .constant(
                                deviceConnectionState.getDeviceConnectionState(deviceId: device.broadcastId)
                            ),
                            category: pairedDevicesModel.getAppCategory(forDevice: device), parentView: .pairedDevice
                        )
                        .listRowSeparator(.hidden)
                        .padding(.trailing, 5)
                        .cornerRadius(8)
                        .shadow(color: .gray.opacity(0.3), radius: 5, x: 0, y: 2)
                        .swipeActions {
                            Button(role: .destructive) {
                                confirmDelete(device: device)
                            } label: {
                                Label("", systemImage: "trash")
                            }
                        }
                    }
                    .onDelete(perform: pairedDevicesModel.deleteDevice)
                    .overlay(
                        CustomDividerView().offset(y: 10), alignment: .bottom
                    )
                    
                }
                .listStyle(.plain)
                .contentMargins(.init(rawValue: 0), 0, for: .scrollContent)
                .listStyle(.grouped)
                
            }
        }
        .onAppear {
            pairedDevicesModel.fetchPairedDevices()
            updateConnectionStates()
        }
        .alert(isPresented: $showDeleteConfirmation) {
            Alert(
                title: Text(CommonStrings.deleteDevice),
                message: Text(CommonStrings.deleteDeviceConfirmation(deviceName: deviceToDelete?.name)),
                primaryButton: .destructive(Text(CommonStrings.delete)) {
                    if let device = deviceToDelete {
                        pairedDevicesModel.deleteDevice(at: IndexSet(integer: pairedDevicesModel.pairedDevices.firstIndex(of: device) ?? 0))
                    }
                    deviceToDelete = nil
                },
                secondaryButton: .cancel {
                    deviceToDelete = nil
                }
            )
        }
        .environmentObject(router)
        .environmentObject(deviceConnectionState)
    }

    private func updateConnectionStates() {
        for device in pairedDevicesModel.filteredDevices {
            let isConnected = deviceConnectionState.getDeviceConnectionState(deviceId: device.broadcastId)
            deviceConnectionState.deviceConnectionStates[device.broadcastId] = isConnected
        }
    }
    
    private func confirmDelete(device: GGBTDevice) {
        deviceToDelete = device
        showDeleteConfirmation = true
    }
}
