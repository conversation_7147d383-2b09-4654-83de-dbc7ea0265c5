//
//  MarqueeTextView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 14/03/25.
//

import SwiftUI

struct MarqueeTextView: View {
    let text: String
    let font: Font
    let speed: Double
    
    @State private var offset: CGFloat = 0
    @State private var textWidth: CGFloat = 0
    @State private var containerWidth: CGFloat = 0

    var body: some View {
        GeometryReader { geometry in
            ScrollView(.horizontal, showsIndicators: false) {
                Text(text)
                    .font(font)
                    .background(GeometryReader { textGeometry in
                        Color.clear
                            .onAppear {
                                textWidth = textGeometry.size.width
                                containerWidth = geometry.size.width
                                startAnimation()
                            }
                    })
                    .offset(x: offset)
            }
            .clipped()
        }
        .frame(height: 25)
    }
    
    private func startAnimation() {
        if textWidth > containerWidth {
            offset = containerWidth
            withAnimation(Animation.linear(duration: speed * (textWidth / containerWidth)).repeatForever(autoreverses: false)) {
                offset = -textWidth
            }
        }
    }
}

#Preview{
    MarqueeTextView(text: "This is a scrolling marquee text tutorial in SwiftUI!", font: .body, speed: 5)
        .padding(.horizontal,50)
}
