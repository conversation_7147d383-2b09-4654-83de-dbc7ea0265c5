//
//  PairedDeviceItemView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 29/11/24.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct PairedDeviceItemView: View {
    @State var device: GGBTDevice?
    @State var sku: String
    @Binding var isConnected: Bool
    @EnvironmentObject var router: Router<DashboardRoutes>
    @StateObject var vm: PairedDeviceItemViewModel = PairedDeviceItemViewModel()
    let category: AppCategory
    let parentView: DeviceDetailParentView
    @State private var isLoggedIn: Bool = false
    
    var body: some View {
        HStack(spacing: 10) {
            ZStack(alignment: .topLeading) {
                if let image = getImage(for: sku) {
                    VStack {
                        image
                            .resizable()
                            .scaledToFit()
                            .frame(width: 80, height: 80)
                            .cornerRadius(10)
                    }
                } else {
                    Image("\(sku)")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 80, height: 80)
                        .cornerRadius(10)
                }
                category.image()
                    .resizable()
                    .scaledToFit()
                    .frame(width: 25, height: 25)
                    .clipShape(Circle())
            }
            
            VStack(alignment: .leading, spacing: 5) {
                MarqueeTextView(
                    text: device?.name ?? "Unknown Device",
                    font: .headline,
                    speed: 5.0
                )
                
                Text("\(device?.broadcastId.count ?? 0 > 15 ? device!.broadcastId.prefix(12) + "..." : device?.broadcastId ?? "Unknown ID")")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                HStack(spacing: 0) {
                    Image(systemName: "circle.fill")
                        .foregroundColor(isConnected ? .green : .red)
                    
                    Text(isConnected ? "Online" : "Offline")
                        .foregroundColor(.secondary)
                        .padding(.horizontal, 5)
                        .foregroundColor(.secondary)
                    
                    if parentView == .pairedDevice && isLoggedIn {
                        Image(systemName: "link")
                            .padding(.leading, 20)
                            .foregroundColor(.blue)
                            .font(.headline)
                    }
                }
                .font(.caption)
            }
            .padding(.leading, 10)
            
            Spacer()
            
            if let userNumber = device?.userNumber, userNumber > 0 {
                HStack(spacing: 5) {
                    Image(systemName: "person.fill")
                        .foregroundColor(.blue)
                        .font(.headline)
                    Text("\(userNumber)")
                        .foregroundColor(.secondary)
                        .font(.headline)
                }
                .padding(.trailing, 15)
            }
            
            Button(action: {
                if let device = device {
                    router.navigate(to: .deviceDetail(category, device, parentView, sku))
                }
            }, label: {
                Image(systemName: "chevron.right")
                    .foregroundColor(.black)
                    .fontWeight(.bold)
            })
        }
        .onAppear {
            if let device = device {
                isConnected = vm.deviceConnectionState.getDeviceConnectionState(deviceId: device.broadcastId)
            }
            if let loginDetails = DeviceService.shared.fetchUserLogin(for: category), loginDetails.isLoggedIn {
                isLoggedIn = true
            }
        }
        .onReceive(vm.deviceConnectionState.$deviceConnectionStates) { _ in
            if let device = device {
                isConnected = vm.deviceConnectionState.getDeviceConnectionState(deviceId: device.broadcastId)
            }
        }
        .environmentObject(router)
    }
}

class PairedDeviceItemViewModel: ObservableObject {
    @Injector var deviceConnectionState: DeviceConnectionState
}

#Preview {
    PairedDeviceItemView(device: GGBTDevice(name: "gG Scale", broadcastId: "1234567890", password: "", token: "", userNumber: 1, preference: nil, syncAllData: false, batteryLevel: 56, protocolType: "", macAddress: ""), sku: "WG-1234", isConnected: .constant(false), category: .weightGurus, parentView: .pairedDevice)
        .padding(15)
}
