//
//  PairedDeviceViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 29/11/24.
//

import SwiftUI

final class PairedDeviceViewModel: ObservableObject {
    @Published var combinedLoginData: CombinedLoginData?
    @Published var combinedAccountDetails: [Any] = []
    @Injector var deviceService: DeviceService
    private let logger: AppLogger = AppLogger(category: String(describing: PairedDeviceViewModel.self))
    
    func fetchLoginData(for category: AppCategory) {
        if category == .all {
            combinedAccountDetails = []
            fetchLoginDataForCategory(.weightGurus) { [weak self] in
                self?.fetchLoginDataForCategory(.balanceHealth) {
                    if let self = self {
                        let allDetails = (self.combinedLoginData?.accountDetails ?? []) + (self.combinedAccountDetails)
                        self.combinedAccountDetails = self.filterDuplicatesByBroadcastId(allDetails)
                    }
                }
            }
        } else {
            fetchLoginDataForCategory(category) { [weak self] in
                if let self = self {
                    self.combinedAccountDetails = self.filterDuplicatesByBroadcastId(self.combinedAccountDetails)
                }
            }
        }
    }
    
    private func fetchLoginDataForCategories(_ categories: [AppCategory], completion: @escaping (AppCategory) -> Void) {
        let dispatchGroup = DispatchGroup()
        var fetchedAccountDetails: [Any] = []
        
        for category in categories {
            dispatchGroup.enter()
            fetchLoginDataForCategory(category) { [weak self] in
                if let self = self, let accountDetails = self.combinedLoginData?.accountDetails {
                    fetchedAccountDetails.append(contentsOf: accountDetails)
                }
                dispatchGroup.leave()
                completion(category)
            }
        }
        
        dispatchGroup.notify(queue: .main) {
            self.combinedAccountDetails = fetchedAccountDetails
            self.combineAndDeduplicateAccountDetails()
        }
    }
    
    private func fetchLoginDataForCategory(_ category: AppCategory, completion: (() -> Void)? = nil) {
        LoginHelper.shared.fetchLoginforAppCategory(for: category) { [weak self] success, combinedLoginData in
            guard let self = self else { return }
            if success, let data = combinedLoginData {
                self.combinedLoginData = data
                self.combinedAccountDetails.append(contentsOf: data.accountDetails)
            } else {
                let nsError = NSError(domain: String(describing: DeviceService.self), code: 4, userInfo: [NSLocalizedDescriptionKey: "Failed to fetch login data for category: \(category)"])
                logger.error(error: nsError, function: #function, line: #line)
            }
            completion?()
        }
    }
    
    private func filterDuplicatesByBroadcastId(_ details: [Any]) -> [Any] {
        var uniqueDetails = [String: Any]()
        
        for detail in details {
            if let monitor = detail as? Monitor {
                uniqueDetails[monitor.broadcastId] = monitor
            } else if let weightGurusDetail = detail as? WeightGurusAccountDetailsResponse {
                uniqueDetails[String(weightGurusDetail.broadcastId)] = weightGurusDetail
            }
        }
        
        return Array(uniqueDetails.values)
    }
    
    private func combineAndDeduplicateAccountDetails() {
        var uniqueAccountDetails = [String: Any]()
        
        for detail in combinedAccountDetails {
            if let identifiableDetail = detail as? (any Identifiable) {
                uniqueAccountDetails[identifiableDetail.id as! String] = detail
            }
        }
        
        combinedAccountDetails = Array(uniqueAccountDetails.values)
    }
    
    func fetchAllUserLogins() -> [(UserLogin, AppCategory)]? {
        let result = deviceService.fetchAllUserLogins()
        return result
    }
}
