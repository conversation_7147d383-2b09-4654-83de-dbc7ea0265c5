//
//  PairedDeviceView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 25/11/24.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct PairedDeviceView: View {
    @StateObject var viewModel = PairedDeviceViewModel()
    @StateObject var localPairedDevicesModel = LocalPairedDeviceViewModel()
    @StateObject var router: Router<DashboardRoutes> = .init()
    @EnvironmentObject var deviceConnectionState: DeviceConnectionState
    
    @State private var selectedCategory: AppCategory? = .all
    @State private var selection = 0
    @State private var userLogins: [UserLogin] = []
    
    var body: some View {
            RoutingView(stack: $router.stack) {
                VStack(alignment: .leading, spacing: 0) {
                    if userLogins.isEmpty {
                        LocalPairedDevicesView(
                            pairedDevicesModel: localPairedDevicesModel,
                            deviceConnectionState: _deviceConnectionState
                        )
                    } else {
                        DeviceSelectionView(
                            selection: $selection,
                            localView: LocalPairedDevicesView(
                                pairedDevicesModel: localPairedDevicesModel,
                                deviceConnectionState: _deviceConnectionState
                            ).environmentObject(deviceConnectionState)
                            ,
                            apiView: UserDashboardPairedDeviceView(
                                accountDetails: viewModel.combinedAccountDetails,
                                category: selectedCategory ?? .weightGurus, parentView: .pairedDevice
                            )
                        )
                    }
                }
                .onAppear {
                    viewModel.fetchLoginData(for: selectedCategory ?? .weightGurus)
                    if let fetchedUserLogins = viewModel.fetchAllUserLogins() {
                        userLogins = fetchedUserLogins.map { $0.0 } 
                    }
                }
                .background(.white)
                .toolbar {
                    ToolbarItem(placement: .principal) {
                        Menu {
                            ForEach(AppCategory.allCases, id: \.self) { category in
                                Button(action: {
                                    localPairedDevicesModel.selectedCategory = category
                                    selectedCategory = category
                                    viewModel.fetchLoginData(for: category)
                                }) {
                                    Text(category.rawValue.uppercased())
                                }
                            }
                        } label: {
                            HStack {
                                Text(customCategoryText(for: selectedCategory).uppercased())
                                    .foregroundColor(.black)
                                    .font(.headline)
                                    .fontWeight(.bold)
                                Image(systemName: "chevron.down")
                                    .font(.subheadline)
                            }
                        }
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .environmentObject(router)
    }
}

#Preview {
    PairedDeviceView()
        .environmentObject(DeviceConnectionState())
}
