//
//  InformationCardView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 24/12/24.
//

import SwiftUI

struct InformationCardView: View {
    @EnvironmentObject var router: Router<DashboardRoutes>
    var device: DeviceInfo
    var body: some View {
        HStack{
            
            ZStack(alignment: .topLeading) {
                Image(device.image)
                    .resizable()
                    .scaledToFit()
                    .frame(width: 80, height: 80)
                    .cornerRadius(10)
                
                device.app.image()
                    .resizable()
                    .scaledToFit()
                    .frame(width: 28, height: 28)
                    .clipShape(Circle())
                    .offset(x: 0, y: 0)
                
            }
            .padding(.trailing, 10)
            
            VStack(alignment: .leading, spacing: 10){
                Text(device.name)
                    .font(.subheadline)
                    .fontWeight(.bold)
                Text("\(DevicesViewStrings.sku.uppercased()): \(device.sku)")
                    .foregroundColor(.secondary)
                    .font(.caption)
                
            }
            Spacer()
            <PERSON><PERSON>(action: {
                router.navigate(to: .devicesInformation(device))
            }, label: {
                Image(systemName: "chevron.right")
            })
        }
        .environmentObject(router)
    }
}

#Preview {
    InformationCardView( device: DeviceInfo(
        name: "MY_DEVICE",
        sku: "0412",
        image: "kitchenScale",
        protocolType: "",
        manufacturer: "Wellland",
        app : .sage,
        errorcodes: [
            ErrorCode(code: "Lo", description: "Low Battery")
        ]
    )
    )
    .padding(12)
}
