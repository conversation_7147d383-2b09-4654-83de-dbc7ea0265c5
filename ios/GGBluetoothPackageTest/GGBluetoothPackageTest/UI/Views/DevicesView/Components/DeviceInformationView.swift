//
//  DeviceInformationView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 24/12/24.
//

import SwiftUI

struct DeviceInformationView: View {
    var device: DeviceInfo
    @State private var showSheet = false
    @State private var showSafari = false
    
    var body: some View {
        List {
            Section {
                Image(device.image)
                    .resizable()
                    .frame(width: UIScreen.main.bounds.height * 0.25,
                           height: UIScreen.main.bounds.height * 0.25)
                    .frame(maxWidth: .infinity, alignment: .center)
            }
            
            Section(header: Text(DevicesViewStrings.deviceInformation)) {
                LabelValuePairView(key: Text(DevicesViewStrings.name), value: Text(device.name))
                LabelValuePairView(key: Text(DevicesViewStrings.sku.uppercased()), value: Text(device.sku))
                LabelValuePairView(key: Text(DevicesViewStrings.protocolType), value: Text(device.protocolType))
                LabelValuePairView(key: Text(DevicesViewStrings.manufacturer.capitalized), value: Text(device.manufacturer.capitalized))
                LabelValuePairView(key: Text(DevicesViewStrings.app), value: Text(customCategoryText(for: device.app).uppercased()))
            }
            
            Section(header: Text(DevicesViewStrings.errorCodes)) {
                Button(action: {
                    showSheet.toggle()
                }) {
                    LabelValuePairView(key: Text(DevicesViewStrings.errorCodes), value: Image(systemName: "chevron.right"))
                }
                .foregroundColor(.black)
                .fullScreenCover(isPresented: $showSheet) {
                    ErrorCodesBottomSheetView(device: device)
                }
            }
            
            Section(header: Text(DevicesViewStrings.support)) {
                Button(action: {
                    showSafari = true
                }) {
                    LabelValuePairView(key: Text(DevicesViewStrings.productGuide), value: Image(systemName: "chevron.right"))
                }
                .foregroundColor(.black)
                .fullScreenCover(isPresented: $showSafari) {
                    SafariView(url: ProductURLHelper.getProductGuideURL(forSKU: device.sku))
                }
            }
        }
    }
}

#Preview {
    DeviceInformationView(device: DeviceInfo(
        name: "MY_SCALE",
        sku: "0412",
        image: AppAssets.kitchenScale,
        protocolType: "",
        manufacturer: "Wellland",
        app : .sage,
        errorcodes: [ErrorCode(code: "Lo", description: "Low Battery")]
    )
    )
}
