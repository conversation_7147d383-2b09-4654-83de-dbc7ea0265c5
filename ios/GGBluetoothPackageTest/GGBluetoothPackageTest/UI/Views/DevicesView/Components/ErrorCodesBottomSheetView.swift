//
//  ErrorCodesBottomSheetView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/12/24.
//

import SwiftUI

struct ErrorCodesBottomSheetView: View {
    @Environment(\.presentationMode) var presentationMode
    var device: DeviceInfo

    var body: some View {
        VStack(alignment: .leading) {
            But<PERSON>(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                HStack{
                    Image(systemName: "chevron.left")
                    Text("Back")
                }
            }
            .padding()

            List {
                LazyVGrid(
                    columns: [
                        GridItem(.flexible(minimum: 50, maximum: 140), alignment: .leading),
                        GridItem(.flexible(), alignment: .leading)
                    ],
                    spacing: 30
                ) {
                    ForEach(device.errorcodes, id: \.self) { errorCode in
                        Text("\(errorCode.code)")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .padding(.trailing, 10)
                        VStack(alignment: .leading, spacing: 4) {
                            if let title = errorCode.title, !title.isEmpty {
                                Text(title.uppercased())
                                    .font(.caption)
                            }
                            Text(errorCode.description)
                                .foregroundColor(.secondary)
                                .font(.caption)
                        }
                    }
                }
            }
        }
    }
}

#Preview {
    let sampleDevice = DeviceInfo(
        name: "MY_SCALE",
        sku: "0412",
        image: AppAssets.kitchenScale,
        protocolType: "",
        manufacturer: "Wellland",
        app: .balanceHealth,
        errorcodes: [
            ErrorCode(code: "E001", title: "Connection Error", description: "Unable to connect to the device."),
            ErrorCode(code: "E002", title: "Battery Low", description: "Please charge the device."),
            ErrorCode(code: "E003", title: "Sensor Error", description: "Sensor malfunction detected.")
        ]
    )
    ErrorCodesBottomSheetView(device: sampleDevice)
}
