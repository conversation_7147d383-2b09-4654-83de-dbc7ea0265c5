//
//  DevicesView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 23/12/24.
//

import SwiftUI

struct DevicesView: View {
    @StateObject var router: Router<DashboardRoutes> = .init()
    @StateObject private var viewmodel = DevicesViewModel()
    @State var selectedCategory: DeviceCategory = .all
    @State private var isSearchVisible: Bool = false
    
    var body: some View {
        RoutingView(stack: $router.stack) {
            VStack {
                if viewmodel.filteredDevices.isEmpty {
                    Text("No devices found")
                        .foregroundColor(.gray)
                } else {
                    List(viewmodel.filteredDevices, id: \.sku) { device in
                        InformationCardView(device: device)
                            .padding(.trailing, 5)
                            .cornerRadius(8)
                            .shadow(color: .gray.opacity(0.3), radius: 5, x: 0, y: 2)
                    }
                    .scrollIndicators(.hidden)
                }
            }
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text(CommonStrings.devices.uppercased())
                        .font(.headline)
                        .foregroundColor(.primary)
                }
                ToolbarItem(placement: .navigationBarLeading) {
                    Menu {
                        ForEach(DeviceCategory.allCases, id: \.self) { category in
                            Button(action: {
                                selectedCategory = category
                                viewmodel.filterDevicesByCategory(category)
                            }, label: {
                                Text(category.rawValue.uppercased())
                            })
                        }
                    } label: {
                        HStack {
                            Text(selectedCategory.rawValue.uppercased())
                                .font(.subheadline)
                                .fontWeight(.bold)
                            Image(systemName: "chevron.up.chevron.down")
                                .font(.subheadline)
                        }
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        isSearchVisible.toggle()
                    }) {
                        Image(systemName: "magnifyingglass")
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
        }
        .searchable(text: $viewmodel.searchText, isPresented: $isSearchVisible)
        .environmentObject(router)
        .onAppear {
            viewmodel.filterDevicesByCategory(selectedCategory)
        }
    }
}

#Preview {
    DevicesView()
        .environmentObject(Router<DashboardRoutes>())
}
