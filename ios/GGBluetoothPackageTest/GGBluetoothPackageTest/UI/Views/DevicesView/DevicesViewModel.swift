//
//  DevicesViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/12/24.
//

import SwiftUI

final class DevicesViewModel: ObservableObject {
    @Published var searchText: String = ""
    @Published var selectedCategory: DeviceCategory?
    @Published private(set) var filteredDevicesList: [DeviceInfo] = devicesList

    var filteredDevices: [DeviceInfo] {
        var devices = devicesList

        if let category = selectedCategory, category != .all {
            devices = devices.filter { device in
                let deviceCategory = getDeviceType(forDeviceIdentifier: device.sku)
                return deviceCategory == category
            }
        }

        if !searchText.isEmpty {
            devices = devices.filter {
                $0.name.localizedCaseInsensitiveContains(searchText) ||
                $0.sku.localizedCaseInsensitiveContains(searchText)
            }
        }

        return devices
    }

    func filterDevicesByCategory(_ category: DeviceCategory) {
        selectedCategory = category
        applyFilters()
    }

    private func applyFilters() {
        filteredDevicesList = filteredDevices
    }
}
