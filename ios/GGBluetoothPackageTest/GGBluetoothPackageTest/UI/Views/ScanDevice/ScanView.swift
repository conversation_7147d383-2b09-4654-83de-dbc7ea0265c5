//
//  ScanView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON><PERSON> Chittibabu on 17/06/24.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct ScanView: View {
    @StateObject var devicesModel = ScanViewModel()
    @StateObject var router: Router<DashboardRoutes> = .init()
    @EnvironmentObject var deviceConnectionState: DeviceConnectionState
    @Binding var isScanning: Bool
    @Binding var selectedCategory: AppCategory?
    var onToastMessage: (String) -> Void
    @Binding var refreshTrigger: Int

    var body: some View {
        NavigationView {
            RoutingView(stack: $router.stack) {
                VStack {
                    if devicesModel.isLoading {
                        ProgressView()
                            .scaleEffect(1.5, anchor: .center)
                            .padding()
                    } else {
                        if (isScanning && devicesModel.allDevices.isEmpty) || (!isScanning && devicesModel.filteredDevices.isEmpty) {
                            Text(isScanning ? ScanViewStrings.noDevicesFound : ScanViewStrings.noDevicesavailable)
                                .foregroundColor(.gray)
                                .padding()
                        } else {
                            List {
                                if isScanning {
                                    ForEach(devicesModel.allDevices, id: \.broadcastId) { device in
                                        DeviceItemView(device: device, selectedCategory: $selectedCategory, onToastMessage: onToastMessage)
                                            .padding(.trailing, 5)
                                            .cornerRadius(8)
                                            .shadow(color: .gray.opacity(0.3), radius: 5, x: 0, y: 2)
                                    }
                                } else {
                                    ForEach(devicesModel.filteredDevices, id: \.broadcastId) { device in
                                        DeviceItemView(device: device, selectedCategory: $selectedCategory, onToastMessage: onToastMessage)
                                            .padding(.trailing, 5)
                                            .cornerRadius(8)
                                            .shadow(color: .gray.opacity(0.3), radius: 5, x: 0, y: 2)
                                    }
                                }
                            }
                            .listStyle(PlainListStyle())
                        }
                    }
                }
                .toolbar {
                    ToolbarItem(placement: .principal) {
                        Menu {
                            ForEach(AppCategory.allCases, id: \.self) { category in
                                Button(action: {
                                    selectedCategory = category
                                    devicesModel.filterDevicesByCategory(category)
                                }) {
                                    Text(category.rawValue.uppercased())
                                }
                            }
                        } label: {
                            HStack(spacing: 4) {
                                Text(customCategoryText(for: selectedCategory).uppercased())
                                    .foregroundColor(.black)
                                    .font(.headline)
                                    .fontWeight(.bold)
                                Image(systemName: "chevron.down")
                                    .font(.subheadline)
                            }
                        }
                    }
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button(action: {
                            let category = selectedCategory ?? .all
                            devicesModel.refreshScanning(for: category, isScanning: isScanning)
                        }, label: {
                            HStack {
                                Image(systemName: "arrow.clockwise")
                                    .font(.subheadline)
                                    .fontWeight(.bold)
                            }
                        })
                    }
                }
            }
            .onAppear {
                if selectedCategory == nil {
                    selectedCategory = devicesModel.selectedCategory ?? .all
                } else {
                    devicesModel.loadSelectedCategory()
                }
                if let category = selectedCategory {
                    devicesModel.filterDevicesByCategory(category)
                }
                devicesModel.deviceConnectionState = deviceConnectionState
            }
            .onChange(of: isScanning) { newValue in
                if let category = selectedCategory {
                    devicesModel.filterDevicesByCategory(category)
                }
            }
            .onChange(of: refreshTrigger) { _ in
                let category = selectedCategory ?? .all
                devicesModel.refreshScanning(for: category, isScanning: isScanning)
            }
        }
        .environmentObject(router)
    }
}

#Preview {
    @Previewable @State var isScanning: Bool = false
    @Previewable @State var selectedCategory: AppCategory? = .all
    ScanView(isScanning: $isScanning, selectedCategory: $selectedCategory, onToastMessage: {_ in }, refreshTrigger: .constant(0))
}
