//
//  CustomInputModalViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 08/01/25.
//

import Foundation

class CustomInputModalViewModel: ObservableObject {
    @Published var selectedUserNumber: Int = 1
    @Published var availableUserNumbers: [Int] = []
    @Published var errorMessage: String? = nil
    @Published var isConfirmEnabled: Bool = false
    
    var sku: String
    var category: AppCategory
    
    init(sku: String, category: AppCategory) {
        self.sku = sku
        self.category = category
        self.setupAvailableUserNumbers()
    }
    
    func setupAvailableUserNumbers() {
        availableUserNumbers = self.category == .balanceHealth
        ? [1, 2]
        : Array(1...8)
    }
    
    func setupUserNumber(_ userNumber: Int?) {
        if let userNumber = userNumber, availableUserNumbers.contains(userNumber) {
            selectedUserNumber = userNumber
        } else {
            selectedUserNumber = availableUserNumbers.first ?? 1
        }
        validateUserNumber()
    }
    
    func validateUserNumber() {
        errorMessage = nil
        if !availableUserNumbers.contains(selectedUserNumber) {
            errorMessage = "Selected user number is invalid."
            isConfirmEnabled = false
        } else {
            isConfirmEnabled = true
        }
    }
}

