//
//  CustomInputModalView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 08/01/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct CustomInputModalView: View {
    var device: GGBTDevice?
    @Binding var userNumber: Int?
    var onConfirm: () -> Void
    var onCancel: () -> Void
    
    @StateObject private var viewModel: CustomInputModalViewModel
    
    init(device: GGBTDevice?, userNumber: Binding<Int?>, onConfirm: @escaping () -> Void, onCancel: @escaping () -> Void, sku: String, category: AppCategory) {
        self.device = device
        self._userNumber = userNumber
        self.onConfirm = onConfirm
        self.onCancel = onCancel
        self._viewModel = StateObject(wrappedValue: CustomInputModalViewModel(sku: sku, category: category))
    }
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Select the User Number")
                .font(.headline)
            
            Picker("User Number", selection: $viewModel.selectedUserNumber) {
                ForEach(viewModel.availableUserNumbers, id: \.self) { number in
                    if viewModel.category == .balanceHealth {
                        Text(number == 1 ? "A" : "B").tag(number)
                    } else {
                        Text("U\(number)").tag(number)
                    }
                }
            }
            .pickerStyle(WheelPickerStyle())
            .padding()
            
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
                    .foregroundColor(.red)
                    .font(.caption)
                    .padding(.top, 0)
            }
            
            HStack {
                Button("Cancel") {
                    onCancel()
                }
                .foregroundColor(.white)
                .background(Color.red)
                .buttonStyle(.bordered)
                .cornerRadius(7)
                
                Button("Confirm") {
                    userNumber = viewModel.selectedUserNumber
                    onConfirm()
                }
                .buttonStyle(.borderedProminent)
                .disabled(!viewModel.isConfirmEnabled)
            }
        }
        .padding()
        .presentationDetents([.fraction(0.4)])
        .onAppear {
            viewModel.setupUserNumber(userNumber)
        }
    }
}

#Preview {
    @Previewable @State  var previewUserNumber: Int? = 1
    CustomInputModalView(device: GGBTDevice(name: "Device", broadcastId: "1234566789", password: "", token: "", userNumber: 1, preference: nil, syncAllData: true, batteryLevel: 90, protocolType: "", macAddress: ""), userNumber: $previewUserNumber, onConfirm: {}, onCancel: {}, sku: "123456", category: .weightGurus)
}
