//
//  DeviceItemViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/11/24.
//

import Foundation
import Combine
@preconcurrency import GGBluetoothSwiftPackage
import SwiftUICore

class DeviceItemViewModel: ObservableObject {
    @Injector var bluetoothService: BluetoothService
    @Injector var deviceService: DeviceService
    @Injector var r4scaleService: R4ScaleService
    @Injector var userProfileService: UserProfileUpdationService
    @Published var isConnected: Bool = false
    @Published var updatedBroadcastId: String?
    @Published var userNumber: Int?
    @Published var toast: Toast?
    @Published var showAlert: Bool = false
    @Published var alertTitle: String = ""
    @Published var alertMessage: String = ""
    @Published var alertPrimaryButtonTitle: String = ""
    @Published var alertSecondaryButtonTitle: String = ""
    @Published var isUserAdded: Bool = false
    @Published var userName: String = ""
    @Published var isConnecting: Bool = false
    var alertPrimaryButtonAction: (() -> Void)?
    var alertSecondaryButtonAction: (() -> Void)?
    var navigateToLogin: (() -> Void)?
    private let logger: AppLogger = AppLogger(category: String(describing: DeviceService.self))
    var toastLang = ToastMessageStrings.self
    var errorLang = ErrorStrings.self
    var commonLang = CommonStrings.self
    private let r4Helper = R4ScaleHelper()
    
    func confirmPair(for device: GGBTDevice, selectedCategory: AppCategory, userNumber: Int?,
                     batteryLevel: Int, macAddress: String, password: String,
                     protocolType: String, token: String, deviceType: String,
                     preference: Preference?) async -> Bool {
        do {
            let (updatedBroadcastId, updatedPassword) = await bluetoothService.confirmPair(device, selectedCategory: selectedCategory, userNumber: userNumber)
            DispatchQueue.main.async {
                self.updatedBroadcastId = updatedBroadcastId
            }
            DispatchQueue.main.async {
                self.deviceService.saveDevice(name: device.name, broadcastId: updatedBroadcastId, selectedCategory: selectedCategory, userNumber: userNumber, batteryLevel: batteryLevel, macAddress: macAddress, password: updatedPassword ?? password, protocolType: protocolType, token: token, deviceType: deviceType, preference: preference)
            }
            return true
        } catch {
            let nsError = NSError(domain: String(describing: DeviceItemViewModel.self), code: 2, userInfo: [NSLocalizedDescriptionKey: "\(errorLang.savingUserEditError) \(error.localizedDescription)"])
            showAlert(title: "Pairing Error", message: "\(errorLang.savingUserEditError) \(error.localizedDescription)", primaryButtonTitle: commonLang.ok)
            DispatchQueue.main.async {
                self.logger.error(error: nsError, function: #function, line: #line)
            }
            return false
        }
    }
    
    func connectDevice(_ device: GGBTDevice, userNumber: Int? = nil, selectedCategory: AppCategory?, deviceConnectionState: DeviceConnectionState, onToastMessage: @escaping (String) -> Void)  {
        Task{
            DispatchQueue.main.async {
                self.isConnecting = true
            }
            
            let pairingSuccess = await confirmPair(
                for: device,
                selectedCategory: selectedCategory ?? .weightGurus,
                userNumber: userNumber,
                batteryLevel: device.batteryLevel ?? 0,
                macAddress: device.macAddress ?? "",
                password: device.password ?? "",
                protocolType: device.protocolType ?? "",
                token: device.token ?? "",
                deviceType: "",
                preference: nil
            )
            
            if pairingSuccess {
                DispatchQueue.main.async {
                    deviceConnectionState.updateDeviceConnectionState(deviceId: device.broadcastId, isConnected: true)
                    self.updatedBroadcastId = self.updatedBroadcastId
                    self.saveStoredData(device: device, broadcastId: self.updatedBroadcastId, userNumber: userNumber)
                    onToastMessage("\(device.name) \(self.toastLang.connectedSuccessfully)")
                }
                
            } else {
                DispatchQueue.main.async {
                    onToastMessage("\(self.toastLang.failedToConnect) \(device.name).")
                }
            }
            
            DispatchQueue.main.async {
                self.isConnecting = false
            }
        }
    }
    
    func saveStoredData(device: GGBTDevice?, broadcastId: String?, userNumber: Int?) {
        guard let deviceName = device?.name else { return }
        if let id = broadcastId {
            UserDefaults.standard.set(id, forKey: "updatedBroadcastId_\(deviceName)")
        }
        if let userNumber = userNumber {
            UserDefaults.standard.set(userNumber, forKey: "userNumber_\(deviceName)")
        }
    }
    
    func loadStoredData(device: GGBTDevice?) {
        if let deviceName = device?.name {
            updatedBroadcastId = UserDefaults.standard.string(forKey: "updatedBroadcastId_\(deviceName)")
            userNumber = UserDefaults.standard.integer(forKey: "userNumber_\(deviceName)")
        }
    }
    
    private func showAlert(title: String, message: String, primaryButtonTitle: String, secondaryButtonTitle: String = "", primaryButtonAction: (() -> Void)? = nil, secondaryButtonAction: (() -> Void)? = nil) {
        DispatchQueue.main.async {
            self.alertTitle = title
            self.alertMessage = message
            self.alertPrimaryButtonTitle = primaryButtonTitle
            self.alertSecondaryButtonTitle = secondaryButtonTitle
            self.alertPrimaryButtonAction = primaryButtonAction
            self.alertSecondaryButtonAction = secondaryButtonAction
            self.showAlert = true
        }
    }
    
    func checkIfUserAdded(for broadcastId: String) {
        DispatchQueue.main.async {
            self.isUserAdded = self.deviceService.isUserAdded(for: broadcastId) ?? false
        }
    }
    
    func initializeData(
        device: GGBTDevice?,
        deviceConnectionState: DeviceConnectionState,
        onToastMessage: @escaping (String) -> Void,
        router: Router<DashboardRoutes>,
        selectedCategory: AppCategory?
    ) {
        loadStoredData(device: device)
        if let deviceId = device?.broadcastId {
            DispatchQueue.main.async {
                self.isConnected = deviceConnectionState.getDeviceConnectionState(deviceId: deviceId)
                self.checkIfUserAdded(for: deviceId)
            }
        }
        navigateToLogin = {
            router.navigate(to: .login(selectedCategory, .scan))
        }
    }
    
    func shouldShowUserNumber(selectedCategory: AppCategory?, device: GGBTDevice?) -> Bool {
        let protocolType = device?.protocolType ?? ""
        switch selectedCategory {
        case .balanceHealth:
            return protocolType != ProtocolType.WELLAND_BATH_SCALE.rawValue && (protocolType == ProtocolType.A3.rawValue || protocolType == ProtocolType.A6.rawValue)
        case .weightGurus:
            return protocolType != ProtocolType.WELLAND_BATH_SCALE.rawValue && protocolType == ProtocolType.A3.rawValue
        default:
            return false
        }
    }
    
    func navigateToDeviceDetail(
        router: Router<DashboardRoutes>,
        selectedCategory: AppCategory?,
        device: GGBTDevice,
        updatedBroadcastId: String?
    ) {
        let broadcastIdToSend = updatedBroadcastId ?? device.broadcastId
        var updatedDevice = device
        updatedDevice.broadcastId = broadcastIdToSend
        router.navigate(to: .deviceDetail(selectedCategory, updatedDevice, .scan, getSKU(device.name)))
    }
    
    func handleAddUserConfirmationAlert(
        device: GGBTDevice?,
        selectedCategory: AppCategory?,
        router: Router<DashboardRoutes>,
        isLoadingAddUser: Binding<Bool>
    ) {
        guard let deviceDetail = device else { return }
        
        Task {
            isLoadingAddUser.wrappedValue = true
            
            let (user, responseType) = await r4Helper.addR4ScaleUser(
                category: selectedCategory ?? .weightGurus,
                device: deviceDetail,
                onDuplicateUser: { [weak self] userName in
                    DispatchQueue.main.async {
                        self?.userName = userName
                    }
                },
                onUserAdded: { [weak self] in
                    self?.isUserAdded = true
                }, router: router,
                showAlert: { [weak self] title, message, primaryTitle, secondaryTitle, action in
                    self?.showAlert(
                        title: title,
                        message: message,
                        primaryButtonTitle: primaryTitle,
                        secondaryButtonTitle: secondaryTitle,
                        primaryButtonAction: action
                    )
                },
                navigateToLogin: navigateToLogin
            )
            
            DispatchQueue.main.async {
                switch responseType {
                case UserCreationResponseType.duplicateUserError.rawValue:
                    isLoadingAddUser.wrappedValue = false
                    guard let duplicateUser = user else { return }
                    
                    router.navigate(to: .duplicateUserModal(
                        duplicateUser,
                        onEditName: { [weak self] in
                            guard let self = self else { return }
                            self.navigateToEditUsernameView(
                                deviceDetail: deviceDetail,
                                router: router,
                                isLoadingAddUser: isLoadingAddUser
                            )
                        },
                        onRestore: { [weak self] in
                            self?.restoreDuplicateUser(
                                device: deviceDetail,
                                duplicateUserName: duplicateUser.name,
                                isLoading: isLoadingAddUser,
                                router: router
                            )
                        }
                    ))
                    
                case UserCreationResponseType.memoryFull.rawValue:
                    isLoadingAddUser.wrappedValue = false
                    self.handleMemoryFullCase(router: router, device: device)
                    
                default:
                    isLoadingAddUser.wrappedValue = false
                }
            }
            
        }
    }
    
    
    func handleMemoryFullCase(router: Router<DashboardRoutes>, device: GGBTDevice?) {
        ToastService.shared.presentToast(with: toastLang.memoryFullMessage)
        router.navigate(to: .r4ScaleUserView(
            device: device ?? DefaultDevice.ggbtDevice,
            selectedCategory: .weightGurus,
            ParentViewType: .deviceItemView
        ))
    }
    
    private func navigateToEditUsernameView(
        deviceDetail: GGBTDevice,
        router: Router<DashboardRoutes>,
        isLoadingAddUser: Binding<Bool>
    ) {
        router.navigate(to: .editUsernameView(
            isPresented: .constant(true),
            newUserName: self.userNameBinding(),
            onSave: { [weak self] in
                Task {
                    isLoadingAddUser.wrappedValue = true
                    do {
                        let result = try await self?.r4Helper.saveUserEdit(
                            device: deviceDetail,
                            userName: self?.userName ?? "",
                            router: router
                        )
                        isLoadingAddUser.wrappedValue = false
                        
                        switch result {
                        case UserCreationResponseType.memoryFull.rawValue:
                            self?.handleMemoryFullCase(router: router, device: deviceDetail)
                            
                        case UserCreationResponseType.duplicateUserError.rawValue:
                            ToastService.shared.presentToast(with: ToastMessageStrings.duplicateUserFound)
                                                                
                        case UserCreationResponseType.creationCompleted.rawValue:
                            router.navigateToRoot()
                            
                        default:
                            break
                        }
                    } catch {
                        isLoadingAddUser.wrappedValue = false
                        let nsError = NSError(
                            domain: String(describing: DeviceItemViewModel.self),
                            code: 2,
                            userInfo: [NSLocalizedDescriptionKey: "\(self?.errorLang.errorRestoringUser ?? "") \(error.localizedDescription)"]
                        )
                        self?.logger.error(error: nsError, function: #function, line: #line)
                    }
                }
            },
            onCancel: {
                router.navigateBack()
            },
            parentView: .DuplicateUserModel
        ))
    }
    
    private func restoreDuplicateUser(
        device: GGBTDevice,
        duplicateUserName: String,
        isLoading: Binding<Bool>,
        router: Router<DashboardRoutes>
    ) {
        Task {
            isLoading.wrappedValue = true
            do {
                try await r4Helper.restoreUser(for: device, duplicateUser: duplicateUserName)
                isLoading.wrappedValue = false
                router.navigateBack()
            } catch {
                isLoading.wrappedValue = false
                let nsError = NSError(
                    domain: String(describing: DeviceItemViewModel.self),
                    code: 2,
                    userInfo: [NSLocalizedDescriptionKey: " \(error.localizedDescription)"]
                )
                logger.error(error: nsError, function: #function, line: #line)
            }
        }
    }
    
    func userNameBinding() -> Binding<String> {
        Binding(
            get: { self.userName },
            set: { self.userName = $0 }
        )
    }
    
}
