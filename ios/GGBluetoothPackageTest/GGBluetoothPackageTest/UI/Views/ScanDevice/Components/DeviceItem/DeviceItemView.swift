//
//  DeviceItemView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON><PERSON> Chittibabu on 17/06/24.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct DeviceItemView: View {
    @State var device: GGBTDevice?
    @State private var isConnected: Bool = false
    @State private var isConnecting: Bool = false
    @State private var showConfirmationModal: Bool = false
    @State private var showAddUserConfirmationAlert: Bool = false
    @State private var isLoadingAddUser: Bool = false
    @State private var selectedDevice: GGBTDevice?
    @State private var userNumber: Int?
    @State private var updatedBroadcastId: String?
    
    @EnvironmentObject var deviceConnectionState: DeviceConnectionState
    @EnvironmentObject var router: Router<DashboardRoutes>
    
    @StateObject private var viewModel = DeviceItemViewModel()
    @Binding var selectedCategory: AppCategory?
    
    var onToastMessage: (String) -> Void
    var lang =  ScanViewStrings.self
    var commonLang = CommonStrings.self
    var alertLang = AlertStrings.self
    
    var body: some View {
        VStack(alignment: .leading) {
            if let deviceDetail = device {
                let isConnected = deviceConnectionState.getDeviceConnectionState(deviceId: deviceDetail.broadcastId)
                if isConnected {
                    HStack(spacing: 0) {
                        ZStack(alignment: .topLeading) {
                            if let image = getImage(for: getSKU(device?.name ?? "")) {
                                VStack {
                                    image
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: 80, height: 80)
                                        .cornerRadius(10)
                                }
                            } else {
                                Image("\(getSKU(device?.name ?? ""))")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 80, height: 80)
                                    .cornerRadius(10)
                            }
                            
                            if let selectedCategory = selectedCategory {
                                selectedCategory.image()
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 25, height: 25)
                                    .clipShape(Circle())
                            }
                        }
                        .padding(.trailing, 10)
                        
                        VStack(alignment: .leading, spacing: 5) {
                            Text("\(deviceDetail.name)")
                                .font(.headline)
                            
                            let displayBroadcastId = updatedBroadcastId ?? deviceDetail.broadcastId
                            Text(displayBroadcastId.count > 15 ? "\(displayBroadcastId.prefix(12))..." : displayBroadcastId)
                                .foregroundColor(.secondary)
                                .font(.subheadline)
                            
                            HStack(spacing: 0) {
                                Image(systemName: "circle.fill")
                                    .foregroundColor(.green)
                                Text(lang.online)
                                    .foregroundColor(.secondary)
                                    .padding(.horizontal, 5)
                                Spacer(minLength: 2)
                            }
                            .font(.caption)
                        }
                        .padding(.leading, 10)
                        
                        Spacer()
                        
                        if viewModel.shouldShowUserNumber(selectedCategory: selectedCategory, device: device) {
                            HStack(spacing: 5) {
                                Image(systemName: "person.fill")
                                    .foregroundColor(.blue)
                                    .font(.headline)
                                
                                Text(selectedCategory == .balanceHealth ?
                                     (userNumber == 1 ? "A" : (userNumber == 2 ? "B" : "\(userNumber ?? 0)")) :
                                        "\(userNumber ?? 0)"
                                )
                                .foregroundColor(.secondary)
                                .font(.headline)
                            }
                            .padding(.trailing, 15)
                        }
                        
                        Spacer()
                        
                        if deviceDetail.protocolType == ProtocolType.R4.rawValue && !viewModel.isUserAdded {
                            Image(systemName: "person.badge.plus")
                                .font(.subheadline)
                                .foregroundColor(.white)
                                .padding(8)
                                .background(Color.blue)
                                .cornerRadius(20)
                                .onTapGesture {
                                    showAddUserConfirmationAlert = true
                                }
                        }
                        
                        Spacer(minLength: 15)
                        
                        Button {
                            viewModel.navigateToDeviceDetail(router: router, selectedCategory: selectedCategory, device: deviceDetail, updatedBroadcastId: updatedBroadcastId)
                        } label: {
                            Image(systemName: "chevron.right")
                                .foregroundColor(.black)
                                .fontWeight(.bold)
                        }
                    }
                } else {
                    HStack {
                        ZStack(alignment: .topLeading) {
                            if let image = getImage(for: getSKU(device?.name ?? "")) {
                                VStack {
                                    image
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: 80, height: 80)
                                        .cornerRadius(10)
                                }
                            } else {
                                Image("\(getSKU(device?.name ?? ""))")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 80, height: 80)
                                    .cornerRadius(10)
                            }
                            
                            if let selectedCategory = selectedCategory {
                                selectedCategory.image()
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 25, height: 25)
                                    .clipShape(Circle())
                            }
                        }
                        .padding(.trailing, 10)
                        
                        VStack(alignment: .leading, spacing: 5) {
                            Text("\(deviceDetail.name)")
                                .font(.headline)
                            Text(deviceDetail.broadcastId.count > 15 ? "\(deviceDetail.broadcastId.prefix(12))..." : deviceDetail.broadcastId)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        Button(action: {
                            if viewModel.shouldShowUserNumber(selectedCategory: selectedCategory, device: device) {
                                selectedDevice = deviceDetail
                                showConfirmationModal = true
                            } else {
                                viewModel.connectDevice(deviceDetail, userNumber: userNumber, selectedCategory: selectedCategory, deviceConnectionState: deviceConnectionState, onToastMessage: onToastMessage)
                                updatedBroadcastId = viewModel.updatedBroadcastId
                            }
                        }, label: {
                            Text(viewModel.isConnecting ? lang.connecting : lang.connect)
                                .font(.subheadline)
                                .padding(5)
                                .foregroundColor(viewModel.isConnecting ? Color.yellow : Color.blue)
                                .fontWeight(.bold)
                        })
                        .disabled(isConnecting)
                    }
                }
            }
        }
        .ignoresSafeArea(edges: .top)
        .ignoresSafeArea(edges: .bottom)
        .onAppear {
            viewModel.initializeData(device: device, deviceConnectionState: deviceConnectionState, onToastMessage: onToastMessage, router: router, selectedCategory: selectedCategory)
        }
        .fullScreenCover(isPresented: $isLoadingAddUser) {
            StatusIndicationLoaderView(customTitle: commonLang.addingUser, loaderImage: "person.fill.badge.plus")
        }
        .sheet(isPresented: $showConfirmationModal, content: {
            CustomInputModalView(
                device: selectedDevice,
                userNumber: $userNumber,
                onConfirm: {
                    if let selectedDevice = selectedDevice, let number = userNumber {
                        isConnecting = true
                        viewModel.connectDevice(selectedDevice, userNumber: number, selectedCategory: selectedCategory, deviceConnectionState: deviceConnectionState, onToastMessage: onToastMessage)
                        updatedBroadcastId = viewModel.updatedBroadcastId
                        isConnecting = false
                        showConfirmationModal = false
                    }
                },
                onCancel: {
                    showConfirmationModal = false
                },
                sku: getSKU(selectedDevice?.name ?? ""),
                category: selectedCategory ?? .weightGurus
            )
        })
        .alert(isPresented: $viewModel.showAlert) {
            Alert(
                title: Text(viewModel.alertTitle),
                message: Text(viewModel.alertMessage),
                primaryButton: .default(Text(viewModel.alertPrimaryButtonTitle), action: {
                    viewModel.alertPrimaryButtonAction?()
                }),
                secondaryButton: .cancel(Text(viewModel.alertSecondaryButtonTitle))
            )
        }
        .alert(isPresented: $showAddUserConfirmationAlert) {
            Alert(
                title: Text(alertLang.addUserTitle),
                message: Text(alertLang.addUserMessage),
                primaryButton: .default(Text(commonLang.ok.uppercased()), action: {
                    viewModel.handleAddUserConfirmationAlert(
                        device: device,
                        selectedCategory: selectedCategory,
                        router: router,
                        isLoadingAddUser: $isLoadingAddUser
                    )
                    
                }),
                secondaryButton: .cancel()
            )
        }
    }
}
#Preview {
    DeviceItemView(
        device: GGBTDevice(
            name: "Device",
            broadcastId: "1234567890",
            password: "",
            token: "",
            userNumber: 1,
            preference: nil,
            syncAllData: false,
            batteryLevel: 45,
            protocolType: "",
            macAddress: ""
        ),
        selectedCategory: .constant(.weightGurus),
        onToastMessage: { message in
            print(message)
        }
    )
}
