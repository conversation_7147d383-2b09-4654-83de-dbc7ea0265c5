//
//  ScanViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON>ath Chittibabu on 17/06/24.
//

import Combine
import Foundation
import GGBluetoothSwiftPackage
import SwiftUICore

final class ScanViewModel: ObservableObject {
    @Published var selectedCategory: AppCategory? {
        didSet {
            handleAppTypeChange()
        }
    }
    @Published var filteredDevices: [GGBTDevice] = []
    @Published var devices: [GGBTDevice] = []
    @Published var allDevices: [GGBTDevice] = []
    @Published var bluetoothPermissionEnabled: Bool = true
    @Published var isLoading: Bool = false
    @Injector var scannedDeviceModel: ScannedDeviceModel
    @Injector var deviceService: DeviceService
    @Injector var toastService: ToastService
    @Injector var bluetoothService: BluetoothService
    @Injector var permissionsService: PermissionsService
    var deviceConnectionState: DeviceConnectionState
    private var cancellables: Set<AnyCancellable> = []
    
    init(deviceConnectionState: DeviceConnectionState = DeviceConnectionState()) {
        self.deviceConnectionState = deviceConnectionState
        
        if let fetchedCategory = deviceService.fetchSelectedCategory() {
            self.selectedCategory = AppCategory(rawValue: fetchedCategory)
        } else {
            self.selectedCategory = .all
        }
        
        bluetoothService.checkPermission(appType: GGAppType(rawValue: selectedCategory?.rawValue ?? GGAppType.BALANCE_HEALTH.rawValue) ?? .BALANCE_HEALTH) { result in
            switch result {
            case .success(let data):
                if let permissionStatus = data.data as? GGPermissionResponseData {
                    DispatchQueue.main.async {
                        self.bluetoothPermissionEnabled = permissionStatus.permissions[.BLUETOOTH] == .ENABLED
                    }
                }
            case .failure(let error):
                DispatchQueue.main.async {
                    self.bluetoothPermissionEnabled = false
                }
            }
        }
        
        permissionsService.fetchInitialPermissions(for: selectedCategory ?? .weightGurus, completion: {_ in })
        
        scannedDeviceModel.$devices
            .sink(receiveValue: { data in
                self.devices = data
                self.updateAllDevices()
            })
            .store(in: &self.cancellables)
        
        scannedDeviceModel.$connectedDevices
            .sink { [weak self] value in
                DispatchQueue.main.async {
                    value.forEach { device in
                        self?.deviceConnectionState.updateDeviceConnectionState(deviceId: device.broadcastId, isConnected: true)
                    }
                    self?.updateAllDevices()
                }
            }
            .store(in: &cancellables)
    }
    
    func updateAllDevices() {
        var deviceDict = [String: GGBTDevice]()
        devices.forEach { deviceDict[$0.broadcastId] = $0 }
        scannedDeviceModel.connectedDevices.forEach {
            deviceDict[$0.broadcastId] = $0
        }
        allDevices = Array(deviceDict.values)
        applyCategoryFilter()
    }
    
    func refreshScanning(for category: AppCategory, isScanning: Bool) {
        self.isLoading = true
        self.devices.removeAll()
        self.filteredDevices.removeAll()
        scannedDeviceModel.connectedDevices.removeAll()
        scannedDeviceModel.clearDiscoveredDevices(for: category)
        
        if isScanning {
            scannedDeviceModel.startScanning(for: category)
        } else {
            filterDevicesByCategory(category)
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            self.isLoading = false
        }
    }
    
    func stopScanning() {
        scannedDeviceModel.stopScanning()
    }
    
    func getDevices() -> [GGBTDevice] {
        return scannedDeviceModel.devices
    }
    
    func filterDevicesByCategory(_ category: AppCategory) {
        selectedCategory = category
        if let category = selectedCategory {
            deviceService.saveSelectedCategory(category)
        }
        applyCategoryFilter()
    }
    
    private func applyCategoryFilter() {
        if let category = selectedCategory {
            filteredDevices = allDevices.filter { device in
                let appCategory = getApp(forDevice: device.broadcastId, deviceName: device.name)
                return appCategory == category
            }
        } else {
            filteredDevices = allDevices
        }
    }
    
    func loadSelectedCategory() {
        if let category = deviceService.loadSelectedCategory() {
            selectedCategory = category
            filterDevicesByCategory(category)
        }
    }
    
    private func handleAppTypeChange() {
        guard let newCategory = selectedCategory, newCategory != deviceService.loadSelectedCategory() else {
            return
        }
        guard !devices.isEmpty || !scannedDeviceModel.connectedDevices.isEmpty else {
            return
        }
        
        for device in scannedDeviceModel.connectedDevices {
            Task {
                do {
                    try await bluetoothService.disconnectDevice(device)
                } catch {
                    print("Failed to disconnect device: \(error)")
                }
            }
            deviceConnectionState.updateDeviceConnectionState(deviceId: device.broadcastId, isConnected: false)
        }
        
        devices.removeAll()
        filteredDevices.removeAll()
        scannedDeviceModel.clearDiscoveredDevices(for: newCategory)
    }
}
