//
//  AppsViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 03/12/24.
//

import SwiftUI
import Foundation
import GGBluetoothSwiftPackage

final class AppsViewModel: ObservableObject {
    @Published var pairedDevices: [GGBTDevice] = []
    private let deviceService = DeviceService()
    @Published var jsonScales: [Scale] = []
    @Published var jsonMonitors: [MonitorType] = []
    @Published var loginStatus: [AppCategory: Bool] = [:]
    
    func fetchPairedDevices() {
        let storedBroadcastIds = getAllStoredBroadcastIds()
        
        var uniqueDevices: [GGBTDevice] = []
        
        for broadcastId in storedBroadcastIds {
            if let device = deviceService.fetchDevice(by: broadcastId) {
                let newDevice = GGBTDevice(
                    name: device.name ?? "",
                    broadcastId: device.broadcastId ?? "",
                    password: "",
                    token: "",
                    userNumber: Int(device.userNumber),
                    preference: nil,
                    syncAllData: true,
                    batteryLevel: Int(device.batteryLevel),
                    protocolType: "",
                    macAddress: ""
                )
                
                if !uniqueDevices.contains(where: { $0.broadcastId == newDevice.broadcastId }) {
                    uniqueDevices.append(newDevice)
                }
            }
        }
        pairedDevices = uniqueDevices
    }
    
    private func getAllStoredBroadcastIds() -> [String] {
        guard let storedDevices = deviceService.fetchAllDevices() else { return [] }
        return storedDevices.compactMap { $0.broadcastId }
    }
    
    func groupDevicesByAppCategory() -> [AppCategory: [GGBTDevice]] {
        var groupedDevices: [AppCategory: [GGBTDevice]] = [:]
        
        for device in pairedDevices {
            let appCategory = getApp(forDevice: device.broadcastId, deviceName: device.name)
            groupedDevices[appCategory, default: []].append(device)
        }
        
        return groupedDevices
    }
    
    func deleteDevice(at offsets: IndexSet) {
        for index in offsets {
            let device = pairedDevices[index]
            Task {
                await deviceService.deleteDevice(by: device)
            }
        }
        pairedDevices.remove(atOffsets: offsets)
    }
    
    func fetchUserLogin(for category: AppCategory) -> (email: String?, password: String?, isLoggedIn: Bool)? {
        return deviceService.fetchUserLogin(for: category)
    }
    
    func updateLoginStatus() {
        for category in AppCategory.allCases {
            if let loginInfo = fetchUserLogin(for: category) {
                loginStatus[category] = loginInfo.isLoggedIn
            } else {
                loginStatus[category] = false
            }
        }
    }
}
