//
//  AppsView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 03/12/24.
//

import SwiftUI

struct AppsView: View {
    @StateObject var router: Router<DashboardRoutes> = .init()
    @State private var selectedCategory: AppCategory?
    @StateObject private var pairedDevicesModel = AppsViewModel()
    
    private var filteredCategories: [AppCategory] {
        AppCategory.allCases.filter { $0 != .all && $0 != .rpm }
    }
    
    var body: some View {
        RoutingView(stack: $router.stack) {
            VStack(alignment: .leading) {
                ScrollView {
                    VStack(spacing: 20) {
                        ForEach(filteredCategories, id: \.self) { category in
                            Button(action: {
                                selectedCategory = category
                                router.navigate(to: .login(category, .apps))
                            }) {
                                categoryRow(for: category)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        .onAppear {
                            pairedDevicesModel.fetchPairedDevices()
                            pairedDevicesModel.updateLoginStatus()
                        }
                    }
                    .padding()
                }
            }
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text(CommonStrings.apps.uppercased())
                        .font(.headline)
                        .foregroundColor(.primary)
                }
            }
            .navigationBarTitleDisplayMode(.inline)
        }
        .environmentObject(router)
    }
    
    private func categoryRow(for category: AppCategory) -> some View {
        HStack {
            category.image()
                .resizable()
                .scaledToFit()
                .frame(width: 30, height: 30)
                .padding(.leading, 20)
            
            Text(customCategoryText(for: category).uppercased())
                .font(.headline)
                .foregroundColor(.primary)
                .padding()
            
            Spacer()
            
            Text(pairedDevicesModel.loginStatus[category] == true ? "Logged in" : "Logged out")
                .padding(.trailing, 20)
                .font(.caption)
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity)
        .background(Color.blue.opacity(0.1))
        .cornerRadius(10)
        .shadow(color: .gray.opacity(0.3), radius: 5, x: 0, y: 2)
    }
}

#Preview {
    AppsView()
        .environmentObject(DeviceConnectionState())
}
