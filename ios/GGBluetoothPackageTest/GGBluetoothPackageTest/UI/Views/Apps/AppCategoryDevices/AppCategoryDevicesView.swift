//
//  AppCategoryDevicesView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 04/12/24.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct AppCategoryDevicesView: View {
    let category: AppCategory
    @StateObject var pairedDevicesModel = AppsViewModel()
    @EnvironmentObject var deviceConnectionState: DeviceConnectionState
    @State private var selectedCategory: AppCategory?
    @State private var showDeleteConfirmation = false
    @State private var deviceToDelete: GGBTDevice?
    
    var body: some View {
        VStack {
            if pairedDevicesModel.pairedDevices.isEmpty {
                Text("No devices found for \(category.rawValue)")
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
            } else {
                let devices = pairedDevicesModel.pairedDevices.filter {
                    getApp(forDevice: $0.broadcastId, deviceName: $0.name) == category
                }

                List {
                    ForEach(devices, id: \.broadcastId) { device in
                        PairedDeviceItemView(
                            device: device, sku: getSKU(device.name),
                            isConnected: .constant(
                                deviceConnectionState.getDeviceConnectionState(deviceId: device.broadcastId)
                            ),
                            category: selectedCategory ?? .weightGurus, parentView: .Apps
                        )
                        .padding(.trailing, 5)
                        .cornerRadius(8)
                        .shadow(color: .gray.opacity(0.3), radius: 5, x: 0, y: 2)
                        .swipeActions {
                            Button(role: .destructive) {
                                confirmDelete(device: device)
                            } label: {
                                Label("", systemImage: "trash")
                            }
                        }
                    }
                }
            }
        }
        .navigationTitle(category.rawValue.uppercased())
        .onAppear {
            pairedDevicesModel.fetchPairedDevices()
            selectedCategory = category
        }
        .alert(isPresented: $showDeleteConfirmation) {
            Alert(
                title: Text(CommonStrings.deleteDevice),
                message: Text(CommonStrings.deleteDeviceConfirmation(deviceName: deviceToDelete?.name)),
                primaryButton: .destructive(Text(CommonStrings.delete)) {
                    if let device = deviceToDelete {
                        if let index = pairedDevicesModel.pairedDevices.firstIndex(of: device) {
                            pairedDevicesModel.deleteDevice(at: IndexSet([index]))
                        }
                    }
                    deviceToDelete = nil
                },
                secondaryButton: .cancel {
                    deviceToDelete = nil
                }
            )
        }
    }
    
    private func confirmDelete(device: GGBTDevice) {
        deviceToDelete = device
        showDeleteConfirmation = true
    }
}

#Preview {
    AppCategoryDevicesView(category: .sage)
}
