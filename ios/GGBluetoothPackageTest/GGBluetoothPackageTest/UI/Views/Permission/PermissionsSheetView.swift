//
//  PermissionsSheetView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/12/24.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct PermissionsSheetView: View {
    @State private var permissions: [GGPermissionType] = [
        .BLUETOOTH_SWITCH,
        .BLUETOOTH,
        .LOCATION_SWITCH,
        .LOCATION,
        .NOTIFICATION,
        .CAMERA
    ]
    
    @Environment(\.dismiss) var dismiss
    
    var body: some View {
        NavigationStack {
            List(self.permissions, id: \.self) { permission in
                PermissionItem(type: permission)
            }
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text(SettingsViewStrings.permissions.uppercased())
                        .font(.title3)
                        .fontWeight(.semibold)
                }
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        dismiss()
                    }) {
                        HStack {
                            Image(systemName: "chevron.left")
                            Text(CommonStrings.back)
                        }
                    }
                }
            }
        }
    }
}

#Preview {
    PermissionsSheetView()
}
