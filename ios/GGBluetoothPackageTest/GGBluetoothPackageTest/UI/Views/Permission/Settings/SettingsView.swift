//
//  PermissionsView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON><PERSON> on 17/06/24.
//

import SwiftUI

struct SettingsView: View {
    @State private var showPermissionsSheet = false
    @StateObject var router: Router<DashboardRoutes> = .init()
    @StateObject var viewModel = SettingsViewModel()
    var lang = SettingsViewStrings.self
    var commonLang = CommonStrings.self
    
    var body: some View {
        RoutingView(stack: $router.stack) {
            VStack {
                VStack {
                    
                    ProfileHeaderView(
                        name: viewModel.name,
                        email:viewModel.email,
                        profileImage: AppAssets.profile
                    )
                    
                    Button(action: {
                        router.navigate(to: .userProfileUpdation)
                    }, label: {
                        Text(commonLang.edit.uppercased())
                    })
                    .padding(5)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .foregroundColor(.white)
                    .background(Color.blue)
                    .fontWeight(.bold)
                    .cornerRadius(25)
                }
                
                SettingsSectionView(
                    sectionTitle: lang.appSettings,
                    buttonTitle: lang.permissions
                ) {
                    showPermissionsSheet.toggle()
                }
                                
                Spacer()
            }
            .padding(.horizontal, 20)
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text(lang.settings.uppercased())
                        .font(.headline)
                        .foregroundColor(.primary)
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                viewModel.fetchUserProfile()
            }
            .fullScreenCover(isPresented: $showPermissionsSheet) {
                PermissionsSheetView()
            }
        }
        .environmentObject(router)
    }
    
    @ViewBuilder
    func SettingsSectionView(
        sectionTitle: String,
        buttonTitle: String,
        onButtonTap: @escaping () -> Void
    ) -> some View {
        VStack {
            HStack {
                Text(sectionTitle)
                    .foregroundColor(.gray)
                    .font(.headline)
                    .padding(.vertical, 10)
                Spacer()
            }
            
            Button(action: {
                onButtonTap()
            }, label: {
                HStack {
                    Text(buttonTitle)
                    Spacer()
                    Image(systemName: "chevron.right")
                }
                .padding(5)
                .padding(.horizontal, 20)
                .padding(.vertical, 5)
                .foregroundColor(.white)
                .background(Color.blue)
                .fontWeight(.bold)
                .cornerRadius(10)
            })
        }
    }

}

#Preview {
    SettingsView()
}
