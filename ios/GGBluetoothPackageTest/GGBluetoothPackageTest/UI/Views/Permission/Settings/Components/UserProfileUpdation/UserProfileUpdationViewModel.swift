//
//  UserProfileUpdationViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 06/01/25.
//

import Foundation
import SwiftUI

class UserProfileUpdationViewModel: ObservableObject {
    private let userProfileService: UserProfileUpdationService
    
    @Published var name: String = "John Doe"
    @Published var email: String = "<EMAIL>"
    @Published var birthday: Date = Date()
    @Published var height: Int = 170
    @Published var weight: Double = 70
    @Published var goalWeight: Double = 70
    @Published var gender: String = "Male"
    @Published var bodyType: String = "Normal"
    @Published var goalType: String = "Maintain"
    @Published var unitType: String = "kg & cm"
    
    @Published var nameError: String? = nil
    @Published var emailError: String? = nil
    @Published var heightError: String? = nil
    @Published var weightError: String? = nil
    @Published var goalWeightError: String? = nil
    
    @Published private(set) var isFormModified: Bool = false
    
    init(userProfileService: UserProfileUpdationService) {
        self.userProfileService = userProfileService
        loadUserProfile()
    }
    
    func validateName() {
        nameError = ValidationHelper.validateName(name)
    }
    
    func validateEmail() {
        emailError = ValidationHelper.validateEmail(email)
    }
    
    func validateHeight() {
        heightError = ValidationHelper.validateHeight(Double(height))
    }
    
    func validateWeight() {
        weightError = ValidationHelper.validateWeight(weight)
    }
    
    func validateGoalWeight() {
        goalWeightError = ValidationHelper.validateGoalWeight(goalWeight)
    }
    
    private func loadUserProfile() {
        let profile = userProfileService.fetchUserProfile()
        
        if let profile = profile {
            self.name = profile.name ?? "John Doe"
            self.email = profile.email ?? "<EMAIL>"
            self.birthday = profile.birthday ?? Date()
            self.height = Int(profile.height)
            self.weight = profile.weight
            self.gender = profile.gender ?? "Male"
            self.bodyType = profile.bodyType ?? "Normal"
            self.goalType = profile.goalType ?? "Maintain"
            self.goalWeight = profile.goalWeight
            self.unitType = profile.unitType ?? "kg & cm"
        }
    }
    
    func saveUserProfile() {
        
        var heightToSave = height
        var weightToSave = weight
        var goalWeightToSave = goalWeight
        
        if unitType != "kg & cm" {
            heightToSave = height * Int(2.54)
            
            weightToSave = weight * 0.453592
            
            goalWeightToSave = goalWeight * 0.453592
        }
        
        userProfileService.saveUserProfile(
            name: name,
            email: email,
            birthday: birthday,
            height: heightToSave,
            weight: weightToSave,
            gender: gender,
            bodyType: bodyType,
            goalType: goalType,
            goalWeight: goalWeightToSave,
            unitType: unitType
        )
    }
    
    func fieldChanged() {
        isFormModified = true
    }
    
    func hasValidationErrors() -> Bool {
        return nameError != nil ||
        emailError != nil ||
        heightError != nil ||
        weightError != nil ||
        goalWeightError != nil
    }
}
