//
//  UserProfileUpdationView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 02/01/25.
//

import SwiftUI

struct UserProfileUpdationView: View {
    @StateObject private var viewModel = UserProfileUpdationViewModel(userProfileService: UserProfileUpdationService())
    @EnvironmentObject var router: Router<DashboardRoutes>
    
    var body: some View {
        VStack {
            Form {
                CustomFieldView(
                    label: "Name",
                    text: $viewModel.name,
                    placeholder: "Enter your name",
                    errorMessage: viewModel.nameError
                )
                .listRowSeparator(.hidden)
                .onChange(of: viewModel.name) { _ in
                    viewModel.validateName()
                    viewModel.fieldChanged()
                }

                CustomFieldView(
                    label: "Email",
                    text: $viewModel.email,
                    placeholder: "Enter your email",
                    errorMessage: viewModel.emailError
                )
                .listRowSeparator(.hidden)
                .onChange(of: viewModel.email) { _ in
                    viewModel.validateEmail()
                    viewModel.fieldChanged()
                }

                CustomFieldView(
                    label: viewModel.unitType == "kg & cm" ? "Height (cm)" : "Height (ft)",
                    text: Binding(
                        get: {
                            if viewModel.unitType == "kg & cm" {
                                return String(viewModel.height)
                            } else {
                                let heightInFt = Double(viewModel.height) * 0.0328084
                                return String(format: "%.2f", heightInFt)
                            }
                        },
                        set: { newValue in
                            if let newHeight = Double(newValue) {
                                if viewModel.unitType == "kg & cm" {
                                    viewModel.height = Int(newHeight)
                                } else {
                                    viewModel.height = Int(newHeight * 30.48)
                                }
                            } else {
                                viewModel.height = 0
                            }
                        }
                    ),
                    isNumeric: true,
                    placeholder: "Height",
                    errorMessage: viewModel.heightError
                )
                .listRowSeparator(.hidden)
                .onChange(of: viewModel.height) { _ in
                    viewModel.validateHeight()
                    viewModel.fieldChanged()
                }

                CustomFieldView(
                    label: viewModel.unitType == "kg & cm" ? "Weight (kg)" : "Weight (lbs)",
                    text: Binding(
                        get: {
                            if viewModel.unitType == "kg & cm" {
                                return String(format: "%.1f", viewModel.weight)
                            } else {
                                let weightInLbs = viewModel.weight * 2.20462
                                return String(format: "%.1f", weightInLbs)
                            }
                        },
                        set: { newValue in
                            if let newWeight = Double(newValue) {
                                if viewModel.unitType == "kg & cm" {
                                    viewModel.weight = newWeight
                                } else {
                                    viewModel.weight = newWeight / 2.20462
                                }
                            } else {
                                viewModel.weight = 0.0
                            }
                        }
                    ),
                    isNumeric: true,
                    placeholder: "Weight",
                    errorMessage: viewModel.weightError
                )
                .listRowSeparator(.hidden)
                .onChange(of: viewModel.weight) { _ in
                    viewModel.validateWeight()
                    viewModel.fieldChanged()
                }

                CustomFieldView(
                    label: viewModel.unitType == "kg & cm" ? "Goal Weight (kg)" : "Goal Weight (lbs)",
                    text: Binding(
                        get: {
                            if viewModel.unitType == "kg & cm" {
                                return String(format: "%.1f", viewModel.goalWeight)
                            } else {
                                let weightInLbs = viewModel.goalWeight * 2.20462
                                return String(format: "%.1f", weightInLbs)
                            }
                        },
                        set: { newValue in
                            if let newWeight = Double(newValue) {
                                if viewModel.unitType == "kg & cm" {
                                    viewModel.goalWeight = newWeight
                                } else {
                                    viewModel.goalWeight = newWeight / 2.20462
                                }
                            } else {
                                viewModel.goalWeight = 0.0
                            }
                        }
                    ),
                    isNumeric: true,
                    placeholder: "Goal Weight",
                    errorMessage: viewModel.goalWeightError
                )
                .listRowSeparator(.hidden)
                .onChange(of: viewModel.goalWeight) { _ in
                    viewModel.validateGoalWeight()
                    viewModel.fieldChanged()
                }

                DatePicker("Birthday", selection: $viewModel.birthday, in: ...Date(), displayedComponents: .date)
                    .foregroundColor(.gray)
                    .datePickerStyle(.compact)
                    .padding(.vertical, 3)
                    .onChange(of: viewModel.birthday) { _ in viewModel.fieldChanged() }
                    .listRowSeparator(.hidden)
                    .overlay(
                        CustomDividerView().offset(y: 10), alignment: .bottom
                    )

                Picker("Gender", selection: $viewModel.gender) {
                    Text("Male").tag("Male")
                    Text("Female").tag("Female")
                    Text("Other").tag("Other")
                }
                .foregroundColor(.gray)
                .pickerStyle(.automatic)
                .onChange(of: viewModel.gender) { _ in viewModel.fieldChanged() }
                .listRowSeparator(.hidden)
                .overlay(
                    CustomDividerView(), alignment: .bottom
                )

                Picker("Body Type", selection: $viewModel.bodyType) {
                    Text("Athlete").tag("Athlete")
                    Text("Normal").tag("Normal")
                }
                .foregroundColor(.gray)
                .pickerStyle(.automatic)
                .onChange(of: viewModel.bodyType) { _ in viewModel.fieldChanged() }
                .listRowSeparator(.hidden)
                .overlay(
                    CustomDividerView(), alignment: .bottom
                )

                Picker("Goal Type", selection: $viewModel.goalType) {
                    Text("Lose Weight").tag("lose")
                    Text("Maintain Weight").tag("maintain")
                    Text("Gain Weight").tag("gain")
                }
                .foregroundColor(.gray)
                .pickerStyle(.automatic)
                .onChange(of: viewModel.goalType) { _ in viewModel.fieldChanged() }
                .listRowSeparator(.hidden)
                .overlay(
                    CustomDividerView(), alignment: .bottom
                )

                Picker("Unit Type", selection: $viewModel.unitType) {
                    Text("kg & cm").tag("kg & cm")
                    Text("lbs & ft").tag("lbs & ft")
                }
                .foregroundColor(.gray)
                .pickerStyle(.automatic)
                .onChange(of: viewModel.unitType) { _ in
                    viewModel.fieldChanged()
                }
                .listRowSeparator(.hidden)
                .overlay(
                    CustomDividerView(), alignment: .bottom
                )
                
                Button(action: {
                    viewModel.saveUserProfile()
                    router.navigateBack()
                }) {
                    Text(CommonStrings.save.uppercased())
                        .font(.headline)
                        .fontWeight(.bold)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 7)
                        .background(viewModel.isFormModified && !viewModel.hasValidationErrors() ? Color.blue : Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(40)
                        .disabled(!viewModel.isFormModified)
                }
                .padding(.vertical, 50)
                .buttonStyle(.borderless)
            }
            .scrollContentBackground(.hidden)
            .background(Color.white)
            .scrollIndicators(.hidden)
        }
        .listRowInsets(EdgeInsets())
        .listStyle(.plain)
        .toolbar {
            ToolbarItem(placement: .principal) {
                Text(SettingsViewStrings.profile.uppercased())
                    .font(.headline)
                    .foregroundColor(.primary)
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .background(Color(UIColor.systemGroupedBackground))
        .onTapGesture {
            hideKeyboard()
        }
    }
}

#Preview {
    UserProfileUpdationView()
}
