//
//  CustomFieldView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 06/01/25.
//

import SwiftUI

struct CustomFieldView: View {
    var label: String
    @Binding var text: String
    var isPassword: Bool = false
    var isNumeric: Bool = false
    var placeholder: String = ""
    var errorMessage: String? = nil

    @State private var isPasswordVisible: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: 5) {
            // Label
            Text(label.uppercased())
                .font(.subheadline)
                .foregroundColor(.gray)

            // TextField or SecureField
            HStack {
                ZStack(alignment: .leading) {
                    if text.isEmpty {
                        Text(placeholder)
                            .font(.footnote)
                            .foregroundColor(.gray)
                    }

                    if isPassword && !isPasswordVisible {
                        SecureField("", text: $text)
                            .autocapitalization(.none)
                            .textContentType(.password)
                    } else {
                        TextField("", text: $text)
                            .keyboardType(isNumeric ? .numberPad : .default)
                            .autocapitalization(isPassword ? .none : .words)
                    }
                }

                // Password visibility toggle button
                if isPassword {
                    Button(action: {
                        isPasswordVisible.toggle()
                    }) {
                        Image(systemName: isPasswordVisible ? "eye.slash" : "eye")
                            .foregroundColor(.blue)
                            .padding(.trailing, 20)
                    }
                    .buttonStyle(BorderlessButtonStyle())
                }
            }

            // Custom Divider to replace the default TextField line
            Rectangle()
                .frame(height: 1)
                .foregroundColor(errorMessage != nil ? .red : .gray.opacity(0.5))
                .frame(maxWidth: .infinity)

            // Error message
            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .foregroundColor(.red)
                    .font(.caption)
                    .padding(.top, 2)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
}

struct CustomFieldView_Previews: PreviewProvider {
    @State static var email = "<EMAIL>"
    @State static var password = "password123"
    @State static var errorMessage: String? = "Invalid input"

    static var previews: some View {
        VStack(spacing: 20) {
            CustomFieldView(
                label: "Email",
                text: $email,
                placeholder: "Enter your email",
                errorMessage: errorMessage
            )
            
            CustomFieldView(
                label: "Password",
                text: $password,
                isPassword: true,
                placeholder: "Enter your password",
                errorMessage: errorMessage
            )
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
