//
//  SettingsViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 02/01/25.
//

import Foundation
import Combine

class SettingsViewModel: ObservableObject {
    @Published var name: String = "<PERSON>"
    @Published var email: String = "<EMAIL>"
    
    @Injector var userProfileService: UserProfileUpdationService
    
    init() {
        fetchUserProfile()
    }
    
    func fetchUserProfile() {
        if let profile = userProfileService.fetchUserProfile() {
            self.name = profile.name ?? "<PERSON>"
            self.email = profile.email ?? "<EMAIL>"
        }
    }
}
