//
//  PermissionItemViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON><PERSON> Chittibabu on 17/06/24.
//

import Combine
import Foundation
import GGBluetoothSwiftPackage

final class PermissionsViewModel: ObservableObject {
    @Injector var bluetoothService: BluetoothService
    
    var permissionStatus: [GGPermissionType: GGPermissionState] = [:]
    var permissionStatusSub: AnyCancellable?
    var permissionType: GGPermissionType
    @Published var permissionState: Bool = false
    
    init(permissionType: GGPermissionType) {
        self.permissionType = permissionType
        self.permissionStatusSub = bluetoothService.permissionStatusSubject.sink { value in
            DispatchQueue.main.async {
                self.permissionStatus = value
                self.permissionState = self.getPermissionStatus(self.permissionType)
            }
        }
    }
    
    deinit {
        permissionStatusSub?.cancel()
    }
    
    func getPermissionStatus(_ type: GGPermissionType) -> Bool {
        if let status = self.permissionStatus[type] {
            return status == GGPermissionState.ENABLED
        }
        return false
    }
    
    func requestPermission(_ type: GGPermissionType) async {
        await bluetoothService.requestPermission(permissionType: type)
    }
}
