//
//  HistoryView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 06/02/25.
//

import SwiftUI

struct HistoryView: View {
    @StateObject var router: Router<DashboardRoutes> = .init()
    @StateObject private var viewModel = HistoryViewModel()
    @State private var selection = 0
    @State private var appCategory: AppCategory = .all
    @State private var showGraph = false
    @State private var selectedRPMDeviceSKU: String = ""
    var toastLang = ToastMessageStrings.self
    var body: some View {
        RoutingView(stack: $router.stack) {
            VStack(alignment: .leading) {
                if showGraph {
                    GraphView(
                        parentView: .history,
                        device: DefaultDevice.ggbtDevice,
                        appCategory: appCategory,
                        selectedSKU: $selectedRPMDeviceSKU
                    )
                    .id(appCategory)
                } else {
                    if let userLogins = viewModel.fetchAllUserLogins(), !userLogins.isEmpty {
                        DeviceSelectionView(
                            selection: $selection,
                            localView: LocalHistoryView(
                                appCategory: appCategory,
                                parentView: .historytab,
                                deviceDetailParentView: .pairedDevice,
                                selectedSKU: $selectedRPMDeviceSKU),
                            apiView: UserDashboardHistoryView(
                                measurements: viewModel.filteredMeasurements,
                                appCategory: appCategory,
                                parentView: .history)
                        )
                    } else {
                        LocalHistoryView(
                            appCategory: appCategory,
                            parentView: .historytab,
                            deviceDetailParentView: .Apps,
                            selectedSKU: $selectedRPMDeviceSKU)
                    }
                }
            }
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Menu {
                        ForEach(AppCategory.allCases, id: \.self) { category in
                            Button(action: {
                                appCategory = category
                                viewModel.fetchLoginData(for: category)
                            }) {
                                Text(category.rawValue.uppercased())
                            }
                        }
                    } label: {
                        HStack {
                            Text(customCategoryText(for: appCategory).uppercased())
                                .foregroundColor(.black)
                                .font(.headline)
                                .fontWeight(.bold)
                            Image(systemName: "chevron.down")
                                .font(.subheadline)
                        }
                    }
                }
                
                ToolbarItem(placement: .topBarLeading) {
                    Button(action: {
                        showGraph.toggle()
                    }, label: {
                        Image(systemName: showGraph ? "arrow.backward" : "chart.bar.xaxis")
                            .font(.headline)
                    })
                    .disabled(appCategory == .all)
                    .onTapGesture {
                        ToastService.shared.presentToast(with: toastLang.selectAppCategory)
                    }
                }
                
                if appCategory == .rpm {
                    ToolbarItem(placement: .topBarTrailing) {
                        Menu {
                            ForEach(rpmDevices.sorted(by: { $0.name.localizedCaseInsensitiveCompare($1.name) == .orderedAscending }), id: \.sku) { device in
                                Button(device.name) {
                                    selectedRPMDeviceSKU = device.sku
                                }
                            }
                        } label: {
                            Text(
                                rpmDeviceShortForms[selectedRPMDeviceSKU]
                                ?? (selectedRPMDeviceSKU == "" ? CommonStrings.all.uppercased() : GraphStrings.selectDevice)
                            )
                            .font(.headline)
                            .foregroundColor(.primary)
                        }
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
        }
        .environmentObject(router)
        .onAppear {
            viewModel.fetchLoginData(for: appCategory)
        }
    }
}

#Preview {
    HistoryView()
}
