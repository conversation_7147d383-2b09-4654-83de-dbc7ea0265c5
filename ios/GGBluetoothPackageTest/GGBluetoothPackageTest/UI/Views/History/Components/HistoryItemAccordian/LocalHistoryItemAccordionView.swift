//
//  LocalHistoryItemAccordionView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 13/02/25.
//

import SwiftUI

struct LocalHistoryItemAccordionView: View {
    var entry: Operation
    let sfSymbols = ["doc.text.magnifyingglass", "app.badge", "externaldrive.fill", "waveform.path.ecg"]
    let lang = HistoryViewStrings.self
    var body: some View {
        let details = getDetails(for: entry)
        
        ForEach(details.indices, id: \.self) { index in
            DetailView(detail: details[index], isEven: index.isMultiple(of: 2), symbols: sfSymbols)
        }
        .listStyle(.plain)
    }
    
    private func getDetails(for entry: Operation) -> [(String, String, String)] {
        switch entry.appCategory {
        case .balanceHealth:
            return filterDetails([
                (lang.meanPressure, entry.meanPressure.flatMap { "\($0)" }, "waveform.path.ecg"),
                (lang.pulse, entry.pulse.flatMap { "\($0) BPM" }, AppAssets.heartRate),
                (lang.category, customCategoryText(for: entry.appCategory), "app.badge")
            ])
            
        case .rpm:
            let deviceNames: [String: String] = [
                RPMDeviceSKUs.pulseOximeter: lang.pulseOximeter,
                RPMDeviceSKUs.thermometer: lang.thermometer,
                RPMDeviceSKUs.bloodGlucoseMeter: lang.bloodGlucoseMeter,
                RPMDeviceSKUs.babyScale: lang.babyScale,
                RPMDeviceSKUs.weighingScale: lang.weighingScale
            ]
            
            return filterDetails([
                (lang.device, deviceNames[entry.deviceSKU] ?? lang.unknownDevice, "externaldrive.fill"),
                (lang.pulse, entry.pulse.map { "\($0)" }, "waveform.path.ecg"),
                (lang.category, customCategoryText(for: entry.appCategory), "app.badge")
            ] + (entry.pulseAmplitudeIndex.flatMap { $0 > 0.0 ? [(lang.pulseAmplitudeIndex, String($0), "waveform.path.ecg")] : [] } ?? []))
            
        default:
            return filterDetails([
                (lang.bmi, entry.bmi.flatMap { $0 > 0.0 ? formatMetric($0) : nil }, AppAssets.bmi),
                (lang.bodyFat, entry.bodyFat.flatMap { $0 > 0 ? formatMetric($0) + " %" : nil }, AppAssets.bodyFat),
                (lang.muscleMass, entry.muscleMass.flatMap { $0 > 0 ? formatMetric($0) + " %" : nil }, AppAssets.muscleMass),
                (lang.bodyWater, entry.water.flatMap { $0 > 0 ? formatMetric($0) + " %" : nil }, AppAssets.bodyWater),
                (lang.category, customCategoryText(for: entry.appCategory), "app.badge")
            ])
        }
    }
    
    private func filterDetails(_ details: [(String, String?, String)]) -> [(String, String, String)] {
        return details.compactMap { (label, value,icon ) in
            guard let value = value, !value.isEmpty, value != "0", value != "0.0" else {
                return nil
            }
            return (label, value, icon )
        }
    }

    private func formatMetric<T: Numeric>(_ value: T) -> String {
        if let doubleValue = value as? Double {
            let roundedValue = floor(doubleValue * 10) / 10 // Always rounds down
            return String(format: "%.1f", roundedValue)
        } else if let floatValue = value as? Float {
            let roundedValue = floor(Double(floatValue) * 10) / 10
            return String(format: "%.1f", roundedValue)
        }
        return "\(value)"
    }
}
