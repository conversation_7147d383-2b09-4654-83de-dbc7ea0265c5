//
//  DeviceIconView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 13/02/25.
//

import SwiftUI

struct DeviceIconView: View {
    var entry: Operation
    
    var body: some View {
        if entry.appCategory == .balanceHealth {
            deviceImage(AppAssets.bloodPressureMonitor)
        } else if entry.appCategory == .rpm {
            getRPMIcon(for: entry.deviceSKU)
        } else {
            deviceImage(AppAssets.scaleIcon, width: 25, height: 25)
        }
    }
    
    @ViewBuilder
    private func getRPMIcon(for sku: String) -> some View {
        switch sku {
        case RPMDeviceSKUs.thermometer:
            deviceImage(AppAssets.thermometerIcon)
        case RPMDeviceSKUs.pulseOximeter:
            deviceImage(AppAssets.pulseOximeterIcon)
        case RPMDeviceSKUs.bloodGlucoseMeter:
            deviceImage(AppAssets.bgmIcon)
        default:
            deviceImage(AppAssets.scaleIcon)
        }
    }
    
    private func deviceImage(_ asset: String, width: CGFloat = 30, height: CGFloat = 30) -> some View {
        Image(asset)
            .resizable()
            .frame(width: width, height: height)
    }
}
