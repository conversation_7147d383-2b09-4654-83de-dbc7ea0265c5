//  HistoryItemView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 21/01/25.
//

import SwiftUI

struct HistoryItemView: View {
    @State private var isExpanded = false
    var entry: Operation
    private var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMM dd, yyyy"
        return formatter.string(from: entry.entryTimestamp)
    }
    
    var body: some View {
        VStack {
            HStack {
                VStack(alignment: .center, spacing: 5) {
                    Text(formattedDate)
                        .fontWeight(.bold)
                        .font(.subheadline)
                    Text(entry.entryTimestamp, style: .time)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.trailing, 15)
                
                DeviceIconView(entry: entry)
                
                Spacer()
                
                MeasurementView(entry: entry)
                
                if entry.appCategory != .all {
                    Button(action: {
                        withAnimation {
                            isExpanded.toggle()
                        }
                    }, label: {
                        Image(systemName: isExpanded ? "chevron.down" : "chevron.right")
                            .foregroundColor(.blue)
                            .padding(.leading, 20)
                            .frame(width: 25, height: 25)
                    })
                }
            }
            .padding(.horizontal, 15)
            
            if isExpanded{
                LocalHistoryItemAccordionView(entry: entry)
            }
        }
    }
}
