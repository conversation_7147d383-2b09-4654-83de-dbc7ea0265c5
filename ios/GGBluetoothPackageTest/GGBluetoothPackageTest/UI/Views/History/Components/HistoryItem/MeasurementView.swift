//
//  MeasurementView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 13/02/25.
//

import SwiftUI

struct MeasurementView: View {
    var entry: Operation
    let commonLang = CommonStrings.self
    let lang = MeasurementUnitStrings.self
    var body: some View {
        switch entry.appCategory {
        case .balanceHealth:
            measurementView(value: "\(Int(entry.systolic ?? 120))/\(Int(entry.diastolic ?? 80))", unit: lang.millimetersOfMercury)
            
        case .smartBaby:
            measurementView(value: String(format: "%.1f", (entry.weightInKg ?? 0.0)), unit: MeasurementUnit.kg.rawValue)
            
        case .rpm:
            getRPMMeasurementView(for: entry)
            
        case  .weightGurus:
            if entry.measurementUnit == .kg {
                measurementView(value: String(format: "%.1f", entry.weightInKg ?? 0.0), unit: entry.measurementUnit?.rawValue ?? MeasurementUnit.kg.rawValue)
            }else{
                measurementView(value: String(format: "%.1f", entry.weight ?? 0.0), unit: entry.measurementUnit?.rawValue ?? MeasurementUnit.kg.rawValue)
            }
            
        default:
            measurementView(value: String(format: "%.1f", entry.weight ?? 0.0), unit: entry.measurementUnit?.rawValue ?? MeasurementUnit.kg.rawValue)
        }
    }
    
    @ViewBuilder
    private func getRPMMeasurementView(for entry: Operation) -> some View {
        switch entry.deviceSKU {
        case RPMDeviceSKUs.thermometer:
            measurementView(value: String(format: "%.1f", entry.temperature ?? 0.0), unit: entry.measurementUnit?.rawValue ?? commonLang.unknown)
        case RPMDeviceSKUs.pulseOximeter:
            measurementView(value: "\(entry.spo.map { "\($0)" } ?? "N/A")", unit: lang.percent)
        case RPMDeviceSKUs.bloodGlucoseMeter:
            measurementView(value: "\(entry.bgm.map { "\($0)" } ?? "N/A")", unit: lang.milligramsPerDeciliter)
        default:
            measurementView(value: String(format: "%.1f", entry.weight ?? 0.0), unit: entry.measurementUnit?.rawValue ?? MeasurementUnit.kg.rawValue)
        }
    }
    
    private func measurementView(value: String, unit: String) -> some View {
        HStack(alignment: .bottom) {
            Text(value)
                .font(.title3)
            if !unit.isEmpty {
                Text(unit)
                    .foregroundColor(.gray)
                    .font(.caption)
            }
        }
    }
}
