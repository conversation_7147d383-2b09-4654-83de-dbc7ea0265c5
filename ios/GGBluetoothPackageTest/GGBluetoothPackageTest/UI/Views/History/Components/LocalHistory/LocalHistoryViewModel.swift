//
//  HistoryViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 04/02/25.
//

import Foundation
import SwiftUI

final class LocalHistoryViewModel: ObservableObject {
    @Published var entries: [Operation] = []
    
    public let service = MeasurementEntryService()
    
    func fetchMeasurementEntries() {
        entries = service.getAllMeasurementEntries()
            .filter { entry in
                if entry.appCategory == .balanceHealth {
                    return (entry.systolic ?? 0) > 0 || (entry.diastolic ?? 0) > 0
                } else if entry.appCategory == .sage || entry.appCategory == .weightGurus || entry.appCategory == .smartBaby {
                    return (entry.weight ?? 0) > 0
                } else if entry.appCategory == .rpm {
                    return (entry.temperature ?? 0) > 0 ||
                           (entry.spo ?? 0) > 0 ||
                           (entry.pulse ?? 0) > 0 ||
                           (entry.bgm ?? 0) > 0 ||
                           (entry.pulseAmplitudeIndex ?? 0) > 0

                } else {
                    return (entry.weight ?? 0) > 0
                }
            }
            .unique { $0.entryTimestamp }
    }
    
    func deleteEntry(at offsets: IndexSet) {
        guard let index = offsets.first else {
            return
        }
        
        let entry = entries[index]
        let entryTimestamp = entry.entryTimestamp
        
        service.deleteMeasurementEntry(entryTimestamp: entryTimestamp)
        entries.remove(atOffsets: offsets)
    }
    
    func fetchMeasurementEntriesWithBroadcastId(broadcastId: String) {
        let entriesWithBroadcastId = service.fetchMeasurementEntriesWithBroadcastId(forBroadcastId: broadcastId)
        entries = entriesWithBroadcastId.map { entry in
            Operation(
                appCategory: AppCategory(rawValue: entry.appCategory ?? "all") ?? .all,
                bgm: Int(entry.bgm),
                bmi: Float(entry.bmi),
                bodyFat: Int(entry.bodyFat),
                boneMass: Int(entry.boneMass),
                bmr: Int(entry.bmr),
                deviceSKU: entry.deviceSKU ?? "unknown",
                diastolic: Int(entry.diastolic),
                entryTimestamp: entry.entryTimestamp ?? Date(),
                id: UUID(),
                impedance: Int(entry.impedance),
                meanPressure: Int(entry.meanPressure),
                metabolicAge: Int(entry.metabolicAge),
                muscleMass: Int(entry.muscleMass),
                operationType: .create,
                proteinPercent: Int(entry.proteinPercent),
                pulse: Int(entry.pulse),
                protocolType: entry.protocolType,
                serverTimestamp: nil,
                skeletalMusclePercent: Int(entry.skeletalMusclePercent),
                source: Source(rawValue: entry.source ?? "unknown") ?? .manual,
                spo: Int(entry.spo),
                subcutaneousFatPercent: Int(entry.subcutaneousFatPercent),
                systolic: Int(entry.systolic),
                temperature: Float(entry.temperature),
                visceralFatLevel: Int(entry.visceralFatLevel),
                water: Int(entry.water),
                weight: Float(entry.weight),
                weightInKg: Float(entry.weightInKg),
                measurementUnit: Unit(rawValue: entry.unit ?? "unknown") ?? .unknown,
                pulseAmplitudeIndex: Float(entry.pulseAmplitudeIndex)
            )
        }
        .unique { $0.entryTimestamp }
    }
    
    func filteredEntries(appCategory: AppCategory, selectedSKU: String) -> [Operation] {
        switch appCategory {
        case .rpm:
            return entries.filter { $0.appCategory == .rpm && (selectedSKU.isEmpty || $0.deviceSKU == selectedSKU) }
        case .all:
            return entries
        default:
            return entries.filter { $0.appCategory == appCategory }
        }
    }
}
