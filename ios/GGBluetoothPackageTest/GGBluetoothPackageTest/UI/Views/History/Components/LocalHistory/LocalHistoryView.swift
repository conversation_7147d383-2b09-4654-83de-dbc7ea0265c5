//
//  LocalHistoryView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 05/02/25.
//

import SwiftUI

struct LocalHistoryView: View {
    @StateObject private var viewModel = LocalHistoryViewModel()
    @State private var showDeleteConfirmation: Bool = false
    @State private var entryToDelete: Operation? = nil
    var appCategory: AppCategory
    var parentView: HistoryPageParentView
    var deviceDetailParentView: DeviceDetailParentView
    var broadcastId: String?
    let commonLang = CommonStrings.self
    @Binding var selectedSKU: String

    var body: some View {
        VStack {
            let filteredEntries = viewModel.filteredEntries(appCategory: appCategory, selectedSKU: selectedSKU)
            if filteredEntries.isEmpty {
                VStack{
                    Spacer()
                    Text(commonLang.noEntriesAvailable)
                        .foregroundColor(.gray)
                        .padding()
                    Spacer()
                }
            } else {
                List {
                    ForEach(filteredEntries, id: \.self) { entry in
                        HistoryItemView(entry: entry)
                            .padding(.vertical, 5)
                            .swipeActions {
                                Button(role: .destructive) {
                                    entryToDelete = entry
                                    showDeleteConfirmation = true
                                } label: {
                                    Label("", systemImage: "trash")
                                }
                            }
                    }
                    .onDelete(perform: viewModel.deleteEntry)
                }
            }
        }
        .onAppear {
            viewModel.service.clearShownToasts()
            if parentView == .historytab {
                viewModel.fetchMeasurementEntries()
            } else if let broadcastId = broadcastId {
                let finalBroadcastId = (deviceDetailParentView == .Apps && appCategory == .weightGurus && Int(broadcastId) != nil)
                ? HexConversionHelper.convertIntToHex(value: Int(broadcastId)!, protocolType: .other)
                : broadcastId
                
                viewModel.fetchMeasurementEntriesWithBroadcastId(broadcastId: finalBroadcastId)
            }
        }
        .alert(isPresented: $showDeleteConfirmation) {
            Alert(
                title: Text(CommonStrings.deleteEntry),
                message: Text(CommonStrings.deleteEntryPlaceholder),
                primaryButton: .destructive(Text(CommonStrings.delete)) {
                    if let entryToDelete = entryToDelete,
                       let index = viewModel.entries.firstIndex(of: entryToDelete) {
                        viewModel.deleteEntry(at: IndexSet([index]))
                    }
                    self.entryToDelete = nil
                },
                secondaryButton: .cancel {
                    self.entryToDelete = nil
                }
            )
        }
    }
}

#Preview {
    LocalHistoryView(appCategory: .all, parentView: .historytab, deviceDetailParentView: .Apps, selectedSKU: .constant(""))
}
