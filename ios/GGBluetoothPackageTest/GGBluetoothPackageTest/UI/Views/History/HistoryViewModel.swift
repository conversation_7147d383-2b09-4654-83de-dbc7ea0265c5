//
//  HistoryViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 04/02/25.
//

import Foundation
import SwiftUICore

final class HistoryViewModel: ObservableObject {
    @Published var combinedLoginData: CombinedLoginData?
    @Published var measurements: [[String: Any]] = []
    @Published var filteredMeasurements: [[String: Any]] = []
    @Injector var deviceService: DeviceService
    private let logger: AppLogger = AppLogger(category: String(describing: HistoryViewModel.self))
    
    func fetchLoginData(for category: AppCategory) {
        if category == .all {
            let specificCategories: [AppCategory] = [.weightGurus, .balanceHealth, .smartBaby]
            var allMeasurements: [[String: Any]] = []
            let dispatchGroup = DispatchGroup()
            
            for specificCategory in specificCategories {
                dispatchGroup.enter()
                LoginHelper.shared.fetchLoginforAppCategory(for: specificCategory) { [weak self] success, combinedLoginData in
                    guard let self = self else {
                        dispatchGroup.leave()
                        return
                    }
                    
                    if success, let data = combinedLoginData {
                        self.combinedLoginData = data
                        if let measurements = data.measurements {
                            allMeasurements.append(contentsOf: measurements)
                        }
                    } else {
                        let nsError = NSError(domain: String(describing: DeviceService.self), code: 4, userInfo: [NSLocalizedDescriptionKey: "Failed to fetch login data for \(specificCategory)"])
                        self.logger.error(error: nsError, function: #function, line: #line)
                    }
                    dispatchGroup.leave()
                }
            }
            
            dispatchGroup.notify(queue: .main) {
                self.measurements = allMeasurements
                self.filteredMeasurements = LoginHelper.shared.filterMeasurements(allMeasurements, for: .all)
            }
        } else {
            LoginHelper.shared.fetchLoginforAppCategory(for: category) { [weak self] success, combinedLoginData in
                guard let self = self else { return }
                
                if success, let data = combinedLoginData {
                    self.combinedLoginData = data
                    self.extractMeasurements(data, for: category)
                } else {
                    let nsError = NSError(domain: String(describing: DeviceService.self), code: 4, userInfo: [NSLocalizedDescriptionKey: "Failed to fetch login data for \(category)"])
                    self.logger.error(error: nsError, function: #function, line: #line)
                }
            }
        }
    }
    
    private func extractMeasurements(_ data: CombinedLoginData, for category: AppCategory) {
        if let measurements = data.measurements {
            self.measurements = measurements
            self.filteredMeasurements = LoginHelper.shared.filterMeasurements(measurements, for: category)
        } else {
            let nsError = NSError(domain: String(describing: DeviceService.self), code: 4, userInfo: [NSLocalizedDescriptionKey: "No measurements available"])
            logger.error(error: nsError, function: #function, line: #line)
        }
    }
    
    func fetchAllUserLogins() -> [(UserLogin, AppCategory)]? {
        return deviceService.fetchAllUserLogins()
    }
}
