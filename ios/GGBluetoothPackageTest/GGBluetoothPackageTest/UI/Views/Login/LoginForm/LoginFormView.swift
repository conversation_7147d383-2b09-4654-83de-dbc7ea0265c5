//
//  LoginPage.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 23/01/25.
//

import SwiftUI

struct LoginFormView: View {
    @StateObject private var viewModel = LoginFormViewModel()
    @EnvironmentObject var router: Router<DashboardRoutes>
    let category: AppCategory
    let parentView: LoginPageParentView
    var lang = DeviceDetailStrings.self
    var commonLang = CommonStrings.self
    var alertLang = AlertStrings.self
    var toastLang = ToastMessageStrings.self
    @State private var showErrorAlert = false
    @State private var errorMessage = ""
    @State private var isLoggedIn = false
    
    var body: some View {
        VStack(alignment: .center) {
            Text(customCategoryText(for: category).uppercased())
                .foregroundColor(.blue)
                .fontWeight(.heavy)
                .font(.largeTitle)
                .padding(.top)
            
            Text(lang.loginToAccount)
                .font(.headline)
                .fontWeight(.bold)
                .padding(.top)
            
            Form {
                CustomFieldView(
                    label: commonLang.email,
                    text: $viewModel.email,
                    errorMessage: viewModel.emailError
                )
                .onChange(of: viewModel.email) { _ in
                    viewModel.validateEmail()
                }
                .padding(.top, 20)
                .listRowSeparator(.hidden)
                
                CustomFieldView(
                    label: commonLang.password,
                    text: $viewModel.password,
                    isPassword: true,
                    errorMessage: viewModel.passwordError
                )
                .onChange(of: viewModel.password) { _ in
                    viewModel.validatePassword()
                }
                .listRowSeparator(.hidden)
                .padding(.top, 20)
                
                Button(action: {
                    viewModel.handleLogin(for: category) { result in
                        switch result {
                        case .success:
                            isLoggedIn = true
                            ToastService.shared.presentToast(with: toastLang.loggedInSuccessfully)
                            if parentView == .apps {
                                router.navigate(to: .accountDashboard(category, viewModel.combinedLoginData))
                            } else {
                                router.navigateBack(1)
                            }
                            
                        case .failure(let error):
                            errorMessage = error.localizedDescription
                            showErrorAlert = true
                        }
                    }
                }) {
                    ZStack {
                        if viewModel.isLoading {
                            FourDotsLoaderView(color: .white)
                        } else {
                            Text(isLoggedIn ? lang.loggedIn : lang.logIn.uppercased())
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .frame(minHeight: 30)
                    .padding(10)
                    .background(viewModel.isButtonEnabled ? Color.blue : Color.gray)
                    .foregroundColor(.white)
                    .fontWeight(.bold)
                    .cornerRadius(10)
                }
                .disabled(!viewModel.isButtonEnabled)
                .padding(.vertical, 20)
            }
            .scrollContentBackground(.hidden)
        }
        .background(.gray.opacity(0.2))
        .alert(alertLang.loginFailed, isPresented: $showErrorAlert) {
            Button(commonLang.ok, role: .cancel) {}
        } message: {
            Text(errorMessage)
        }
    }
}

#Preview {
    LoginFormView(category: .sage, parentView: .apps)
}
