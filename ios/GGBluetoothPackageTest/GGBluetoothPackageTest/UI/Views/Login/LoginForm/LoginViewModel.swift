//
//  LoginViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 24/01/25.
//

import SwiftUI

class LoginFormViewModel: ObservableObject {
    @Published var email: String = ""
    @Published var password: String = ""
    @Published var emailError: String? = nil
    @Published var passwordError: String? = nil
    @Published var isLoading: Bool = false
    @Published var combinedLoginData: CombinedLoginData?

    @Injector var deviceService: DeviceService
    @Injector var loginService: LoginService
    
    var isButtonEnabled: Bool {
        !email.isEmpty && !password.isEmpty && emailError == nil && passwordError == nil
    }
    
    func validateEmail() {
        emailError = ValidationHelper.validateEmail(email)
    }
    
    func validatePassword() {
        passwordError = ValidationHelper.validatePassword(password)
    }
   
    func handleLogin(for category: AppCategory, completion: @escaping (Result<Void, Error>) -> Void) {
        isLoading = true
        
        loginService.login(for: category, email: email, password: password) { result in
            DispatchQueue.main.async {
                self.isLoading = false
                switch result {
                case .success(let combinedData):
                    self.combinedLoginData = combinedData
                    self.deviceService.saveUserLogin(category: category, email: self.email, password: self.password, isLoggedIn: true)
                    completion(.success(()))
                case .failure(let error):
                    completion(.failure(error))
                }
            }
        }
    }
}
