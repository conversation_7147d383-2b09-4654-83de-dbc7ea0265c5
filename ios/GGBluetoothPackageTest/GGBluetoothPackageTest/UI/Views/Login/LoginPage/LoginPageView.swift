//
//  LoginPageView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 03/02/25.
//

import SwiftUI

struct LoginPageView: View {
    @StateObject private var viewModel = LoginPageViewModel()
    @EnvironmentObject var router: Router<DashboardRoutes>
    let category: AppCategory
    var parentView: LoginPageParentView
    @State private var showErrorAlert = false
    @State private var errorMessage = ""
    @State private var isLoggedIn = false
    @State private var loginCompleted = false
    @State private var loginFetchFailed = false
    
    var body: some View {
        VStack {
            if loginCompleted {
                UserDashboardView(appCategory: category, loginData: viewModel.combinedLoginData)
            } else if loginFetchFailed {
                LoginFormView(category: category, parentView: parentView)
            } else {
                FourDotsLoaderView()
            }
        }
        .onAppear {
            viewModel.fetchLoginforAppCategory(for: category) { success in
                self.loginCompleted = success
                if !success {
                    self.errorMessage = "Login failed. Please try again."
                    self.showErrorAlert = true
                    self.loginFetchFailed = true
                }
            }
        }
    }
}

#Preview {
    LoginPageView(category: .sage, parentView: .apps)
}

