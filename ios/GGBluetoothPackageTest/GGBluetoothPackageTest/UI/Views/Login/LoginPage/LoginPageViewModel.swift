//
//  LoginPageViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 03/02/25.
//

import Foundation

class LoginPageViewModel: ObservableObject {
    @Published var combinedLoginData: CombinedLoginData?
    @Published var isLoading: Bool = false
    private(set) var isFormModified: Bool = false
    private let logger: AppLogger = AppLogger(category: String(describing: DeviceService.self))
    
    func fetchLoginforAppCategory(for category: AppCategory, completion: @escaping (Bool) -> Void) {
        isLoading = true
        
        LoginHelper.shared.fetchLoginforAppCategory(for: category) { success, combinedLoginData in
            DispatchQueue.main.async {
                self.isLoading = false
                if success, let data = combinedLoginData {
                    self.combinedLoginData = data
                } else {
                    let nsError = NSError(domain: String(describing: DeviceService.self), code: 4, userInfo: [NSLocalizedDescriptionKey: "Login failed"])
                    self.logger.error(error: nsError, function: #function, line: #line)
                }
                completion(success)
            }
        }
    }
}
