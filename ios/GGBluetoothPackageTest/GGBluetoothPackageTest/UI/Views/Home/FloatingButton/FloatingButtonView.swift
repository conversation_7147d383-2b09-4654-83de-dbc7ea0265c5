//
//  FloatingButtonView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 23/12/24.
//

import SwiftUI
import GGBluetoothSwiftPackage
import Combine

struct FloatingButtonView: View {
    @Binding var position: CGPoint
    @Binding var isScanning: Bool
    @Binding var selectedCategory: AppCategory?
    @StateObject var devicesModel = FloatingButtonViewModel()
    @State private var showAlert = false
    var showToast: (String) -> Void
    
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 15)
                .fill(isScanning ? Color.red : (devicesModel.isBluetoothPermissionEnabled ? (selectedCategory == nil ? Color.gray : Color.blue) : Color.gray))
                .frame(width: 60, height: 60)
            
            Image(systemName: isScanning
                  ? "stop.fill"
                  : (selectedCategory == nil ? "play.slash" : "play.fill"))
            .resizable()
            .frame(width: 20, height: 20)
            .foregroundColor(.white)
            .shadow(radius: 10)
        }
        .onTapGesture {
            if devicesModel.isBluetoothPermissionEnabled {
                if isScanning {
                    devicesModel.stopScanning()
                    isScanning = false
                    showToast(ToastMessageStrings.scanningStopped)
                } else {
                    if let selectedCategory = selectedCategory {
                        devicesModel.startScanning(for: selectedCategory)
                        isScanning = true
                        showToast(ToastMessageStrings.scanningStarted)
                    }
                }
            } else {
                showAlert = true
            }
        }
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text("Bluetooth Permission Required"),
                message: Text("Bluetooth permissions are not enabled. Please enable Bluetooth permissions in the settings to proceed."),
                primaryButton: .default(Text("OK")) {
                    Task {
                        await devicesModel.requestPermission()
                    }
                },
                secondaryButton: .cancel()
            )
        }
        .onAppear {
            if let selectedCategory = selectedCategory {
                devicesModel.checkBluetoothPermission(for: selectedCategory)
            }
        }
        .onChange(of: selectedCategory) { newValue in
            if let newCategory = newValue {
                devicesModel.checkBluetoothPermission(for: newCategory)
            }
        }
    }
}

#Preview {
    FloatingButtonView(
        position: .constant(CGPoint(x: 100, y: 100)),
        isScanning: .constant(false),
        selectedCategory: .constant(nil),
        showToast: { message in
            print("Toast message: \(message)")
        }
    )
}
