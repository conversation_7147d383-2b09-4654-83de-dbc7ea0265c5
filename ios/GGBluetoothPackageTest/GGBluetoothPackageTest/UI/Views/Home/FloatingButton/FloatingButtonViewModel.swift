//
//  FloatingButtonViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 23/12/24.
//

import Foundation
import GGBluetoothSwiftPackage
import Combine

final class FloatingButtonViewModel: ObservableObject {
    @Published var devices: [GGBTDevice] = []
    @Published var connectedDevices: [GGBTDevice] = []
    @Published var filteredDevices: [GGBTDevice] = []
    @Published var selectedCategory: AppCategory?
    @Published var isBluetoothPermissionEnabled: Bool = true
    private var cancellables: Set<AnyCancellable> = []
    @Injector var scannedDeviceModel: ScannedDeviceModel
    @Injector var bluetoothService: BluetoothService
    @Injector var deviceService: DeviceService
    @Injector var permissionsService: PermissionsService
    
    func startScanning(for category: AppCategory) {
        selectedCategory = category
        scannedDeviceModel.startScanning(for: category)
        
        scannedDeviceModel.$devices
            .sink { [weak self] value in
                DispatchQueue.main.async {
                    self?.devices = value
                    self?.applyCategoryFilter()
                }
            }
            .store(in: &cancellables)
    }
    
    func stopScanning() {
        scannedDeviceModel.stopScanning()
    }
    
    func filterDevicesByCategory(_ category: AppCategory) {
        selectedCategory = category
        applyCategoryFilter()
    }
    private func applyCategoryFilter() {
        if let category = selectedCategory {
            filteredDevices = devices.filter { device in
                let appCategory = getApp(forDevice: device.broadcastId, deviceName: device.name)
                return appCategory == category
            }
        } else {
            filteredDevices = devices
        }
    }
    
    func checkBluetoothPermission(for category: AppCategory) {
        permissionsService.fetchInitialPermissions(for: category) { [weak self] isEnabled in
            DispatchQueue.main.async {
                self?.isBluetoothPermissionEnabled = isEnabled
            }
        }
    }
    
    func requestPermission() async {
        let permissions = bluetoothService.permissionStatusSubject.value
        let bluetoothState = permissions[.BLUETOOTH] ?? .NOT_REQUESTED
        let bluetoothSwitchState = permissions[.BLUETOOTH_SWITCH] ?? .NOT_REQUESTED
        if bluetoothState == .DISABLED {
            await bluetoothService.requestPermission(permissionType: .BLUETOOTH)
        } else if bluetoothSwitchState == .DISABLED {
            await bluetoothService.requestPermission(permissionType: .BLUETOOTH_SWITCH)
        }
    }
}
