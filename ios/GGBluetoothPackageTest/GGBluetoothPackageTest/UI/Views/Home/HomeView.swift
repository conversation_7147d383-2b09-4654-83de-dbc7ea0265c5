//
// HomeView.swift
// GGBluetoothPackageTest
//
// Created by <PERSON> on 27/11/24.
//

import SwiftUI
import Alamofire

struct HomeView: View {
    @StateObject private var viewModel = HomeViewModel()
    @EnvironmentObject var toastService: ToastService
    @State private var refreshOffset: CGFloat = 0
    @State private var isRefreshing = false
    @State private var scanViewRefreshTrigger: Int = 0
    
    var body: some View {
        VStack(spacing: 0) {
            if viewModel.selectedTab == 0 && refreshOffset > 0 {
                HStack {
                    Spacer()
                    ProgressView()
                        .scaleEffect(1.2)
                        .opacity(min(refreshOffset / 100, 1.0))
                    Spacer()
                }
                .frame(height: 50)
                .background(Color.white)
            }
            
            Group {
                switch viewModel.selectedTab {
                case 0:
                    ScanView(
                        isScanning: $viewModel.isScanning,
                        selectedCategory: $viewModel.selectedCategory,
                        onToastMessage: { message in
                            ToastService.shared.presentToast(with: message, style: .success)
                        },
                        refreshTrigger: $scanViewRefreshTrigger
                    )
                case 1:
                    PairedDeviceView()
                case 2:
                    AppsView()
                case 3:
                    DevicesView()
                case 4:
                    HistoryView()
                case 5:
                    SettingsView()
                default:
                    EmptyView()
                }
            }
            .gesture(
                viewModel.selectedTab == 0 ?
                DragGesture()
                    .onChanged { value in
                        if value.translation.height > 0 && value.translation.height < 200 {
                            refreshOffset = value.translation.height
                        }
                    }
                    .onEnded { value in
                        if refreshOffset > 50 && !isRefreshing {
                            isRefreshing = true
                            scanViewRefreshTrigger += 1
                            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                                isRefreshing = false
                            }
                        }
                        withAnimation(.spring()) {
                            refreshOffset = 0
                        }
                    }
                : nil
            )
            
            Spacer(minLength: 0)
            
            if viewModel.canShowTabBar {
                CustomTabBarView(selectedTab: $viewModel.selectedTab, showTabBar: $viewModel.canShowTabBar, tabItems: viewModel.tabItems)
                    .padding(.top,8)
            }
        }
        .ignoresSafeArea(.keyboard)
        .toastView(toast: $viewModel.toast)
        .overlay(
            FloatingButtonView(position: $viewModel.buttonPosition, isScanning: $viewModel.isScanning, selectedCategory: $viewModel.selectedCategory, showToast: { message in
                ToastService.shared.presentToast(with: message, style: .success)
            })
            .position(viewModel.buttonPosition)
        )
        .onAppear {
            viewModel.setupTabBarObserver()
            let devices = DeviceService.shared.fetchAllGGBTDevices()
            BluetoothService.shared.syncDevices(devices)
        }
    }
}

#Preview {
    HomeView()
}
