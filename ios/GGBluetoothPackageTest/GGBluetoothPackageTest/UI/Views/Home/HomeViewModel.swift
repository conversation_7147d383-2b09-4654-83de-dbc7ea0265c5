//
// HomeViewModel.swift
// GGBluetoothPackageTest
//
// Created by <PERSON> on 09/01/25.
//

import Foundation
import SwiftUI
import Combine

var tabBarStateSubject: CurrentValueSubject<Bool, Never> = CurrentValueSubject(true)

class HomeViewModel: ObservableObject {
    @Published var selectedTab = 0
    @Published var isScanning: Bool = false
    @Published var selectedCategory: AppCategory? = nil
    @Published var toast: Toast? = nil
    @Published var buttonPosition: CGPoint
    @Published public var canShowTabBar: Bool = true
    
    private var tabBarVisibilityObserver: AnyCancellable?
    private var toastObserver: AnyCancellable?

    let tabItems: [TabItem] = [
        TabItem(title: CommonStrings.scan, icon: Image(systemName: "magnifyingglass")),
        TabItem(title: CommonStrings.pairedDevice, icon: Image(AppAssets.bluetooth)),
        TabItem(title: CommonStrings.apps, icon: Image(systemName: "apps.iphone")),
        TabItem(title: CommonStrings.devices, icon: Image(systemName: "list.bullet.clipboard")),
        TabItem(title: CommonStrings.history, icon: Image(systemName: "note.text")),
        TabItem(title: CommonStrings.settings, icon: Image(systemName: "gearshape"))
    ]

    init() {
        let screenWidth = UIScreen.main.bounds.width
        let screenHeight = UIScreen.main.bounds.height
        let xPosition: CGFloat = (screenWidth >= 390 && screenWidth < 430) ? 0.8 : 0.85
        let yPosition: CGFloat = screenHeight * 0.80 + 3
        _buttonPosition = Published(initialValue: CGPoint(x: screenWidth * xPosition, y: yPosition))
        
        setupToastObserver()
    }

    func setupToastObserver() {
        toastObserver = ToastService.shared.$toast.sink { [weak self] toast in
            self?.toast = toast
        }
    }

    func setupTabBarObserver() {
        tabBarVisibilityObserver = tabBarStateSubject.sink(receiveValue: { value in
            self.canShowTabBar = value
        })
    }

    deinit {
        tabBarVisibilityObserver?.cancel()
        toastObserver?.cancel()
    }
}
