//
//  ContentView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON><PERSON> Chitti<PERSON> on 17/06/24.
//

import SwiftUI
import CoreData

struct ContentView: View {
    @ObservedObject var accountService = AccountService.shared
    @StateObject private var deviceConnectionState = DeviceConnectionState.shared
    @StateObject private var networkMonitor = NetworkMonitor()

    var body: some View {
        HomeView()
            .environmentObject(deviceConnectionState)
            .environmentObject(networkMonitor)
            .environmentObject(ToastService.shared)
    }
}

#Preview {
    ContentView().environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
