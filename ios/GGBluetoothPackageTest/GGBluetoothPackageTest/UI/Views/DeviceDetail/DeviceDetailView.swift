//
//  DeviceDetailView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 17/12/24.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct DeviceDetailView: View {
    let category: AppCategory
    var device: GGBTDevice
    var sku: String?
    @EnvironmentObject var deviceConnectionState: DeviceConnectionState
    @State public var selectedTabIndex: Int = 0
    var parentView: DeviceDetailParentView
    @EnvironmentObject var router: Router<DashboardRoutes>
    
    var shouldShowLiveMeasurement: Bool {
        return deviceConnectionState.getDeviceConnectionState(deviceId: device.broadcastId) &&
        (category == .sage || category == .smartBaby || category == .rpm || device.protocolType == ProtocolType.WELLAND_BATH_SCALE.rawValue || device.protocolType == ProtocolType.R4.rawValue)
    }
    
    var shouldShowR4ScaleTab: Bool {
        return deviceConnectionState.getDeviceConnectionState(deviceId: device.broadcastId) && device.protocolType == ProtocolType.R4.rawValue
    }
    
    var shouldShowGraphsTab: Bool {
        return !(category == .rpm && (sku == RPMDeviceSKUs.thermometer || sku == RPMDeviceSKUs.pulseOximeter))
    }
    
    var lang = DeviceDetailStrings.self
    var commonLang = CommonStrings.self
    
    var body: some View {
        
        ContentWrapperView(title: .view(AnyView(HStack {
            Image(systemName: "circle.fill")
                .resizable()
                .frame(width: 15, height: 15)
                .foregroundColor(deviceConnectionState.getDeviceConnectionState(deviceId: device.broadcastId) ? .green : .red)
            Text((device.name).count > 15 ? "\(device.name.prefix(20))..." : device.name)
                .fontWeight(.semibold)
            
        }))) {
            SegmentedTabBarView(tabs: createTabs()) { selectedIndex in
                self.selectedTabIndex = selectedIndex
            }
            .padding(10)
        }
        .onAppear{
             let broadcastId = device.broadcastId
        }
        .onChange(of: deviceConnectionState.getDeviceConnectionState(deviceId: device.broadcastId ?? "")) { isConnected in
            let tabs = createTabs()
            if selectedTabIndex >= tabs.count {
                selectedTabIndex = 0
            }
        }
    }
    
    private func createTabs() -> [SegmentedTabItem] {
        var tabs: [SegmentedTabItem] = [
            SegmentedTabItem(titleView: AnyView(
                VStack {
                    Text(lang.basicDetail)
                        .fontWeight(.bold)
                }
            ), view: AnyView(
                DeviceInformationSectionView(device: device, isConnected: .constant((
                    deviceConnectionState.getDeviceConnectionState(deviceId: device.broadcastId)
                )), category: category, sku: sku, parentView: parentView)
            ))
        ]
        
        if shouldShowR4ScaleTab {
            tabs.append(SegmentedTabItem(
                titleView: AnyView(Text(lang.user).fontWeight(.bold)),
                view: AnyView(R4ScaleUserView(device: device, selectedCategory: category, parentView: .deviceDetailView))
            ))
        }
        
        if shouldShowLiveMeasurement {
            tabs.append(SegmentedTabItem(
                titleView: AnyView(Text(lang.live).fontWeight(.bold)),
                view: AnyView(LiveMeasurementView(category: category, device: device))
            ))
        }
        
        tabs.append(SegmentedTabItem(
            titleView: AnyView(Text(commonLang.history).fontWeight(.bold)),
            view: AnyView(LocalHistoryView(appCategory: category, parentView: .deviceDetail, deviceDetailParentView: parentView, broadcastId: device.broadcastId, selectedSKU: .constant(sku ?? "")))
        ))
        
        if shouldShowGraphsTab {
            tabs.append(SegmentedTabItem(
                titleView: AnyView(Text(commonLang.graphs).fontWeight(.bold)),
                view: AnyView(GraphView(parentView: .deviceDetail, device: device, appCategory: category, selectedSKU: .constant(sku ?? "")))
            ))
        }
        
        if shouldShowR4ScaleTab {
            tabs.append(SegmentedTabItem(titleView: AnyView(Text(lang.deviceSettings).fontWeight(.bold)), view: AnyView(DeviceSettingsView(device: device))))
            tabs.append(SegmentedTabItem(titleView: AnyView(Text(lang.deviceLogs).fontWeight(.bold)), view: AnyView(DeviceLogsView(device: device))))
        }
        
        return tabs
    }
}

#Preview {
    DeviceDetailView(category: .weightGurus, device: DefaultDevice.ggbtDevice, parentView: .scan)
}
