//
//  DeviceInformationSectionViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 13/01/25.
//

import Foundation
import GGBluetoothSwiftPackage

class DeviceInformationSectionViewModel: ObservableObject {
    @Injector var deviceService: DeviceService
    @Injector var bluetoothService: BluetoothService
    @Injector var loginService: LoginService
    @Published var device: GGBTDevice?
    @Published var showAlert: Bool = false
    @Published var alertMessage: String = ""
    private let logger: AppLogger = AppLogger(category: String(describing: DeviceInformationSectionViewModel.self))
    
    func isUserLoggedIn(for category: AppCategory) -> Bool {
        if let loginDetails = deviceService.fetchUserLogin(for: category) {
            return loginDetails.isLoggedIn
        }
        return false
    }
    
    func fetchDeviceDetails(by broadcastId: String) -> String? {
        if let fetchedDevice = deviceService.fetchDevice(by: broadcastId) {
            device = GGBTDevice(from: fetchedDevice)
            if let datePaired = fetchedDevice.datePaired {
                let dateFormatter = DateFormatter()
                dateFormatter.dateStyle = .long
                dateFormatter.timeStyle = .none
                return dateFormatter.string(from: datePaired)
            }
        }
        return nil
    }
    
    func deleteDevice(by broadcastId: String) {
        guard !broadcastId.isEmpty else {
            return
        }
        guard let device = deviceService.fetchDevice(by: broadcastId) else {
            return
        }
        let ggDevice = GGBTDevice(from: device)
        let category = getApp(forDevice: device.broadcastId ?? "", deviceName: device.name ?? "")
        let allDevices = deviceService.fetchAllDevices() ?? []
        let isSingleDeviceInCategory = allDevices.filter { getApp(forDevice: $0.broadcastId ?? "", deviceName: $0.name ?? "") == category }.count == 1
        
        if isSingleDeviceInCategory, [.smartBaby, .weightGurus, .balanceHealth].contains(category) {
            deviceService.deleteUserLogin(for: category)
        }
        
        Task {
            await deviceService.deleteDevice(by: ggDevice)
        }
        if self.device?.broadcastId == broadcastId {
            self.device = nil
        }
        ToastService.shared.presentToast(with: ToastMessageStrings.deviceDeletedSuccessfully)
    }
    
    func handleDisconnectButtonTapped(device: GGBTDevice) {
        Task{
        do {
            try await bluetoothService.disconnectDevice(device)
        } catch {
            logger.error(error: NSError(domain: String(describing: DeviceInformationSectionViewModel.self), code: 1, userInfo: [NSLocalizedDescriptionKey: "Failed to disconnect device. Error: \(error.localizedDescription)"]), function: #function, line: #line)
        }
    }
    }
    
    func fetchUserLoginStatus(for category: AppCategory, completion: @escaping (Bool) -> Void) {
        DeviceLinkingHelper.shared.fetchUserLoginStatus(for: category, device: device, completion: completion)
    }
    
    func checkDeviceLinkedStatus(by broadcastId: String) -> Bool? {
        return deviceService.isDeviceLinkedToApp(by: broadcastId)
    }
    
    func linkDeviceToApp(by broadcastId: String, isLinked: Bool) {
        deviceService.linkDeviceToApp(by: broadcastId, isLinked: isLinked)
    }
    
    func unLinkDeviceFromApp(for category: AppCategory, completion: @escaping (Bool) -> Void) {
        if let loginDetails = deviceService.fetchUserLogin(for: category) {
            var deviceId = device?.broadcastId ?? ""
            
            if category == .weightGurus, let broadcastIdString = device?.broadcastId, let broadcastIdInt = HexConversionHelper.convertHexToInt(value: broadcastIdString) {
                deviceId = String(broadcastIdInt)
            }
            
            loginService.deleteDeviceFromApp(for: category, email: loginDetails.email ?? "", password: loginDetails.password ?? "", deviceId: deviceId) { success in
                if success {
                    self.linkDeviceToApp(by: self.device?.broadcastId ?? "", isLinked: false)
                    completion(true)
                } else {
                    if self.loginService.errorMessage == ToastMessageStrings.deviceNotFoundInAccountDetails {
                        self.alertMessage = ToastMessageStrings.deviceNotFoundinApi(for: category)
                        self.showAlert = true
                        self.linkDeviceToApp(by: self.device?.broadcastId ?? "", isLinked: false)
                        completion(true)
                    } else {
                        self.alertMessage = self.loginService.errorMessage
                        self.showAlert = true
                        completion(false)
                    }
                }
            }
        } else {
            alertMessage = ToastMessageStrings.noLoginDataFound
            showAlert = true
            completion(false)
        }
    }
    
    func fetchWifiMACAddress(for device: GGBTDevice, completion: @escaping (String?) -> Void) {
        Task {
            do {
                let macAddress = try await bluetoothService.getWifiMacAddress(device)
                DispatchQueue.main.async {
                    completion(macAddress)
                }
            } catch {
                DispatchQueue.main.async {
                    self.alertMessage = error.localizedDescription
                    self.showAlert = true
                    completion(nil)
                }
            }
        }
    }
    
    func isWifiConfigured(broadcastID: String)  -> Bool {
        deviceService.getIsWifiConfigured(for: broadcastID) ?? false
    }
    
}
