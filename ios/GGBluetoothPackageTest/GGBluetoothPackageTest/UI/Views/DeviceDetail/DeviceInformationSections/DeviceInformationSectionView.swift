//
//  DeviceInformationSectionView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 18/12/24.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct DeviceInformationSectionView: View {
    @State var device: GGBTDevice
    @State private var datePaired: String? = nil
    @State private var showSafari = false
    @State private var keyText: String = DeviceDetailStrings.connectToApp
    @State private var showDeviceLinkConfirmationAlert = false
    @State private var showDisconnectionAlert = false
    @State private var showDeleteAlert = false
    @State private var showUnlinkAlert = false
    @State private var isToggleOn = false
    @State private var toggleIntent: Bool? = nil
    @State private var wifiMacAddress: String = ""
    @State private var isWifiConfigured: Bool? = nil
    @Binding var isConnected: Bool
    @StateObject var viewModel = DeviceInformationSectionViewModel()
    @EnvironmentObject var deviceConnectionState: DeviceConnectionState
    @EnvironmentObject var router: Router<DashboardRoutes>
    let category: AppCategory
    let sku: String?
    var parentView: DeviceDetailParentView
    var computedSKU: String {
        return sku ?? getSKU(device.name ?? "---")
    }
    let lang = DeviceDetailStrings.self
    let commonLang = CommonStrings.self
    
    var body: some View {
        List {
            // Device Image Section
            Section {
                if let image = getImage(for: getSKU(device.name)) {
                    image
                        .resizable()
                        .frame(width: 200, height: 200)
                        .frame(maxWidth: .infinity, alignment: .center)
                } else {
                    Image("\(getSKU(device.name))")
                        .resizable()
                        .frame(width: 200, height: 200)
                        .frame(maxWidth: .infinity, alignment: .center)
                }
            }
            
            
            if device.protocolType == ProtocolType.R4.rawValue && isWifiConfigured == false {
                Button(action: {
                    if isConnected {
                        router.navigate(to: .wifiSetup(device: device, category))
                    }
                }, label: {
                    LabelValuePairView(
                        key: Text(lang.setupIncomplete)
                            .foregroundColor(.red),
                        value: Text(lang.setupWifi)
                            .textCase(.uppercase)
                            .foregroundColor(.blue)
                    )
                    .opacity(isConnected ? 1.0 : 0.5)
                })
            }
            
            // Settings Section
            Section(header: Text(commonLang.settings)) {
                LabelValuePairView(key: Text(lang.scaleName), value: Text(device.name))
                LabelValuePairView(key: Text(lang.broadcastId), value: Text(device.broadcastId))
                LabelValuePairView(key: Text(commonLang.protocol), value: Text(device.protocolType ?? commonLang.unknown))
                
                if let userNumber = device.userNumber, userNumber > 0 {
                    LabelValuePairView(key: Text(lang.userNumber), value: Text("U\(userNumber)"))
                }
                
                if device.protocolType == ProtocolType.R4.rawValue {
                    Button(action: {
                        router.navigate(to: .dashboardMetricsMainView(device, category))
                    },
                           label: {
                        LabelValuePairView(
                            key:
                                Text(DisplayMetricsStrings.customizeSettingsTitle)
                                .foregroundColor(.black),
                            value: Image(systemName: "chevron.right")
                        )
                    })
                    
                    Button(action: {
                        router.navigate(to: .displayMetricsView(device, category))
                    },
                           label: {
                        LabelValuePairView(
                            key:
                                Text(DisplayMetricsStrings.dashboardMetricsTitle)
                                .foregroundColor(.black),
                            value: Image(systemName: "chevron.right")
                        )
                    })
                    
                }
                
            }
            
            // Connection Section
            Section(header: Text(lang.connection)) {
                LabelValuePairView(
                    key: Text(lang.bluetooth),
                    value: HStack {
                        Image(systemName: "circle.fill")
                            .foregroundColor(isConnected ? .green : .red)
                        Text(isConnected ? commonLang.connected : commonLang.disconnected)
                    }
                )
                
                if device.protocolType == ProtocolType.R4.rawValue {
                    Button(action: {
                        if isConnected {
                            router.navigate(to: .wifiSetup(device: device, category))
                        }
                    }, label: {
                        LabelValuePairView(
                            key: Text(commonLang.wifi)
                                .foregroundColor(isConnected ? .black : .gray),
                            value: Image(systemName: "chevron.right")
                        )
                        .opacity(isConnected ? 1.0 : 0.5)
                    })
                    
                    Button(action: {
                        guard isConnected else { return }
                        
                        viewModel.fetchWifiMACAddress(for: device) { macAddress in
                            if let macAddress = macAddress {
                                self.wifiMacAddress = macAddress
                                router.navigate(to: .wifiMacAddress(device: device, wifiMacAddress: macAddress))
                            }
                        }
                    }, label: {
                        LabelValuePairView(
                            key: Text(commonLang.wifiMacAddress)
                                .foregroundColor(isConnected ? .black : .gray),
                            value: Image(systemName: "chevron.right")
                        )
                        .opacity(isConnected ? 1.0 : 0.5)
                    })
                }
                
                if (category == .balanceHealth || (category == .weightGurus && device.protocolType != ProtocolType.WELLAND_BATH_SCALE.rawValue)) && parentView != .Apps {
                    LabelValuePairView(
                        key: Text(keyText),
                        value: HStack {
                            Toggle(isOn: Binding(
                                get: { isToggleOn },
                                set: { newValue in
                                    toggleIntent = newValue
                                    if newValue {
                                        showDeviceLinkConfirmationAlert = true
                                    } else {
                                        showUnlinkAlert = true
                                    }
                                }
                            )) {
                                Text("")
                            }
                            .toggleStyle(SwitchToggleStyle(tint: isToggleOn ? .blue : .gray))
                        }
                    )
                }
            }
            
            // Support Section
            Section(header: Text(lang.support)) {
                LabelValuePairView(key: Text(lang.scaleType), value: Text(commonLang.bluetooth))
                LabelValuePairView(key: Text(lang.sku), value: Text(computedSKU))
                
                LabelValuePairView(key: Text(lang.datePaired), value: Text(datePaired ?? commonLang.notAvailable))
                
                Button(action: {
                    showSafari = true
                }) {
                    LabelValuePairView(key: Text(lang.productGuide), value: Image(systemName: "chevron.right"))
                }
                .foregroundColor(.black)
                .fullScreenCover(isPresented: $showSafari) {
                    SafariView(url: ProductURLHelper.getProductGuideURL(forSKU: getSKU(device.name)))
                }
            }
            
            // Disconnect Section
            Section(header: Text(lang.disconnect)) {
                Button(action: {
                    showDisconnectionAlert = true
                }) {
                    LabelValuePairView(key: Text("\(lang.disconnect) \(commonLang.scale)"), value: Text(""))
                        .foregroundColor(.blue)
                }
            }
            
            // Delete Section
            Section(header: Text(lang.delete)) {
                Button(action: {
                    showDeleteAlert = true
                }) {
                    LabelValuePairView(key: Text("\(lang.delete) \(commonLang.scale)"), value: Text(""))
                        .foregroundColor(.red)
                }
            }
        }
        .onAppear {
            let broadcastId = device.broadcastId
            if let fetchedDatePaired = viewModel.fetchDeviceDetails(by: broadcastId) {
                datePaired = fetchedDatePaired
            }
            isWifiConfigured = viewModel.isWifiConfigured(broadcastID: broadcastId)
            if let isLinked = viewModel.checkDeviceLinkedStatus(by: broadcastId), isLinked {
                keyText = DeviceDetailStrings.connectedToApp
                isToggleOn = true
            }
        }
        .listStyle(InsetGroupedListStyle())
        .scrollIndicators(.hidden)
        .alert(viewModel.alertMessage, isPresented: $viewModel.showAlert) {
            Button(commonLang.ok, role: .cancel) {}
        }
        .alert(lang.confirm, isPresented: $showDeviceLinkConfirmationAlert) {
            Button(commonLang.ok) {
                viewModel.fetchUserLoginStatus(for: category) { success in
                    if success {
                        keyText = lang.linkToApp
                        isToggleOn = true
                    } else {
                        isToggleOn = false
                    }
                }
                toggleIntent = nil
            }
            Button(commonLang.cancel, role: .cancel) {
                isToggleOn = false
                toggleIntent = nil
            }
        } message: {
            Text(lang.connectToAppConfirmation)
        }
        .alert(lang.confirm, isPresented: $showDisconnectionAlert) {
            Button(commonLang.ok) {
                 let broadcastId = device.broadcastId
                viewModel.handleDisconnectButtonTapped(device: device)
                        deviceConnectionState.updateDeviceConnectionState(deviceId: broadcastId, isConnected: false)
                        if parentView == .scan {
                            router.navigateToRoot()
                        }
                toggleIntent = nil
            }
            Button(commonLang.cancel, role: .cancel) {
                toggleIntent = nil
            }
        } message: {
            Text(lang.disconnectConfirmation)
        }
        .alert(lang.confirm, isPresented: $showDeleteAlert) {
            Button(commonLang.ok) {
                let broadcastId = device.broadcastId
                viewModel.deleteDevice(by: broadcastId)
                router.navigateToRoot()                
            }
            Button(commonLang.cancel, role: .cancel) {}
        } message: {
            Text(lang.deleteConfirmation)
        }
        .alert(lang.confirmUnlink, isPresented: $showUnlinkAlert) {
            Button(commonLang.ok) {
                viewModel.unLinkDeviceFromApp(for: category) { success in
                    if success {
                        keyText = lang.connectToApp
                        isToggleOn = false
                    } else {
                        isToggleOn = true
                    }
                }
                toggleIntent = nil
            }
            Button(commonLang.cancel, role: .cancel) {
                isToggleOn = true
                toggleIntent = nil
            }
        } message: {
            Text(lang.unlinkConfirmation)
        }
    }
}

#Preview {
    DeviceInformationSectionView(device: DefaultDevice.ggbtDevice, isConnected: .constant(true), category: .sage, sku: "0375", parentView: .scan)
}
