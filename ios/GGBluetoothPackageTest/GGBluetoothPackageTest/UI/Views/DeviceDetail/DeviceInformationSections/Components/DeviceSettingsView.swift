//
//  DeviceSettingsView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 24/02/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct DeviceSettingsView: View {
    @StateObject var bluetoothService = BluetoothService.shared
    var device: GGBTDevice?
    
    var body: some View {
        VStack(spacing: 0) {
            ScaleConfigView(device: device ?? GGBTDevice(name: "", broadcastId: "", password: "", token: "", userNumber: 0, preference: nil, syncAllData: false, batteryLevel: 0, protocolType: "", macAddress: ""))

        }
    }
}

#Preview {
    DeviceSettingsView()
}
