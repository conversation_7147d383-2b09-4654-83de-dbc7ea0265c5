//
//  ThermometerLiveViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 25/02/25.
//

import Foundation
import GGBluetoothSwiftPackage
import Combine

class ThermometerLiveViewModel: ObservableObject {
    @Published var liveMeasurement: Entry?
    @Published var currentWeightEntry: Entry?
    @Published var unit: TemperatureMeasurementUnit = .celsius
    @Published var stableMeasurement: Entry?
    @Published var singleEntry: Entry?
    @Published var isMeasuring: Bool = false
    @Published var selectedMuteMode: MuteMode = .set
    private var cancellables = Set<AnyCancellable>()
    @Injector var btService: BluetoothService
    
    init() {
        btService.stableMeasurementSubject
            .sink { [weak self] entry in
                guard let self = self else { return }
                self.stableMeasurement = entry
                self.currentWeightEntry = entry
                self.isMeasuring = false 
            }
            .store(in: &cancellables)
    }
    
    func subscribeToLiveData(device: GGBTDevice?) {
        isMeasuring = true
        btService.subscribeToLiveData(device ?? defaultDevice())
    }
    
    func updateUnit(to newUnit: TemperatureMeasurementUnit, device: GGBTDevice?) {
        guard !isMeasuring else { return }
        btService.updateTemperatureUnit(device ?? defaultDevice(), unit: newUnit)
        DispatchQueue.main.async {
            self.unit = newUnit
        }
    }
    
    func setMuteMode(device: GGBTDevice?) {
        btService.setMuteMode(device ?? defaultDevice())
        DispatchQueue.main.async {
            self.selectedMuteMode = .set
        }
    }

    func disableMuteMode(device: GGBTDevice?) {
        btService.disableMuteMode(device ?? defaultDevice())
        DispatchQueue.main.async {
            self.selectedMuteMode = .disable
        }
    }
    
    private func defaultDevice() -> GGBTDevice {
        return GGBTDevice(name: "", broadcastId: "", password: "", token: "", userNumber: 0, preference: nil, syncAllData: false, batteryLevel: 0, protocolType: "", macAddress: "")
    }
}
