//
//  PulseOxyMeterLiveViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 26/02/25.
//

import Foundation
import GGBluetoothSwiftPackage
import Combine

class PulseOxyMeterLiveViewModel: ObservableObject {
    @Published var stableMeasurement: Entry?
    @Published var currentWeightEntry: Entry?
    @Published var liveMeasurement: Entry?
    @Published var singleEntry: Entry?
    public var device: GGBTDevice?
    private var cancellables = Set<AnyCancellable>()
    @Injector var btService: BluetoothService
    
    init() {
        btService.stableMeasurementSubject
            .sink { [weak self] entry in
                self?.stableMeasurement = entry
                self?.currentWeightEntry = entry
            }
            .store(in: &cancellables)
    }
    
    deinit {
        cancellables.forEach { $0.cancel() }
    }
    
}
