//
//  ChevronLoaderView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 26/02/25.
//

import SwiftUI

struct ChevronLoaderView: View {
    @State private var chevronCount = 2
    private let minChevronCount = 2
    private let maxChevronCount = 5
    @State private var isAdding = true

    var body: some View {
        HStack(spacing: 5) {
            // Dynamic chevrons
            ForEach(0..<(chevronCount - minChevronCount), id: \.self) { index in
                chevronIcon()
                    .offset(x: CGFloat(-index * -10))
                    .animation(.easeOut(duration: 0.2), value: chevronCount)
            }
            
            // Static chevrons
            ForEach(0..<minChevronCount, id: \.self) { _ in
                chevronIcon()
                    .offset(x: CGFloat(-(chevronCount - minChevronCount) * -10))
            }
        }
        .onAppear {
            startAnimation()
        }
    }
    
    @ViewBuilder
    func chevronIcon() -> some View {
        Image(systemName: "chevron.left")
            .font(.title)
            .opacity(1)
            .fontWeight(.bold)
            .foregroundColor(.yellow)
    }

    func startAnimation() {
        Timer.scheduledTimer(withTimeInterval: 0.3, repeats: true) { timer in
            withAnimation {
                if isAdding {
                    if chevronCount < maxChevronCount {
                        chevronCount += 1
                    } else {
                        isAdding = false
                    }
                } else {
                    if chevronCount > minChevronCount {
                        chevronCount -= 1
                    } else {
                        isAdding = true
                    }
                }
            }
        }
    }
}

struct ChevronLoaderView_Previews: PreviewProvider {
    static var previews: some View {
        ChevronLoaderView()
    }
}
