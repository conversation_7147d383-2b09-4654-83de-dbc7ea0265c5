//
//  BloodGlucoseMeterLiveView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 10/03/25.
//

import SwiftUI

struct BloodGlucoseMeterLiveView: View {
    @State private var currentTime: String = ""
    @State private var currentDate: String = ""
    @StateObject private var viewModel = ThermometerLiveViewModel()
    var body: some View {
        ZStack {
            CustomRoundedRectangle()
                .fill(Color.white)
                .frame(width: 350, height: 500)
                .shadow(color: Color.black.opacity(0.2), radius: 10, x: 0, y: 5)
                .overlay(
                    CustomRoundedRectangle()
                        .stroke(Color.gray, lineWidth: 2)
                )
            
            VStack(){
                
                NotLiveIndicatorView()
                    .padding(.trailing, 50)
                    .padding(.top,5)
                
                Text("M I O")
                    .font(.title)
                    .foregroundColor(.gray)
                
                VStack(spacing: 10) {
                    
                    HStack {
                        Text(currentTime)
                            .font(.caption)
                        Spacer()
                        Text(currentDate)
                            .font(.caption)
                            .padding(.trailing, 20)
                    }
                    .padding(.horizontal, 15)
                    .padding(.top, 5)
                    .onAppear {
                        updateDateTime()
                    }
                    
                    HStack {
                        Spacer()
                        Image(AppAssets.bluetooth)
                        Spacer()
                        Image(systemName: "battery.75")
                            .padding(.trailing, 40)
                    }
                    .font(.title2)
                    
                    Text(
                        {
                            if let bgm = viewModel.currentWeightEntry?.bgm, bgm > 0 {
                                return String(format: "%.1f", bgm)
                            } else if viewModel.currentWeightEntry?.bgm == 0 {
                                return viewModel.currentWeightEntry?.errorCode ?? "--"
                            } else {
                                return "--"
                            }
                        }()
                    )
                    .font(.system(size: 80, weight: .bold))
                    .padding(.vertical, 10)
                    
                    
                    HStack {
                        Spacer()
                        Image(systemName: "drop.fill")
                        Text("mg/dL")
                            .font(.caption)
                    }
                    .padding(.trailing, 20)
                }
                .foregroundColor(.white)
                .padding()
                .background(Color.black)
                .cornerRadius(10)
                .padding(.horizontal, 20)
                .padding(.bottom,30)
                
                Circle()
                    .frame(width: 60, height: 60)
                    .foregroundColor(.white)
                    .overlay(
                        Circle()
                            .stroke(Color.black, lineWidth: 2)
                    )
                
            }
            .padding(.bottom)
            .padding(.horizontal,20)
        }
        .padding(.horizontal, 20)
        .onAppear {
            updateDateTime()
        }
    }
    
    func updateDateTime() {
        let formatter = DateFormatter()
        formatter.dateFormat = "hh:mm a"
        currentTime = formatter.string(from: Date())
        
        formatter.dateFormat = "dd - MM"
        currentDate = formatter.string(from: Date())
    }
}

struct BloodGlucoseMeterLiveView_Previews: PreviewProvider {
    static var previews: some View {
        BloodGlucoseMeterLiveView()
    }
}
