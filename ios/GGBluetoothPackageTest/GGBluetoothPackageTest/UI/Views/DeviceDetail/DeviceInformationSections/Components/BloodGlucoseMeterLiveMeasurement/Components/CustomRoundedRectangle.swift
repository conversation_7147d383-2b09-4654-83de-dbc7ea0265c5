//
//  CustomRoundedRectangle.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 10/03/25.
//

import SwiftUI

struct CustomRoundedRectangle: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        let topCornerRadius: CGFloat = rect.width * 0.3
        let bottomCurveDepth: CGFloat = rect.height * 0.2
        let bottomCurveWidth: CGFloat = rect.width * 0.6
        
        // Top-left corner
        path.addArc(
            center: CGPoint(x: rect.minX + topCornerRadius, y: rect.minY + topCornerRadius),
            radius: topCornerRadius,
            startAngle: <PERSON>le(degrees: 180),
            endAngle: <PERSON><PERSON>(degrees: 270),
            clockwise: false
        )
        
        // Top-right corner
        path.addLine(to: CGPoint(x: rect.maxX - topCornerRadius, y: rect.minY))
        path.addArc(
            center: CGPoint(x: rect.maxX - topCornerRadius, y: rect.minY + topCornerRadius),
            radius: topCornerRadius,
            startAngle: <PERSON><PERSON>(degrees: 270),
            endAngle: <PERSON><PERSON>(degrees: 0),
            clockwise: false
        )
        
        // Right side
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY - bottomCurveDepth * 0.8))

        // Smooth Bottom U-Shape
        path.addCurve(
                   to: CGPoint(x: rect.minX, y: rect.maxY - bottomCurveDepth * 0.7),
                   control1: CGPoint(x: rect.maxX - bottomCurveWidth * 0.4, y: rect.maxY + bottomCurveDepth * 0.6),
                   control2: CGPoint(x: rect.minX + bottomCurveWidth * 0.4, y: rect.maxY + bottomCurveDepth * 0.6)
               )
        
        // Left side
        path.addLine(to: CGPoint(x: rect.minX, y: rect.minY + topCornerRadius))

        path.closeSubpath()
        
        return path
    }
}
