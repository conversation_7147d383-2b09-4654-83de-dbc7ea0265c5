//
//  LiveMeasurementView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 25/02/25.
//

import SwiftUI
import Combine
import GGBluetoothSwiftPackage

struct LiveMeasurementView: View {
    let category: AppCategory
    let device: GGBTDevice?
    
    var body: some View {
        if category == .sage  || category == .smartBaby || (category == .weightGurus) {
            ScaleMeasurementView(device: device ?? GGBTDevice(name: "", broadcastId: "", password: "", token: "", userNumber: 0, preference: nil, syncAllData: true, batteryLevel: 1, protocolType: "", macAddress: ""), category: category)
        } else if category == .rpm {
            let sku = getSKU(device?.name ?? "")
            
            switch sku {
            case "0062":
                ThermometerLiveView(device: device ?? GGBTDevice(name: "", broadcastId: "", password: "", token: "", userNumber: 0, preference: nil, syncAllData: true, batteryLevel: 1, protocolType: "", macAddress: ""))
            case "0003":
                PulseOxyMeterLiveView()
            case "0005":
                BloodGlucoseMeterLiveView()
            case "0383":
                ScaleMeasurementView(device: device ?? GGBTDevice(name: "", broadcastId: "", password: "", token: "", userNumber: 0, preference: nil, syncAllData: true, batteryLevel: 1, protocolType: "", macAddress: ""), category: category)
            default:
                ThermometerLiveView(device: device ?? GGBTDevice(name: "", broadcastId: "", password: "", token: "", userNumber: 0, preference: nil, syncAllData: true, batteryLevel: 1, protocolType: "", macAddress: ""))
            }
        }else{
            Text ("LiveMeasurementView")
        }
    }
}
