//
//  ThermometerLiveView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 25/02/25.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct ThermometerLiveView: View {
    @StateObject private var viewModel = ThermometerLiveViewModel()
    var device: GGBTDevice

    var body: some View {
        VStack(spacing: 0) {
            VStack {
                HStack{
                    Image(systemName: "person.fill")
                        .font(.system(size: 25))
                        .padding(.trailing, 10)
                    
                    Image(systemName: viewModel.selectedMuteMode == .set ? "speaker.slash.fill" : "speaker.zzz.fill")
                        .font(.system(size: 25))

                    Spacer()
                }
                .padding(10)
                
                HStack(alignment: .firstTextBaseline, spacing: 5) {
                    Text(String(format: "%.1f", viewModel.currentWeightEntry?.temperature ?? 0.0))
                        .font(.system(size: 60))
                    Text(viewModel.stableMeasurement?.unit ?? viewModel.unit.displayValue)
                        .font(.title)
                }
                .foregroundColor(.black)
                .frame(maxWidth: .infinity, minHeight: 150)
                
                HStack{
                    Spacer()
                    
                    Image(AppAssets.bluetooth)
                        .font(.system(size: 25))
                        .padding(.trailing, 10)
                    
                }
                .padding(15)
            }
            
            DividerView(width: .infinity, height: 2, color: .black)
            
            HStack(spacing: 0) {
                CustomMenuButton(
                    title: ThermometerLiveViewStrings.setMuteMode,
                    width: 130,
                    options: [
                        CustomMenuButton.MenuOption(label: "Set Mute Mode") {
                            viewModel.setMuteMode(device: device)
                        },
                        CustomMenuButton.MenuOption(label: "Disable Mute Mode") {
                            viewModel.disableMuteMode(device: device)
                        }
                    ],
                    foregroundColor: viewModel.isMeasuring ? .gray : .black
                )
                .disabled(viewModel.isMeasuring)

                DividerView()

                CustomMenuButton(
                    title: ThermometerLiveViewStrings.setUnit,
                    width: 130,
                    options: TemperatureMeasurementUnit.allCases.map { unit in
                        CustomMenuButton.MenuOption(label: unit.displayValue) {
                            viewModel.updateUnit(to: unit, device: device)
                        }
                    },
                    foregroundColor: viewModel.isMeasuring ? .gray : .black
                )
                .disabled(viewModel.isMeasuring)

                DividerView()

                Button(action: {
                    viewModel.subscribeToLiveData(device: device)
                }, label: {
                    Text(ThermometerLiveViewStrings.startMeasurement)
                        .foregroundColor(viewModel.isMeasuring ? .gray : .black)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                })
                .frame(width: 150)
            }
        }
        .overlay(
            RoundedRectangle(cornerRadius: 15)
                .stroke(Color.black, lineWidth: 2)
        )
        .background(Color(red: 0.9, green: 1.0, blue: 1.0))
        .cornerRadius(15)
        .padding(.horizontal, 15)
    }
    
    @ViewBuilder
    private func DividerView(width: CGFloat = 2, height: CGFloat = 50, color: Color = .black) -> some View {
        Rectangle()
            .frame(width: width, height: height)
            .foregroundColor(color)
    }
}
