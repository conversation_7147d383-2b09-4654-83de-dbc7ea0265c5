//
//  NotLiveIndicatorView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 10/03/25.
//

import SwiftUI

struct NotLiveIndicatorView: View {
    @State private var showOverlay = false
    
    var body: some View {
        HStack {
            Spacer()
            if showOverlay {
                Text("Live entry is Unsupported.")
                    .font(.callout)
                    .foregroundColor(.blue)
                    .padding(.horizontal,10)
                    .padding(.vertical,5)
                    .background(Color.white)
                    .cornerRadius(10)
                    .shadow(radius: 10)
                    .onTapGesture {
                        withAnimation {
                            showOverlay = false
                        }
                    }
                    .transition(.opacity)
            } else {
                HStack {
                    Image(systemName: "info.circle.fill")
                        .foregroundColor(.gray)
                        .onTapGesture {
                            withAnimation {
                                showOverlay = true
                            }
                        }
                    Text("Not Live")
                        .font(.callout)
                        .foregroundColor(.gray)
                        .onTapGesture {
                            withAnimation {
                                showOverlay = true
                            }
                        }
                }.padding(.trailing)
            }
        }
        .frame(minHeight: 30)
    }
}

struct NotLiveIndicatorView_Previews: PreviewProvider {
    static var previews: some View {
        NotLiveIndicatorView()
    }
}
