//
//  PulseOxyMeterLiveView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 26/02/25.
//

import SwiftUI

struct PulseOxyMeterLiveView: View {
    @StateObject private var viewModel = PulseOxyMeterLiveViewModel()
    
    var body: some View {
        VStack(spacing:20){
            PulseOxyMeasurementView(value: viewModel.currentWeightEntry?.spo ?? 0.0, label: "%SPO2", isInteger: true)
            
            HStack (spacing: 0){
                BatteryLoader()
                    .rotationEffect(Angle(degrees: -90))
                Image(AppAssets.bluetooth)
                    .font(.system(size: 25))
            }
            .rotationEffect(Angle(degrees: 90))
            .foregroundColor(.yellow)
            .font(.headline)
            .fontWeight(.bold)
            .frame(width: 250, alignment: .trailing)
            .padding(.vertical,10)
            .padding(.trailing, 5)
            
            PulseOxyMeasurementView(value: viewModel.currentWeightEntry?.pulse ?? 0.0, label: "PR bpm", fontSize: 40, isInteger: true)
            PulseOxyMeasurementView(value: viewModel.currentWeightEntry?.pulseAmplitudeIndex ?? 0.0, label: " PI% ",fontSize: 35, isInteger: false)
            
            HStack{
                ChevronLoaderView()
                    .frame(width: 100, alignment: .trailing)
                Image(systemName: "heart.fill")
                    .foregroundColor(.yellow)
                    .font(.title)
                    .frame(width: 100)
            }
            .frame(maxWidth: .infinity)
            .padding(.top,10)
            .padding(.leading, 60)
        }
        .padding(.vertical)
        .padding(.leading, 20)
        .background(Color.black)
        .cornerRadius(15) 
        .frame(width: 300)
    }

    @ViewBuilder
    func PulseOxyMeasurementView(value: Float, label: String, fontSize: CGFloat = 60, isInteger: Bool = true) -> some View {
        HStack {
            Spacer()
            Text("\(isInteger ? String(format: "%.0f", value) : String(format: "%.1f", value))")
                .font(.system(size: fontSize))
                .foregroundColor(.blue)
                .fontWeight(.semibold)
                .frame(width: 100, alignment: .trailing)
            
            Text(label.uppercased())
                .rotationEffect(Angle(degrees: 90))
                .foregroundColor(.yellow)
                .font(.headline)
                .frame(width: 100)
        }
        .frame(maxWidth: .infinity)
    }
}
