//
//  ScaleMeasurementViewModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 18/12/24.
//

import Foundation
import GGBluetoothSwiftPackage
import Combine

final class ScaleMeasurementViewModel: ObservableObject {
    @Injector var btService : BluetoothService
    @Injector var measurementEntryService : MeasurementEntryService
    @Published var ingredientLabel: String = "unlabeled"
    @Published var liveMeasurement: Entry?
    @Published var currentWeightEntry: Entry?
    @Published var stableMeasurement: Entry?
    @Published var singleEntry: Entry?
    @Published public var unit: MeasurementUnit = .g
    @Published public var babyScaleUnit: MeasurementUnit = .kg
    private var cancellables = Set<AnyCancellable>()
    public var device: GGBTDevice?
    public var deviceInfo: Scale?
    public var category: AppCategory
    
    init(category: AppCategory) {
        self.category = category
        
        btService.liveMeasurementSubject
            .subscribe(on: DispatchQueue.global(qos: .background))
                       .receive(on: DispatchQueue.main)
            .sink { [weak self] entry in
                self?.liveMeasurement = entry
                self?.currentWeightEntry = entry
                if self?.category == .weightGurus {
                    self?.stableMeasurement = nil
                }
            }
            .store(in: &cancellables)
        
        btService.stableMeasurementSubject
            .sink { [weak self] entry in
                self?.singleEntry = entry
                self?.stableMeasurement = entry
            }
            .store(in: &cancellables)
    }
    
    deinit {
        cancellables.forEach { $0.cancel() }
    }
    
    public func tare(device: GGBTDevice) {
        btService.tareScale(device)
    }
    
    public func updateUnit(device: GGBTDevice, unit: MeasurementUnit) {
        btService.updateUnit(device, unit: unit)
        DispatchQueue.main.async {
            self.unit = unit
            self.babyScaleUnit = unit
        }
    }
}
