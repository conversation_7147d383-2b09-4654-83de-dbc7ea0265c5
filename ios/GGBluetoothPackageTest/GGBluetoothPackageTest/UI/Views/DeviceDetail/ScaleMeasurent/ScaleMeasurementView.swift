//
//  ScaleMeasurementView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 17/12/24.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct ScaleMeasurementView: View {
    @StateObject private var viewModel: ScaleMeasurementViewModel
    var device: GGBTDevice
    let category: AppCategory
    
    init(device: GGBTDevice, category: AppCategory) {
        self.device = device
        self.category = category
        _viewModel = StateObject(wrappedValue: ScaleMeasurementViewModel(category: category))
    }
    
    var body: some View {
        VStack(spacing: 2) {
            VStack {
                
                HStack(alignment: .firstTextBaseline, spacing: 2) {
                    
                    if category == .weightGurus && viewModel.stableMeasurement?.displayWeight != nil {
                        Image(systemName: "checkmark.circle.fill")
                            .offset(x: 0, y: -25)
                            .padding(.trailing, 20)
                            .font(.system(size: 20))
                    } else if category == .sage && viewModel.stableMeasurement?.displayWeight != nil && viewModel.stableMeasurement?.displayWeight != 0 {
                        Image(systemName: "checkmark.circle.fill")
                            .offset(x: 0, y: -25)
                            .padding(.trailing, 20)
                            .font(.system(size: 20))
                    }
                    
                    HStack{
                        if category == .sage || category == .weightGurus {
                            Text(String(format: "%.1f", viewModel.currentWeightEntry?.displayWeight ?? 0.0))
                                .font(.system(size: 60))
                        } else {
                            if viewModel.stableMeasurement?.unit == "lb" {
                                Text(String(format: "%.3f", (viewModel.singleEntry?.displayWeight ?? 0.0) / 10))
                                    .font(.system(size: 60))
                                    .padding(.leading, 5)
                            } else {
                                Text(String(format: "%.2f", (viewModel.singleEntry?.weightInKg ?? 0.0)))
                                    .font(.system(size: 60))
                                    .padding(.leading, 5)
                            }
                        }
                    
                    
                    Text(category == .sage || category == .weightGurus ? (viewModel.liveMeasurement?.unit ?? viewModel.unit.displayValue) :
                            (viewModel.stableMeasurement?.unit ?? viewModel.babyScaleUnit.displayValue))
                    .font(.title)
                        
                    }
                    .frame(width: 300)
                        
                }
                .foregroundColor(.white)
                .padding(.top, 20)
            }
            .frame(maxWidth: .infinity, minHeight: 200)
            .background(Color(red: 50 / 255, green: 63 / 255, blue: 59 / 255))
            .clipShape(
                UnevenRoundedRectangle(
                    cornerRadii: .init(
                        topLeading: 15,
                        bottomLeading: 0,
                        bottomTrailing: 0,
                        topTrailing: 15
                    )
                )
            )
            
            HStack(spacing: 0) {
                if category == .sage {
                    Button(action: {
                        viewModel.tare(device: device)
                    }, label: {
                        Text(ScaleMeasurementStrings.Tare)
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity, minHeight: 50)
                            .background(Color(red: 50 / 255, green: 63 / 255, blue: 59 / 255))
                    })
                    
                    Divider()
                        .frame(width: 2, height: 50)
                        .background(Color.white)
                }
                
                CustomMenuButton(
                    title: CommonStrings.unit,
                    options: MeasurementUnit.allCases
                        .filter { unit in
                            if category == .smartBaby || category == .weightGurus {
                                return unit == .kg || unit == .lb
                            } else if category == .sage {
                                return unit != .oz && unit != .kg && unit != .lb
                            }
                            return true
                        }
                        .sorted(by: { $0.displayValue > $1.displayValue })
                        .map { unit in
                            CustomMenuButton.MenuOption(label: unit.displayValue) {
                                viewModel.updateUnit(device: device, unit: unit)
                            }
                        }
                )
                .frame(maxWidth: .infinity, minHeight: 50)
                
            }
            .background(Color(red: 50 / 255, green: 63 / 255, blue: 59 / 255))
            .clipShape(
                UnevenRoundedRectangle(
                    cornerRadii: .init(
                        topLeading: 0,
                        bottomLeading: 15,
                        bottomTrailing: 15,
                        topTrailing: 0
                    )
                )
            )
        }
        .padding()
    }
}

#Preview {
    ScaleMeasurementView(device: GGBTDevice(name: "MY_SCALE", broadcastId: "1234", password: "", token: "", userNumber: 2, preference: nil, syncAllData: true, batteryLevel: 34, protocolType: "", macAddress: ""), category: .sage)
}
