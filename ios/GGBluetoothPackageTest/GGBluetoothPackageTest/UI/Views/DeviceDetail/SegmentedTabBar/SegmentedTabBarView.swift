//
//  SegmentedTabBarView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 20/12/24.
//

import SwiftUI

struct SegmentedTabBarView: View {
    let tabs: [SegmentedTabItem]
    public var onClickTab: (Int) -> Void
    @State private var selectedTabIndex: Int = 0
    
    var body: some View {
        VStack(spacing: 0) {
            if tabs.count > 3 {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 0) {
                        ForEach(tabs.indices, id: \.self) { index in
                            tabButton(for: index)
                        }
                    }
                }
            } else {
                HStack(spacing: 0) {
                    ForEach(tabs.indices, id: \.self) { index in
                        tabButton(for: index)
                            .frame(width: UIScreen.main.bounds.width / CGFloat(tabs.count))
                    }
                }
            }
            displayTabView()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .edgesIgnoringSafeArea(.all)
    }
    
    private func tabButton(for index: Int) -> some View {
        Button(action: {
            self.selectedTabIndex = index
            onClickTab(self.selectedTabIndex)
        }, label: {
            tabs[index].titleView
                .padding(.vertical, 16)
                .padding(.horizontal, 20)
                .frame(maxWidth: .infinity)
                .background(self.selectedTabIndex == index ? Color.blue.opacity(0.1) : Color.white)
        })
        .id(index)
    }
    
    private func displayTabView() -> some View {
        if selectedTabIndex < tabs.count {
            return AnyView(
                tabs[selectedTabIndex].view
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.white)
            )
        } else {
            return AnyView(
                tabs[selectedTabIndex - 1].view
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.white)
            )
        }
    }
}

// For preview purpose
struct SegmentedTabBarTestingView: View {
    var body: some View {
        SegmentedTabBarView(tabs: [
            SegmentedTabItem(titleView: AnyView(
                VStack {
                    Text("Info")
                        .fontWeight(.bold)
                }
            ), view: AnyView(IngredientsView().padding())),
            SegmentedTabItem(titleView: AnyView(Text("Settings").fontWeight(.bold)), view: AnyView(DirectionsView().padding())),
            SegmentedTabItem(titleView: AnyView(Text("Meal").fontWeight(.bold)), view: AnyView(MealView().padding())),
        ]) { selectedIndex in
            
        }
    }
}

struct IngredientsView: View {
    var body: some View {
        Text("Information View")
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .padding()
    }
}

struct DirectionsView: View {
    var body: some View {
        Text("Settings View")
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .padding()
    }
}

struct MealView: View {
    var body: some View {
        Text("Meal View")
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .padding()
    }
}

#Preview {
    VStack {
        SegmentedTabBarTestingView()
    }
    .padding()
}
