//
//  ModalContainerView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 21/04/25.
//

import SwiftUI

struct ModalContainer<Content: View>: View {
    @ViewBuilder var content: () -> Content

    var body: some View {
        VStack {
            content()
        }
        .padding()
        .frame(maxWidth: UIScreen.main.bounds.width * 0.85)
        .background(Color.white)
        .cornerRadius(20)
        .shadow(radius: 5)
       
    }
}

#Preview(){
    ModalContainer(){
        Text("Hello, World!")
    }
}
