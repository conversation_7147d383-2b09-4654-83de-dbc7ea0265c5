//
//  ToastView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 08/01/25.
//

import SwiftUI

struct ToastView: View {
    
    var style: ToastStyle
    var message: String
    var width = CGFloat.infinity
    var onCancelTapped: (() -> Void)
    
    @State private var offset: CGFloat = 0
    
    var body: some View {
        HStack(alignment: .center, spacing: 12) {
            Image(systemName: style.iconFileName)
                .foregroundColor(style.themeColor)
            Text(message)
                .font(.subheadline)
                .foregroundColor(style == .error ? .red : .black)
            
            Spacer(minLength: 10)
            
            Button {
                onCancelTapped()
            } label: {
                Image(systemName: "xmark")
                    .foregroundColor(style.themeColor)
            }
        }
        .padding()
        .frame(minWidth: 0, maxWidth: width)
        .background(Color.white)
        .cornerRadius(8)
        .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .opacity(0.1)
        )
        .padding(.horizontal, 16)
        .offset(x: offset)
        .gesture(
            DragGesture()
                .onChanged { value in
                    offset = value.translation.width
                }
                .onEnded { value in
                    if abs(value.translation.width) > 50 {
                        withAnimation(.easeIn) {
                            onCancelTapped()
                        }
                    } else {
                        withAnimation(.easeIn) {
                            offset = 0
                        }
                    }
                }
        )
    }
}

#Preview {
    // Define the 'toast' state variable
    @Previewable @State var toast: Toast? = nil
    
    VStack(spacing: 32) {
        Button {
            toast = Toast(style: .success, message: "Scan button tapped.")
        } label: {
            Text("Run (Success)")
        }
        
        Button {
            toast = Toast(style: .info, message: "Btw, you are a good person.")
        } label: {
            Text("Run (Info)")
        }
        
        Button {
            toast = Toast(style: .warning, message: "Beware of a dog!")
        } label: {
            Text("Run (Warning)")
        }
        
        Button {
            toast = Toast(style: .error, message: "Fatal error, blue screen level.")
        } label: {
            Text("Run (Error)")
        }
    }
    .toastView(toast: $toast)
}
