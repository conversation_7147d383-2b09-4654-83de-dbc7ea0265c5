//
//  CustomLabel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON><PERSON> on 17/06/24.
//

import SwiftUI

import SwiftUI

struct CustomLabel: View {
    @State var text: String
    var image: Image
       
    var body: some View {
        VStack {
            VStack {
                image
                Text(text)
                    .font(.caption2)
            }
        }
    }
}

#Preview {
    CustomLabel(text: "Dash", image: Image(systemName:"list.dash"))
}
