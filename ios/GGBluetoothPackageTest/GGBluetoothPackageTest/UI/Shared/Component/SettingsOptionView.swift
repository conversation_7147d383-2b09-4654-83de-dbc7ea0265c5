//
//  SettingsOptionView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 26/03/25.
//

import SwiftUI

struct SettingsOptionView: View {
    let title: String
    let buttonText: String
    let iconName: String
    let buttonAction: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            HStack {
                Text(title)
                    .foregroundColor(.gray)
                    .font(.headline)
                    .padding(.vertical, 10)
                Spacer()
            }
            
            But<PERSON>(action: buttonAction, label: {
                HStack {
                    Text(buttonText)
                    Spacer()
                    Image(systemName: iconName)
                        .foregroundColor(.black)
                }
                .padding(5)
                .padding(.horizontal, 20)
                .padding(.vertical, 5)
                .foregroundColor(.gray.opacity(0.7))
                .background(Color.gray.opacity(0.1))
                .cornerRadius(5)
                .font(.subheadline)
            })
        }.padding(.top)
    }
}

#Preview {
    SettingsOptionView(
        title: "Update",
        buttonText: "Check for updates",
        iconName: "square.and.arrow.down",
        buttonAction: {
            print("Check for updates tapped")
        }
    )
}




