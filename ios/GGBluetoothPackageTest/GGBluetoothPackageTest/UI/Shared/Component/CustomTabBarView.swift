//
// CustomTabBarView.swift
// GGBluetoothPackageTest
//
// Created by <PERSON> on 09/01/25.
//

import SwiftUI

struct CustomTabBarView: View {
    @Binding var selectedTab: Int
    @Binding var showTabBar: Bool
    let tabItems: [TabItem]
    
    var body: some View {
        HStack {
            ForEach(0..<tabItems.count, id: \.self) { index in
                Button(action: {
                    selectedTab = index
                }) {
                    VStack {
                        tabItems[index].icon
                            .foregroundColor(selectedTab == index ? .blue : .gray)
                            .padding(.bottom, 3)
                        Text(tabItems[index].title)
                            .font(.system(size: 8))
                            .foregroundColor(selectedTab == index ? .blue : .gray)
                    }
                }
                .frame(maxWidth: .infinity)
                .overlay(
                    Rectangle()
                        .frame(height: 2)
                        .foregroundColor(selectedTab == index ? .blue : .clear)
                        .offset(y: 25)
                        .frame(width: tabItems.count == 3 ? 50 : (index == 0 || index == tabItems.count - 1 ? 35 : nil)) 
                )
            }
        }
        .frame(maxWidth: .infinity)
        .background(Color.white)
    }
}

#Preview {
    @Previewable @State var selectedTab = 0
    let tabItems = [
        TabItem(title: "Home", icon: Image(systemName: "house.fill")),
        TabItem(title: "Search", icon: Image(systemName: "magnifyingglass")),
        TabItem(title: "Profile", icon: Image(systemName: "person.fill"))
    ]
    
    CustomTabBarView(selectedTab: $selectedTab, showTabBar: .constant(true), tabItems: tabItems)
}
