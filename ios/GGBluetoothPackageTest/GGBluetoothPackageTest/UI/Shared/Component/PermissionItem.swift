//
//  PermissionItem.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON><PERSON> on 17/06/24.
//

import SwiftUI
import GGBluetoothSwiftPackage

struct PermissionItem: View {
    @ObservedObject var viewModel: PermissionsViewModel
    @State var type: GGPermissionType?
    
    init(type: GGPermissionType) {
        self.type = type
        self.viewModel = PermissionsViewModel(permissionType: type)
    }
    var body: some View {
        HStack(spacing: 8) {
            Circle()
                .fill(viewModel.permissionState ? Color.green : Color.red)
                .frame(width: 12, height: 12)
                .overlay(
                    Circle()
                        .stroke(Color.white, lineWidth: 2)
                )
            Text(getPermissionText(self.type!))
                .font(.body)
        } .onTapGesture {
            Task {
                await viewModel.requestPermission(type!)
            }
            
        }
    }
}

extension PermissionItem {
    func getPermissionText(_ type: GGPermissionType) -> String {
        switch type {
        case .BLUETOOTH:
            return "Bluetooth Authorization"
        case .BLUETOOTH_SWITCH:
            return "Bluetooth Switch"
        case .LOCATION:
            return "Location Authorization"
        case .LOCATION_SWITCH:
            return "Location Switch"
        case .CAMERA:
            return "Camera"
        case .NOTIFICATION:
            return "Notifications"
        default:
            return "Unkown"
        }
    }
}

#Preview {
    PermissionItem(type: .BLUETOOTH)
}
