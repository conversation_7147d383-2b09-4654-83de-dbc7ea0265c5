//
//  SafariView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/12/24.
//

import SwiftUI
import SafariServices

struct SafariView: UIViewControllerRepresentable {
    let url: URL
    
    func makeUIViewController(context: Context) -> SFSafariViewController {
        return SFSafariViewController(url: url)
    }
    
    func updateUIViewController(_ uiViewController: SFSafariViewController, context: Context) {
        // No update logic needed here
    }
}

#Preview {
    SafariView(url: URL(string: "https://greatergoods.com/service")!)
}

