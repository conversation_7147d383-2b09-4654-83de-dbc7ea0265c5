//
//  DetailView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 13/02/25.
//

import SwiftUI

struct DetailView: View {
    let detail: (String,String, String)
    let isEven: Bool
    let symbols: [String]
    
    var body: some View {
        HStack {
            LabelValuePairView(
                key: Text(detail.0.capitalized)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.gray),
                value: HStack{
                    Text(detail.1)
                        .font(.subheadline)
                    getImage(for: detail.2, in: symbols)
                }
            )
            Spacer()
        }
        .font(.subheadline)
        .frame(maxWidth: .infinity, maxHeight: 20, alignment: .leading)
        .padding()
        .background(isEven ? Color.blue.opacity(0.2) : Color.gray.opacity(0.2))
        .cornerRadius(5)
    }
    
    func getImage(for symbol: String, in symbols: [String]) -> Image {
        return symbols.contains(symbol)
        ? Image(systemName: symbol)
        : Image(symbol)
    }
}
