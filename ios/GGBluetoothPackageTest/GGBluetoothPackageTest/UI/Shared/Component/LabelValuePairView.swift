//
//  LabelValuePairView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 19/12/24.
//

import SwiftUI

struct LabelValuePairView<KeyContent: View, ValueContent: View>: View {
    let key: KeyContent
    let value: ValueContent
    
    init(key: KeyContent, value: ValueContent) {
        self.key = key
        self.value = value
    }
    
    var body: some View {
        HStack {
            key
            Spacer()
            value
                .font(.subheadline)
                .foregroundColor(.gray)
        }
    }
}


#Preview {
    LabelValuePairView(key: Text("Scale Name"), value: Text("gG Scale"))
        .padding()
}

