//
//  ContentWrapperView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 14/02/25.
//

import SwiftUI

struct ContentWrapperView<Content: View>: View {
    enum TitleType {
        case text(String)
        case view(AnyView)
    }
    
    let title: TitleType
    let content: Content
    
    init(title: TitleType, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack {
            content
            Spacer()
        }
        .toolbar {
            ToolbarItem(placement: .principal) {
                switch title {
                case .text(let titleString):
                    Text(titleString.uppercased())
                        .font(.headline)
                        .foregroundColor(.primary)
                case .view(let titleView):
                    titleView
                }
            }
        }
        .navigationBarTitleDisplayMode(.inline)
    }
}

#Preview {
    ContentWrapperView(title: .text("Sample Title")) {
        Text("Sample Content")
    }
}
