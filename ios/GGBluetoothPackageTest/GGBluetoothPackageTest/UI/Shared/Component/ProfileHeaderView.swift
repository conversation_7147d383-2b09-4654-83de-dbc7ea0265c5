//
//  ProfileHeaderView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 29/01/25.
//

import SwiftUI

struct ProfileHeaderView: View {
    var name: String
    var email: String
    var profileImage: String
    
    var body: some View {
        VStack {
            Image(profileImage)
                .resizable()
                .scaledToFit()
                .frame(width: 60, height: 60)
            
            Text(name)
                .font(.title)
                .fontWeight(.bold)
                .padding(.top, 10)
                .padding(.bottom, 5)
            
            Text(email)
                .foregroundColor(.gray)
                .padding(.bottom, 10)
                .textSelection(.disabled)
        }
    }
}

struct ProfileHeaderView_Previews: PreviewProvider {
    static var previews: some View {
        ProfileHeaderView(
            name: "<PERSON>",
            email: "<EMAIL>",
            profileImage: AppAssets.profile
        )
    }
}
