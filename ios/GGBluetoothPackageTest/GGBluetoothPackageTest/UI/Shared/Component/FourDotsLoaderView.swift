//
//  FourDotsLoaderView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 24/01/25.
//

import SwiftUI

struct FourDotsLoaderView: View {
    @State var loading = false
    var color: Color = .blue
    
    var body: some View {
        HStack(spacing: 7) {
            ForEach(0..<4) { index in
                Circle()
                    .fill(color) 
                    .frame(width: 8, height: 8)
                    .scaleEffect(loading ? 1.5 : 0.5)
                    .animation(.easeInOut(duration: 0.8).repeatForever(autoreverses: true).delay(Double(index) * 0.2), value: loading)
            }
        }
        .onAppear() {
            self.loading = true
        }
    }
}

#Preview {
    FourDotsLoaderView()
}
