//
//  CustomMenuButton.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 25/02/25.
//

import SwiftUI

struct CustomMenuButton: View {
    var title: String
    var width: CGFloat = 70
    var height: CGFloat = 40
    var options: [MenuOption]
    var foregroundColor: Color = .white
    
    struct MenuOption {
        let label: String
        let action: () -> Void
    }
    
    var body: some View {
        Menu {
            ForEach(options.indices, id: \.self) { index in
                Button(action: options[index].action) {
                    Text(options[index].label)
                }
            }
        } label: {
            Text(title)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(foregroundColor)
                .padding()
                .frame(width: width, height: height)
        }
        .buttonStyle(.borderless)
    }
}
