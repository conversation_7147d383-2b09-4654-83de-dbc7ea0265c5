//
//  EditUsernameView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 03/04/25.
//

import SwiftUI

struct EditUsernameView: View {
    @Binding var isPresented: Bool
    @Binding var newUserName: String
    var onSave: () -> Void
    var onCancel: () -> Void?
    let parentView: EditUserNameParentView
    var isDuplicate: Bool { parentView == .DuplicateUserModel }
    var lang = EditUsernameStrings.self
    var commonLang = CommonStrings.self
    
    var body: some View {
        VStack {
            if isDuplicate {
                Spacer().frame(maxHeight: 20)
            }
            
            Group {
                if parentView == .UsersTab {
                    ModalContainer {
                        content
                    }
                } else {
                    content
                }
            }
            
            if isDuplicate {
                actionButtons
                    .padding(.top,30)
                    .padding(.horizontal)
                
                Spacer()
            }
        }
        .ignoresSafeArea(.keyboard)
        .onTapGesture {
            hideKeyboard()
        }
    }
    
    @ViewBuilder
    private var content: some View {
        Text(parentView == .UsersTab ? lang.editYourUsername : lang.editUsername)
            .font(parentView == .UsersTab ? .headline : .title2)
            .fontWeight(parentView == .UsersTab ? .semibold : .bold)
            .padding(.top, 20)
            .padding(.bottom, 20)
        
        TextField(lang.usernamePlaceholder, text: $newUserName)
            .padding()
            .background(Color.clear)
            .cornerRadius(isDuplicate ? 30 : 5)
            .overlay(
                RoundedRectangle(cornerRadius: isDuplicate ? 30 : 5)
                    .stroke(Color.gray, lineWidth: 1)
            )
            .padding(.horizontal, 20)
        
        if isDuplicate {
            Text(lang.duplicateUserMessage)
                .foregroundColor(.red)
                .font(.caption)
                .padding(.top, 5)
        }
        
        if parentView == .UsersTab {
            actionButtons
                .padding(.bottom, 20)
        } else {
            Image(AppAssets.scaleDisplayName)
                .resizable()
                .scaledToFit()
                .frame(width: UIScreen.main.bounds.width * 0.85, height: UIScreen.main.bounds.width * 0.85)
                .padding()
        }
    }
    
    private var actionButtons: some View {
        HStack {
            Button(commonLang.cancel.uppercased()) {
                if isDuplicate{
                    onCancel()
                }else{
                    isPresented = false
                }
                
            }
            .padding()
            .foregroundColor(.gray)
            
            if parentView == .DuplicateUserModel {
                Spacer()
                    .frame(maxWidth: UIScreen.main.bounds.width * 0.33)
            }
            
            if isDuplicate {
                PrimaryRoundedButtonView(title: commonLang.save) {
                    onSave()
                    isPresented = false
                }
            } else {
                Button(commonLang.save.uppercased()) {
                    onSave()
                    isPresented = false
                }
                .padding()
            }
        }
        .fontWeight(.semibold)
    }
}

#Preview {
    EditUsernameView(
        isPresented: .constant(true),
        newUserName: .constant("Lp"),
        onSave: {}, onCancel: {},
        parentView: .DuplicateUserModel
    )
}
