//
//  DeviceSelectionView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 05/02/25.
//

import SwiftUI

struct DeviceSelectionView<LocalView: View, APIView: View>: View {
    @Binding var selection: Int
    let localView: LocalView
    let apiView: APIView

    var body: some View {
            VStack(spacing: 0) {
                Picker("Device Type", selection: $selection) {
                    Text("local".capitalized).tag(0)
                    Text("app".capitalized).tag(1)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding(.horizontal, 50)
                .padding(.bottom,15)
                if selection == 0 {
                    localView
                } else {
                    apiView
                }
            }
    }
}

struct DeviceSelectionView_Previews: PreviewProvider {
    @State static var selection = 1

    static var previews: some View {
        DeviceSelectionView(
            selection: $selection,
            localView: Text("Local View Placeholder")
                .frame(maxWidth: .infinity, maxHeight: .infinity),
            apiView: Text("API View Placeholder")
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        )
    }
}
