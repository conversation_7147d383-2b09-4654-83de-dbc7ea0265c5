//
//  PrimaryRoundedButtonView.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 21/04/25.
//

import SwiftUI

struct PrimaryRoundedButtonView: View {
    let title: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title.uppercased())
                .fontWeight(.semibold)
                .padding(.vertical, 10)
                .padding(.horizontal, 20)
                .foregroundColor(.white)
                .background(Color.blue)
                .cornerRadius(30)
        }
    }
}

#Preview {
    PrimaryRoundedButtonView(title: "But<PERSON>", action: {})
}
