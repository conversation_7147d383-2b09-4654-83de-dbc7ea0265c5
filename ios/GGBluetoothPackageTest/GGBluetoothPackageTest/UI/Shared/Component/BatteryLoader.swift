//
//  BatteryLoader.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 26/02/25.
//

import SwiftUI

struct BatteryLoader: View {
    @State private var batteryLevel: Int = 0
    private let batteryLevels = [0, 25, 50, 75, 100, 75, 50, 25, 0]

    var body: some View {
        Image(systemName: "battery.\(batteryLevels[batteryLevel])")
            .font(.headline)
            .foregroundColor(.yellow)
            .onAppear {
                startBatteryAnimation()
            }
    }

    private func startBatteryAnimation() {
        Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { _ in
            if batteryLevel < batteryLevels.count - 1 {
                batteryLevel += 1
            } else {
                batteryLevel = 0
            }
        }
    }
}
