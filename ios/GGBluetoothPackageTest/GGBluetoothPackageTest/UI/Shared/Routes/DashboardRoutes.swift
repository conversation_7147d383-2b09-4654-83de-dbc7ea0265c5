//
//  DashboardRoutes.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/11/24.
//

import SwiftUI
import GGBluetoothSwiftPackage

enum DashboardRoutes: Routable, Equatable {
    case scan
    case apps
    case pairedDevice(AppCategory?)
    case history
    case settings
    case deviceDetail(AppCategory?, GGBTDevice, DeviceDetailParentView, String?)
    case userProfileUpdation
    case devicesInformation(DeviceInfo?)
    case login(AppCategory?, LoginPageParentView)
    case accountDashboard(AppCategory?, CombinedLoginData?)
    case wifiMacAddress(device: GGBTDevice?, wifiMacAddress: String)
    case wifiCredentials(GGBTDevice, selectedSSID: String, AppCategory)
    case wifiSetup(device: GGBTDevice,AppCategory)
    case dashboardMetricsMainView(GGBTDevice, AppCategory)
    case customizeAppDashboard
    case displayMetricsView(GGBTDevice, AppCategory)
    case r4ScaleUserView(device: GGBTDevice, selectedCategory: AppCategory?, ParentViewType: UserTabParentView)
    case scaleModesView(GGBTDevice, AppCategory)
    case duplicateUserModal(GGBTUser, onEditName: () -> Void, onRestore: () -> Void)
    case editUsernameView(isPresented: Binding<Bool>, newUserName: Binding<String>, onSave: () -> Void,  onCancel: () -> Void, parentView: EditUserNameParentView)
    case graphsView(GGBTDevice, GraphParentViewType, AppCategory, Binding<String>)
    
    var body: some View {
        switch self {
        case .apps:
            AppsView()
        case .pairedDevice(let category):
            AppCategoryDevicesView(category: category ?? .weightGurus)
        case .deviceDetail(let category, let device, let parentView, let sku):
            DeviceDetailView(category: category ?? .weightGurus, device: device, sku: sku ?? "", parentView: parentView)
        case .userProfileUpdation:
            UserProfileUpdationView()
        case .devicesInformation(let device):
            DeviceInformationView(device: device ?? DeviceInfo(
                name: "Default",
                sku: "0000",
                image: "defaultImage",
                protocolType: "",
                manufacturer: "Wellland",
                app: .sage,
                errorcodes: [ErrorCode(code: "E00", description: "Default Error Code")]
            ))
        case .login(let category, let parentView):
            LoginPageView(category: category ?? .weightGurus, parentView: parentView)
        case .accountDashboard(let category, let loginData):
            UserDashboardView(appCategory: category ?? .weightGurus, loginData: loginData)
        case .wifiMacAddress(let device, let wifiMacAddress):
            WifiMacAddressView(device: device, wifiMacAddress: wifiMacAddress)
        case .wifiCredentials(let device, let selectedSSID, let category):
            WifiCredentialsView(selectedSSID: selectedSSID, device: device, category: category)
        case .wifiSetup(let device, let category):
            WifiSetupView(device: device, category: category)
        case .dashboardMetricsMainView(let device, let category):
            DashBoardMetricsMainView(device: device, category: category)
        case .customizeAppDashboard:
            CustomizeAppDashboardView()
        case .displayMetricsView(let device,  let category):
            DisplayMetricsView(device: device, category: category)
        case .r4ScaleUserView(let device, let selectedCategory, let parentView):
            R4ScaleUserView(device: device, selectedCategory: selectedCategory, parentView: parentView)
        case .scaleModesView(let device, let category):
            ScaleModesView(device: device, category: category)
        case .duplicateUserModal(let user, let onEditName, let onRestore):
            DuplicateUserModalView(user: user, onEditName: onEditName, onRestore: onRestore)
        case .editUsernameView(let isPresented, let newUserName, let onSave, let onCancel, let parentView):
            EditUsernameView(isPresented: isPresented, newUserName: newUserName, onSave: onSave, onCancel: onCancel, parentView: parentView)
        case .graphsView(let device, let parentView, let category, let selectedSKU):
            GraphView(parentView: parentView, device: device, appCategory: category, selectedSKU: selectedSKU)
        default:
            EmptyView()
        }
    }
    
    static func == (lhs: DashboardRoutes, rhs: DashboardRoutes) -> Bool {
        switch (lhs, rhs) {
        case (.scan, .scan),
            (.apps, .apps),
            (.history, .history),
            (.settings, .settings),
            (.userProfileUpdation, .userProfileUpdation):
            return true
        case let (.pairedDevice(lhsCategory), .pairedDevice(rhsCategory)):
            return lhsCategory == rhsCategory
        case let (.deviceDetail(lhsCategory, lhsDevice, lhsParentView, lhsSku), .deviceDetail(rhsCategory, rhsDevice, rhsParentView, rhsSku)):
            return lhsCategory == rhsCategory && lhsDevice == rhsDevice && lhsParentView == rhsParentView && lhsSku == rhsSku
        case let (.devicesInformation(lhsDevice), .devicesInformation(rhsDevice)):
            return lhsDevice == rhsDevice
        case let (.login(lhsCategory, lhsParentView), .login(rhsCategory, rhsParentView)):
            return lhsCategory == rhsCategory && lhsParentView == rhsParentView
        case let (.wifiMacAddress(lhsDevice, lhsWifiMacAddress), .wifiMacAddress(rhsDevice, rhsWifiMacAddress)):
            return lhsDevice == rhsDevice && lhsWifiMacAddress == rhsWifiMacAddress
        case let (.wifiCredentials(lhsDevice, lhsSelectedSSID, lhsCategory), .wifiCredentials(rhsDevice, rhsSelectedSSID, rhsCategory)):
            return lhsDevice == rhsDevice && lhsSelectedSSID == rhsSelectedSSID && lhsCategory == rhsCategory
        case let (.wifiSetup(lhsDevice, lhsCategory), .wifiSetup(rhsDevice, rhsCategory)):
            return lhsDevice == rhsDevice && lhsCategory == rhsCategory
        case let (.dashboardMetricsMainView(lhsDevice, lhsCategory), .dashboardMetricsMainView(rhsDevice, rhsCategory)):
            return lhsDevice == rhsDevice && lhsCategory == rhsCategory
        case let (.r4ScaleUserView(lhsDevice, lhsCategory, lhsParentView), .r4ScaleUserView(rhsDevice, rhsCategory, rhsParentView)):
            return lhsDevice == rhsDevice && lhsCategory == rhsCategory && lhsParentView == rhsParentView
        case let (.scaleModesView(lhsDevice, lhsCategory), .scaleModesView(rhsDevice, rhsCategory)):
            return lhsDevice == rhsDevice && lhsCategory == rhsCategory
        case let (.editUsernameView(_, _, _,_, lhsParentView),
                  .editUsernameView(_, _, _,_, rhsParentView)):
            return lhsParentView == rhsParentView
        default:
            return false
        }
    }
    
    func hash(into hasher: inout Hasher) {
        switch self {
        case .scan:
            hasher.combine("scan")
        case .apps:
            hasher.combine("apps")
        case .pairedDevice(let category):
            hasher.combine("pairedDevice")
            hasher.combine(category)
        case .history:
            hasher.combine("history")
        case .settings:
            hasher.combine("settings")
        case .deviceDetail(let category, let device, let parentView, let sku):
            hasher.combine("deviceDetail")
            hasher.combine(category)
            hasher.combine(device)
            hasher.combine(parentView)
            hasher.combine(sku)
        case .userProfileUpdation:
            hasher.combine("userProfileUpdation")
        case .devicesInformation(let device):
            hasher.combine("devicesInformation")
            hasher.combine(device)
        case .login(let category, let parentView):
            hasher.combine("login")
            hasher.combine(category)
            hasher.combine(parentView)
        case .accountDashboard(let category, let loginData):
            hasher.combine("accountDashboard")
            hasher.combine(category)
        case .wifiMacAddress(let device, let wifiMacAddress):
            hasher.combine("wifiMacAddress")
            hasher.combine(device)
            hasher.combine(wifiMacAddress)
        case .wifiCredentials(let device, let selectedSSID, let category):
            hasher.combine("wifiCredentials")
            hasher.combine(device)
            hasher.combine(selectedSSID)
            hasher.combine(category)
        case .wifiSetup(let device, let category):
            hasher.combine("wifiSetup")
            hasher.combine(device)
            hasher.combine(category)
        case .dashboardMetricsMainView(let device, let category):
            hasher.combine("dashboardMetricsMainView")
            hasher.combine(device)
            hasher.combine(category)
        case .customizeAppDashboard:
            hasher.combine("customizeAppDashboard")
        case .displayMetricsView(let device, let category):
            hasher.combine("displayMetricsView")
            hasher.combine(device)
            hasher.combine(category)
        case .r4ScaleUserView(let device, let selectedCategory, let parentView):
            hasher.combine("r4ScaleUserView")
            hasher.combine(device)
            hasher.combine(selectedCategory)
            hasher.combine(parentView)
        case .scaleModesView(let device, let category):
            hasher.combine("scaleModesView")
            hasher.combine(device)
            hasher.combine(category)
        case .duplicateUserModal(let user, _, _):
            hasher.combine("duplicateUserModal")
            hasher.combine(user)
        case .editUsernameView(_, _, _,_, let parentView):
            hasher.combine("editUsernameView")
            hasher.combine(parentView)
        case .graphsView:
            hasher.combine("GraphsView")
        }
    }
}
