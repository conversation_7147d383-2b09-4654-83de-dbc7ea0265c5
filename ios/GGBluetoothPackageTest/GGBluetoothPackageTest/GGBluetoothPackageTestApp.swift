//
//  GGBluetoothPackageTestApp.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON><PERSON> on 17/06/24.
//

import SwiftUI

@main
struct GGBluetoothPackageTestApp: App {
    let persistenceController = PersistenceController.shared

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
        }
    }
}
