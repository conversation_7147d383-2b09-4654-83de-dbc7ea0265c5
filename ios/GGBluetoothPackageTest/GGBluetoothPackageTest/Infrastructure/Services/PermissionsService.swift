//
//  PermissionsService.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 21/03/25.
//

import Foundation
import GGBluetoothSwiftPackage
import Combine

class PermissionsService: ObservableObject {
    
    @Injector var bluetoothService: BluetoothService
    private var cancellables: Set<AnyCancellable> = []
    
    func fetchInitialPermissions(for category: AppCategory, completion: @escaping (Bool) -> Void) {
        bluetoothService.fetchInitialPermissions(selectedCategory: category)
        
        bluetoothService.permissionStatusSubject
            .sink { permissions in
                let isBluetoothEnabled = permissions[.BLUETOOTH] == .ENABLED
                let isBluetoothSwitchEnabled = permissions[.BLUETOOTH_SWITCH] == .ENABLED
                let isEnabled = isBluetoothEnabled && isBluetoothSwitchEnabled
                completion(isEnabled)
            }
            .store(in: &cancellables)
    }
}
