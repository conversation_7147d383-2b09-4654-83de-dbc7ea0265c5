//
//  ConnectionStateManager.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 29/11/24.
//

import Foundation
import Combine
import GGBluetoothSwiftPackage

class DeviceConnectionState: ObservableObject {
    static let shared   = DeviceConnectionState()
    @Published var deviceConnectionStates: [String: Bool] = [:]
    
    func getDeviceConnectionState(deviceId: String) -> Bool {
        return deviceConnectionStates[deviceId] ?? false
    }
    
    func updateDeviceConnectionState(deviceId: String, isConnected: Bool) {
        let currentState = getDeviceConnectionState(deviceId: deviceId)

        if currentState != isConnected {
            deviceConnectionStates[deviceId] = isConnected
            objectWillChange.send()
        }
    }
}
