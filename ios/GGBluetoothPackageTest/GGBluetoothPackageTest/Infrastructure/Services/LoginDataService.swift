//
//  LoginDataService.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 14/02/25.
//

import Foundation
import Alamofire

class LoginDataService : ObservableObject {
    static let shared: LoginDataService = LoginDataService()
    
    init() {}
    
    func fetchAccountDetails(for category: AppCategory, accessToken: String, completion: @escaping (Result<[Any], Error>) -> Void) {
        let detailsURL: String
        let headers: HTTPHeaders
        
        switch category {
        case .balanceHealth:
            detailsURL = category.apiUrl() + "monitor"
            headers = ["x-access-token": accessToken]
        case .smartBaby:
            detailsURL = category.apiUrl() + "baby"
            headers = ["Authorization": "Bearer \(accessToken)"]
        default:
            detailsURL = category.apiUrl() + "paired-scale/"
            headers = ["Authorization": "Bearer \(accessToken)"]
        }
        
        AF.request(detailsURL, method: .get, headers: headers)
            .validate()
            .responseData { response in
                switch response.result {
                case .success(let data):
                    let result = self.mapAccountResponse(data: data, for: category)
                    switch result {
                    case .success(let accountDetails):
                        completion(.success(accountDetails))
                    case .failure(let error):
                        completion(.failure(error))
                    }
                case .failure(let error):
                    completion(.failure(error))
                }
            }
    }
    
    func getMeasurements(for category: AppCategory, accessToken: String, completion: @escaping ([[String: Any]]?) -> Void) {
        var allOperations: [[String: Any]] = []
        let dispatchGroup = DispatchGroup()
        
        if category == .smartBaby {
            fetchSmartBabyMeasurements(accessToken: accessToken) { babyMeasurements in
                guard let babyMeasurements = babyMeasurements else {
                    completion(nil)
                    return
                }
                
                for baby in babyMeasurements {
                    dispatchGroup.enter()
                    self.fetchSmartBabyOperations(accessToken: accessToken, babyId: baby.id) { operationsResponse in
                        if let operationsResponse = operationsResponse, !operationsResponse.operations.isEmpty {
                            let operationsDict = operationsResponse.operations.map { operation in
                                return [
                                    "id": operation.id,
                                    "entryId": operation.entryId,
                                    "babyId": operation.babyId,
                                    "babyLengthMillimeters": operation.babyLengthMillimeters as Any,
                                    "babyWeightDecigrams": operation.babyWeightDecigrams as Any,
                                    "diaperType": operation.diaperType as Any,
                                    "entryNote": operation.entryNote as Any,
                                    "entryTimestamp": operation.entryTimestamp,
                                    "entryType": operation.entryType,
                                    "feedingTimeSecondsLeft": operation.feedingTimeSecondsLeft as Any,
                                    "feedingTimeSecondsRight": operation.feedingTimeSecondsRight as Any,
                                    "feedingMilliliters": operation.feedingMilliliters as Any,
                                    "operationType": operation.operationType,
                                    "photo": operation.photo as Any,
                                    "source": operation.source ?? [],
                                    "serverTimestamp": operation.serverTimestamp,
                                    "sleepTimeMinutes": operation.sleepTimeMinutes as Any
                                ] as [String: Any]
                            }
                            allOperations.append(contentsOf: operationsDict)
                        }
                        dispatchGroup.leave()
                    }
                }
                
                dispatchGroup.notify(queue: .main) {
                    completion(allOperations)
                }
            }
            return
        }
        
        let url = URL(string: "\(category.apiUrl())operation")!
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        switch category {
        case .weightGurus:
            request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        case .balanceHealth:
            request.setValue(accessToken, forHTTPHeaderField: "x-access-token")
        default:
            completion(nil)
            return
        }
        
        dispatchGroup.enter()
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            defer { dispatchGroup.leave() }
            
            if let error = error {
                completion(nil)
                return
            }
            
            guard let data = data else {
                completion(nil)
                return
            }
            
            do {
                if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                   let operations = json["operations"] as? [[String: Any]] {
                    allOperations.append(contentsOf: operations)
                } else {
                    completion(nil)
                }
            } catch {
                completion(nil)
            }
        }
        
        task.resume()
        
        dispatchGroup.notify(queue: .main) {
            completion(allOperations)
        }
    }
    
    func mapAccountResponse(data: Data, for category: AppCategory) -> Result<[Any], Error> {
        do {
            let decoder = JSONDecoder()
            
            switch category {
            case .weightGurus:
                let accountDetails = try decoder.decode([WeightGurusAccountDetailsResponse].self, from: data)
                return .success(accountDetails as [Any])
            case .balanceHealth:
                let accountDetailsResponse = try decoder.decode(BalanceHealthAccountDetailsResponse.self, from: data)
                return .success(accountDetailsResponse.data)
            case .smartBaby:
                let accountDetails = try decoder.decode([SmartBabyAccountResponse].self, from: data)
                return .success(accountDetails as [Any])
            default:
                let error = NSError(domain: "AccountService", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unknown app category."])
                return .failure(error)
            }
        } catch {
            return .failure(error)
        }
    }
    
    func fetchSmartBabyMeasurements(accessToken: String, completion: @escaping ([SmartBabyAccountResponse]?) -> Void) {
        guard let url = URL(string: "\(AppCategory.smartBaby.apiUrl())baby") else {
            completion(nil)
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(nil)
                return
            }
            
            if let httpResponse = response as? HTTPURLResponse {
                if httpResponse.statusCode != 200 {
                    completion(nil)
                    return
                }
            }
            
            guard let data = data else {
                completion(nil)
                return
            }
            
            do {
                let decoder = JSONDecoder()
                let babyData = try decoder.decode([SmartBabyAccountResponse].self, from: data)
                completion(babyData)
            } catch {
                completion(nil)
            }
        }
        
        task.resume()
    }
    
    func fetchSmartBabyOperations(accessToken: String, babyId: String, startTimestamp: String? = nil, completion: @escaping (SmartBabyOperationsResponse?) -> Void) {
        var urlString = "\(AppCategory.smartBaby.apiUrl())operation/\(babyId)"
        if let timestamp = startTimestamp {
            urlString.append("?start=\(timestamp)")
        }
        
        guard let url = URL(string: urlString) else {
            completion(nil)
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(nil)
                return
            }
            
            if let httpResponse = response as? HTTPURLResponse {
                if httpResponse.statusCode != 200 {
                    completion(nil)
                    return
                }
            }
            
            guard let data = data else {
                completion(nil)
                return
            }
            
            let decoder = JSONDecoder()
            decoder.keyDecodingStrategy = .convertFromSnakeCase
            
            do {
                let response = try decoder.decode(SmartBabyOperationsResponse.self, from: data)
                completion(response)
            } catch {
                completion(nil)
            }
        }
        
        task.resume()
    }
}
