//
//  ToastService.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 09/01/25.
//

import Foundation
import Combine

class ToastService: ObservableObject {
    static let shared = ToastService()
    
    @Published var toast: Toast?
    
    init() {}
    
    func presentToast(with message: String, style: ToastStyle = .success, duration: Double = 3.0) {
        let toast = Toast(style: style, message: message, duration: duration)
        self.toast = toast
    }
}
