//
//  MeasurementEntryService.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 21/01/25.
//

import Foundation
import CoreData
import Combine
import GGBluetoothSwiftPackage
import SwiftUICore

class MeasurementEntryService: ObservableObject {
    private let repository: MeasurementEntryRepository
    private let container: NSPersistentContainer
    private let logger: AppLogger = AppLogger(category: String(describing: MeasurementEntryService.self))
    private var cancellables = Set<AnyCancellable>()
    @Injector var deviceService: DeviceService
    @Injector var btService: BluetoothService
    @Injector var userProfileService: UserProfileUpdationService
    @Injector var loginService: LoginService
    @Published var stableMeasurement: Entry?
    @Published var stableWeightEntry: Entry?
    @EnvironmentObject var networkMonitor: NetworkMonitor
    var measurementSubject = CurrentValueSubject<[Entry], Never>([])
    lazy var multiEntries: [Entry] = self.measurementSubject.value
    private var shownToasts = Set<UUID>()
    
    init() {
        container = NSPersistentContainer(name: "GGBluetoothPackageTest")
        self.repository = MeasurementEntryRepository(viewContext: container.viewContext)
        container.loadPersistentStores { [weak self] description, error in
            guard let self = self else { return }
            if let error = error {
                let nsError = NSError(domain: String(describing: MeasurementEntryService.self), code: 1, userInfo: [NSLocalizedDescriptionKey: "Error loading Core Data. \(error.localizedDescription)"])
                self.logger.error(error: nsError, function: #function, line: #line)
            } else {
                self.logger.info(info: "MeasurementEntryData loaded successfully.", function: #function, line: #line)
            }
        }
        
        btService.stableMeasurementSubject
            .debounce(for: .seconds(4), scheduler: DispatchQueue.main)
            .sink { [weak self] entry in
                guard let self = self else { return }
                let deviceInfo = self.getDeviceDetails(for: entry.broadcastId)
                self.stableMeasurement = entry
                self.stableWeightEntry = entry
                if entry.measurementType == .bloodPressure || entry.measurementType == .weight {
                    self.sendEntriesToApi(entry: entry)
                }
                let operation = self.createOperation(from: entry, appCategory: deviceInfo.appCategory, deviceSKU: deviceInfo.deviceSKU)
                self.saveMeasurementEntry(
                    from: operation ?? Operation(
                        appCategory: .all,
                        bgm: nil,
                        bmi: nil,
                        bodyFat: nil,
                        boneMass: nil,
                        broadcastId: "",
                        broadcastIdString: "",
                        bmr: nil,
                        deviceSKU: "",
                        diastolic: nil,
                        entryTimestamp: Date(),
                        id: UUID(),
                        impedance: nil,
                        meanPressure: nil,
                        metabolicAge: nil,
                        muscleMass: nil,
                        operationType: .create,
                        proteinPercent: nil,
                        pulse: nil,
                        protocolType: nil,
                        serverTimestamp: nil,
                        skeletalMusclePercent: nil,
                        source: nil,
                        spo: nil,
                        subcutaneousFatPercent: nil,
                        systolic: nil,
                        temperature: nil,
                        visceralFatLevel: nil,
                        water: nil,
                        weight: nil,
                        weightInKg: nil,
                        measurementUnit: nil
                    ),
                    appCategory: deviceInfo.appCategory,
                    broadcastId: entry.broadcastId,
                    deviceSKU: deviceInfo.deviceSKU
                )
            }
            .store(in: &cancellables)
        
        btService.measurementSubject
            .sink { [weak self] entries in
                guard let self = self else { return }
                self.multiEntries = entries
                entries.forEach { entry in
                    if entry.measurementType == .bloodPressure || entry.measurementType == .weight {
                        self.sendEntriesToApi(entry: entry)
                    }
                    let deviceInfo = self.getDeviceDetails(for: entry.broadcastId)
                    let operation = self.createOperation(from: entry, appCategory: deviceInfo.appCategory, deviceSKU: deviceInfo.deviceSKU)
                    self.saveMeasurementEntry(
                        from: operation ?? Operation(
                            appCategory: .all,
                            bgm: nil,
                            bmi: nil,
                            bodyFat: nil,
                            boneMass: nil,
                            broadcastId: "",
                            broadcastIdString: "",
                            bmr: nil,
                            deviceSKU: "",
                            diastolic: nil,
                            entryTimestamp: Date(),
                            id: UUID(),
                            impedance: nil,
                            meanPressure: nil,
                            metabolicAge: nil,
                            muscleMass: nil,
                            operationType: .create,
                            proteinPercent: nil,
                            pulse: nil,
                            protocolType: nil,
                            serverTimestamp: nil,
                            skeletalMusclePercent: nil,
                            source: nil,
                            spo: nil,
                            subcutaneousFatPercent: nil,
                            systolic: nil,
                            temperature: nil,
                            visceralFatLevel: nil,
                            water: nil,
                            weight: nil,
                            weightInKg: nil,
                            measurementUnit: nil
                        ),
                        appCategory: deviceInfo.appCategory,
                        broadcastId: entry.broadcastId,
                        deviceSKU: deviceInfo.deviceSKU
                    )
                }
            }
            .store(in: &cancellables)
    }
    
    func createOperation(from entry: Entry, appCategory: String?, deviceSKU: String?) -> Operation? {
        
        guard !entry.broadcastId.isEmpty else {
            return nil
        }
        
        let timestamp: Date
        if let entryTimestamp = entry.entryTimestamp, entryTimestamp.timeIntervalSince1970 > 0 {
            if appCategory == AppCategory.smartBaby.rawValue {
                timestamp = (entryTimestamp > Date()) ? Date() : entryTimestamp
            } else {
                timestamp = entryTimestamp
            }
        } else {
            if appCategory == AppCategory.smartBaby.rawValue {
                timestamp = Date()
            } else {
                timestamp = entry.entryTimestamp ?? Date()
            }
        }
        
        var bmi = entry.bmi
        if bmi == 0.0 {
            bmi = Float(calculateBMI(for: Double(entry.weightInKg ?? 0.0)) ?? 0.0)
        }
        
        let operation = Operation(
            appCategory: AppCategory(rawValue: appCategory ?? "all") ?? .all,
            bgm: Int(entry.bgm ?? 0),
            bmi: bmi,
            bodyFat: Int(entry.bodyFat ?? 0),
            boneMass: Int(entry.boneMass ?? 0),
            broadcastId: entry.broadcastId,
            broadcastIdString: entry.broadcastId,
            bmr: Int(entry.bmr ?? 0),
            deviceSKU: deviceSKU ?? "",
            diastolic: Int(entry.diastolic ?? 0),
            entryTimestamp: timestamp,
            id: UUID(),
            impedance: Int(entry.impedance ?? 0),
            meanPressure: Int(entry.meanPressure ?? 0),
            metabolicAge: Int(entry.metabolicAge ?? 0),
            muscleMass: Int(entry.muscleMass ?? 0),
            operationType: .create,
            proteinPercent: Int(entry.proteinPercent ?? 0),
            pulse: Int(entry.pulse ?? 0),
            protocolType: "",
            serverTimestamp: nil,
            skeletalMusclePercent: Int(entry.skeletalMusclePercent ?? 0),
            source: Source(rawValue: entry.source ?? "unknown") ?? .manual,
            spo: Int(entry.spo ?? 0),
            subcutaneousFatPercent: Int(entry.subcutaneousFatPercent ?? 0),
            systolic: Int(entry.systolic ?? 0),
            temperature: Float(entry.temperature ?? 0),
            visceralFatLevel: Int(entry.visceralFatLevel ?? 0),
            water: Int(entry.water ?? 0),
            weight: Float(entry.displayWeight ?? 0),
            weightInKg: Float(entry.weightInKg ?? 0),
            measurementUnit: Unit(rawValue: entry.unit ?? "unknown") ?? .unknown,
            pulseAmplitudeIndex: Float(entry.pulseAmplitudeIndex ?? 0)
        )
        return operation
    }
    
    func saveMeasurementEntry(from operation: Operation, appCategory: String?, broadcastId: String?, deviceSKU: String?) -> Bool {
        guard !operation.broadcastId.isEmpty else { return false }
        if shownToasts.contains(operation.id) {
            return false
        }
        let success = repository.saveMeasurementEntry(operation, appCategory: appCategory, broadcastId: broadcastId ?? "", deviceSKU: deviceSKU ?? "")
        if success {
            shownToasts.insert(operation.id)
            ToastService.shared.presentToast(with: ToastMessageStrings.entryAddedSuccessfully)
            return true
        }
        return false
    }
    
    func clearShownToasts() {
        shownToasts.removeAll()
    }
    
    func getDeviceDetails(for broadcastId: String) -> (appCategory: String?, deviceSKU: String?) {
        guard !broadcastId.isEmpty else {
            return (nil, nil)
        }
        
        let deviceInfo = deviceService.fetchDeviceAppCategory(by: broadcastId)
        let deviceSKU = getSKU(deviceInfo.deviceName ?? "")
        return (deviceInfo.appCategory, deviceSKU)
    }
    
    func getAllMeasurementEntries() -> [Operation] {
        let entries = repository.fetchAllMeasurementEntries()
        let operations = entries.map { entry in
            Operation(
                appCategory: AppCategory(rawValue: entry.appCategory ?? "all") ?? .all,
                bgm: Int(entry.bgm),
                bmi: Float(entry.bmi),
                bodyFat: Int(entry.bodyFat),
                boneMass: Int(entry.boneMass),
                bmr: Int(entry.bmr),
                deviceSKU: entry.deviceSKU ?? "unknown",
                diastolic: Int(entry.diastolic),
                entryTimestamp: entry.entryTimestamp ?? Date(),
                id: UUID(),
                impedance: Int(entry.impedance),
                meanPressure: Int(entry.meanPressure),
                metabolicAge: Int(entry.metabolicAge),
                muscleMass: Int(entry.muscleMass),
                operationType: .create,
                proteinPercent: Int(entry.proteinPercent),
                pulse: Int(entry.pulse),
                protocolType: entry.protocolType,
                serverTimestamp: nil,
                skeletalMusclePercent: Int(entry.skeletalMusclePercent),
                source: Source(rawValue: entry.source ?? "unknown") ?? .manual,
                spo: Int(entry.spo),
                subcutaneousFatPercent: Int(entry.subcutaneousFatPercent),
                systolic: Int(entry.systolic),
                temperature: Float(entry.temperature),
                visceralFatLevel: Int(entry.visceralFatLevel),
                water: Int(entry.water),
                weight: Float(entry.weight),
                weightInKg: Float(entry.weightInKg),
                measurementUnit: Unit(rawValue: entry.unit ?? "unknown") ?? .unknown,
                pulseAmplitudeIndex: Float(entry.pulseAmplitudeIndex)
            )
        }
        return operations
    }
    
    func deleteMeasurementEntry(entryTimestamp: Date) {
        repository.deleteMeasurementEntry(entryTimestamp: entryTimestamp)
        ToastService.shared.presentToast(with: ToastMessageStrings.entryDeletedSuccessfully)
    }
    
    func calculateBMI(for weight: Double?) -> Double? {
        guard let weight = weight, weight > 0 else {
            return nil
        }
        
        guard let userProfile = userProfileService.fetchUserProfile(),
              userProfile.height > 0 else {
            return nil
        }
        
        let heightInMeters = Double(userProfile.height) / 100.0
        let bmi = weight / (heightInMeters * heightInMeters)
        
        return bmi
    }
    
    func fetchMeasurementEntriesWithBroadcastId(forBroadcastId broadcastId: String) -> [MeasurementEntry] {
        return repository.fetchMeasurementEntriesWithBroadcastId(forBroadcastId: broadcastId)
    }
    
    
    func sendEntriesToApi(entry: Entry) {
        guard let isLinked = deviceService.isDeviceLinkedToApp(by: entry.broadcastId), isLinked else {
            return
        }
        
        let category: AppCategory
        switch entry.measurementType {
        case .weight:
            category = .weightGurus
        case .bloodPressure:
            category = .balanceHealth
        default:
            return
        }
        
        if let loginDetails = deviceService.fetchUserLogin(for: category), loginDetails.isLoggedIn {
            let email = loginDetails.email ?? "N/A"
            let password = loginDetails.password ?? "N/A"
            
            switch entry.measurementType {
            case .weight:
                let weightEntry = convertToWeightGurusOperationDetails(entry: entry)
                loginService.addOperationToApp(for: .weightGurus, email: email, password: password, weightGurusDetails: weightEntry) { success in
                    if success {
                        self.logger.info(info: "Weight entry sent successfully.", function: #function, line: #line)
                        ToastService.shared.presentToast(with: ToastMessageStrings.entryAddedSuccessfully)
                        
                    } else {
                        let nsError = NSError(domain: String(describing: MeasurementEntryService.self), code: 1, userInfo: [NSLocalizedDescriptionKey: "Failed to send weight entry."])
                        self.logger.error(error: nsError, function: #function, line: #line)
                    }
                }
                
            case .bloodPressure:
                let bloodPressureEntry = convertToBalanceHealthOperationDetails(entry: entry)
                
                loginService.addOperationToApp(for: .balanceHealth, email: email, password: password, balanceHealthDetails: bloodPressureEntry) { success in
                    if success {
                        self.logger.info(info: "Blood pressure entry sent successfully.", function: #function, line: #line)
                        ToastService.shared.presentToast(with: ToastMessageStrings.entryAddedSuccessfully)
                    } else {
                        let nsError = NSError(domain: String(describing: MeasurementEntryService.self), code: 1, userInfo: [NSLocalizedDescriptionKey: "Failed to send blood pressure entry."])
                        self.logger.error(error: nsError, function: #function, line: #line)
                    }
                }
                
            default:
                self.logger.info(info: "Unsupported measurement type: \(entry.measurementType)", function: #function, line: #line)
            }
        } else {
            let nsError = NSError(domain: String(describing: MeasurementEntryService.self), code: 1, userInfo: [NSLocalizedDescriptionKey: "User is not logged in or login details are missing."])
            self.logger.error(error: nsError, function: #function, line: #line)
        }
    }
    
    func convertToWeightGurusOperationDetails(entry: Entry) -> WeightGurusOperationDetails {
        return WeightGurusOperationDetails(
            operationType: "create",
            entryTimestamp: formatDateForOperations(entry.entryTimestamp, format: .iso8601),
            weight: Int((entry.displayWeight ?? 0) * 10),
            bodyFat: Int(entry.bodyFat ?? 0),
            muscleMass: Int(entry.muscleMass ?? 0),
            water: Int(entry.water ?? 0),
            bmi: Int(entry.bmi ?? 0),
            source: .bluetoothScale
        )
    }
    
    func convertToBalanceHealthOperationDetails(entry: Entry) -> BalanceHealthOperationDetails {
        return BalanceHealthOperationDetails(
            diastolic: Int(entry.diastolic ?? 0),
            entryTimestamp: formatDateForOperations(entry.entryTimestamp, format: .posix),
            operation: "create",
            pulse: Int(entry.pulse ?? 0),
            systolic: Int(entry.systolic ?? 0),
            type: .device,
            userId: entry.broadcastId
        )
    }
}
