//
//  BluetoothService.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON><PERSON> Chittibabu on 17/06/24.
//

import Foundation
import GGBluetoothSwiftPackage
import Combine

public class BluetoothService: ObservableObject {
    public static let shared = BluetoothService()
    @Injector var deviceService: DeviceService
    @Injector var userProfileService: UserProfileUpdationService
    @Injector var connectionStateManager: DeviceConnectionState
    
    let ggBlePackage: GGBluetoothSwiftPackage = GGBluetoothSwiftPackage()
    var permissionStatusSubject = CurrentValueSubject<[GGPermissionType: GGPermissionState], Never>([:])
    var discoveredDevices = CurrentValueSubject<[GGBTDevice], Never>([])
    var connectedDevices = CurrentValueSubject<[GGBTDevice], Never>([])
    var deviceConnectionState = CurrentValueSubject<[String: Bool], Never>([:])
    var deviceDisconnected = PassthroughSubject<GGBTDevice, Never>()
    
    var liveMeasurementSubject = CurrentValueSubject<Entry, Never>(Entry(broadcastId: "", displayWeight: nil, unit: nil, weightInKg: nil))
    lazy var currentEntries: Entry = self.liveMeasurementSubject.value
    
    var stableMeasurementSubject = CurrentValueSubject<Entry, Never>(Entry(broadcastId: "", displayWeight: nil, unit: nil, weightInKg: nil))
    lazy var singleEntries: Entry = self.stableMeasurementSubject.value
    
    var measurementSubject = CurrentValueSubject<[Entry], Never>([])
    lazy var multiEntries: [Entry] = self.measurementSubject.value
    
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        syncDevices(connectedDevices.value)

        liveMeasurementSubject
            .subscribe(on: DispatchQueue.global(qos: .background))
            .receive(on: DispatchQueue.main)
            .sink { [weak self] entries in
                self?.currentEntries = entries
            }
            .store(in: &cancellables)
        
        stableMeasurementSubject
            .sink { [weak self] entry in
                guard self != nil else { return }
            }
            .store(in: &cancellables)
        
        measurementSubject
            .sink { [weak self] entries in
                self?.multiEntries = entries
            }
            .store(in: &cancellables)
    }
    
    func scan(selectedCategory: AppCategory) {
        let profileDetails = userProfileService.fetchUserProfile()
        
        let profile = GGBTUserProfile(
            name: profileDetails?.name ?? "John Doe",
            age: profileDetails?.birthday.map {
                Calendar.current.dateComponents([.year], from: $0, to: Date()).year
            } ?? 20,
            sex: profileDetails?.gender ?? "male",
            unit: profileDetails?.unitType ?? "kg",
            height: profileDetails?.height != nil ? Double(profileDetails!.height) : 170.0,
            weight: {
                let weight = profileDetails?.weight ?? 70.0
                let isUsingKg = profileDetails?.unitType == "kg"
                return isUsingKg ? (weight * 2.2046 * 10).rounded() / 10 : weight
            }(),
            goalWeight: {
                let goalWeight = profileDetails?.goalWeight ?? 80.0
                let isUsingKg = profileDetails?.unitType == "kg"
                return isUsingKg ? (goalWeight * 2.2046 * 10).rounded() / 10 : goalWeight
            }(),
            isAthlete: profileDetails?.bodyType == "Athlete",
            goalType: profileDetails?.goalType ?? "lose",
            metrics: []
        )
        
        let appType = appType(for: selectedCategory)
        
        ggBlePackage.scan(appType, profile) { result in
            switch result {
            case .success(let data):
                self.handleScanData(data)
            case .failure(let error):
                print("Failed \(error.localizedDescription)")
            }
        }
    }
    
    func stopScan() {
        ggBlePackage.stop()
    }
    
    func confirmPair(_ btDevice: GGBTDevice, selectedCategory: AppCategory, userNumber: Int?) async -> (broadcastId: String, password: String?) {
        print("btDevice: \(btDevice), protocolType: \(btDevice.protocolType)")
        _ = appType(for: selectedCategory)
        var btDevice = btDevice
        btDevice.userNumber = userNumber
        _ = await self.ggBlePackage.confirmPair(btDevice)
        
        if btDevice.protocolType == "A3" {
            let newBroadcastId = await getDeviceInfo(btDevice)
            btDevice.broadcastId = newBroadcastId.broadcastIdString
            btDevice.password = newBroadcastId.password
        } else if btDevice.protocolType == "A6" {
            print("device is A6")
            let result = await getDeviceInfo(btDevice)
            print("result: \(result)")
        }else if btDevice.protocolType == "R4" {
            print("device is R4")
            let result = await getDeviceInfo(btDevice)
            print("result: \(result)")
        }
        
        var connectedDevices = self.connectedDevices.value
        if !connectedDevices.contains(where: { $0.broadcastId == btDevice.broadcastId }) {
            connectedDevices.append(btDevice)
            self.connectedDevices.send(connectedDevices)
            let result: () = syncDevices(connectedDevices)
        } else {
            print("BluetoothService: Device is already connected, no need to add.")
        }
        return (btDevice.broadcastId, btDevice.password)
    }
    
    func handleScanData(_ data: GGScanResponse) {
        let type = data.type
        switch(type) {
        case .PERMISSION_STATUS:
            let permissionStatus = (data.data as! GGPermissionResponseData).permissions
            self.permissionStatusSubject.send(permissionStatus)
            
            let bluetoothState = permissionStatus[.BLUETOOTH] ?? .NOT_REQUESTED
            let bluetoothSwitchState = permissionStatus[.BLUETOOTH_SWITCH] ?? .NOT_REQUESTED
            var errorMessage: String?
            
            if bluetoothState == .DISABLED && bluetoothSwitchState == .DISABLED {
                errorMessage = "Please enable Bluetooth and Bluetooth switch in settings."
            } else if bluetoothState == .DISABLED {
                errorMessage = "Please enable Bluetooth in settings."
            } else if bluetoothSwitchState == .DISABLED {
                errorMessage = "Please enable the Bluetooth switch in settings."
            }
            
            if let message = errorMessage {
                ToastService.shared.presentToast(with: message)
            }
            
        case .NEW_DEVICE:
            let device = (data.data as! GGDeviceDetails)
            var devices = self.discoveredDevices.value
            
            print("New device detected: \(device.deviceName) - \(String(describing: device.broadcastId))")
            
            if !devices.contains(where: { $0.broadcastId == device.broadcastId }) {
                let newDevice = self.getBTDevice(device)
                devices.append(newDevice)
                self.discoveredDevices.send(devices)
                
                print("Added new device: \(newDevice.name) - \(newDevice.broadcastId)")
            } else {
                print("Device already discovered: \(device.deviceName) - \(String(describing: device.broadcastId))")
            }
            
        case .DEVICE_CONNECTED:
            let device = (data.data as! GGDeviceDetails)
            var devices = self.discoveredDevices.value
            var connectedDevices = self.connectedDevices.value
            
            print("Device connected: \(device.deviceName) - \(String(describing: device.broadcastId))")
            
            self.discoveredDevices.send(devices)  // Ensure UI updates if needed
            
            if !connectedDevices.contains(where: { $0.broadcastId == device.broadcastId }) {
                let newDevice = self.getBTDevice(device)
                devices.append(newDevice)
                connectedDevices.append(newDevice)
                ggBlePackage.syncDevices(connectedDevices)
                print("connected devices btService: \(connectedDevices)")
                self.connectedDevices.send(connectedDevices)
                
                print("Added to connected devices: \(newDevice.name) - \(newDevice.broadcastId)")
            } else {
                print("Device already in connected list: \(device.deviceName) - \(String(describing: device.broadcastId))")
            }
            
        case .DEVICE_DISCONNECTED:
            let device = (data.data as! GGDeviceDetails)
            var connectedDevices = self.connectedDevices.value
            
            print("Device disconnected: \(device.deviceName) - \(String(describing: device.broadcastId))")
            
            if let index = connectedDevices.firstIndex(where: { $0.broadcastId == device.broadcastId }) {
                let disconnectedDevice = connectedDevices.remove(at: index)
                self.connectedDevices.send(connectedDevices)
                self.deviceDisconnected.send(disconnectedDevice)
                self.deviceConnectionState.value[disconnectedDevice.broadcastId] = false
                
                print("Removed from connected devices: \(disconnectedDevice.name) - \(disconnectedDevice.broadcastId)")
            } else {
                print("Device not found in connected list: \(device.deviceName) - \(String(describing: device.broadcastId))")
            }
            
        case .SINGLE_ENTRY:
            print(data.data, "SINGLE_ENTRY")
            
            if let data = data.data as? GGWeightEntry {
                let entry = Entry(
                    measurementType: .weight,
                    broadcastId: data.broadcastId ?? "",
                    displayWeight: data.displayWeight,
                    unit: data.unit,
                    weightInKg: data.weightInMg
                )
                self.stableMeasurementSubject.send(entry)
            } else if let data = data.data as? GGThermometerEntry {
                let entry = Entry(
                    measurementType: .thermometer,
                    broadcastId: data.broadcastId ?? "",
                    entryTimestamp: data.date != nil ? Date(timeIntervalSince1970: TimeInterval(data.date!) / 1000) : nil,
                    temperature: data.temperature,
                    unit: data.unit
                )
                self.stableMeasurementSubject.send(entry)
            } else if let data = data.data as? GGEntry {
                let entry = Entry(
                    measurementType: .weight,
                    bmr: Float(data.bmr),
                    bmi: data.bmi,
                    bodyFat: data.bodyFat,
                    boneMass: data.boneMass,
                    broadcastId: data.broadcastId ?? "",
                    displayWeight: data.weight,
                    entryTimestamp: data.date != nil && data.date! > 0
                    ? Date(timeIntervalSince1970: TimeInterval(data.date!) / 1000)
                    : Date(),
                    impedance: data.impedance,
                    metabolicAge: Float(data.metabolicAge),
                    muscleMass: data.muscleMass,
                    proteinPercent: data.proteinPercent,
                    pulse: Float(data.pulse),
                    skeletalMusclePercent: data.skeletalMusclePercent,
                    subcutaneousFatPercent: data.subcutaneousFatPercent,
                    unit: data.unit,
                    visceralFatLevel: Float(data.visceralFatLevel),
                    water: data.water,
                    weightInKg: data.weightInKg,
                    weight: data.weight
                )
                self.stableMeasurementSubject.send(entry)
            } else if let data = data.data as? GGBPMEntryList {
                let entries = data.list.map { entry in
                    Entry(
                        measurementType: .bloodPressure,
                        broadcastId: entry.broadcastIdString ?? "",
                        diastolic: entry.diastolic != nil ? Float(entry.diastolic!) : nil,
                        entryTimestamp: entry.date != nil ? Date(timeIntervalSince1970: TimeInterval(entry.date!) / 1000) : nil,
                        meanPressure: entry.meanPressure != nil ? Float(entry.meanPressure!) : nil,
                        pulse: entry.pulse != nil ? Float(entry.pulse!) : nil,
                        systolic: entry.systolic != nil ? Float(entry.systolic!) : nil
                    )
                }
                self.measurementSubject.send(entries)
            } else if let data = data.data as? GGPulseOxyEntry {
                let entry = Entry(
                    measurementType: .pulseOxy,
                    broadcastId: data.broadcastId ?? "",
                    entryTimestamp: data.date != nil ? Date(timeIntervalSince1970: TimeInterval(data.date!) / 1000) : nil,
                    pulse: data.pulse != nil ? Float(data.pulse!) : nil,
                    spo: data.oxygenSaturation != nil ? Float(data.oxygenSaturation!) : nil,
                    pulseAmplitudeIndex: data.pulseAmplitudeIndex != nil ? Float(data.pulseAmplitudeIndex!) : nil
                )
                self.stableMeasurementSubject.send(entry)
            } else if let data = data.data as? GGBloodGlucoseEntry {
                let entry = Entry(
                    measurementType: .bloodGlucose,
                    bgm: data.glucose != nil ? Float(data.glucose!) : nil,
                    broadcastId: data.broadcastId ?? "",
                    entryTimestamp: data.date != nil ? Date(timeIntervalSince1970: TimeInterval(data.date!)) : nil,
                    unit: data.unit,
                    errorCode: data.errorCode
                )
                self.stableMeasurementSubject.send(entry)
            } else {
                print("BluetoothService: Unknown data type for SINGLE_ENTRY")
            }
            
        case .MULTI_ENTRIES:
            print(data.data, "MULTI_ENTRIES")
            
            if let data = data.data as? GGEntryList {
                let entries = data.list.map { entry in
                    Entry(
                        measurementType: .weight,
                        bmr: Float(entry.bmr),
                        bmi: entry.bmi,
                        bodyFat: entry.bodyFat,
                        boneMass: entry.boneMass,
                        broadcastId: entry.broadcastId ?? "",
                        displayWeight: entry.weight,
                        entryTimestamp: entry.date != nil ? Date(timeIntervalSince1970: TimeInterval(entry.date!) / 1000) : nil,
                        impedance: entry.impedance,
                        metabolicAge: Float(entry.metabolicAge),
                        muscleMass: entry.muscleMass,
                        proteinPercent: entry.proteinPercent,
                        pulse: Float(entry.pulse),
                        skeletalMusclePercent: entry.skeletalMusclePercent,
                        subcutaneousFatPercent: entry.subcutaneousFatPercent,
                        unit: entry.unit,
                        visceralFatLevel: Float(entry.visceralFatLevel),
                        water: entry.water,
                        weightInKg: entry.weightInKg
                    )
                }
                self.measurementSubject.send(entries)
            } else if let data = data.data as? GGBPMEntryList {
                let entries = data.list.map { entry in
                    Entry(
                        measurementType: .bloodPressure,
                        broadcastId: entry.broadcastIdString ?? "",
                        diastolic: entry.diastolic != nil ? Float(entry.diastolic!) : nil,
                        entryTimestamp: entry.date != nil ? Date(timeIntervalSince1970: TimeInterval(entry.date!) / 1000) : nil,
                        meanPressure: entry.meanPressure != nil ? Float(entry.meanPressure!) : nil,
                        pulse: entry.pulse != nil ? Float(entry.pulse!) : nil,
                        systolic: entry.systolic != nil ? Float(entry.systolic!) : nil
                    )
                }
                self.measurementSubject.send(entries)
            } else if let data = data.data as? GGPulseOxyEntryList {
                let entries = data.list.map { entry in
                    Entry(
                        measurementType: .pulseOxy,
                        broadcastId: entry.broadcastId ?? "",
                        entryTimestamp: entry.date != nil ? Date(timeIntervalSince1970: TimeInterval(entry.date!) / 1000) : nil,
                        pulse: entry.pulse != nil ? Float(entry.pulse!) : nil,
                        spo: entry.oxygenSaturation != nil ? Float(entry.oxygenSaturation!) : nil
                    )
                }
                self.measurementSubject.send(entries)
            } else if let data = data.data as? GGBloodGlucoseEntryList {
                let entries = data.list.map { entry in
                    Entry(
                        measurementType: .bloodGlucose,
                        bgm: entry.glucose != nil ? Float(entry.glucose!) : nil,
                        broadcastId: entry.broadcastId ?? "",
                        entryTimestamp: entry.date != nil ? Date(timeIntervalSince1970: TimeInterval(entry.date!) / 1000) : nil, unit: entry.unit
                    )
                }
                self.measurementSubject.send(entries)
            } else {
                print("BluetoothService: Unknown data type for MULTI_ENTRIES")
            }
            
        case .LIVE_MEASUREMENT:
            print(data.data, "LIVE_MEASUREMENT")
            
            if let data = data.data as? GGWeightEntry {
                let entry = Entry(
                    measurementType: .weight,
                    broadcastId: data.broadcastId ?? "",
                    displayWeight: data.displayWeight,
                    unit: data.unit,
                    weightInKg: data.weightInMg
                )
                print("live: \(entry)")
                DispatchQueue.main.async {
                    self.liveMeasurementSubject.send(entry)
                }
            } else if let data = data.data as? GGThermometerEntry {
                let entry = Entry(
                    measurementType: .thermometer,
                    bgm: nil, broadcastId: data.broadcastId ?? "",
                    entryTimestamp: data.date != nil ? Date(timeIntervalSince1970: TimeInterval(data.date!) / 1000) : nil,
                    temperature: data.temperature,
                    unit: data.unit
                )
                DispatchQueue.main.async {
                    self.liveMeasurementSubject.send(entry)
                }
            } else if let data = data.data as? GGPulseOxyEntry {
                let entry = Entry(
                    measurementType: .pulseOxy,
                    broadcastId: data.broadcastId ?? "",
                    entryTimestamp: data.date != nil ? Date(timeIntervalSince1970: TimeInterval(data.date!) / 1000) : nil,
                    pulse: data.pulse != nil ? Float(data.pulse!) : nil,
                    spo: data.oxygenSaturation != nil ? Float(data.oxygenSaturation!) : nil
                )
                DispatchQueue.main.async {
                    self.liveMeasurementSubject.send(entry)
                }
            } else if let data = data.data as? GGBloodGlucoseEntry {
                let entry = Entry(
                    measurementType: .bloodGlucose,
                    bgm: data.glucose != nil ? Float(data.glucose!) : nil,
                    broadcastId: data.broadcastId ?? "",
                    entryTimestamp: data.date != nil ? Date(timeIntervalSince1970: TimeInterval(data.date!) / 1000) : nil,
                    unit: data.unit
                )
                DispatchQueue.main.async {
                    self.liveMeasurementSubject.send(entry)
                }
            } else {
                print("BluetoothService: Unknown data type for LIVE_MEASUREMENT")
            }
            
        default:
            break
        }
    }
}
