//
//  BluetoothServiceExtension.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 28/02/25.
//

import Foundation
import GGBluetoothSwiftPackage
import Combine

extension BluetoothService {
    
    func requestPermission(permissionType: GGPermissionType) async {
        _ = await ggBlePackage.requestPermission(permissionType: permissionType)
    }
    
    func checkPermission(appType: GGAppType, callback: @escaping GGBTScanCallback) {
        ggBlePackage.checkPermission(appType, callback)
    }
    
    func fetchInitialPermissions(selectedCategory: AppCategory) {
        let appType = appType(for: selectedCategory)
        self.checkPermission(appType: appType) { result in
            switch result {
            case .success(let data):
                if let permissionStatus = data.data as? GGPermissionResponseData {
                    self.permissionStatusSubject.send(permissionStatus.permissions)
                }
            case .failure(let error):
                print("Error fetching initial permissions: \(error.localizedDescription)")
            }
        }
    }
    
    func getBTDevice(_ device: GGDeviceDetails) -> GGBTDevice {
        let btDevice: GGBTDevice = GGBTDevice(
            name: device.deviceName,
            broadcastId: device.broadcastIdString,
            password: device.password,
            token: "",
            userNumber: device.userNumber,
            preference: nil,
            syncAllData: true,
            batteryLevel: device.batteryLevel,
            protocolType: device.protocolType,
            macAddress: device.macAddress
        )
        return btDevice
    }
    
    func updateDeviceSetting(_ device: GGBTDevice, key: GGBTSettingType, value: GGBTSettingValue) {
        let settings = [GGBTSetting(key: key, value: value)]
        ggBlePackage.updateSetting(device, settings)
    }
    
    public func tareScale(_ device: GGBTDevice) {
        let device =
        GGBTDevice(
            name: device.name,
            broadcastId: device.broadcastId,
            password: device.password,
            token: device.token,
            userNumber: device.userNumber,
            preference: nil,
            syncAllData: false,
            batteryLevel: 0,
            protocolType: "",
            macAddress: ""
        )
        updateDeviceSetting(device, key: .TARE, value: .bool(true))
    }
    
    func updateUnit(_ device: GGBTDevice, unit: MeasurementUnit) {
        let updatedDevice = GGBTDevice(
            name: device.name,
            broadcastId: device.broadcastId,
            password: device.password,
            token: device.token,
            userNumber: device.userNumber,
            preference: nil,
            syncAllData: false,
            batteryLevel: 0,
            protocolType: "",
            macAddress: ""
        )
        updateDeviceSetting(updatedDevice, key: .UNIT, value: .string("\(unit.unitName)"))
    }
    
    func updateTemperatureUnit(_ device: GGBTDevice, unit: TemperatureMeasurementUnit) {
        let updatedDevice = GGBTDevice(
            name: device.name,
            broadcastId: device.broadcastId,
            password: device.password,
            token: device.token,
            userNumber: device.userNumber,
            preference: nil,
            syncAllData: false,
            batteryLevel: 0,
            protocolType: "",
            macAddress: ""
        )
        updateDeviceSetting(updatedDevice, key: .TEMPERATURE_UNIT, value: .string("\(unit.unitName)"))
    }
    
    func setMuteMode(_ device: GGBTDevice) {
        updateDeviceSetting(device, key: .SET_MUTEMODE, value: .bool(true))
    }
    
    func disableMuteMode(_ device: GGBTDevice) {
        updateDeviceSetting(device, key: .DISABLE_MUTEMODE, value: .bool(false))
    }
    
    func subscribeToLiveData(_ device: GGBTDevice) {
        updateDeviceSetting(device, key: .SUBSCRIBE_TO_LIVE_DATA, value: .bool(true))
    }
    
    func getDeviceInfo(_ btDevice: GGBTDevice) async -> GGDeviceDetails {
        let result = await self.ggBlePackage.getDeviceInfo(btDevice)
        return result
    }
    
    func disconnectDevice(_ device: GGBTDevice) async throws {
        ggBlePackage.disconnectDevice(device.broadcastId)
    }
    
    func syncDevices(_ devices: [GGBTDevice]) {
        ggBlePackage.syncDevices(devices)
    }
    
    func startFirmwareUpdate(_ device: GGBTDevice, _ timestamp: Int64) {
        ggBlePackage.startFirmwareUpdate(device, UInt32(timestamp))
    }
    
    func updateAccount(device: GGBTDevice) async throws -> String? {
        do {
            let result = try await ggBlePackage.updateAccount(device)
            return result.rawValue
        } catch {
            throw error
        }
    }

    func deleteUser(device: GGBTDevice, canDisconnect: Bool)async{
        await  ggBlePackage.deleteUser(device, canDisconnect: canDisconnect)
    }
    
    func fetchUsers(for device: GGBTDevice) async -> [GGBTUser] {
        let response = await  ggBlePackage.getUsers(device)
        return response.user
    }
    
    public func getWifiMacAddress(_ device: GGBTDevice) async throws -> String {
        do {
            let macAddress = try await ggBlePackage.getWifiMacAddress(device)
            return macAddress
        } catch {
            
            throw  error
        }
    }
    
    public func getWifiList(_ device: GGBTDevice) async throws -> GGWifiResponse<GGWifiDetails> {
        do {
            let wifiList = try await ggBlePackage.getWifiList(device)
            return wifiList
        } catch {
            throw error
        }
    }
    
    public func configureDeviceWifi(device: GGBTDevice, ssid: String, password: String) async -> GGWifiSetupResponse {
        let wifiConfig = GGBTWifiConfig(ssid: ssid, password: password)
        let response =  await ggBlePackage.setupWifi(device, wifiConfig)
        return response
    }
    
    public func getDeviceLogs(device: GGBTDevice)async -> GGDeviceLogResponse<DeviceLog> {
        let result =   await ggBlePackage.getDeviceLogs(device)
        return result
    }
    
    public func restoreAccount(device: GGBTDevice, accountName: String) async throws {
        do {
            try await ggBlePackage.restoreAccount(device: device, accountName: accountName)
        } catch {
            throw(error)
            
        }
    }
    
    public func updateProfile(profile: GGBTUserProfile) async throws {
        do {
          try await ggBlePackage.updateProfile(profile: profile)
            
        } catch {
            throw error
        }
    }
    
}
