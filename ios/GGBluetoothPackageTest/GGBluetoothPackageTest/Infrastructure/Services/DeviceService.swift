//
//  DeviceService.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/11/24.
//

import Foundation
import CoreData
import GGBluetoothSwiftPackage

class DeviceService: ObservableObject {
    private let repository: DeviceRepository
    private let container: NSPersistentContainer
    @Injector var bluetoothService: BluetoothService
    @Injector var measuremnetEntryService: MeasurementEntryService
    private let logger: AppLogger = AppLogger(category: String(describing: DeviceService.self))
    @Published var pairedUserNumber: Int?
    static let shared = DeviceService()
    init() {
        container = NSPersistentContainer(name: "GGBluetoothPackageTest")
        self.repository = DeviceRepository(viewContext: container.viewContext)
        container.loadPersistentStores { [weak self] description, error in
            guard let self = self else { return }
            
            if let error = error {
                let nsError = NSError(domain: String(describing: DeviceService.self), code: 1, userInfo: [NSLocalizedDescriptionKey: "Error loading Core Data. \(error.localizedDescription)"])
                self.logger.error(error: nsError, function: #function, line: #line)
            } else {
                self.logger.info(info: "Core Data loaded successfully.", function: #function, line: #line)
                self.fetchSelectedCategory()
            }
        }
    }
    
    func pairDiscoveredDevice(broadcastId: String, selectedCategory: AppCategory) {
        if let device = repository.fetchDevice(by: broadcastId) {
            let btDevice = GGBTDevice(
                name: device.name ?? "",
                broadcastId: device.broadcastId ?? "",
                password: device.password ?? "",
                token: device.token ?? "",
                userNumber: Int(device.userNumber),
                preference: nil,
                syncAllData: true,
                batteryLevel: 0,
                protocolType: device.protocolType ?? "A3",
                macAddress:  device.macAddress ?? ""
            )
            
            Task {
                logger.info(info: "Attempting to pair discovered device: \(btDevice.name), Broadcast ID: \(btDevice.broadcastId)", function: #function, line: #line)
                
                let userNumber = selectedCategory == .weightGurus || selectedCategory == .balanceHealth
                ? Int(device.userNumber)
                : 0
                pairedUserNumber = userNumber
                
                await bluetoothService.confirmPair(btDevice, selectedCategory: selectedCategory, userNumber: userNumber)
            }
        } else {
            let nsError = NSError(domain: String(describing: DeviceService.self), code: 4, userInfo: [NSLocalizedDescriptionKey: "No device found to pair for broadcastId: \(broadcastId)"])
            logger.error(error: nsError, function: #function, line: #line)
        }
    }
    
    func saveDevice(name: String, broadcastId: String, selectedCategory: AppCategory, userNumber: Int?, batteryLevel: Int, macAddress: String, password: String, protocolType: String, token: String, deviceType: String, preference: Preference?) {
        var actualUserNumber: Int? = nil
        if (selectedCategory == .weightGurus && protocolType != "R4") || selectedCategory == .balanceHealth {
            actualUserNumber = userNumber ?? 0
        }
        repository.saveDevice(appCategory: selectedCategory.rawValue,
                              name: name,
                              broadcastId: broadcastId,
                              userNumber: actualUserNumber,
                              batteryLevel: batteryLevel,
                              macAddress: macAddress,
                              password: password,
                              protocolType: protocolType,
                              token: token,
                              deviceType: deviceType,
                              preference: preference)
    }
    
    func fetchDevice(by broadcastId: String) -> Device? {
        return repository.fetchDevice(by: broadcastId)
    }
    
    func fetchAllDevices() -> [Device]? {
        return repository.fetchAllDevices()
    }
    
    func fetchAllGGBTDevices() -> [GGBTDevice] {
        return repository.fetchAllGGBTDevices()
    }
    
    func fetchDeviceAppCategory(by broadcastId: String) -> (appCategory: String?, deviceName: String?) {
        let device = repository.fetchDevice(by: broadcastId)
        return (device?.appCategory, device?.name)
    }
    
    func deleteDevice(by device: GGBTDevice) async {
        repository.deleteDevice(by: device.broadcastId)
        await bluetoothService.deleteUser(device: device, canDisconnect: true)
        bluetoothService.syncDevices([device])
    }
    
    func saveSelectedCategory(_ selectedCategory: AppCategory) {
        let categoryString = selectedCategory.rawValue
        repository.saveSelectedCategory(categoryString)
    }
    
    func fetchSelectedCategory() -> String? {
        if let savedCategoryString = repository.fetchSelectedCategory() {
            if let savedCategory = AppCategory(rawValue: savedCategoryString) {
                return savedCategory.rawValue
            } else {
                return nil
            }
        } else {
            return nil
        }
    }
    
    func loadSelectedCategory() -> AppCategory? {
        if let savedCategoryString = repository.fetchSelectedCategory() {
            if let savedCategory = AppCategory(rawValue: savedCategoryString) {
                return savedCategory
            } else {
                return nil
            }
        } else {
            return nil
        }
    }
    
    func saveUserLogin(category: AppCategory, email: String, password: String, isLoggedIn: Bool) {
        repository.saveUserLogin(category: category, email: email, password: password, isLoggedIn: isLoggedIn)
    }
    
    func fetchUserLogin(for category: AppCategory) -> (email: String?, password: String?, isLoggedIn: Bool)? {
        return repository.fetchUserLogin(for: category)
    }
    
    func deleteUserLogin(for category: AppCategory) {
        repository.deleteUserLogin(for: category)
    }
    
    func fetchAllUserLogins() -> [(UserLogin, AppCategory)]? {
        let allUserLogins = repository.fetchAllUserLogins()
        return allUserLogins
    }
    
    func isDeviceLinkedToApp(by broadcastId: String) -> Bool? {
        return repository.isDeviceLinkedToApp(by: broadcastId)
    }
    
    func linkDeviceToApp(by broadcastId: String, isLinked: Bool) {
        repository.setDeviceLinkedToApp(by: broadcastId, isLinked: isLinked)
    }
    
    func isDeviceStoredLocally(broadcastId: String) -> Bool {
        guard let devices = fetchAllDevices() else {
            return false
        }
        return devices.contains { $0.broadcastId == broadcastId }
    }
    
    func updateDeviceTokenAndUser(broadcastId: String, token: String?, isUserAdded: Bool) {
        repository.updateDevice(broadcastId: broadcastId, token: token, isUserAdded: isUserAdded)
    }
    
    func isUserAdded(for broadcastId: String) -> Bool? {
        return repository.isUserAdded(for: broadcastId)
    }
    
    func savePreference(
        for broadcastId: String,
        displayMetrics: [String],
        displayName: String,
        shouldMeasureImpedance: Bool,
        shouldMeasurePulse: Bool,
        timeFormat: String
    ) {
        repository.savePreference(
            for: broadcastId,
            displayMetrics: displayMetrics,
            displayName: displayName,
            shouldMeasureImpedance: shouldMeasureImpedance,
            shouldMeasurePulse: shouldMeasurePulse,
            timeFormat: timeFormat
        )
    }
    
    func fetchPreference(for broadcastId: String) -> Preference? {
        return repository.fetchPreference(by: broadcastId)
    }
    
    func getIsWifiConfigured(for broadcastId: String) -> Bool? {
        return repository.getIsWifiConfigured(for: broadcastId)
    }
    
    func setIsWifiConfigured(for broadcastId: String, isConfigured: Bool) {
        repository.setIsWifiConfigured(for: broadcastId, isConfigured: isConfigured)
    }
    
}
