//
//  UserProfileUpdationService.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 02/01/25.
//

import CoreData
import Foundation

class UserProfileUpdationService: ObservableObject {
    private let repository: UserProfileRepository
    private let container: NSPersistentContainer
    @Published var name: String = "Default Name"
    @Published var email: String = "<EMAIL>"
    private let logger: AppLogger = AppLogger(category: String(describing: UserProfileUpdationService.self))
    
    init() {
        container = NSPersistentContainer(name: "GGBluetoothPackageTest")
        self.repository = UserProfileRepository(viewContext: container.viewContext)
        
        container.loadPersistentStores { [weak self] description, error in
            guard let self = self else { return }
            
            if let error = error {
                let nsError = NSError(
                    domain: String(describing: UserProfileUpdationService.self),
                    code: 1,
                    userInfo: [NSLocalizedDescriptionKey: "Error loading Core Data. \(error.localizedDescription)"]
                )
                self.logger.error(error: nsError, function: #function, line: #line)
            } else {
                self.logger.info(info: "UserProfile Data loaded successfully.", function: #function, line: #line)
            }
        }
    }
    
    func saveUserProfile(
        name: String,
        email: String,
        birthday: Date,
        height: Int,
        weight: Double,
        gender: String,
        bodyType: String,
        goalType: String,
        goalWeight: Double,
        unitType: String
    ) {
        repository.saveUserProfile(
            name: name,
            email: email,
            birthday: birthday,
            height: Int16(height),
            weight: weight,
            gender: gender,
            bodyType: bodyType,
            goalType: goalType,
            goalWeight: goalWeight,
            unitType: unitType
        )
        logger.info(info: """
               UserProfileService: SaveUserProfile called with:
               Name: \(name),
               Email: \(email),
               Birthday: \(birthday),
               Height: \(height),
               Weight: \(weight),
               Gender: \(gender),
               BodyType: \(bodyType),
               GoalType: \(goalType),
               GoalWeight: \(goalWeight),
               UnitType: \(unitType)
           """, function: #function, line: #line)
    }
    
    func fetchUserProfile() -> UserProfile? {
        let fetchRequest: NSFetchRequest<UserProfile> = UserProfile.fetchRequest()
        
        do {
            let profiles = try container.viewContext.fetch(fetchRequest)
            return profiles.first
        } catch let error {
            let nsError = NSError(
                domain: String(describing: UserProfileUpdationService.self),
                code: 4,
                userInfo: [NSLocalizedDescriptionKey: "Error fetching user profile: \(error.localizedDescription)"]
            )
            logger.error(error: nsError, function: #function, line: #line)
            return nil
        }
    }
    
    func getUserHeight() -> Int? {
        guard let userProfile = fetchUserProfile() else {
            return nil
        }
        return Int(userProfile.height)
    }
    
    func updateUserName(newName: String) {
        guard let userProfile = fetchUserProfile() else {
            logger.error(error: NSError(
                domain: String(describing: UserProfileUpdationService.self),
                code: 5,
                userInfo: [NSLocalizedDescriptionKey: "User profile not found."]
            ), function: #function, line: #line)
            return
        }
        
        userProfile.name = newName
        
        do {
            try container.viewContext.save()
            self.name = newName
            logger.info(info: "User name updated successfully to: \(newName)", function: #function, line: #line)
        } catch let error {
            let nsError = NSError(
                domain: String(describing: UserProfileUpdationService.self),
                code: 6,
                userInfo: [NSLocalizedDescriptionKey: "Failed to update user name: \(error.localizedDescription)"]
            )
            logger.error(error: nsError, function: #function, line: #line)
        }
    }
    
    func saveMetricsEntities(metricsEntities: [MetricsEntityModel]) {
        repository.saveMetricsEntities(metricsEntities: metricsEntities)
    }
    
    func fetchMetricsEntities() -> [MetricsEntityModel] {
        let metricsEntities = repository.fetchMetricsEntities()
        return metricsEntities
    }
}

