//
//  DeviceInfoManager.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 03/12/24.
//

import Foundation

class DeviceInfoManager: ObservableObject{
    private let deviceInfoRepository : DeviceInfoRepository

    init(){
        self.deviceInfoRepository = DeviceInfoRepository()
    }
    
    func loadScalesFromFile() -> [Scale]? {
        return deviceInfoRepository.readScaleDetails()
    }
    func loadMonitorDetailsFromFile() -> [MonitorType]? {
        return deviceInfoRepository.readMonitorDetails()
    }
        
    func mapSKUFromBroadcastName(broadcastName: String) -> String? {
        guard let scales = loadScalesFromFile(), let monitors = loadMonitorDetailsFromFile() else {
            return nil
        }
        for monitor in monitors {
            if monitor.broadcastName.contains(broadcastName) {
                return monitor.sku
            }
        }
        for scale in scales {
            return scale.sku
        }
        return nil
    }

    
}
