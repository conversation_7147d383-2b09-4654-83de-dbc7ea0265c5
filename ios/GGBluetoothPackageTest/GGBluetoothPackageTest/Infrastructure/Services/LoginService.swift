//
//  LoginService.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 04/02/25.
//

import Foundation
import Alamofire
import SwiftUICore

class LoginService : ObservableObject {
    static let shared: LoginService = LoginService()
    private var rootUrl: String = ""
    private var apiUrl: String = ""
    private let logger: AppLogger = AppLogger(category: String(describing: LoginService.self))
    @Injector var loginDataService: LoginDataService
    @Injector var toastService: ToastService
    @State private var networkMonitor = NetworkMonitor()
    var errorMessage: String = "Failed to unlink device from app."
    
    init() {}
    
    func configureApiUrl(for category: AppCategory) {
        self.rootUrl = category.apiUrl()
        
        switch category {
        case .weightGurus:
            self.apiUrl = "\(self.rootUrl)account/"
        case .balanceHealth:
            self.apiUrl = "\(self.rootUrl)user/"
        default:
            self.apiUrl = "\(self.rootUrl)account/"
        }
    }
    
    func login(for category: AppCategory, email: String, password: String, completion: @escaping (Result<CombinedLoginData, Error>) -> Void) {

        if !networkMonitor.isConnected {
            completion(.failure(NSError(domain: "", code: -1, userInfo: [NSLocalizedDescriptionKey: "No Internet Connection"])))
            return
        }
        
        configureApiUrl(for: category)
        
        let parameters: [String: Any] = [
            "email": email,
            "password": password
        ]
 
        AF.request(self.apiUrl + "login", method: .post, parameters: parameters, encoding: JSONEncoding.default)
            .validate()
            .responseData { response in

                switch response.result {
                case .success(let data):
                    let result = self.mapLoginResponse(data: data, for: category)
                    switch result {
                    case .success(let loginResponse):
                        if category == .smartBaby || category == .all {
                            self.loginDataService.getMeasurements(for: category, accessToken: loginResponse.token ?? "") { measurements in
                                var mutableMeasurements = measurements ?? []
                                
                                if category == .all {
                                    self.loginDataService.fetchAccountDetails(for: .balanceHealth, accessToken: loginResponse.token ?? "") { balanceHealthResult in
                                        switch balanceHealthResult {
                                        case .success(let balanceHealthAccountDetails):
                                            
                                            self.loginDataService.getMeasurements(for: .balanceHealth, accessToken: loginResponse.token ?? "") { balanceHealthMeasurements in
                                                mutableMeasurements.append(contentsOf: balanceHealthMeasurements ?? [])
                                                
                                                self.loginDataService.fetchAccountDetails(for: .weightGurus, accessToken: loginResponse.token ?? "") { weightGurusResult in
                                                    switch weightGurusResult {
                                                    case .success(let weightGurusAccountDetails):
                                                        
                                                        self.loginDataService.getMeasurements(for: .weightGurus, accessToken: loginResponse.token ?? "") { weightGurusMeasurements in
                                                            mutableMeasurements.append(contentsOf: weightGurusMeasurements ?? [])
                                                            
                                                            let combinedData = CombinedLoginData(
                                                                loginResponse: loginResponse,
                                                                accountDetails: balanceHealthAccountDetails + weightGurusAccountDetails,
                                                                measurements: mutableMeasurements
                                                            )
                                                            completion(.success(combinedData))
                                                        }
                                                    case .failure(let error):
                                                        completion(.failure(error))
                                                    }
                                                }
                                            }
                                        case .failure(let error):
                                            completion(.failure(error))
                                        }
                                    }
                                } else {
                                    let combinedData = CombinedLoginData(
                                        loginResponse: loginResponse,
                                        accountDetails: [],
                                        measurements: mutableMeasurements
                                    )
                                    completion(.success(combinedData))
                                }
                            }
                        } else {
                            self.loginDataService.fetchAccountDetails(for: category, accessToken: loginResponse.token ?? "") { accountDetailsResult in
                                switch accountDetailsResult {
                                case .success(let accountDetails):

                                    self.loginDataService.getMeasurements(for: category, accessToken: loginResponse.token ?? "") { measurements in
                                        
                                        let combinedData = CombinedLoginData(
                                            loginResponse: loginResponse,
                                            accountDetails: accountDetails,
                                            measurements: measurements
                                        )
                                        completion(.success(combinedData))
                                    }
                                case .failure(let error):
                                    completion(.failure(error))
                                }
                            }
                        }
                    case .failure(let error):
                        self.retryLoginRequest(parameters: parameters, completion: completion)
                    }
                case .failure(let error):
                    self.retryLoginRequest(parameters: parameters, completion: completion)
                }
            }
    }

    
    private func retryLoginRequest(parameters: [String: Any], completion: @escaping (Result<CombinedLoginData, Error>) -> Void) {
        var retryCount = 0
        let maxRetryLimit = 1
        let retryDelay: TimeInterval = 3
        
        func attemptLogin() {
            if retryCount >= maxRetryLimit {
                let nsError = NSError(domain: String(describing: LoginService.self), code: 1, userInfo: [NSLocalizedDescriptionKey: "Invalid credentials."])
                self.logger.error(error: nsError, function: #function, line: #line)
                completion(.failure(nsError))
                return
            }
            
            retryCount += 1
            
            AF.request(self.apiUrl + "login", method: .post, parameters: parameters, encoding: JSONEncoding.default)
                .validate()
                .responseData { response in
                    switch response.result {
                    case .success(let data):
                        let result = self.mapLoginResponse(data: data, for: .weightGurus)
                        switch result {
                        case .success(let loginResponse):
                            completion(.success(CombinedLoginData(loginResponse: loginResponse, accountDetails: [], measurements: nil)))
                        case .failure(let error):
                            self.logger.error(error: error as NSError, function: #function, line: #line)
                            completion(.failure(error))
                        }
                    case .failure(let error):
                        self.logger.error(error: error as NSError, function: #function, line: #line)
                        DispatchQueue.global().asyncAfter(deadline: .now() + retryDelay) {
                            attemptLogin()
                        }
                    }
                }
        }
        attemptLogin()
    }
    
    func mapLoginResponse(data: Data, for category: AppCategory) -> Result<WrappedLoginResponse, Error> {
        do {
            let decoder = JSONDecoder()
            
            switch category {
            case .weightGurus:
                let loginResponse = try decoder.decode(WeightGurusLoginResponse.self, from: data)
                return .success(WrappedLoginResponse(loginResponse))
            case .balanceHealth:
                let loginResponse = try decoder.decode(BalanceHealthLoginResponse.self, from: data)
                return .success(WrappedLoginResponse(loginResponse))
            case .smartBaby:
                let loginResponse = try decoder.decode(SmartBabyLoginResponse.self, from: data)
                return .success(WrappedLoginResponse(loginResponse))
            default:
                throw NSError(domain: "LoginService", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unknown app category."])
            }
        } catch {
            self.logger.error(error: error as NSError, function: #function, line: #line)
            return .failure(error)
        }
    }
    
    func addDeviceToApp(for category: AppCategory, email: String, password: String, deviceDetails: [String: Any], completion: @escaping (Bool) -> Void) {
        configureApiUrl(for: category)
        
        login(for: category, email: email, password: password) { result in
            switch result {
            case .success(let combinedData):
                guard let token = combinedData.loginResponse.token else {
                    completion(false)
                    return
                }
                
                var headers: HTTPHeaders = [:]
                var deviceEndpoint: String = ""
                switch category {
                case .balanceHealth:
                    headers = ["x-access-token": token]
                    deviceEndpoint = "\(self.rootUrl)monitor/"
                case .weightGurus:
                    headers = ["Authorization": "Bearer \(token)"]
                    deviceEndpoint = "\(self.rootUrl)paired-scale/"
                default:
                    completion(false)
                    return
                }
                
                AF.request(deviceEndpoint, method: .post, parameters: deviceDetails, encoding: JSONEncoding.default, headers: headers)
                    .validate()
                    .responseData { response in
                        switch response.result {
                        case .success:
                            self.logger.info(info: "Device added successfully", function: #function, line: #line)
                            ToastService.shared.presentToast(with: ToastMessageStrings.deviceLinkedSuccessfully)
                            completion(true)
                        case .failure(let error):
                            self.logger.error(error: error as NSError, function: #function, line: #line)
                            if let data = response.data, let errorString = String(data: data, encoding: .utf8) {
                                self.logger.error(error: NSError(domain: "AFError", code: -1, userInfo: [NSLocalizedDescriptionKey: errorString]), function: #function, line: #line)
                            }
                            completion(false)
                        }
                    }
            case .failure(let error):
                self.logger.error(error: error as NSError, function: #function, line: #line)
                completion(false)
            }
        }
    }
    
    func deleteDeviceFromApp(for category: AppCategory, email: String, password: String, deviceId: String, completion: @escaping (Bool) -> Void) {
        configureApiUrl(for: category)
        
        login(for: category, email: email, password: password) { result in
            switch result {
            case .success(let combinedData):
                guard let token = combinedData.loginResponse.token else {
                    self.errorMessage = ToastMessageStrings.failedToRetrieveToken
                    completion(false)
                    return
                }
                
                var headers: HTTPHeaders = [:]
                var deviceEndpoint: String = ""
                
                switch category {
                case .balanceHealth:
                    headers = ["x-access-token": token]
                    deviceEndpoint = "\(self.rootUrl)monitor/"
                    
                    if let accountDetails = combinedData.accountDetails as? [Monitor] {
                        if let monitor = accountDetails.first(where: { $0.broadcastId == deviceId }) {
                            let deleteEndpoint = "\(deviceEndpoint)\(monitor.id)"
                            
                            AF.request(deleteEndpoint, method: .delete, headers: headers)
                                .validate()
                                .responseData { deleteResponse in
                                    switch deleteResponse.result {
                                    case .success(let responseData):
                                        let responseString = String(data: responseData, encoding: .utf8) ?? "No Response Data"
                                        self.logger.info(info: "Device deleted successfully. Response: \(responseString)", function: #function, line: #line)
                                        ToastService.shared.presentToast(with: ToastMessageStrings.deviceUnlinkedSuccessfully)
                                        completion(true)
                                    case .failure(let error):
                                        if let responseData = deleteResponse.data {
                                            _ = String(data: responseData, encoding: .utf8) ?? "No Response Data"
                                            self.logger.error(error: error as NSError, function: #function, line: #line)
                                        }
                                        self.logger.error(error: error as NSError, function: #function, line: #line)
                                        completion(false)
                                    }
                                }
                        } else {
                            self.errorMessage = ToastMessageStrings.deviceNotFoundInAccountDetails
                            completion(false)
                        }
                    } else {
                        self.errorMessage = ToastMessageStrings.invalidAccountDetailsFormat
                        completion(false)
                    }
                    
                case .weightGurus:
                    headers = ["Authorization": "Bearer \(token)"]
                    deviceEndpoint = "\(self.rootUrl)paired-scale/"
                    
                    if let accountDetails = combinedData.accountDetails as? [WeightGurusAccountDetailsResponse] {
                        if let accountDetail = accountDetails.first(where: { $0.broadcastId == Int(deviceId) }) {
                            let deleteEndpoint = "\(deviceEndpoint)\(accountDetail.id)"
                            
                            AF.request(deleteEndpoint, method: .delete, headers: headers)
                                .validate()
                                .responseData { deleteResponse in
                                    switch deleteResponse.result {
                                    case .success(let responseData):
                                        let responseString = String(data: responseData, encoding: .utf8) ?? "No Response Data"
                                        self.logger.info(info: "Device deleted successfully. Response: \(responseString)", function: #function, line: #line)
                                        ToastService.shared.presentToast(with: ToastMessageStrings.deviceUnlinkedSuccessfully)
                                        completion(true)
                                    case .failure(let error):
                                        if let responseData = deleteResponse.data {
                                            let responseString = String(data: responseData, encoding: .utf8) ?? "No Response Data"
                                            self.logger.error(error: error as NSError,function: #function, line: #line)
                                        }
                                        self.logger.error(error: error as NSError, function: #function, line: #line)
                                        completion(false)
                                    }
                                }
                        } else {
                            self.errorMessage = ToastMessageStrings.deviceNotFoundInAccountDetails
                            completion(false)
                        }
                    } else {
                        self.errorMessage = ToastMessageStrings.invalidAccountDetailsFormat
                        completion(false)
                    }
                    
                default:
                    self.errorMessage = ToastMessageStrings.unsupportedCategory
                    completion(false)
                    return
                }
            case .failure(let error):
                self.errorMessage = "Login failed with error: \(error.localizedDescription)"
                self.logger.error(error: error as NSError, function: #function, line: #line)
                completion(false)
            }
        }
    }
    
    func addOperationToApp(for category: AppCategory, email: String, password: String, weightGurusDetails: WeightGurusOperationDetails? = nil, balanceHealthDetails: BalanceHealthOperationDetails? = nil, completion: @escaping (Bool) -> Void) {
        
        login(for: category, email: email, password: password) { result in
            switch result {
            case .success(let combinedData):
                guard let token = combinedData.loginResponse.token else {
                    completion(false)
                    return
                }
                
                var headers: HTTPHeaders = [:]
                var operationEndpoint: String = ""
                var operationDetails: [String: Any] = [:]
                
                switch category {
                case .weightGurus:
                    guard let details = weightGurusDetails else {
                        completion(false)
                        return
                    }
                    headers = ["Authorization": "Bearer \(token)"]
                    operationEndpoint = "\(self.rootUrl)operation"
                    operationDetails = [
                        "operationType": details.operationType,
                        "entryTimestamp": details.entryTimestamp,
                        "weight": details.weight,
                        "bodyFat": details.bodyFat,
                        "muscleMass": details.muscleMass,
                        "water": details.water,
                        "bmi": details.bmi,
                        "source": details.source.rawValue
                    ]
                case .balanceHealth:
                    guard let details = balanceHealthDetails else {
                        completion(false)
                        return
                    }
                    headers = ["x-access-token": token]
                    operationEndpoint = "\(self.rootUrl)operation"
                    operationDetails = [
                        "diastolic": details.diastolic,
                        "entryTimestamp": details.entryTimestamp,
                        "operation": details.operation,
                        "pulse": details.pulse,
                        "systolic": details.systolic,
                        "type": details.type.rawValue,
                        "userId": details.userId
                    ]
                default:
                    completion(false)
                    return
                }
                
                AF.request(operationEndpoint, method: .post, parameters: operationDetails, encoding: JSONEncoding.default, headers: headers)
                    .validate()
                    .responseData { response in
                        switch response.result {
                        case .success(let data):
                            self.logger.info(info: "Operation added successfully", function: #function, line: #line)
                            ToastService.shared.presentToast(with: ToastMessageStrings.entryAddedSuccessfully)
                            completion(true)
                        case .failure(let error):
                            self.logger.error(error: error as NSError, function: #function, line: #line)
                            completion(false)
                        }
                    }
            case .failure(let error):
                self.logger.error(error: error as NSError, function: #function, line: #line)
                completion(false)
            }
        }
    }
    
    func deleteOperationFromApp(for category: AppCategory, email: String, password: String, entryTimestamp: String, completion: @escaping (Bool) -> Void) {
        
        login(for: category, email: email, password: password) { result in
            switch result {
            case .success(let combinedData):
                guard let token = combinedData.loginResponse.token else {
                    completion(false)
                    return
                }
                
                var headers: HTTPHeaders = [:]
                var operationEndpoint: String = ""
                var operationDetails: [String: Any] = [:]
                
                switch category {
                case .weightGurus:
                    headers = ["Authorization": "Bearer \(token)"]
                    operationEndpoint = "\(self.rootUrl)operation"
                    operationDetails = [
                        "operationType": "delete",
                        "entryTimestamp": entryTimestamp
                    ]
                case .balanceHealth:
                    headers = ["x-access-token": token]
                    operationEndpoint = "\(self.rootUrl)operation"
                    operationDetails = [
                        "diastolic": 0,
                        "entryTimestamp": entryTimestamp,
                        "operation": "delete",
                        "pulse": 0,
                        "systolic": 0,
                        "type": "device",
                        "userId": ""
                    ]
                default:
                    completion(false)
                    return
                }
                
                AF.request(operationEndpoint, method: .post, parameters: operationDetails, encoding: JSONEncoding.default, headers: headers)
                    .validate()
                    .responseData { response in
                        switch response.result {
                        case .success(_):
                            self.logger.info(info: "Operation deleted successfully", function: #function, line: #line)
                            ToastService.shared.presentToast(with: ToastMessageStrings.entryDeletedSuccessfully)
                            completion(true)
                        case .failure(let error):
                            self.logger.error(error: error as NSError, function: #function, line: #line)
                            completion(false)
                        }
                    }
            case .failure(let error):
                self.logger.error(error: error as NSError, function: #function, line: #line)
                completion(false)
            }
        }
    }
}
