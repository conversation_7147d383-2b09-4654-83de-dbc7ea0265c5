//
//  R4ScaleService.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 26/03/25.
//

import Foundation
import GGBluetoothSwiftPackage

class R4ScaleService: ObservableObject {
    public static let shared = R4ScaleService()
    @Injector var userProfileService: UserProfileUpdationService
    private let session = URLSession.shared
    var toastLang = ToastMessageStrings.self
    var commonLang = CommonStrings.self
    var lang = APIKeyStrings.self
    private let logger: AppLogger = AppLogger(category: String(describing: R4ScaleService.self))
    
    func R4ScaleAccountCreation(for category: AppCategory, router: Router<DashboardRoutes>) async -> String? {
        let loginDetails = DeviceService().fetchUserLogin(for: category)
        
        guard loginDetails?.isLoggedIn == true,
              let email = loginDetails?.email,
              let password = loginDetails?.password else {
            ToastService.shared.presentToast(with: toastLang.noLoginDataFound)
            router.navigate(to: .login(category, .scan))
            return nil
        }
        
        guard let accessToken = await authenticate(email: email, password: password, for: category) else {
            return nil
        }
        
        return await fetchAdditionalScaleInfo(accessToken: accessToken, for: category)
    }
    
    func authenticate(email: String, password: String, for category: AppCategory) async -> String? {
        guard let url = category.loginURL else { return nil }
        
        var request = URLRequest(url: url)
        request.httpMethod = HTTPMethod.post.rawValue
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let body: [String: Any] = [commonLang.email: email, commonLang.password: password]
        request.httpBody = try? JSONSerialization.data(withJSONObject: body)
        
        do {
            let (data, _) = try await session.data(for: request)
            let json = try JSONSerialization.jsonObject(with: data) as? [String: Any]
            return json?[commonLang.accessToken] as? String
        } catch {
            return nil
        }
    }
    
    func fetchAdditionalScaleInfo(accessToken: String, for category: AppCategory) async -> String? {
        guard let url = category.scaleInfoURL else { return nil }
        
        var request = URLRequest(url: url)
        request.httpMethod = HTTPMethod.get.rawValue
        request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        
        do {
            let (data, _) = try await session.data(for: request)
            let json = try JSONSerialization.jsonObject(with: data) as? [String: Any]
            return json?[commonLang.token] as? String
        } catch {
            return nil
        }
    }
    
    func storeR4ScalePreference(broadcastId: String, preference: R4ScalePreference, for category: AppCategory) async -> Bool {
        guard let loginDetails = DeviceService().fetchUserLogin(for: category),
              loginDetails.isLoggedIn,
              let email = loginDetails.email,
              let password = loginDetails.password else {
            ToastService.shared.presentToast(with: toastLang.noLoginDataFound)
            return false
        }
        
        guard let accessToken = await authenticate(email: email, password: password, for: category) else {
            return false
        }
        
        guard let url = category.pairedScaleURL else { return false }
        
        var request = URLRequest(url: url)
        request.httpMethod = HTTPMethod.get.rawValue
        request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        
        do {
            let (data, response) = try await session.data(for: request)
            guard (response as? HTTPURLResponse)?.statusCode == 200 else { return false }
            
            guard let scaleList = try JSONSerialization.jsonObject(with: data) as? [[String: Any]] else { return false }
            
            // Refactored broadcastId conversion logic
            guard let broadcastIdInt = convertBroadcastIdToInt(broadcastId) else {
                return false
            }
            
            guard let matchingScale = scaleList.first(where: {
                ($0["broadcastId"] as? Int == broadcastIdInt) &&
                (["0412", "0413"].contains($0["sku"] as? String ?? ""))
            }), let scaleId = matchingScale["id"] as? String else {
                return false
            }
            
            return await postScalePreference(scaleId: scaleId, accessToken: accessToken, preference: preference, for: category)
        } catch {
            return false
        }
    }
    
    private func convertBroadcastIdToInt(_ broadcastId: String) -> Int? {
        if let intBroadcastId = Int(broadcastId) {
            return intBroadcastId
        } else {
            let nsError = NSError(domain: String(describing: DeviceService.self), code: 1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert broadcastId to Int: \(broadcastId)"])
            self.logger.error(error: nsError, function: #function, line: #line)
        }
        
        if let hexBroadcastId = HexConversionHelper.convertHexToInt(value: broadcastId) {
            return hexBroadcastId
        } else {
            let nsError = NSError(domain: String(describing: DeviceService.self), code: 1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert broadcastId to Int: \(broadcastId)"])
            self.logger.error(error: nsError, function: #function, line: #line)
        }

        let nsError = NSError(domain: String(describing: DeviceService.self), code: 1, userInfo: [NSLocalizedDescriptionKey: "Both integer and hex conversions failed for broadcastId: \(broadcastId)"])
                self.logger.error(error: nsError, function: #function, line: #line)
        
        return nil
    }
    
    func postScalePreference(scaleId: String, accessToken: String, preference: R4ScalePreference, for category: AppCategory) async -> Bool {
        guard let url = category.postPreferenceURL else { return false }
        var request = URLRequest(url: url)
        request.httpMethod = HTTPMethod.post.rawValue
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        
        let body: [String: Any] = [
            lang.scaleId: scaleId,
            lang.tzOffset: preference.tzOffset,
            lang.timeFormat: preference.timeFormat,
            lang.displayName: preference.displayName,
            lang.displayMetrics: preference.displayMetrics,
            lang.shouldMeasurePulse: preference.shouldMeasurePulse,
            lang.shouldMeasureImpedance: preference.shouldMeasureImpedance,
            lang.shouldFactoryReset: preference.shouldFactoryReset,
            lang.wifiFotaScheduleTime: preference.wifiFotaScheduleTime
        ]
        
        request.httpBody = try? JSONSerialization.data(withJSONObject: body)
        
        do {
            let (_, response) = try await session.data(for: request)
            return (response as? HTTPURLResponse)?.statusCode == 200
        } catch {
            return false
        }
    }
}
