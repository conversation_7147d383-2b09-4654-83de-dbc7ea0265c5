//
//  AccountService.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/11/24.
//

import Foundation

class AccountService: ObservableObject {
    static let shared: AccountService = AccountService()
    
    private init() {
        registerService()
    }
    
    private func registerService() {
        DependencyContainer.shared.register(BluetoothService())
        DependencyContainer.shared.register(ScannedDeviceModel())
        DependencyContainer.shared.register(DeviceService())
        DependencyContainer.shared.register(DeviceInfoManager())
        DependencyContainer.shared.register(UserProfileUpdationService())
        DependencyContainer.shared.register(ToastService())
        DependencyContainer.shared.register(MeasurementEntryService())
        DependencyContainer.shared.register(LoginService())
        DependencyContainer.shared.register(LoginDataService())
        DependencyContainer.shared.register(DeviceConnectionState())
        DependencyContainer.shared.register(PermissionsService())
        DependencyContainer.shared.register(R4ScaleService())
        DependencyContainer.shared.register(PercentileService())
    }
    
    private func deRegisterService() {
        DependencyContainer.shared.dependencies.removeValue(forKey: String(describing: BluetoothService.self))
        DependencyContainer.shared.dependencies.removeValue(forKey: String(describing: ScannedDeviceModel.self))
        DependencyContainer.shared.dependencies.removeValue(forKey: String(describing: DeviceService.self))
        DependencyContainer.shared.dependencies.removeValue(forKey: String(describing: DeviceInfoManager.self))
        DependencyContainer.shared.dependencies.removeValue(forKey: String(describing: UserProfileUpdationService.self))
        DependencyContainer.shared.dependencies.removeValue(forKey: String(describing: ToastService.self))
        DependencyContainer.shared.dependencies.removeValue(forKey: String(describing: MeasurementEntryService.self))
        DependencyContainer.shared.dependencies.removeValue(forKey: String(describing: LoginService.self))
        DependencyContainer.shared.dependencies.removeValue(forKey: String(describing: LoginDataService.self))
        DependencyContainer.shared.dependencies.removeValue(forKey: String(describing: DeviceConnectionState.self))
        DependencyContainer.shared.dependencies.removeValue(forKey: String(describing: PermissionsService.self))
        DependencyContainer.shared.dependencies.removeValue(forKey: String(describing: R4ScaleService.self))
        DependencyContainer.shared.dependencies.removeValue(forKey: String(describing: PercentileService.self))
    }
    
    deinit {
        deRegisterService()
    }
}
