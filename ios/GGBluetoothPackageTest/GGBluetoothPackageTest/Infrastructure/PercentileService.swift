//
//  PercentileService.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 15/05/25.
//

import Foundation

class PercentileService: ObservableObject {
    @Injector var userProfileService: UserProfileUpdationService
    private let growthChartHelper = GrowthChartDataHelper()
    
    func getPercentileData() -> [BabyWeightPercentileData] {
        guard let profile = userProfileService.fetchUserProfile(),
              let gender = extractGender(from: profile),
              let birthDate = profile.birthday else {
            return []
        }
        
        let daysSinceBirth = calculateDaysSinceBirth(birthDate: birthDate)
        
        guard let percentileData = growthChartHelper.loadPercentileLineData(for: gender) else {
            return []
        }
        
        return filterAndTransformData(percentileData, maxDay: daysSinceBirth)
    }
    
    private func extractGender(from profile: UserProfile) -> Gender? {
        guard let genderString = profile.gender else { return nil }
        return Gender(rawValue: genderString.lowercased())
    }
    
    private func calculateDaysSinceBirth(birthDate: Date, to date: Date = Date()) -> Int {
        let calendar = Calendar.current
        let days = calendar.dateComponents([.day], from: birthDate, to: date).day ?? 0
        return max(0, days)
    }
    
    private func filterAndTransformData(_ data: [BabyWeightPercentileData], minDay: Int = 0, maxDay: Int) -> [BabyWeightPercentileData] {
        var filteredData = data.filter { $0.day >= minDay && $0.day <= maxDay }
        
        if filteredData.count == 1 {
            if let before = data.last(where: { $0.day < minDay }) {
                filteredData.insert(before, at: 0)
            }
            if let after = data.first(where: { $0.day > maxDay }) {
                filteredData.append(after)
            }
        }
        
        return filteredData.map {
            BabyWeightPercentileData(
                day: $0.day,
                fifth: $0.fifth,
                tenth: $0.tenth,
                twentyFifth: $0.twentyFifth,
                fiftieth: $0.fiftieth,
                seventyFifth: $0.seventyFifth,
                ninetieth: $0.ninetieth,
                ninetyFifth: $0.ninetyFifth
            )
        }
    }
    
    func dateFromBirth(birthDate: Date, offsetDays: Int) -> Date? {
        Calendar.current.date(byAdding: .day, value: offsetDays, to: birthDate)
    }
    
    func filteredData(for timeFrame: TimeFrame) -> [BabyWeightPercentileData] {
        guard let profile = userProfileService.fetchUserProfile(),
              let gender = extractGender(from: profile),
              let birthDate = profile.birthday,
              let percentileData = growthChartHelper.loadPercentileLineData(for: gender) else {
            return []
        }
        
        let daysSinceBirth = calculateDaysSinceBirth(birthDate: birthDate)
        let range: Int
        
        switch timeFrame {
        case .week:
            range = 7
        case .month:
            range = 30
        case .year:
            range = 365
        case .total:
            range = daysSinceBirth
        }
        
        let startDay = max(0, daysSinceBirth - range)
        let endDay = daysSinceBirth
        let result = filterAndTransformData(percentileData, minDay: startDay, maxDay: endDay)
        return result
    }
    
}
