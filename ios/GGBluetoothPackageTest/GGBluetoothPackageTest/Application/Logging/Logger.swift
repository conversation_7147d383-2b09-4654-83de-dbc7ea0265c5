//
//  Logger.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 28/11/24.
//

import Foundation
import os

public class AppLogger {
    private let logger: Logger?
    
    public func info(info: String, function: StaticString, line: UInt) {
        if let logger = self.logger {
            logger.info("\(function)(\(line)): \(info)")
        }
    }
    
    public func debug(info: String, function: StaticString, line: UInt) {
        if let logger = self.logger {
            logger.debug("\(function)(\(line)): \(info)")
        }
    }
    
    public func error(error: NSError, function: StaticString, line: UInt) {
        if let logger = self.logger {
            logger.error("\(function)(\(line)) Error : \(error.localizedDescription)")
        }
    }
    
    init(category: String) {
        logger = Logger(
            subsystem: Bundle.main.bundleIdentifier!,
            category: category
        )
    }

}
