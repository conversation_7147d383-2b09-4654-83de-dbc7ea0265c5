//
//  OperationProtocol.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 23/01/25.
//

import SwiftUI

protocol OperationProtocol {
    var weight: Float? {get set}
    var bodyFat: Int? {get set}
    var muscleMass: Int? {get set}
    var water : Int? {get set}
    var bmi : Float? {get set}
    var pulse : Int? {get set}
    var visceralFatLevel : Int? {get set}
    var subcutaneousFatPercent: Int? {get set}
    var proteinPercent: Int? {get set}
    var skeletalMusclePercent : Int? {get set}
    var bmr: Int? {get set}
    var metabolicAge: Int? {get set}
}
