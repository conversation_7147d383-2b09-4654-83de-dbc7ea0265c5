//
//  DeviceRepositoryProtocol.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 02/12/24.
//

import Foundation

protocol DeviceRepositoryProtocol {
    func fetchDevice(by broadcastId: String) throws -> Device?
    func fetchAllDevices() throws -> [Device]?
    func saveDevice(
        appCategory: String,
        name: String,
        broadcastId: String,
        userNumber: Int?,
        batteryLevel: Int?,
        macAddress: String?,
        password: String?,
        protocolType: String?,
        token: String?,
        deviceType: String?,
        preference: Preference?,
        isDeviceLinkedToApp: Bool,
        isUserAdded: Bool,
        isWifiConfigured: Bool
    ) async throws
}

protocol DeviceInfoProtocol {
    func readScaleDetails() -> [Scale]?
    func readMonitorDetails() -> [MonitorType]?
}
