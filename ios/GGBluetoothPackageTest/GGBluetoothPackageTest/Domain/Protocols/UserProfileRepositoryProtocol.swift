//
//  UserProfileRepositoryProtocol.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 06/01/25.
//

import Foundation

protocol UserProfileRepositoryProtocol {
     func saveUserProfile(
        name: String,
        email: String,
        birthday: Date,
        height: Int16,
        weight: Double,
        gender: String,
        bodyType: String,
        goalType: String,
        goalWeight: Double,
        unitType: String
     )
}

