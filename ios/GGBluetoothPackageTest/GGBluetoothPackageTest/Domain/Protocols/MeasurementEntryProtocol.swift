//
//  MeasurementEntryProtocol.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 10/02/25.
//

import Foundation

protocol MeasurementEntryProtocol {
    func fetchAllMeasurementEntries() -> [MeasurementEntry]
    func saveMeasurementEntry(_ operation: Operation, appCategory: String?, broadcastId: String, deviceSKU: String?) -> Bool
    func deleteMeasurementEntry(entryTimestamp: Date)
}
