//
//  Scale.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 03/12/24.
//

import Foundation

public struct Scale: Codable, Hashable {
    public var productName: String?
    public var sku: String?
    public var imgPath: String?
    public var nickname: String?
    public var bodyComp: Bool?
    public var isConnected: Bool? = false
    public var isWifiConfigured: Bool?
    
    public var name: String?
    public var type: String?
    public var userId: String?
    public var userNumber: Int?
    public var scaleToken: String?
    public var broadcastId: String?
    public var broadcastIdString: String?
    public var latestVersion: String?
    public var id: String?
    public var mac: String?
    public var password: String?
    public var createdAt: String?
    public var isTemporary: Bool?
    public var isDeleted: Bool?
}

struct MonitorType: Codable {
    let name: String
    let sku: String
    let broadcastName: String
    let hasNumericUsers: Bool
    let `protocol`: String
    let toggleButton: Bool
    let darkColor: Bool
    let wristMonitor: Bool
    let hasStartButton: Bool
}
