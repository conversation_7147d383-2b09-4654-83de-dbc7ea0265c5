//
//  WrappedLoginResponse.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 27/01/25.
//

import Foundation

struct WrappedLoginResponse: LoginResponseProtocol {
    let wrapped: LoginResponseProtocol
    
    init<T: LoginResponseProtocol>(_ wrapped: T) {
        self.wrapped = wrapped
    }
    
    private enum CodingKeys: String, CodingKey {
        case type, wrapped
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(String(describing: type(of: wrapped)), forKey: .type)
        try wrapped.encode(to: encoder)
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let type = try container.decode(String.self, forKey: .type)
        
        if type == String(describing: WeightGurusLoginResponse.self) {
            wrapped = try WeightGurusLoginResponse(from: decoder)
        } else if type == String(describing: BalanceHealthLoginResponse.self) {
            wrapped = try BalanceHealthLoginResponse(from: decoder)
        } else {
            throw DecodingError.dataCorruptedError(forKey: .type, in: container, debugDescription: "Unknown type: \(type)")
        }
    }
    
    var token: String? {
        if let weightGurusResponse = wrapped as? WeightGurusLoginResponse {
            return weightGurusResponse.accessToken
        } else if let balanceHealthResponse = wrapped as? BalanceHealthLoginResponse {
            return balanceHealthResponse.token
        }else if let smartBabyResponse = wrapped as? SmartBabyLoginResponse {
            return smartBabyResponse.accessToken
        }
        return nil
    }
}
