//
//  GraphEntry.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 30/04/25.
//

import Foundation

struct GraphWeightEntry: Identifiable, Equatable, Hashable {
    let id = UUID()
    let date: Date
    let weight: Double
    let sku: String
}

struct BalanceHealthEntry: Identifiable, Equatable, Hashable {
    let id = UUID()
    let date: Date
    let systole: Int
    let diastole: Int
    let pulse: Int
}

struct GraphSeries: Identifiable, Equatable {
    let id = UUID()
    let date: Date
    let value: Double
    let series: String
}

struct BabyWeightData: Codable {
    let day: Int
    let m: Int
    let sd: Int
}

struct BabyWeightPercentileData: Codable {
    let day: Int
    let fifth: Int
    let tenth: Int
    let twentyFifth: Int
    let fiftieth: Int
    let seventyFifth: Int
    let ninetieth: Int
    let ninetyFifth: Int
}

struct BGMData: Identifiable, Equatable, Hashable {
    let id = UUID()
    let date: Date
    let glucose: Double
    let unit: String
}

struct GraphDataResult {
    var graphWeights: [GraphWeightEntry] = []
    var healthEntries: [BalanceHealthEntry] = []
    var goalWeight: Double = 0
    var rawWeights: [GraphWeightEntry] = []
    var babyPercentiles: [BabyWeightPercentileData] = []
    var bgmEntries: [BGMData] = []
}
