//
//  AppAccountDetailResponses.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 04/02/25.
//

import SwiftUI

struct BalanceHealthAccountDetailsResponse: Codable {
    let data: [Monitor]
}

struct Monitor: Codable, Hashable {
    let id: String
    let name: String
    let sku: String
    let type: String
    let broadcastId: String
    let peripheralIdentifier: String
    let mac: String
    let password: String
    let userNumber: Int
    let nickname: String
    let createdAt: String
}

struct WeightGurusAccountDetailsResponse: LoginResponseProtocol, Identifiable, Codable, Hashable {
    let id: String
    let nickname: String
    let type: String?
    let createdAt: String?
    let userNumber: Int?
    let mac: String?
    let broadcastId: Int
    let password: Int?
    let sku: String
    let name: String
    let peripheralIdentifier: String?
    let scaleToken: String?
    let preference: Preference?
    let latestVersion: String?
    
    struct Preference: Codable, Hashable {
        let tzOffset: Int?
        let timeFormat: String?
        let displayName: String?
        let displayMetrics: [String]?
        let shouldMeasurePulse: Bool?
        let shouldMeasureImpedance: Bool?
        let shouldFactoryReset: Bool?
        let wifiFotaScheduleTime: Int?
    }
}

struct SmartBabyAccountResponse: Codable {
    let id: String
    let babyPermissions: Int
    let birthdate: String
    let dueDate: String?
    let isBorn: Bool?
    let name: String
    let sex: String
    let birthWeightDecigrams: Int
    let birthLengthMillimeters: Int
    let isOwnedByAccount: Bool
}


struct SmartBabyOperationsResponse: Codable {
    let operations: [SmartBabyOperation]
    let timestamp: String
}

struct SmartBabyOperation: Codable {
    let babyId: String
    let babyLengthMillimeters: Int?
    let babyWeightDecigrams: Int?
    let diaperType: String?
    let entryId: String
    let entryNote: String?
    let entryTimestamp: String
    let entryType: String
    let feedingMilliliters: Int?
    let feedingTimeSecondsLeft: Int?
    let feedingTimeSecondsRight: Int?
    let id: String
    let operationType: String
    let photo: String?
    let serverTimestamp: String
    let sleepTimeMinutes: Int?
    let source: String?
}
