//
//  AppLoginResponses.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 04/02/25.
//

import SwiftUI

struct WeightGurusLoginResponse: LoginResponseProtocol, Hashable {
    struct Account: Codable, Hashable {
        let id: String
        let email: String
        let firstName: String
        let lastName: String
        let gender: String
        let zipcode: String
        let weightUnit: String
        let isWeightlessOn: Bool
        let preferredInputMethod: String?
        let height: Int
        let activityLevel: String
        let dob: String
        let weightlessBodyFat: Double?
        let weightlessMuscle: Double?
        let weightlessTimestamp: String?
        let weightlessWeight: Double?
        let isStreakOn: Bool
        let dashboardType: String
        let dashboardMetrics: [String]
        let goalType: String
        let goalWeight: Int
        let initialWeight: Int
        let shouldSendEntryNotifications: Bool
        let shouldSendWeightInEntryNotifications: Bool
    }
    
    let account: Account
    let accessToken: String
    let refreshToken: String
    let expiresAt: String
}

struct BalanceHealthLoginResponse: LoginResponseProtocol, Hashable {
    struct User: Codable, Hashable {
        let id: String
        let email: String
        let firstName: String
        let lastName: String
        let gender: String
        let zipcode: String
        let dob: String
    }
    
    let user: User
    let token: String
}

struct SmartBabyLoginResponse: LoginResponseProtocol, Hashable {
    let id: String
    let email: String
    let firstName: String
    let lastName: String
    let measurementUnits: String
    let zipcode: String
    let tokenExpiresAt: String
    let accountSettings: [AccountSetting]
    let hasSeenAppReview: Bool
    let hasSeenScaleReview: Bool
    let accessToken: String
    let refreshToken: String
}

struct AccountSetting: Codable, Hashable {
    let key: String
    let value: String
}
