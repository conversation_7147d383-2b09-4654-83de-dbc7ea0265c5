//
//  DashboardMetricsData.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 09/04/25.
//

import Foundation

struct MetricItem: Equatable, Identifiable {
    let id: String
    var label: String
    var icon: String
    var order: Int16 
    var isRemoved: Bool = false
    var tileSizeType: TileSizeType = .small
   
    static func == (lhs: MetricItem, rhs: MetricItem) -> Bool {
        lhs.id == rhs.id
    }
}

struct MetricsDataStore {
    
    static let bodyMetrics: [MetricItem] = [
        MetricItem(id: "BMI", label: DisplayMetricsStrings.bmi, icon: AppAssets.bmi, order: 0),
        MetricItem(id: "BF", label: DisplayMetricsStrings.bodyFat, icon: AppAssets.bodyFat, order: 1),
        MetricItem(id: "Muscle", label: DisplayMetricsStrings.muscleMass, icon: AppAssets.muscleMass, order: 2),
        MetricItem(id: "BodyWater", label: DisplayMetricsStrings.bodyWater, icon: AppAssets.bodyWater, order: 3),
        MetricItem(id: "BoneMass", label: DisplayMetricsStrings.boneMass, icon: AppAssets.boneMassIcon, order: 4),
        MetricItem(id: "VisceralFat", label: DisplayMetricsStrings.visceralFat, icon: AppAssets.visceralFatIcon, order: 5),
        MetricItem(id: "SubcutaneousFat", label: DisplayMetricsStrings.subcutaneousFat, icon: AppAssets.subcutaneousFatIcon, order: 6),
        MetricItem(id: "Protein", label: DisplayMetricsStrings.protein, icon: AppAssets.proteinIcon, order: 7),
        MetricItem(id: "SkeletalMuscles", label: DisplayMetricsStrings.skeletalMuscles, icon: AppAssets.skeletalMuscleIcon, order: 8),
        MetricItem(id: "BMR", label: DisplayMetricsStrings.bmr, icon: AppAssets.bmrIcon, order: 9),
        MetricItem(id: "MetabolicAge", label: DisplayMetricsStrings.metabolicAge, icon: AppAssets.metabolicAgeIcon, order: 10),
        MetricItem(id: "HeartRate", label: DisplayMetricsStrings.heartRate, icon: AppAssets.pulseIcon, order: 11)
    ]
    
    static let otherMetrics: [MetricItem] = [
        MetricItem(id: "GoalProgress", label: DisplayMetricsStrings.goalProgress, icon: "", order: 0),
        MetricItem(id: "DailyAverage", label: DisplayMetricsStrings.dailyAverage, icon: "", order: 1),
        MetricItem(id: "WeeklyAverage", label: DisplayMetricsStrings.weeklyAverage, icon: "", order: 2),
        MetricItem(id: "MonthlyAverage", label: DisplayMetricsStrings.monthlyAverage, icon: "", order: 3)
    ]

}
