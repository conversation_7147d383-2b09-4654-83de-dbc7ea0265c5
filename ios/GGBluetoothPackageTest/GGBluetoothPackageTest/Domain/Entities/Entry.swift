//
//  Entry.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 03/01/25.
//

import Foundation

struct Entry {
    var measurementType: EntryMeasurementType = .unknown
    var bgm: Float?
    var bmr: Float?
    var bmi: Float?
    var bodyFat: Float?
    var boneMass: Float?
    var broadcastId: String
    var diastolic: Float?
    var displayWeight: Float?
    var entryTimestamp: Date?
    var impedance: Float?
    var meanPressure: Float?
    var metabolicAge: Float?
    var muscleMass: Float?
    var proteinPercent: Float?
    var pulse: Float?
    var skeletalMusclePercent: Float?
    var source: String?
    var spo: Float?
    var subcutaneousFatPercent: Float?
    var systolic: Float?
    var temperature: Float?
    var unit: String?
    var visceralFatLevel: Float?
    var water: Float?
    var weightInKg: Float?
    var weight: Float?
    var pulseAmplitudeIndex: Float?
    var errorCode: String?
}
