//
//  APIOperationDetails.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 14/03/25.
//

import Foundation

struct WeightGurusOperationDetails {
    let operationType: String
    let entryTimestamp: String
    let weight: Int
    let bodyFat: Int
    let muscleMass: Int
    let water: Int
    let bmi: Int
    let source: WGOperationSource
}

struct BalanceHealthOperationDetails {
    let diastolic: Int
    let entryTimestamp: String
    let operation: String
    let pulse: Int
    let systolic: Int
    let type: BHOperationSource
    let userId: String
}
