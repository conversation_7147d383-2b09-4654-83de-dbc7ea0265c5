//
//  DeviceInfo.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 24/12/24.
//

import Foundation

struct DeviceInfo: Codable, Equatable, Hashable {
    var name: String
    var sku: String
    var image: String
    var protocolType: String
    var manufacturer: String
    var app : AppCategory
    var errorcodes: [ErrorCode]
    
    static func == (lhs: DeviceInfo, rhs: DeviceInfo) -> Bool {
        return lhs.name == rhs.name && lhs.sku == rhs.sku && lhs.image == rhs.image
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(name)
        hasher.combine(sku)
        hasher.combine(image)
    }
}

struct ErrorCode: Codable, Equatable, Hashable {
    var code: String
    var title: String?
    var description: String
}


