//
//  Operation.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 23/01/25.
//

import Foundation
import Combine

struct GetOperationResponse: Codable {
    let operations: [Operation]
    let timestamp: String
}

struct Operation: Codable, OperationProtocol, Hashable {
    var appCategory: AppCategory
    var bgm: Int?
    var bmi: Float?
    var bodyFat: Int?
    var boneMass: Int?
    var broadcastId: String
    var broadcastIdString: String
    var bmr: Int?
    var deviceSKU: String
    var diastolic: Int?
    var entryTimestamp: Date
    var id: UUID
    var impedance: Int?
    var meanPressure: Int?
    var metabolicAge: Int?
    var muscleMass: Int?
    var operationType: OperationType
    var proteinPercent: Int?
    var pulse: Int?
    var protocolType: String?
    var serverTimestamp: String?
    var skeletalMusclePercent: Int?
    var source: Source?
    var spo: Int?
    var subcutaneousFatPercent: Int?
    var systolic: Int?
    var temperature: Float?
    var visceralFatLevel: Int?
    var water: Int?
    var weight: Float?
    var weightInKg: Float?
    var measurementUnit: Unit?
    var pulseAmplitudeIndex: Float?
    
    init(
        appCategory: AppCategory? = nil,
        bgm: Int? = nil,
        bmi: Float? = nil,
        bodyFat: Int? = nil,
        boneMass: Int? = nil,
        broadcastId: String? = nil,
        broadcastIdString: String? = nil,
        bmr: Int? = nil,
        deviceSKU: String? = nil,
        diastolic: Int? = nil,
        entryTimestamp: Date,
        id: UUID? = nil,
        impedance: Int? = nil,
        meanPressure: Int? = nil,
        metabolicAge: Int? = nil,
        muscleMass: Int? = nil,
        operationType: OperationType,
        proteinPercent: Int? = nil,
        pulse: Int? = nil,
        protocolType: String? = nil,
        serverTimestamp: String? = nil,
        skeletalMusclePercent: Int? = nil,
        source: Source? = nil,
        spo: Int? = nil,
        subcutaneousFatPercent: Int? = nil,
        systolic: Int? = nil,
        temperature: Float? = nil,
        visceralFatLevel: Int? = nil,
        water: Int? = nil,
        weight: Float? = nil,
        weightInKg: Float? = nil,
        measurementUnit: Unit? = nil,
        pulseAmplitudeIndex: Float? = nil
    ) {
        self.appCategory = appCategory ?? .all
        self.bgm = bgm
        self.bmi = bmi
        self.bodyFat = bodyFat
        self.boneMass = boneMass
        self.broadcastId = broadcastId ?? ""
        self.broadcastIdString = broadcastIdString ?? ""
        self.bmr = bmr
        self.deviceSKU = deviceSKU ?? ""
        self.diastolic = diastolic
        self.entryTimestamp = entryTimestamp
        self.id = id ?? UUID()
        self.impedance = impedance
        self.meanPressure = meanPressure
        self.metabolicAge = metabolicAge
        self.muscleMass = muscleMass
        self.operationType = operationType
        self.proteinPercent = proteinPercent
        self.pulse = pulse
        self.protocolType = protocolType
        self.serverTimestamp = serverTimestamp
        self.skeletalMusclePercent = skeletalMusclePercent
        self.source = source
        self.spo = spo
        self.subcutaneousFatPercent = subcutaneousFatPercent
        self.systolic = systolic
        self.temperature = temperature
        self.visceralFatLevel = visceralFatLevel
        self.water = water
        self.weight = weight
        self.weightInKg = weightInKg
        self.measurementUnit = measurementUnit
        self.pulseAmplitudeIndex = pulseAmplitudeIndex
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(appCategory)
        hasher.combine(bgm)
        hasher.combine(bmi)
        hasher.combine(bodyFat)
        hasher.combine(boneMass)
        hasher.combine(broadcastId)
        hasher.combine(broadcastIdString)
        hasher.combine(bmr)
        hasher.combine(deviceSKU)
        hasher.combine(diastolic)
        hasher.combine(entryTimestamp)
        hasher.combine(id)
        hasher.combine(impedance)
        hasher.combine(meanPressure)
        hasher.combine(metabolicAge)
        hasher.combine(muscleMass)
        hasher.combine(operationType)
        hasher.combine(proteinPercent)
        hasher.combine(pulse)
        hasher.combine(protocolType)
        hasher.combine(serverTimestamp)
        hasher.combine(skeletalMusclePercent)
        hasher.combine(source)
        hasher.combine(spo)
        hasher.combine(subcutaneousFatPercent)
        hasher.combine(systolic)
        hasher.combine(temperature)
        hasher.combine(visceralFatLevel)
        hasher.combine(water)
        hasher.combine(weight)
        hasher.combine(weightInKg)
        hasher.combine(measurementUnit)
        hasher.combine(pulseAmplitudeIndex)
    }
    
    static func == (lhs: Operation, rhs: Operation) -> Bool {
        return lhs.appCategory == rhs.appCategory &&
        lhs.bgm == rhs.bgm &&
        lhs.bmi == rhs.bmi &&
        lhs.bodyFat == rhs.bodyFat &&
        lhs.boneMass == rhs.boneMass &&
        lhs.broadcastId == rhs.broadcastId &&
        lhs.broadcastIdString == rhs.broadcastIdString &&
        lhs.bmr == rhs.bmr &&
        lhs.deviceSKU == rhs.deviceSKU &&
        lhs.diastolic == rhs.diastolic &&
        lhs.entryTimestamp == rhs.entryTimestamp &&
        lhs.id == rhs.id &&
        lhs.impedance == rhs.impedance &&
        lhs.meanPressure == rhs.meanPressure &&
        lhs.metabolicAge == rhs.metabolicAge &&
        lhs.muscleMass == rhs.muscleMass &&
        lhs.operationType == rhs.operationType &&
        lhs.proteinPercent == rhs.proteinPercent &&
        lhs.pulse == rhs.pulse &&
        lhs.protocolType == rhs.protocolType &&
        lhs.serverTimestamp == rhs.serverTimestamp &&
        lhs.skeletalMusclePercent == rhs.skeletalMusclePercent &&
        lhs.source == rhs.source &&
        lhs.spo == rhs.spo &&
        lhs.subcutaneousFatPercent == rhs.subcutaneousFatPercent &&
        lhs.systolic == rhs.systolic &&
        lhs.temperature == rhs.temperature &&
        lhs.visceralFatLevel == rhs.visceralFatLevel &&
        lhs.water == rhs.water &&
        lhs.weight == rhs.weight &&
        lhs.weightInKg == rhs.weightInKg &&
        lhs.measurementUnit == rhs.measurementUnit &&
        lhs.pulseAmplitudeIndex == rhs.pulseAmplitudeIndex
    }
}
