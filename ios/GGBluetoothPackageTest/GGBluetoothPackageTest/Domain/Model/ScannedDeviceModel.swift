//
//  ScannedDeviceModel.swift
//  GGBluetoothPackageTest
//
//  Created by <PERSON> on 02/12/24.
//

import Combine
import Foundation
import GGBluetoothSwiftPackage
import SwiftUICore

final class ScannedDeviceModel {
    static let shared = ScannedDeviceModel()
    
    private var cancellables: Set<AnyCancellable> = []
    private var pairedDevices: Set<String> = []
    @Injector var deviceConnectionState: DeviceConnectionState
    
    @Injector var bluetoothService: BluetoothService
    @Injector var deviceService: DeviceService
    
    @Published var devices: [GGBTDevice] = []
    @Published var connectedDevices: [GGBTDevice] = []
    
    init(deviceConnectionState: DeviceConnectionState = DeviceConnectionState.shared) {
        self.deviceConnectionState = deviceConnectionState
    }
    
    private func removeDevice(_ device: GGBTDevice) {
        devices = devices.filter { $0.broadcastId != device.broadcastId }
        connectedDevices.removeAll { $0.broadcastId == device.broadcastId }
    }
    
    func startScanning(for category: AppCategory) {
        bluetoothService.scan(selectedCategory: category)
        
        bluetoothService.discoveredDevices
            .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .sink { [weak self] value in
                DispatchQueue.main.async {
                    self?.devices = value
                    value.forEach { device in
                        if !(self?.pairedDevices.contains(device.broadcastId) ?? false) {
                            self?.deviceService.pairDiscoveredDevice(broadcastId: device.broadcastId, selectedCategory: category)
                            self?.pairedDevices.insert(device.broadcastId)
                        }
                    }
                }
            }
            .store(in: &cancellables)
        
        bluetoothService.connectedDevices
            .sink { [weak self] value in
                DispatchQueue.main.async {
                    value.forEach { device in
                        self?.deviceConnectionState.updateDeviceConnectionState(deviceId: device.broadcastId, isConnected: true)
                        
                        ToastService.shared.presentToast(with: ToastMessageStrings.deviceConnected)
                    }
                    self?.connectedDevices = value
                }
            }
            .store(in: &cancellables)
        
        bluetoothService.deviceDisconnected
            .sink { [weak self] disconnectedDevice in DispatchQueue.main.async {
                
                self?.deviceConnectionState.updateDeviceConnectionState(deviceId: disconnectedDevice.broadcastId, isConnected: false)
                self?.removeDevice(disconnectedDevice)
                
                ToastService.shared.presentToast(with: ToastMessageStrings.deviceDisconnected)
            }
            }
            .store(in: &cancellables)
    }
    
    func stopScanning() {
        bluetoothService.stopScan()
        cancellables = cancellables.filter { cancellable in
            return cancellable !== bluetoothService.deviceDisconnected
        }
    }
    
    func clearDiscoveredDevices(for category: AppCategory) {
        devices.removeAll()
        bluetoothService.discoveredDevices.send([])
    }
}
