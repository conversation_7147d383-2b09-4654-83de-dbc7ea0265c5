package com.dmdbrands.library.ggbluetooth.helper

import android.app.Activity
import android.os.Handler
import android.os.Looper
import com.dmdbrands.library.ggbluetooth.enums.ClearDataType
import com.dmdbrands.library.ggbluetooth.enums.ConnectionStatus
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.dmdbrands.library.ggbluetooth.enums.GGBTSettingType
import com.dmdbrands.library.ggbluetooth.enums.GGScanResponseType
import com.dmdbrands.library.ggbluetooth.enums.GGUserActionResponseType
import com.dmdbrands.library.ggbluetooth.enums.GoalType
import com.dmdbrands.library.ggbluetooth.enums.TimeFormat
import com.dmdbrands.library.ggbluetooth.helper.CommonHelper.convertStringToPreference
import com.dmdbrands.library.ggbluetooth.helper.CommonHelper.convertToBGMEntry
import com.dmdbrands.library.ggbluetooth.helper.CommonHelper.convertToDeviceDetail
import com.dmdbrands.library.ggbluetooth.helper.CommonHelper.convertToEntry
import com.dmdbrands.library.ggbluetooth.helper.CommonHelper.convertToPulseEntry
import com.dmdbrands.library.ggbluetooth.helper.CommonHelper.convertToTemperatureEntry
import com.dmdbrands.library.ggbluetooth.helper.CommonHelper.convertToWeightEntry
import com.dmdbrands.library.ggbluetooth.helper.CommonHelper.covertToBPMEntry
import com.dmdbrands.library.ggbluetooth.helper.CommonHelper.getAppType
import com.dmdbrands.library.ggbluetooth.helper.CommonHelper.getTemperatureMuteMode
import com.dmdbrands.library.ggbluetooth.helper.CommonHelper.getWeightUnit
import com.dmdbrands.library.ggbluetooth.helper.CommonHelper.getWifiState
import com.dmdbrands.library.ggbluetooth.model.BluetoothContext
import com.dmdbrands.library.ggbluetooth.model.GGBGMEntry
import com.dmdbrands.library.ggbluetooth.model.GGBPMEntry
import com.dmdbrands.library.ggbluetooth.model.GGBTDevice
import com.dmdbrands.library.ggbluetooth.model.GGBTSetting
import com.dmdbrands.library.ggbluetooth.model.GGBTSettingValue
import com.dmdbrands.library.ggbluetooth.model.GGBTUser
import com.dmdbrands.library.ggbluetooth.model.GGBTUserProfile
import com.dmdbrands.library.ggbluetooth.model.GGBTWifiConfig
import com.dmdbrands.library.ggbluetooth.model.GGDevice
import com.dmdbrands.library.ggbluetooth.model.GGDeviceDetail
import com.dmdbrands.library.ggbluetooth.model.GGDeviceLog
import com.dmdbrands.library.ggbluetooth.model.GGDeviceLogResponse
import com.dmdbrands.library.ggbluetooth.model.GGDevicePreference
import com.dmdbrands.library.ggbluetooth.model.GGEntry
import com.dmdbrands.library.ggbluetooth.model.GGLiveDataResponse
import com.dmdbrands.library.ggbluetooth.model.GGPulseEntry
import com.dmdbrands.library.ggbluetooth.model.GGScaleEntry
import com.dmdbrands.library.ggbluetooth.model.GGScaleUserResponse
import com.dmdbrands.library.ggbluetooth.model.GGScanResponse
import com.dmdbrands.library.ggbluetooth.model.GGWeightEntry
import com.dmdbrands.library.ggbluetooth.model.GGWifiResponse
import com.dmdbrands.library.ggbluetooth.model.GGWifiSetupResponse
import com.google.gson.Gson
import com.greatergoods.ggbluetoothsdk.external.GGIBluetoothHandler
import com.greatergoods.ggbluetoothsdk.external.GGIStub
import com.greatergoods.ggbluetoothsdk.external.Utils
import com.greatergoods.ggbluetoothsdk.external.callbacks.GGReceiveDataCallback
import com.greatergoods.ggbluetoothsdk.external.callbacks.GGScanCallback
import com.greatergoods.ggbluetoothsdk.external.devices.GGIBLEDevice
import com.greatergoods.ggbluetoothsdk.external.enums.GGDeviceProtocolType
import com.greatergoods.ggbluetoothsdk.external.enums.GGGender
import com.greatergoods.ggbluetoothsdk.external.enums.GGGoalType
import com.greatergoods.ggbluetoothsdk.external.enums.GGMeasurementType
import com.greatergoods.ggbluetoothsdk.external.enums.GGOperationType
import com.greatergoods.ggbluetoothsdk.external.enums.GGResultType
import com.greatergoods.ggbluetoothsdk.external.enums.GGSwitchState
import com.greatergoods.ggbluetoothsdk.external.enums.GGThermometerErrorCode
import com.greatergoods.ggbluetoothsdk.external.enums.GGTimeDisplayType
import com.greatergoods.ggbluetoothsdk.external.enums.GGWeighingScaleDisplayDetail
import com.greatergoods.ggbluetoothsdk.external.enums.GGWifiState
import com.greatergoods.ggbluetoothsdk.external.enums.TemperatureUnit
import com.greatergoods.ggbluetoothsdk.external.enums.WeightUnit
import com.greatergoods.ggbluetoothsdk.external.models.GGAccountInfo
import com.greatergoods.ggbluetoothsdk.external.models.GGDeviceLogInfo
import com.greatergoods.ggbluetoothsdk.external.models.GGIBloodGlucoseInfo
import com.greatergoods.ggbluetoothsdk.external.models.GGIBloodPressureInfo
import com.greatergoods.ggbluetoothsdk.external.models.GGIBodyMetricsInfo
import com.greatergoods.ggbluetoothsdk.external.models.GGIDeviceInfo
import com.greatergoods.ggbluetoothsdk.external.models.GGIHealthTemperatureInfo
import com.greatergoods.ggbluetoothsdk.external.models.GGIMeasurementInfo
import com.greatergoods.ggbluetoothsdk.external.models.GGIPulseOximeterInfo
import com.greatergoods.ggbluetoothsdk.external.models.GGIWeightInfo
import com.greatergoods.ggbluetoothsdk.external.models.GGTimeData
import com.greatergoods.ggbluetoothsdk.external.models.GGUserPreference
import com.greatergoods.ggbluetoothsdk.external.models.GGWifiInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.Timer
import java.util.TimerTask

class GGBluetoothSDKHelper {

    companion object {
        private const val TAG = "GGBluetoothSDKHelper"
        private const val TEMPORARY_SKIP_TIME = 30 * 1000 // skip time 30 sec
        private val btHandler = Handler(Looper.getMainLooper())
        private var devices = mutableMapOf<String, GGDevice>()
        private var deviceLogs = mutableListOf<GGDeviceLog>()
        private var showPairingState: Boolean = false
        private var pairedDevices = mutableListOf<GGBTDevice>()
        private var account: GGBTUserProfile? = null
        private var accounts: Map<String, GGBTUserProfile>? = null
        private var appType: String? = GGAppType.NONE
        private var liveMeasurementInfo = mutableListOf<GGEntry>()
        private var temporarySkipDevices = mutableMapOf<String, Long>()
        private var skipDevices: MutableList<String> = mutableListOf()

        // Callback
        private lateinit var scanCallback: (data: GGScanResponse) -> Unit
        private var userCreationCallBack: ((data: GGUserActionResponseType) -> Unit)? = null
        private var userDeletionCallBack: ((data: GGUserActionResponseType) -> Unit)? = null
        private var wifiListCallBack: ((data: GGWifiResponse) -> Unit)? = null
        private var userListCallBack: ((data: GGScaleUserResponse) -> Unit)? = null
        private var wifiMacCallBack: ((data: String) -> Unit)? = null
        private var wifiSetupCallBack: ((data: GGWifiSetupResponse) -> Unit)? = null
        private var userUpdateCallBack: ((data: GGUserActionResponseType) -> Unit)? = null
        private var wifiCancelCallBack: ((data: Boolean) -> Unit)? = null
        private var wifiSSIDCallBack: ((data: String) -> Unit)? = null
        private var liveDataCallback: ((data: GGLiveDataResponse) -> Unit)? = null
        private var clearDataCallback: ((data: String) -> Unit)? = null
        private var deviceLogCallBack: ((data: GGDeviceLogResponse) -> Unit)? = null

        //Continuous Scan
        private var isContinuousScanningStarted: Boolean = false
        private var isScanInitialized: Boolean = false
        private var isScanIntervalInitialised: Boolean = false
        private var canPauseDeviceScan: Boolean = false
        private var canPauseContinuousScan: Boolean = false
        private var isPairingStarted: Boolean = false
        private var scanDuration: Long = 20000
        private var delayBetweenScan: Long = 10000

        //Handler
        private lateinit var bleHandler: GGIBluetoothHandler
        private lateinit var permissionHandler: GGPermissionHandler

        // Device Callback
        private val deviceScanCallback: GGScanCallback =
            object : GGScanCallback() {
                override fun onDeviceFound(deviceInfo: GGIDeviceInfo) {
                    super.onDeviceFound(deviceInfo)
                    log("New device found ${deviceInfo.macAddress}")
                    onNewDeviceFound(deviceInfo)
                }

                override fun onConnectionFailed(ggiDeviceInfo: GGIDeviceInfo) {
                    log("Device Connection Failed ${ggiDeviceInfo.macAddress}")
                    handleOnConnectionFailed(ggiDeviceInfo)
                }

            }

        private fun setAccountData(userProfile: GGBTUserProfile) {
            account = userProfile
        }

        private fun setAppType(appName: String) {
            appType = appName
            val licenseKey = getLicenseKey(appName)
            GGIStub.setLicenseKey(licenseKey)
        }

        fun setPairedDevices(devices: List<GGBTDevice>) {
            pairedDevices = devices as MutableList<GGBTDevice>
        }

        fun setAccounts(newAccounts: Map<String, GGBTUserProfile>) {
            accounts = newAccounts
        }

        fun clearPairedDevices() {
            devices.forEach {
                if (it.value.connectionStatus != ConnectionStatus.DISCONNECTED) {
                    it.value.bleDevice?.disconnect()
                }
            }
            pairedDevices.clear()
        }

        private fun setBleHandler(activity: Activity) {
            try {
                this.bleHandler = GGIStub.getHandler(activity.applicationContext, btHandler)
                log("Ble handler is set")
            } catch (err: Exception) {
                log(err.message.toString())
            }
        }

        private fun setPermissionHandler(handler: GGPermissionHandler) {
            permissionHandler = handler
            handler.subscribePermissions(scanCallback, appType.toString())
        }

        fun initializeScan(
            activity: BluetoothContext,
            userProfile: GGBTUserProfile,
            appType: String,
            permissionHandler: GGPermissionHandler,
            callback: (data: GGScanResponse) -> Unit
        ) {
            val bluetoothActivity = when (activity) {

                is BluetoothContext.ComposeActivity -> {
                    activity.activity
                }

                is BluetoothContext.AppActivity -> {
                    activity.activity
                }
            }
            scanCallback = callback
            setAppType(appType)
            setAccountData(userProfile)
            setPermissionHandler(permissionHandler)
            isScanInitialized = true
            val scope = CoroutineScope(Dispatchers.Main)

            scope.launch {
                permissionHandler.getScanPermissionStatus().collect { isScanPermissionGranted ->
                    if (isScanInitialized && isScanPermissionGranted && !isContinuousScanningStarted && !isScanIntervalInitialised) {
                        withContext(Dispatchers.Main) {
                            setBleHandler(bluetoothActivity)
                        }
                        startContinuousScan()
                        log("Scan permission granted")
                    } else if (!isScanPermissionGranted) {
                        stopScan(true)
                        isContinuousScanningStarted = false
                        isScanIntervalInitialised = false
                        log("Scan permission not granted")
                    }
                }
            }
        }


        fun stopScan(forceStop: Boolean, canReinitialization: Boolean = false) {
            if (forceStop) {
                clearSkipDevices()
            }
            if (::bleHandler.isInitialized) {
                bleHandler.stopScan()
            }
            if (canReinitialization) {
                canPauseContinuousScan = true
                isScanInitialized = false
                isContinuousScanningStarted = false
                isScanIntervalInitialised = false
                permissionHandler.stopPermissionSubscription()
                liveDataCallback = null
                log("Stop scanning")
            }
        }

        fun resumeScan(clearOnlyPairing: Boolean) {
            isPairingStarted = false
            if (!clearOnlyPairing) {
                canPauseDeviceScan = false
            }
            log("Scan resumed")
        }

        fun pauseScan() {
            canPauseDeviceScan = true
            log("Scan paused")
        }

        fun scanForPairing() {
            isPairingStarted = true
            clearPairedDevices()
            stopScan(true)
            scanDevices()
            log("Scan for pairing")
        }


        private fun clearSkipDevices() {
            log("Clearing skip devices")
            temporarySkipDevices.clear()
            skipDevices.clear()
        }

        @OptIn(DelicateCoroutinesApi::class)
        fun updateSetting(settings: List<GGBTSetting>, ggBTDevice: GGBTDevice) {
            log("Updating Setting $settings")
            val device = getBLEByDevice(ggBTDevice)
            if (device != null && device.connectionStatus == ConnectionStatus.CONNECTED) {
                settings.forEach {
                    GlobalScope.launch {
                        updateDeviceConfig(it, device)
                        delay(1000)
                    }
                }
            }
        }

        private fun startContinuousScan() {
            isContinuousScanningStarted = true
            canPauseContinuousScan = false
            scanDevices()
            if (!isScanIntervalInitialised) {
                isScanIntervalInitialised = true
                val scanIntervalTimer = Timer()
                scanIntervalTimer.schedule(object : TimerTask() {
                    override fun run() {
                        if (isPairingStarted || canPauseContinuousScan) {
                            return
                        }
                        stopScan(true)
                        Timer().schedule(object : TimerTask() {
                            override fun run() {
                                if (isPairingStarted || canPauseContinuousScan) {
                                    return
                                }
                                scanDevices()
                            }
                        }, delayBetweenScan)
                    }
                }, scanDuration + delayBetweenScan)
            }
        }

        @OptIn(DelicateCoroutinesApi::class)
        private fun scanDevices() {
            GlobalScope.launch {
                log("Start scanning")
                bleHandler.startScan(deviceScanCallback)
            }
        }

        private fun updateDeviceConfig(ggBTSetting: GGBTSetting, ggDevice: GGDevice) {
            log("Updating Device Config $ggBTSetting")
            val deviceInfo = ggDevice.bleDevice?.deviceInfo
            when (ggBTSetting.key) {
                GGBTSettingType.IMPEDANCE -> {
                    val value: Boolean = GGBTSettingValue.getSettingValue(ggBTSetting) as Boolean
                    ggDevice.bleDevice?.setImpedanceSwitch(value, false)
                }

                GGBTSettingType.SESSION_IMPEDANCE -> {
                    val value: Boolean = GGBTSettingValue.getSettingValue(ggBTSetting) as Boolean
                    ggDevice.bleDevice?.setImpedanceSwitch(value, true)
                }

                GGBTSettingType.HEART_RATE -> {
                    val value: Boolean = GGBTSettingValue.getSettingValue(ggBTSetting) as Boolean
                    ggDevice.bleDevice?.setHeartRateSwitch(value)
                }

                GGBTSettingType.RESTORE_FACTORY -> {
                    ggDevice.bleDevice?.resetFactorySettings()
                }

                GGBTSettingType.INITIAL_LOGO_ANIM -> {
                    val value: Boolean = GGBTSettingValue.getSettingValue(ggBTSetting) as Boolean
                    ggDevice.bleDevice?.setAnimationPageSwitch(
                        value,
                        deviceInfo?.endAnimationState == GGSwitchState.GG_SWITCH_STATE_ON
                    )
                }

                GGBTSettingType.FINAL_LOGO_ANIM -> {
                    val value: Boolean = GGBTSettingValue.getSettingValue(ggBTSetting) as Boolean
                    ggDevice.bleDevice?.setAnimationPageSwitch(
                        deviceInfo?.startAnimationState == GGSwitchState.GG_SWITCH_STATE_ON,
                        value
                    )
                }

                GGBTSettingType.RESET_FIRMWARE -> {
                    ggDevice.bleDevice?.resetFirmware()
                }

                GGBTSettingType.UNIT -> {
                    val value: String = GGBTSettingValue.getSettingValue(ggBTSetting) as String
                    ggDevice.bleDevice?.setWeightUnit(getWeightUnit(value))
                }

                GGBTSettingType.TEMPERATURE_UNIT -> {
                    val value = GGBTSettingValue.getSettingValue(ggBTSetting) as String
                    ggDevice.bleDevice?.setTemperatureUnit(CommonHelper.getTemperatureUnit(value))
                }

                GGBTSettingType.BGM_UNIT -> {
                    val value = GGBTSettingValue.getSettingValue(ggBTSetting) as String
                    ggDevice.bleDevice?.setBgmUnit(CommonHelper.getBgmUnit(value))
                }

                GGBTSettingType.MUTE_MODE -> {
                    val value: String =
                        GGBTSettingValue.getSettingValue(ggBTSetting) as String
                    ggDevice.bleDevice?.setMuteMode(getTemperatureMuteMode(value))
                }

                GGBTSettingType.TARE -> {
                    ggDevice.bleDevice?.tare()
                }

                GGBTSettingType.TIME_FORMAT -> {
                    val value: Boolean = GGBTSettingValue.getSettingValue(ggBTSetting) as Boolean
                    ggDevice.bleDevice?.setTime(
                        GGTimeData.Builder()
                            .setUTCTime(Utils.getUTCTimeInSeconds())
                            .setTZOffset(Utils.getTimeZoneInMinutes())
                            .setDisplayType(
                                if (value) GGTimeDisplayType.GG_TIME_DISPLAY_12_HRS
                                else GGTimeDisplayType.GG_TIME_DISPLAY_24_HRS
                            )
                            .build()
                    )
                }
            }
        }

        fun checkAndPair(
            connectTo: GGBTDevice,
            callback: (data: GGUserActionResponseType) -> Unit
        ) {
            val token = connectTo.token ?: ""
            val userNumber = connectTo.userNumber ?: 0
            log(
                "Checking device and start adding account token - $token user number - $userNumber"
            )
            userCreationCallBack = callback
            val device = getDeviceByBroadcastId(connectTo.broadcastId)
            if (device != null) {
                log("Device is available $token")
                device.token = token
                device.userNumber = userNumber
                devices[connectTo.broadcastId] = device

                if (device.connectionStatus == ConnectionStatus.CONNECTED &&
                    device.bleDevice != null
                ) {
                    this.showPairingState = true
                    addUserAccount(token, userNumber, device.bleDevice!!, connectTo.preference)
                } else {
                    log("New Device pair $token")
                    connectDevice(device.ggDevice, true)
                }
            } else {
                log("Device creation failed")
                respondPairingCallback(GGUserActionResponseType.CREATION_FAILED)
            }
        }

        fun deleteAccount(
            ggBTDevice: GGBTDevice,
            disconnect: Boolean,
            callback: (data: GGUserActionResponseType) -> Unit
        ) {
            log("Deleting Account $ggBTDevice")
            val isDeviceAvailable = devices.containsKey(ggBTDevice.broadcastId)
            if (isDeviceAvailable) {
                val device = devices.getValue(ggBTDevice.broadcastId)
                if (device.bleDevice != null) {
                    userDeletionCallBack = callback
                    if (disconnect) {
                        skipDevice(ggBTDevice.broadcastId)
                    }
                    val result = if (ggBTDevice.token != null) {
                        device.bleDevice!!.deleteAccount(
                            Utils.hexStringToByteArray(ggBTDevice.token)
                        )
                    } else null
                    if (result == GGResultType.GG_OK) {
                        if (disconnect) {
                            device.bleDevice!!.disconnect()
                        }
                    }
                }
            }
        }

        fun getWifiList(device: GGBTDevice, callback: (data: GGWifiResponse) -> Unit) {
            log("Get Wifi List Called")
            val isDeviceAvailable = devices.containsKey(device.broadcastId)
            if (isDeviceAvailable) {
                val ggDevice = devices.getValue(device.broadcastId)
                if (ggDevice.bleDevice != null) {
                    wifiListCallBack = callback
                    ggDevice.bleDevice!!.wifiList
                }
            }
        }

        fun getUserList(device: GGBTDevice, callback: (data: GGScaleUserResponse) -> Unit) {
            log("Get user List Called")
            val isDeviceAvailable = devices.containsKey(device.broadcastId)
            if (isDeviceAvailable) {
                val ggDevice = devices.getValue(device.broadcastId)
                if (ggDevice.bleDevice != null) {
                    userListCallBack = callback
                    ggDevice.bleDevice!!.fetchAccountIDList(true)
                }
            }
        }

        fun getWifiMacAddress(device: GGBTDevice, callback: (data: String) -> Unit) {
            log("Get Wifi Mac Called")
            val isDeviceAvailable = devices.containsKey(device.broadcastId)
            if (isDeviceAvailable) {
                val ggDevice = devices.getValue(device.broadcastId)
                if (ggDevice.bleDevice != null) {
                    wifiMacCallBack = callback
                    ggDevice.bleDevice!!.readWifiMACAddress()
                }
            }
        }

        fun getDeviceInfo(device: GGBTDevice, callback: (GGDeviceDetail?) -> Unit) {
            log("Get Device Info")
            val isDeviceAvailable = devices.containsKey(device.broadcastId)
            if (isDeviceAvailable) {
                val ggDevice = devices.getValue(device.broadcastId)
                if (ggDevice.bleDevice != null) {
                    callback(convertToDeviceDetail(ggDevice.bleDevice!!.deviceInfo))
                }
            } else {
                callback(null)
            }
        }

        fun subscribeLiveData(device: GGBTDevice) {
            val isDeviceAvailable = devices.containsKey(device.broadcastId)
            if (isDeviceAvailable) {
                val ggDevice = devices.getValue(device.broadcastId)
                ggDevice.bleDevice?.subscribeToLiveData()
            }
        }

        fun getDeviceLogs(device: GGBTDevice, callback: (data: GGDeviceLogResponse) -> Unit) {
            log("Get Device Logs")
            val isDeviceAvailable = devices.containsKey(device.broadcastId)

            if (isDeviceAvailable) {
                val ggDevice = devices.getValue(device.broadcastId)
                if (ggDevice.bleDevice != null) {
                    deviceLogCallBack = callback
                    ggDevice.bleDevice?.synchronizeLog()
                }
            }
        }

        fun startFirmwareUpdate(device: GGBTDevice, timestamp: Long?) {
            log("Start Firmware Update Called")
            val isDeviceAvailable = devices.containsKey(device.broadcastId)
            if (isDeviceAvailable) {
                val ggDevice = devices.getValue(device.broadcastId)
                if (ggDevice.bleDevice != null) {
                    upgradeFirmware(ggDevice.bleDevice!!, timestamp ?: Utils.getUTCTimeInSeconds())
                }
            }
        }

        fun getConnectedWifiSSID(device: GGBTDevice, callback: (data: String) -> Unit) {
            log("Get connected wifi info")
            val isDeviceAvailable = devices.containsKey(device.broadcastId)
            if (isDeviceAvailable) {
                val ggDevice = devices.getValue(device.broadcastId)
                wifiSSIDCallBack = callback
                if (ggDevice.bleDevice != null) {
                    ggDevice.bleDevice!!.wifiSSID
                }
            }
        }

        fun setupWifi(
            device: GGBTDevice,
            wifiConfig: GGBTWifiConfig,
            callback: (data: GGWifiSetupResponse) -> Unit
        ) {
            log("Setup WiFi called")
            val ggDevice = getBLEByDevice(device)
            if (ggDevice?.bleDevice != null) {
                wifiSetupCallBack = callback
                val ggWifiInfo = GGWifiInfo()
                ggWifiInfo.ssid = wifiConfig.ssid
                ggWifiInfo.password = wifiConfig.password
                log("Wifi Config $wifiConfig")
                ggDevice.bleDevice!!.selectWifiInfo(ggWifiInfo)
            }
        }

        fun cancelWifi(device: GGBTDevice, callback: (data: Boolean) -> Unit) {
            log("Cancel WiFi called")
            val ggDevice = getBLEByDevice(device)
            if (ggDevice?.bleDevice != null) {
                wifiCancelCallBack = callback
                ggDevice.bleDevice!!.cancelWifiInfo()
            }
        }

        fun updateAccount(
            device: GGBTDevice,
            callback: (data: GGUserActionResponseType) -> Unit
        ) {
            log("Update Account $device")
            val ggDevice = getBLEByDevice(device)
            if (ggDevice?.bleDevice != null &&
                ggDevice.connectionStatus == ConnectionStatus.CONNECTED
            ) {
                userUpdateCallBack = callback
                val token = device.token ?: ""
                if (ggDevice.token != token) {
                    ggDevice.token = token
                    devices[device.broadcastId] = ggDevice
                }
                addUserAccount(
                    device.token ?: "",
                    ggDevice.userNumber ?: 0,
                    ggDevice.bleDevice!!,
                    device.preference
                )
            }
        }

        fun restoreAccount(
            device: GGBTDevice,
            accountName: String,
            callback: (data: GGUserActionResponseType) -> Unit
        ) {
            log("Restore Account $device")
            val ggDevice = getBLEByDevice(device)
            if (ggDevice?.bleDevice != null &&
                ggDevice.connectionStatus == ConnectionStatus.CONNECTED
            ) {
                getUserList(device) {
                    val token = it.user.find { it.name == accountName }?.token
                    deleteAccount(device.copy(token = token), false) {
                        if (it == GGUserActionResponseType.DELETE_COMPLETED) {
                            userUpdateCallBack = callback
                            addUserAccount(
                                device.token ?: "",
                                ggDevice.userNumber ?: 0,
                                ggDevice.bleDevice!!,
                                device.preference
                            )
                        }
                    }
                }

            }
        }

        fun updateProfile(userProfile: GGBTUserProfile, callback: (Boolean) -> Unit) {
            log("updateProfile $userProfile")
            setAccountData(userProfile)
            updateProfileInConnectedDevices()
            callback(true)
        }

        fun tare(device: GGBTDevice) {
            log("Set device tare $device")
            val ggDevice = getBLEByDevice(device)
            if (ggDevice?.bleDevice != null &&
                ggDevice.connectionStatus == ConnectionStatus.CONNECTED
            ) {
                ggDevice.bleDevice!!.tare()
            }
        }

        private fun updateProfileInConnectedDevices() {
            for (pairedDevice in pairedDevices) {
                val ggDevice = getBLEByDevice(pairedDevice)
                if (ggDevice?.bleDevice != null &&
                    ggDevice.connectionStatus == ConnectionStatus.CONNECTED &&
                    ggDevice.token != null
                ) {
                    addUserAccount(
                        ggDevice.token!!,
                        ggDevice.userNumber ?: 0,
                        ggDevice.bleDevice!!,
                        pairedDevice.preference
                    )
                }
            }
        }

        fun disconnectDevice(deviceBroadcastId: String) {
            val device =
                getBLEByDevice(
                    GGBTDevice(
                        name = "",
                        broadcastId = deviceBroadcastId,
                        password = null,
                        userNumber = 0,
                        syncAllData = null
                    )
                )
            if (device?.bleDevice != null) {
                device.bleDevice?.disconnect()
            }
        }

        fun skipDevice(deviceBroadcastId: String) {
            if (temporarySkipDevices.containsKey(deviceBroadcastId)) {
                skipDevices.add(deviceBroadcastId)
                temporarySkipDevices.remove(deviceBroadcastId)
            } else {
                temporarySkipDevices[deviceBroadcastId] = Utils.getCurrentTime()
            }
            disconnectDevice(deviceBroadcastId)
        }

        fun isDeviceSkipped(deviceBroadcastId: String): Boolean {
            return temporarySkipDevices.containsKey(deviceBroadcastId) || skipDevices.contains(
                deviceBroadcastId
            )
        }

        fun getMeasurementLiveData(device: GGBTDevice, callback: (GGLiveDataResponse) -> Unit) {
            log("Get measurement live data")
            val isDeviceAvailable = devices.containsKey(device.broadcastId)
            if (isDeviceAvailable) {
                val ggDevice = devices.getValue(device.broadcastId)
                if (ggDevice.bleDevice != null) {
                    ggDevice.bleDevice!!.subscribeToLiveData()
                    liveDataCallback = callback

                }
            }
        }

        fun clearData(
            device: GGBTDevice,
            clearDataType: ClearDataType,
            callback: (String) -> Unit
        ) {
            log("Clear data - $clearDataType")

            devices[device.broadcastId]?.let { storedDevice ->
                when (clearDataType) {
                    ClearDataType.ALL -> storedDevice.bleDevice?.clearAllData()
                    ClearDataType.ACCOUNT -> storedDevice.bleDevice?.clearUserAccountData()
                    ClearDataType.HISTORY -> storedDevice.bleDevice?.clearHistoryData()
                    ClearDataType.WIFI -> storedDevice.bleDevice?.clearWifiNetworkData()
                    ClearDataType.SETTINGS -> storedDevice.bleDevice?.clearSettingsData()
                }

                clearDataCallback = callback
            }
        }

        private fun getLicenseKey(appType: String): String {
            return when (appType) {
                GGAppType.WEIGHT_GURUS -> "a7b3f494990146d083530e3e1befa2907fd256ce2a2840d49a1a979193fd7c83"
                GGAppType.BALANCE_HEALTH -> "ec670222a28f41a9bb52eb8e16750318384cafa405cd46978e2f20486284bcd7"
                GGAppType.SMART_BABY -> "226eacbf2c93407da9e6768e009cdbeb462f4d96feed4de1aa2fbad778ab9815"
                GGAppType.SAGE -> "81e417fc98d148c981c0c6f2fa0538c2b9c79ebd4a1f40d49c99a4e516f4e1ae"
                GGAppType.RPM -> "9ee29d74c441464fafc64138fa0552c983d9b6592b4f4e3892645ba6ed4c7db3"
                GGAppType.ALL -> "9ee29d74c441464fafc64138fa0552c983d9b6592b4f4e3892645ba6ed4c7db4"
                else -> ""
            }
        }

        private fun getBLEByDevice(device: GGBTDevice): GGDevice? {
            val isDeviceAvailable = devices.containsKey(device.broadcastId)
            if (isDeviceAvailable) {
                return devices.getValue(device.broadcastId)
            }
            return null
        }

        private fun addUserAccount(
            scaleToken: String,
            userNumber: Int,
            ggBLEDevice: GGIBLEDevice,
            preference: GGDevicePreference? = null,
        ) {
            val currentAccount =
                accounts?.get(ggBLEDevice.deviceInfo.getAppType(appType)) ?: this.account
            val accountName = preference?.displayName ?: currentAccount?.name
            val accountInfo = GGAccountInfo()
            accountInfo.accountID(Utils.hexStringToByteArray(scaleToken))
            accountInfo.name(accountName)
            accountInfo.userNumber(userNumber)
            accountInfo.gender(
                if (currentAccount?.sex?.lowercase() == "male") GGGender.GG_GENDER_MALE
                else GGGender.GG_GENDER_FEMALE
            )
            currentAccount?.age?.let { accountInfo.age(it) }
            currentAccount?.height?.let { (accountInfo.height(it.toInt())) }
            currentAccount?.weight?.let { accountInfo.weight((it * 100).toInt()) }
            var targetWeight = 0
            currentAccount?.goalWeight?.let { targetWeight = (it * 100).toInt() }
            val goalType: GGGoalType
            when (currentAccount?.goalType) {
                GoalType.LOSE -> {
                    goalType = GGGoalType.GG_GOAL_LOSE
                    accountInfo.targetWeight(targetWeight)
                    accountInfo.maintainWeight(0)
                }

                GoalType.GAIN -> {
                    goalType = GGGoalType.GG_GOAL_GAIN
                    accountInfo.targetWeight(targetWeight)
                    accountInfo.maintainWeight(0)
                }

                GoalType.MAINTAIN -> {
                    goalType = GGGoalType.GG_GOAL_MAINTAIN
                    accountInfo.targetWeight(0)
                    accountInfo.maintainWeight(targetWeight)
                }

                else -> goalType = GGGoalType.GG_GOAL_UNSET
            }
            accountInfo.goalType(goalType)
            currentAccount?.isAthlete?.let { accountInfo.isAthlete(it) }

            val unit =
                if (currentAccount?.unit == "kg") WeightUnit.WEIGHT_KILOGRAMS
                else WeightUnit.WEIGHT_POUNDS

            var metrics =
                arrayListOf(
                    GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BMI,
                    GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BODY_FAT,
                    GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_MUSCLE,
                    GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BODY_WATER,
                    GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BONE_MASS,
                    GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_HEART_RATE,
                    GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_VISCERAL_FAT,
                    GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_SUBCUTANEOUS_FAT,
                    GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_PROTEIN,
                    GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_SKELETAL_MUSCLE,
                    GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BMR,
                    GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BODY_AGE,
                    GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_TARGET,
                    GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_DAILY_AVERAGE,
                    GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_WEEKLY_AVERAGE,
                    GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_MONTHLY_AVERAGE
                )

            var timeFormat = GGTimeDisplayType.GG_TIME_DISPLAY_12_HRS
            val timeOffset = preference?.tzOffset ?: Utils.getTimeZoneInMinutes()
            val shouldMeasureImpedance = preference?.shouldMeasureImpedance != false
            val shouldMeasurePulse = preference?.shouldMeasurePulse == true
            if (preference != null) {
                timeFormat =
                    if (preference.timeFormat == TimeFormat.TWENTY_FOUR)
                        GGTimeDisplayType.GG_TIME_DISPLAY_24_HRS
                    else GGTimeDisplayType.GG_TIME_DISPLAY_12_HRS
                if (preference.displayMetrics != null) {
                    metrics = convertStringToPreference(preference.displayMetrics)
                }
                // TODO: Enable FOTA
                // preference.wifiFotaScheduleTime?.let { upgradeFirmware(GGIBLEDevice, it) }
            }
            val userPreference =
                GGUserPreference.Builder()
                    .setWeightUnit(unit)
                    .setTimeData(
                        GGTimeData.Builder()
                            .setUTCTime(Utils.getUTCTimeInSeconds())
                            .setTZOffset(timeOffset)
                            .setDisplayType(timeFormat)
                            .build()
                    )
                    .setMeasureImpedance(shouldMeasureImpedance)
                    .setMeasureHeartRate(shouldMeasurePulse)
                    .build()

            log("Account Info${Gson().toJson(accountInfo)}")

            try {
                when (ggBLEDevice.deviceInfo.getAppType(appType)) {
                    GGAppType.WEIGHT_GURUS -> ggBLEDevice.addAccount(
                        accountInfo,
                        userPreference,
                        metrics,
                        this.showPairingState
                    )

                    GGAppType.BALANCE_HEALTH -> ggBLEDevice.addAccount(
                        accountInfo,
                        this.showPairingState
                    )

                    GGAppType.SAGE -> ggBLEDevice.addAccount(
                        accountInfo,
                        this.showPairingState
                    )

                    GGAppType.SMART_BABY -> ggBLEDevice.addAccount(
                        accountInfo,
                        userPreference,
                        metrics,
                        this.showPairingState
                    )

                    GGAppType.ALL -> {
                        ggBLEDevice.deviceInfo
                    }

                    else -> return
                }
                this.showPairingState = false
            } catch (ex: Exception) {
                log(Gson().toJson(ex))
            }
        }

        private fun onNewDeviceFound(deviceInfo: GGIDeviceInfo) {
            if (!canPairDevices(deviceInfo)) {
                return
            }
            if (canPauseDeviceScan && isPairingStarted) {
                handleNewDeviceInPairingMode(deviceInfo)
            } else {
                handleNewDeviceInRegularMode(deviceInfo)
            }
        }

        private fun handleNewDeviceInPairingMode(ggDeviceInfo: GGIDeviceInfo) {
            if (ggDeviceInfo.protocolType == GGDeviceProtocolType.GG_DEVICE_PROTOCOL_A3 && ggDeviceInfo.isInPairingMode === false) {
                return
            }
            if (handleAlreadyPairedDevice(ggDeviceInfo)) {
                return
            }
            addDeviceToList(ggDeviceInfo, false)
            resolveNewDeviceFound(ggDeviceInfo)
        }

        private fun handleAlreadyPairedDevice(ggDeviceInfo: GGIDeviceInfo): Boolean {
            val device = checkDeviceAlreadyPaired(ggDeviceInfo)
            if (device != null) {
                if ((!devices.containsKey(ggDeviceInfo.broadcastId) ||
                            devices.getValue(ggDeviceInfo.broadcastId).connectionStatus ==
                            ConnectionStatus.DISCONNECTED
                            ) && !(isDeviceSkipped(ggDeviceInfo.broadcastId))
                ) {
                    addDeviceToList(ggDeviceInfo, true, device.token)
                    connectDevice(ggDeviceInfo, false)
                    return true
                }
            }
            return false
        }

        private fun handleNewDeviceInRegularMode(ggDeviceInfo: GGIDeviceInfo) {
            if (handleAlreadyPairedDevice(ggDeviceInfo)) {
                return
            }
            if (!canPauseDeviceScan) {
                addDeviceToList(ggDeviceInfo, false)
                resolveNewDeviceFound(ggDeviceInfo)
            }
        }

        private fun resolveNewDeviceFound(deviceInfo: GGIDeviceInfo) {
            respondDeviceDetailCallback(GGScanResponseType.NEW_DEVICE, deviceInfo)
        }

        private fun addDeviceToList(
            ggiDeviceInfo: GGIDeviceInfo,
            isPaired: Boolean,
            scaleToken: String? = null
        ) {
            val broadcastId = ggiDeviceInfo.broadcastId
            devices[broadcastId] =
                GGDevice(ggiDeviceInfo, null, scaleToken, isPaired, syncAllData = false)
        }

        private fun connectDevice(ggiDeviceInfo: GGIDeviceInfo, pairDevice: Boolean) {
            this.showPairingState = pairDevice
            val broadcastId = ggiDeviceInfo.broadcastId
            val device = getDeviceByBroadcastId(broadcastId)
            device?.let {
                device.connectionStatus = ConnectionStatus.CONNECTING
                devices[broadcastId] = device
            }
            bleHandler.connectDevice(ggiDeviceInfo, getDataCallBack())
        }

        @OptIn(DelicateCoroutinesApi::class)
        private fun getDataCallBack(): GGReceiveDataCallback {
            val dataCallback: GGReceiveDataCallback =
                object : GGReceiveDataCallback() {

                    override fun onConnectionFailed(bleDevice: GGIBLEDevice): GGResultType {
                        super.onConnectionFailed(bleDevice)
                        log("Device Connection Failed ${bleDevice.deviceInfo.macAddress}")
                        handleOnConnectionFailed(bleDevice.deviceInfo)
                        return GGResultType.GG_OK
                    }

                    override fun onDeviceConnect(ggBLEDevice: GGIBLEDevice): GGResultType {
                        super.onDeviceConnect(ggBLEDevice)
                        val deviceInfo = ggBLEDevice.deviceInfo
                        log("Device Connected ${deviceInfo.macAddress}")
                        val broadcastId = deviceInfo.broadcastId
                        val device: GGDevice = devices.getValue(broadcastId)
                        device.bleDevice = ggBLEDevice
                        device.connectionStatus = ConnectionStatus.CONNECTED
                        devices[broadcastId] = device
                        val pairedDevice = checkDeviceAlreadyPaired(deviceInfo)
                        if (deviceInfo.protocolType ==
                            GGDeviceProtocolType.GG_DEVICE_PROTOCOL_A3
                        ) {
                            deviceInfo.password = pairedDevice?.password ?: ""
                        }
                        if (device.existingDevice) {
                            respondDeviceDetailCallback(
                                GGScanResponseType.KNOWN_DEVICE, deviceInfo
                            )
                        }
                        try {
                            // TODO: Have to assign default value to the token.
                            device.token = device.token ?: ""
                            addUserAccount(
                                device.token!!,
                                pairedDevice?.userNumber ?: device.userNumber ?: 0,
                                ggBLEDevice,
                                pairedDevice?.preference
                            )
                        } catch (e: Exception) {
                            respondDeviceDetailCallback(
                                GGScanResponseType.DEVICE_CONNECTION_FAILED,
                                deviceInfo
                            )
                        }
                        if (appType == GGAppType.WEIGHT_GURUS || appType == GGAppType.SMART_BABY) {
                            ggBLEDevice.subscribeToLiveData()
                        }
                        when (ggBLEDevice.deviceInfo.getAppType(appType)) {
                            GGAppType.SAGE, GGAppType.RPM, GGAppType.WEIGHT_GURUS -> {
                                device.bleDevice = ggBLEDevice
                                device.connectionStatus = ConnectionStatus.CONNECTED
                                devices[broadcastId] = device

                                respondPairingCallback(
                                    GGUserActionResponseType.CREATION_COMPLETED
                                )
                                respondDeviceDetailCallback(
                                    GGScanResponseType.DEVICE_CONNECTED,
                                    deviceInfo
                                )
                                if (appType == GGAppType.RPM) {
                                    ggBLEDevice.syncData(byteArrayOf())
                                }
                            }

                        }
                        return GGResultType.GG_OK
                    }

                    override fun onDeviceDisconnect(bleDevice: GGIBLEDevice): GGResultType {
                        super.onDeviceDisconnect(bleDevice)
                        log("Device Disconnected")
                        if (devices.containsKey(bleDevice.deviceInfo.broadcastId)) {
                            val device: GGDevice =
                                devices.getValue(bleDevice.deviceInfo.broadcastId)
                            device.connectionStatus = ConnectionStatus.DISCONNECTED
                            devices[bleDevice.deviceInfo.broadcastId] = device
                        }
                        if (appType == GGAppType.RPM) {
                            respondDeviceEntryCallback(
                                GGScanResponse.Entry(
                                    GGScanResponseType.MULTI_ENTRIES,
                                    liveMeasurementInfo.toList()
                                )
                            )
                            liveMeasurementInfo.clear()
                        }
                        respondDeviceDetailCallback(
                            GGScanResponseType.DEVICE_DISCONNECTED,
                            bleDevice.deviceInfo
                        )
                        return GGResultType.GG_OK
                    }

                    override fun onReceiveDeviceInfo(
                        result: GGResultType,
                        device: GGIBLEDevice,
                        deviceInfo: GGIDeviceInfo
                    ): GGResultType {
                        log("Device info received")
                        sendDeviceInfo(device)
                        return GGResultType.GG_OK
                    }

                    override fun onReceiveOnlineAccountConfig(
                        result: GGResultType,
                        device: GGIBLEDevice,
                        status: Int
                    ) {
                        super.onReceiveOnlineAccountConfig(result, device, status)
                        log("Device Added")
                    }

                    override fun onReceiveDeviceLog(
                        result: GGResultType,
                        bleDevice: GGIBLEDevice,
                        log: String
                    ) {
                        super.onReceiveDeviceLog(result, bleDevice, log)
                        bleDevice.acknowledgeLogRawDataReceived()
                        deviceLogs.add(
                            GGDeviceLog(
                                macAddress = bleDevice.deviceInfo.macAddress,
                                log = log
                            )
                        )
                        if (result == GGResultType.GG_OK) {
                            deviceLogCallBack?.let {
                                it(
                                    GGDeviceLogResponse.Completed(
                                        deviceLogs.toList()
                                    )
                                )
                            }
                        }
                    }

                    override fun onReceiveDeviceLogUpdate(
                        result: GGResultType,
                        device: GGIBLEDevice,
                        deviceLogInfo: GGDeviceLogInfo
                    ) {
                        super.onReceiveDeviceLogUpdate(result, device, deviceLogInfo)
                        log("Log percent - ${deviceLogInfo.receivedPercentage} ${deviceLogInfo.data}")
                        deviceLogCallBack?.let {
                            it(
                                GGDeviceLogResponse.Fetching(
                                    deviceLogInfo.receivedPercentage,
                                )
                            )
                        }
                    }


                    override fun onReceiveLiveMeasurement(
                        result: GGResultType,
                        device: GGIBLEDevice,
                        measurementInfo: GGIMeasurementInfo
                    ): GGResultType {
                        super.onReceiveLiveMeasurement(result, device, measurementInfo)
                        try {
                        } catch (ex: Exception) {
                            log("Device info is not available")
                            return GGResultType.GG_FAIL
                        }
                        when (device.deviceInfo.getAppType(appType)) {
                            GGAppType.WEIGHT_GURUS -> {
                                if ((measurementInfo as GGIBodyMetricsInfo).weight > 0) {
                                    GlobalScope.launch {
                                        delay(1000)
                                        if (device.protocolType == GGDeviceProtocolType.GG_DEVICE_PROTOCOL_R4) {
                                            liveDataCallback?.let {
                                                it(
                                                    GGLiveDataResponse.Entry(
                                                        convertToEntry(
                                                            device.deviceInfo,
                                                            measurementInfo
                                                        )
                                                    )
                                                )
                                            }
                                        } else {
                                            liveDataCallback?.let {
                                                it(
                                                    GGLiveDataResponse.Entry(
                                                        convertToEntry(
                                                            device.deviceInfo,
                                                            measurementInfo
                                                        )
                                                    )
                                                )
                                            }
                                        }
                                    }
                                }
                            }

                            GGAppType.SAGE -> {
                                GlobalScope.launch {
                                    delay(1000)
                                    liveDataCallback?.let {
                                        it(
                                            GGLiveDataResponse.Entry(
                                                convertToWeightEntry(
                                                    device.deviceInfo,
                                                    measurementInfo as GGIWeightInfo
                                                )
                                            )
                                        )
                                    }
                                }
                            }

                            GGAppType.RPM -> {

                                GlobalScope.launch {
                                    liveDataCallback?.let {
                                        it(
                                            GGLiveDataResponse.Entry(
                                                convertToPulseEntry(
                                                    device.deviceInfo,
                                                    measurementInfo as GGIPulseOximeterInfo
                                                )
                                            )
                                        )
                                    }
                                    val pulseEntry = convertToPulseEntry(
                                        device.deviceInfo,
                                        measurementInfo as GGIPulseOximeterInfo
                                    )
                                    if (pulseEntry.pr != 0 && pulseEntry.spO2 != 0 && pulseEntry.pulseAmplitudeIndex != 0f) {
                                        if (liveMeasurementInfo.isEmpty() || with(
                                                liveMeasurementInfo.last() as GGPulseEntry
                                            ) {
                                                pr != pulseEntry.pr || spO2 != pulseEntry.spO2 || pulseAmplitudeIndex != pulseEntry.pulseAmplitudeIndex
                                            }
                                        ) {
                                            liveMeasurementInfo.add(pulseEntry)
                                        }
                                    }
                                    if (liveMeasurementInfo.size == 10) {
                                        respondDeviceEntryCallback(
                                            GGScanResponse.Entry(
                                                GGScanResponseType.MULTI_ENTRIES,
                                                liveMeasurementInfo.toList()
                                            )
                                        )
                                        liveMeasurementInfo.clear()

                                    }
                                }
                            }
                        }
                        return GGResultType.GG_OK
                    }

                    override fun onReceiveAccountConfig(
                        result: GGResultType,
                        device: GGIBLEDevice,
                        status: Int
                    ) {
                        super.onReceiveAccountConfig(result, device, status)
                        log("Device Added - $status")
                        when (status) {
                            0x00 -> {
                                // create online account with same account ID
                                device.addOnlineAccount()
                            }

                            0x01 -> {
                                respondPairingCallback(GGUserActionResponseType.MEMORY_FULL)
                            }

                            0xFF -> {
                                respondPairingCallback(GGUserActionResponseType.CREATION_FAILED)
                            }

                            0xF4 -> {
                                respondPairingCallback(
                                    GGUserActionResponseType.INPUT_DATA_ERROR
                                )
                            }

                            0xF5 -> {
                                respondPairingCallback(GGUserActionResponseType.MEMORY_FULL)
                            }

                            else -> {
                                respondPairingCallback(GGUserActionResponseType.CREATION_FAILED)
                            }
                        }
                    }


                    override fun onReceiveAcknowledgement(
                        result: GGResultType,
                        device: GGIBLEDevice,
                        operation: GGOperationType
                    ): GGResultType {
                        super.onReceiveAcknowledgement(result, device, operation)
                        when (operation) {
                            GGOperationType.GG_OPERATION_ACCOUNT_CREATION -> {
                                log("Device Added")

                                when (result) {
                                    GGResultType.GG_OK -> {
                                        respondPairingCallback(
                                            GGUserActionResponseType.CREATION_COMPLETED
                                        )
                                        if (appType == GGAppType.WEIGHT_GURUS || appType == GGAppType.SMART_BABY) {
                                            device.subscribeToLiveData()
                                        }
                                        val ggDevice =
                                            devices.getValue(device.deviceInfo.broadcastId)
                                        val broadcastId = device.deviceInfo.broadcastId
                                        respondDeviceDetailCallback(
                                            GGScanResponseType.DEVICE_CONNECTED,
                                            ggDevice.ggDevice
                                        )
                                        sendDeviceInfo(device)
                                        userUpdateCallBack?.let { it(GGUserActionResponseType.UPDATE_COMPLETED) }
                                        userCreationCallBack = null
                                        var scaleToken: String? = null
                                        when (device.deviceInfo.getAppType(appType)) {
                                            GGAppType.WEIGHT_GURUS -> {
                                                if (ggDevice.token != null) {
                                                    scaleToken = ggDevice.token
                                                }
                                            }

                                            GGAppType.BALANCE_HEALTH -> {
                                                val pairDevice =
                                                    pairedDevices.find { it.broadcastId == broadcastId }

                                                if (pairDevice != null && ggDevice.ggDevice.protocolType == GGDeviceProtocolType.GG_DEVICE_PROTOCOL_A6) {
                                                    if (pairDevice.syncAllData == true) {
                                                        scaleToken = when (ggDevice.userNumber) {
                                                            1 -> "02"
                                                            2 -> "01"
                                                            else -> {
                                                                null
                                                            }
                                                        }
                                                    } else {
                                                        scaleToken = when (ggDevice.userNumber) {
                                                            1 -> "01"
                                                            2 -> "02"
                                                            else -> {
                                                                null
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            GGAppType.SAGE -> {
                                                // Set scale token if any
                                            }
                                        }
                                        if (scaleToken != null) {
                                            device.syncData(
                                                Utils.hexStringToByteArray(scaleToken)
                                            )
                                        } else {
                                            device.syncData(byteArrayOf())
                                        }
                                    }

                                    GGResultType.GG_INPUT_DATA_ERROR -> {
                                        respondPairingCallback(
                                            GGUserActionResponseType.INPUT_DATA_ERROR
                                        )
                                    }

                                    GGResultType.GG_DIFFERENT_USER -> {
                                        respondPairingCallback(
                                            GGUserActionResponseType.DIFFERENT_USER
                                        )
                                    }

                                    GGResultType.GG_MEMORY_FULL_ERROR -> {
                                        respondPairingCallback(
                                            GGUserActionResponseType.MEMORY_FULL
                                        )
                                        if (checkDeviceAlreadyPaired(device.deviceInfo) != null) {
                                            respondDeviceDetailCallback(
                                                GGScanResponseType.DEVICE_MEMORY_FULL,
                                                device.deviceInfo
                                            )
                                        } else {
                                            log("paired device is not found")
                                        }
                                    }

                                    GGResultType.GG_DUPLICATE_USER_ERROR -> {
                                        respondPairingCallback(
                                            GGUserActionResponseType.DUPLICATE_USER_ERROR
                                        )
                                        if (checkDeviceAlreadyPaired(device.deviceInfo) != null) {
                                            respondDeviceDetailCallback(
                                                GGScanResponseType.DEVICE_DUPLICATE_USER,
                                                device.deviceInfo
                                            )
                                        } else {
                                            log("paired device is not found")
                                        }
                                    }

                                    GGResultType.GG_USER_SELECTION_IN_PROGRESS -> {
                                        respondPairingCallback(
                                            GGUserActionResponseType.USER_SELECTION_IN_PROGRESS
                                        )
                                    }

                                    else -> {
                                        respondPairingCallback(
                                            GGUserActionResponseType.CREATION_FAILED
                                        )
                                    }
                                }
                            }

                            GGOperationType.GG_OPERATION_CLEAR_ALL_DATA,
                            GGOperationType.GG_OPERATION_CLEAR_USER_ACCOUNT_DATA,
                            GGOperationType.GG_OPERATION_CLEAR_HISTORY_DATA,
                            GGOperationType.GG_OPERATION_CLEAR_WIFI_NETWORK_DATA,
                            GGOperationType.GG_OPERATION_CLEAR_SETTINGS_DATA -> {
                                clearDataCallback?.let { it("Success") }
                                clearDataCallback = null
                            }

                            GGOperationType.GG_OPERATION_SET_SESSION_IMPEDANCE_SWITCH -> {
                                sendDeviceInfo(device)
                            }

                            GGOperationType.GG_OPERATION_SET_UNIT -> {
                                sendDeviceInfo(device)
                            }

                            else -> {

                            }
                        }
                        return GGResultType.GG_OK
                    }


                    override fun onReceiveMeasurementHistory(
                        result: GGResultType,
                        device: GGIBLEDevice,
                        measurementHistoryList: ArrayList<GGIMeasurementInfo>
                    ): GGResultType {
                        super.onReceiveMeasurementHistory(
                            result,
                            device,
                            measurementHistoryList
                        )
                        when (device.deviceInfo.getAppType(appType)) {
                            GGAppType.WEIGHT_GURUS -> {
                                val historyList = mutableListOf<GGScaleEntry>()
                                measurementHistoryList.forEach {
                                    historyList.add(
                                        convertToEntry(device.deviceInfo, it as GGIBodyMetricsInfo)
                                    )
                                }
                                respondDeviceEntryCallback(
                                    GGScanResponse.Entry(
                                        GGScanResponseType.MULTI_ENTRIES,
                                        historyList.toList()
                                    )
                                )
                            }

                            GGAppType.BALANCE_HEALTH -> {
                                val historyList = mutableListOf<GGBPMEntry>()
                                measurementHistoryList.forEach {
                                    historyList.add(
                                        covertToBPMEntry(
                                            device.deviceInfo,
                                            it as GGIBloodPressureInfo
                                        )
                                    )
                                }
                                respondDeviceEntryCallback(
                                    GGScanResponse.Entry(
                                        GGScanResponseType.MULTI_ENTRIES,
                                        historyList.toList()
                                    )
                                )
                            }

                            GGAppType.SAGE -> {
                                val historyList = mutableListOf<GGWeightEntry>()
                                measurementHistoryList.forEach {
                                    historyList.add(
                                        convertToWeightEntry(
                                            device.deviceInfo,
                                            it as GGIWeightInfo
                                        )
                                    )
                                }
                                respondDeviceEntryCallback(
                                    GGScanResponse.Entry(
                                        GGScanResponseType.MULTI_ENTRIES,
                                        historyList.toList()
                                    )
                                )
                            }

                            GGAppType.RPM -> {
                                if (measurementHistoryList.all { it.measurementType == GGMeasurementType.GG_BLOOD_GLUCOSE_METER_MEASUREMENT }) {
                                    val historyList = mutableListOf<GGBGMEntry>()
                                    measurementHistoryList.forEach {
                                        historyList.add(
                                            convertToBGMEntry(
                                                device.deviceInfo,
                                                it as GGIBloodGlucoseInfo
                                            )
                                        )
                                    }
                                    respondDeviceEntryCallback(
                                        GGScanResponse.Entry(
                                            GGScanResponseType.MULTI_ENTRIES,
                                            historyList.toList()
                                        )
                                    )
                                }
                            }
                        }
                        return GGResultType.GG_OK
                    }

                    override fun onReceiveTemperatureUnit(
                        p0: GGResultType,
                        p1: GGIBLEDevice,
                        p2: TemperatureUnit
                    ): GGResultType? {
                        log("onReceiveMeasurement: live temperature measurement")
                        val ggDeviceInfo = p1.deviceInfo
                        if (ggDeviceInfo == null) {
                            log("Device info is not available")
                            return GGResultType.GG_FAIL
                        }
                        liveDataCallback?.let {
                            it(
                                GGLiveDataResponse.Update(
                                    data = p2,
                                )
                            )
                        }
                        return p0
                    }

                    override fun onReceiveMuteMode(
                        p0: GGResultType,
                        p1: GGIBLEDevice,
                        p2: GGSwitchState
                    ): GGResultType? {
                        log("onReceiveMeasurement: live temperature measurement")
                        val ggDeviceInfo = p1.deviceInfo
                        if (ggDeviceInfo == null) {
                            log("Device info is not available")
                            return GGResultType.GG_FAIL
                        }
                        respondDeviceDetailCallback(
                            GGScanResponseType.DEVICE_INFO_UPDATE,
                            ggDeviceInfo,
                            muteMode = p2 == GGSwitchState.GG_SWITCH_STATE_ON
                        )
                        return p0

                    }

                    override fun onReceiveThermometerErrorCode(
                        p0: GGResultType,
                        p1: GGIBLEDevice,
                        p2: GGThermometerErrorCode
                    ): GGResultType? {
                        log("onReceiveMeasurement: live temperature measurement")
                        val ggDeviceInfo = p1.deviceInfo
                        if (ggDeviceInfo == null) {
                            log("Device info is not available")
                            return GGResultType.GG_FAIL
                        }
                        scanCallback(
                            GGScanResponse.DeviceDetail(
                                data = convertToDeviceDetail(ggDeviceInfo, error = p2.name),
                                type = GGScanResponseType.ERROR
                            )
                        )
                        return p0
                    }

                    override fun onReceiveMeasurement(
                        result: GGResultType,
                        device: GGIBLEDevice,
                        measurementInfo: GGIMeasurementInfo
                    ): GGResultType {
                        super.onReceiveMeasurement(result, device, measurementInfo)
                        when (measurementInfo.measurementType) {
                            GGMeasurementType.GG_BODY_METRICS_MEASUREMENT -> {
                                val bodyMetricsInfo: GGIBodyMetricsInfo =
                                    measurementInfo as GGIBodyMetricsInfo
                                val ggDeviceInfo = device.deviceInfo
                                if (ggDeviceInfo == null) {
                                    log("Device info is not available")
                                    return GGResultType.GG_FAIL
                                }
                                val protocolType = ggDeviceInfo.protocolType
                                val broadcastId = ggDeviceInfo.broadcastId
                                val entry =
                                    convertToEntry(
                                        device.deviceInfo,
                                        bodyMetricsInfo
                                    )
                                if (protocolType == GGDeviceProtocolType.GG_DEVICE_PROTOCOL_A6 ||
                                    protocolType == GGDeviceProtocolType.GG_DEVICE_PROTOCOL_A3 ||
                                    protocolType == GGDeviceProtocolType.GG_DEVICE_PROTOCOL_WELLAND_BATH_SCALE
                                ) {
                                    respondDeviceEntryCallback(
                                        GGScanResponse.Entry(
                                            GGScanResponseType.SINGLE_ENTRY,
                                            listOf(entry)
                                        )
                                    )
                                    return result
                                } else if (protocolType == GGDeviceProtocolType.GG_DEVICE_PROTOCOL_R4) {
                                    val deviceInfo = getDeviceByBroadcastId(broadcastId)
                                    if (deviceInfo == null) {
                                        log("Device not found in device list")
                                        return GGResultType.GG_FAIL
                                    }
                                    when (measurementInfo.deviceMatchingAccountIDStatus) {
                                        0x00 -> {
                                            val scaleToken: String? = deviceInfo.token
                                            if (scaleToken != null &&
                                                scaleToken == Utils.byteArrayToHexString(
                                                    measurementInfo
                                                        .deviceMatchingAccountID
                                                )
                                            ) {
                                                if (measurementInfo.weight > 0) {
                                                    log(
                                                        "Weight:  ${measurementInfo.weight}   BMI: ${measurementInfo.bmi} Data: ${
                                                            Gson().toJson(
                                                                entry
                                                            )
                                                        }"
                                                    )
                                                    respondDeviceEntryCallback(
                                                        GGScanResponse.Entry(
                                                            GGScanResponseType.SINGLE_ENTRY,
                                                            listOf(entry)
                                                        )
                                                    )
                                                }
                                                measurementInfo.reset()
                                                // Acknowledge weight data received for informing live
                                                // weight data received from scale
                                                // (skipping entry in scale history)
                                                device.acknowledgeDeviceMatchingAccountIDReceived(
                                                    GGResultType.GG_OK
                                                )
                                            }
                                        }

                                        else -> {
                                            log("Measurement error")
                                        }
                                    }
                                }
                            }

                            GGMeasurementType.GG_BLOOD_PRESSURE_MEASUREMENT -> {
                                log("onReceiveMeasurement: live bpm measurement")
                                val bodyMetricsInfo: GGIBloodPressureInfo =
                                    measurementInfo as GGIBloodPressureInfo
                                val ggDeviceInfo = device.deviceInfo
                                if (ggDeviceInfo == null) {
                                    log("Device info is not available")
                                    return GGResultType.GG_FAIL
                                }
                                val protocolType = ggDeviceInfo.protocolType
                                val entry = covertToBPMEntry(
                                    device.deviceInfo,
                                    bodyMetricsInfo
                                )
                                if (protocolType == GGDeviceProtocolType.GG_DEVICE_PROTOCOL_A6 ||
                                    protocolType == GGDeviceProtocolType.GG_DEVICE_PROTOCOL_A3
                                ) {
                                    respondDeviceEntryCallback(
                                        GGScanResponse.Entry(
                                            GGScanResponseType.SINGLE_ENTRY,
                                            listOf(entry)
                                        )
                                    )
                                    return result
                                }
                            }

                            GGMeasurementType.GG_WEIGHT_MEASUREMENT -> {
                                if ((measurementInfo as GGIWeightInfo).weight > 0f) {
                                    log("onReceiveMeasurement: live weight measurement")
                                    val entryInfo: GGIWeightInfo =
                                        measurementInfo
                                    val ggDeviceInfo = device.deviceInfo
                                    if (ggDeviceInfo == null) {
                                        log("Device info is not available")
                                        return GGResultType.GG_FAIL
                                    }
                                    val entry = convertToWeightEntry(
                                        device.deviceInfo,
                                        entryInfo
                                    )
                                    respondDeviceEntryCallback(
                                        GGScanResponse.Entry(
                                            GGScanResponseType.SINGLE_ENTRY,
                                            listOf(entry)
                                        )
                                    )
                                    return result
                                }
                            }

                            GGMeasurementType.GG_HEALTH_THERMOMETER_MEASUREMENT -> {
                                log("onReceiveMeasurement: live temperature measurement")

                                val entryInfo = measurementInfo as GGIHealthTemperatureInfo
                                val ggDeviceInfo = device.deviceInfo
                                if (ggDeviceInfo == null) {
                                    log("Device info is not available")
                                    return GGResultType.GG_FAIL
                                }
                                val entry =
                                    convertToTemperatureEntry(
                                        device.deviceInfo,
                                        entryInfo,
                                    )
                                respondDeviceEntryCallback(
                                    GGScanResponse.Entry(
                                        GGScanResponseType.SINGLE_ENTRY,
                                        listOf(entry)
                                    )
                                )
                                return result
                            }

                            GGMeasurementType.GG_BLOOD_GLUCOSE_METER_MEASUREMENT -> {
                                log("onReceiveMeasurement: live bgm measurement")

                                val entryInfo = measurementInfo as GGIBloodGlucoseInfo
                                val ggDeviceInfo = device.deviceInfo
                                if (ggDeviceInfo == null) {
                                    log("Device info is not available")
                                    return GGResultType.GG_FAIL
                                }
                                val entry = convertToBGMEntry(
                                    device.deviceInfo,
                                    entryInfo
                                )
                                if (entry.bgm != 0.0) {
                                    respondDeviceEntryCallback(
                                        GGScanResponse.Entry(
                                            GGScanResponseType.SINGLE_ENTRY,
                                            listOf(entry)
                                        )
                                    )
                                }
                                return result
                            }

                            else -> {}
                        }
                        return result
                    }

                    override fun onReceiveWifiSSID(
                        result: GGResultType,
                        device: GGIBLEDevice,
                        ssid: String?
                    ) {
                        super.onReceiveWifiSSID(result, device, ssid)
                        log("WIFI SSID:  $ssid")
                        wifiSSIDCallBack?.let { it(ssid ?: "") }
                        device.acknowledgeWifiSSID()
                    }

                    override fun onReceiveWifiList(
                        result: GGResultType,
                        device: GGIBLEDevice,
                        wifiList: ArrayList<GGWifiInfo>
                    ) {
                        super.onReceiveWifiList(result, device, wifiList)
                        wifiListCallBack?.let { it(GGWifiResponse(wifi = wifiList)) }
                    }

                    override fun onReceiveWifiConnectState(
                        result: GGResultType,
                        device: GGIBLEDevice,
                        wifiState: GGWifiState,
                        displayErrorCode: String
                    ) {
                        super.onReceiveWifiConnectState(
                            result,
                            device,
                            wifiState,
                            displayErrorCode
                        )
                        wifiSetupCallBack?.let {
                            it(GGWifiSetupResponse(getWifiState(wifiState), displayErrorCode))
                        }
                        respondDeviceDetailCallback(
                            GGScanResponseType.WIFI_STATUS_UPDATE, device.deviceInfo
                        )
                        log(
                            "Wifi Connection State: $wifiState - $displayErrorCode ${device.deviceInfo.wifiSetupState}"
                        )
                        when (wifiState) {
                            GGWifiState.GG_WIFI_STATE_NOT_CONNECTED -> {
                            }

                            GGWifiState.GG_WIFI_STATE_CANCELLED -> {
                                wifiCancelCallBack?.let { it(true) }
                            }

                            else -> {}
                        }
                    }

                    override fun onReceiveAccountIDDelete(
                        result: GGResultType,
                        device: GGIBLEDevice,
                        accountID: ByteArray
                    ): GGResultType {
                        super.onReceiveAccountIDDelete(result, device, accountID)
                        userDeletionCallBack?.let { it(if (result == GGResultType.GG_OK) GGUserActionResponseType.DELETE_COMPLETED else GGUserActionResponseType.DELETE_FAILED) }
                        if (!isDeviceSkipped(device.deviceInfo.broadcastId)) {
                            sendDeviceInfo(device)
                        }
                        return GGResultType.GG_OK
                    }

                    override fun onReceiveAccountIDList(
                        result: GGResultType,
                        device: GGIBLEDevice,
                        accountIDList: ArrayList<GGAccountInfo>
                    ): GGResultType {
                        super.onReceiveAccountIDList(result, device, accountIDList)
                        var users = listOf<GGBTUser>()
                        if (accountIDList.isNotEmpty()) {
                            users =
                                accountIDList.map {
                                    val user =
                                        GGBTUser(
                                            name = it.name(),
                                            token =
                                                Utils.byteArrayToHexString(
                                                    it.accountID()
                                                ),
                                            lastActive = it.updateTimeStamp.toLong(),
                                            isBodyMetricsEnabled = it.impedanceSwitch() == GGSwitchState.GG_SWITCH_STATE_ON
                                        )
                                    user
                                }
                        }
                        userListCallBack?.let { it(GGScaleUserResponse(user = users)) }

                        return GGResultType.GG_OK
                    }

                    override fun onScaleWakeUp(bleDevice: GGIBLEDevice): GGResultType {
                        log("Device wake up")
                        respondDeviceDetailCallback(
                            GGScanResponseType.DEVICE_WAKE_UP, bleDevice.deviceInfo
                        )
                        sendDeviceInfo(bleDevice)
                        return GGResultType.GG_OK
                    }

                    override fun onReceiveImpedanceState(
                        result: GGResultType,
                        device: GGIBLEDevice,
                        state: GGSwitchState,
                        onlyCurrentSession: Boolean
                    ): GGResultType {
                        super.onReceiveImpedanceState(
                            result,
                            device,
                            state,
                            onlyCurrentSession
                        )
                        sendDeviceInfo(device)
                        return GGResultType.GG_OK
                    }

                    override fun onReceiveWifiMacAddress(
                        result: GGResultType,
                        device: GGIBLEDevice,
                        deviceInfo: GGIDeviceInfo
                    ): GGResultType {
                        super.onReceiveWifiMacAddress(result, device, deviceInfo)
                        log("Wifi Mac Address: ${deviceInfo.wifiMacAddress}")
                        wifiMacCallBack?.let { it(deviceInfo.wifiMacAddress) }
                        return GGResultType.GG_OK
                    }
                }
            return dataCallback
        }

        private fun handleOnConnectionFailed(ggiDeviceInfo: GGIDeviceInfo) {
            val device = getDeviceByBroadcastId(ggiDeviceInfo.broadcastId)
            // TODO: Check and enable bleDevice
            if (device != null) {
                device.connectionStatus = ConnectionStatus.DISCONNECTED
                devices[ggiDeviceInfo.broadcastId] = device
            }
            val pairedDevice = checkDeviceAlreadyPaired(ggiDeviceInfo)
            if (pairedDevice != null) {
                connectDevice(ggiDeviceInfo, false)
            }
            respondDeviceDetailCallback(
                GGScanResponseType.DEVICE_CONNECTION_FAILED,
                ggiDeviceInfo
            )
            respondPairingCallback(GGUserActionResponseType.CREATION_FAILED)
        }

        private fun log(message: String) {
            Timber.tag(TAG).i(message)
        }

        private fun getDeviceByBroadcastId(broadcastId: String): GGDevice? {
            return devices.getOrDefault(broadcastId, null)
        }

        private fun checkDeviceAlreadyPaired(ggiDeviceInfo: GGIDeviceInfo): GGBTDevice? {
            return pairedDevices.find { it.broadcastId == ggiDeviceInfo.broadcastId }
        }

        private fun canPairDevices(ggiDeviceInfo: GGIDeviceInfo): Boolean {
            val broadcastId = ggiDeviceInfo.broadcastId
            if (skipDevices.contains(broadcastId)) {
                return false
            } else if (temporarySkipDevices.containsKey(broadcastId)) {
                val currentTime = Utils.getCurrentTime()
                val scaleTime = temporarySkipDevices.getValue(broadcastId)
                return (scaleTime + TEMPORARY_SKIP_TIME) >= currentTime
            }
            return true
        }

        private fun respondPairingCallback(status: GGUserActionResponseType) {
            userCreationCallBack?.let { it(status) }
            userUpdateCallBack?.let { it(status) }
            userCreationCallBack = null
            userUpdateCallBack = null
        }

        private fun upgradeFirmware(ggiBleDevice: GGIBLEDevice, timeStamp: Long) {
            ggiBleDevice.upgradeFirmware(timeStamp)
        }

        private fun sendDeviceInfo(device: GGIBLEDevice) {
            if (devices.containsKey(device.deviceInfo.broadcastId)) {
                val deviceInfo = devices.getValue(device.deviceInfo.broadcastId)
                deviceInfo.ggDevice = device.deviceInfo
                deviceInfo.bleDevice = device
                devices[device.deviceInfo.broadcastId] = deviceInfo
            } else {
                log("Device not found")
                return
            }
            if (checkDeviceAlreadyPaired(device.deviceInfo) != null) {
                respondDeviceDetailCallback(
                    GGScanResponseType.DEVICE_INFO_UPDATE,
                    device.deviceInfo
                )
            } else {
                log("Paired Device not found")
                return
            }

        }

        private fun respondDeviceEntryCallback(data: GGScanResponse) {
            try {
                scanCallback(data)
            } catch (ex: Exception) {
                log(ex.stackTraceToString())
            }
        }

        private fun respondDeviceDetailCallback(
            type: GGScanResponseType,
            deviceInfo: GGIDeviceInfo,
            muteMode: Boolean? = null,
        ) {
            try {
                scanCallback(
                    GGScanResponse.DeviceDetail(
                        type,
                        convertToDeviceDetail(deviceInfo, muteMode)
                    )
                )
            } catch (ex: Exception) {
                log(ex.stackTraceToString())
            }
        }
    }
}