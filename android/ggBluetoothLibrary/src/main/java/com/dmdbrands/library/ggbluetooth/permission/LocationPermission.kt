package com.dmdbrands.library.ggbluetooth.permission

import android.Manifest
import android.app.Activity
import android.os.Build
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionState
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionType

class LocationPermission(activity: Activity) : Permission(activity) {
    override var permissionType = GGPermissionType.LOCATION

    init {
        getPermissionStatusPreference()
    }

    override var isSwitchAvailable = false

    override fun getStatus(): String {
        val fineLocationStatus = getPermissionState(Manifest.permission.ACCESS_FINE_LOCATION)
        val coarseLocationStatus = getPermissionState(Manifest.permission.ACCESS_COARSE_LOCATION)
        val status = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) { //Android 11 and above
            when {
                (fineLocationStatus == GGPermissionState.ENABLED) -> GGPermissionState.ENABLED
                (fineLocationStatus == GGPermissionState.NOT_DETERMINED) -> GGPermissionState.NOT_DETERMINED
                (coarseLocationStatus == GGPermissionState.ENABLED) -> GGPermissionState.APPROX_LOCATION
                else -> GGPermissionState.DISABLED
            }
        } else if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.Q) { //Android 10 and below
            fineLocationStatus
        } else {
            GGPermissionState.NOT_DETERMINED
        }


        if(!isLocationPermissionStatusUpdating && !(permissionMap[permissionType] == GGPermissionState.NOT_REQUESTED && status == GGPermissionState.DISABLED)  &&
            !(permissionMap[permissionType] == GGPermissionState.PERMANENTLY_DENIED && status == GGPermissionState.DISABLED)) {
            setPermissionStatusPreference(permissionType, status)
        }
        if(permissionMap[permissionType] == GGPermissionState.PERMANENTLY_DENIED && isLocationPermissionStatusUpdating) {
            isLocationPermissionStatusUpdating = false
        }
        return status
    }

    override fun requestPermission(permissionRequestCallback: (data: Any?) -> Unit) {
        val locationStatus = getStatus()
        when (locationStatus) {
            GGPermissionState.NOT_DETERMINED,
            GGPermissionState.NOT_REQUESTED,
            GGPermissionState.DISABLED,
            GGPermissionState.APPROX_LOCATION
            -> {
                if(permissionMap[permissionType] == GGPermissionState.NOT_REQUESTED || permissionMap[permissionType] == GGPermissionState.NOT_DETERMINED
                    || permissionMap[permissionType] == GGPermissionState.DISABLED ){
                    when {
                        (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) -> { //Android 11 and above
                            launchMultiplePermission(
                                arrayOf(Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION)
                            )
                        }
                        (Build.VERSION.SDK_INT <= Build.VERSION_CODES.Q) -> { //Android 10 and below
                            launchMultiplePermission(
                                arrayOf( Manifest.permission.ACCESS_FINE_LOCATION)
                            )
                        }
                    }
                }
                else if(permissionMap[permissionType] == GGPermissionState.APPROX_LOCATION || permissionMap[permissionType] == GGPermissionState.PERMANENTLY_DENIED){
                    permissionRequestCallback.invoke(locationStatus)
                    gotoAppSettings()
                }
            }
            else -> return
        }
    }
}
