package com.dmdbrands.library.ggbluetooth.helper

import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.Looper
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionState
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionType
import com.dmdbrands.library.ggbluetooth.enums.GGScanResponseType
import com.dmdbrands.library.ggbluetooth.model.AppPermissions
import com.dmdbrands.library.ggbluetooth.model.BluetoothContext
import com.dmdbrands.library.ggbluetooth.model.GGPermissionStatusMap
import com.dmdbrands.library.ggbluetooth.model.GGScanResponse
import com.dmdbrands.library.ggbluetooth.permission.PermissionLocator
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import timber.log.Timber.Forest.tag


class GGPermissionHandler {
    private val tag = "GGPermissionHandler"
    private var mAppType: String = GGAppType.NONE
    private val permissionSubscriptionTimeout: Long = 2000
    private val scanPermissionSubscriptionTimeout: Long = 1000
    private lateinit var requestSinglePermission: ActivityResultLauncher<String>
    private lateinit var requestMultiplePermissions: ActivityResultLauncher<Array<String>>
    private lateinit var intentLauncher: ActivityResultLauncher<Intent>
    private lateinit var intentSenderLauncher: ActivityResultLauncher<IntentSenderRequest>
    private lateinit var scanCallback: (data: GGScanResponse) -> Unit
    private val permissionHandler = Handler(Looper.getMainLooper())
    private lateinit var permissionRunnable: Runnable
    private var startPermissionSubscription: Boolean = false
    private var permissionRequestCallback: ((data: Any?) -> Unit)? = null

    fun setContext(activity: BluetoothContext) {
        val convertedActivity = when (activity) {

            is BluetoothContext.ComposeActivity -> {
                activity.activity
            }

            is BluetoothContext.AppActivity -> {
                activity.activity
            }
        }

        //Single permission request callback
        requestSinglePermission = convertedActivity.registerForActivityResult(
            ActivityResultContracts.RequestPermission()
        ) {
            respondPermissionRequestCallback()
        }

        //Multiple permission request callback
        requestMultiplePermissions = convertedActivity.registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) {
            PermissionLocator.handlePermanentlyDeniedStatus(it)
            respondPermissionRequestCallback()
        }

        //Intent permission request callback
        intentLauncher =
            convertedActivity.registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
                respondPermissionRequestCallback()
            }

        //Intent Sender permission request callback
        intentSenderLauncher = convertedActivity.registerForActivityResult(
            ActivityResultContracts
                .StartIntentSenderForResult()
        ) {
            respondPermissionRequestCallback()
        }

        //Instance of individual permission class is created in PermissionLocator initialization
        PermissionLocator.initialize(
            convertedActivity as Activity, requestSinglePermission, requestMultiplePermissions,
            intentLauncher, intentSenderLauncher
        )
    }


    @OptIn(DelicateCoroutinesApi::class)
    fun subscribePermissions(callback: (data: GGScanResponse) -> Unit, appType: String) {
        scanCallback = callback
        mAppType = appType
        startPermissionSubscription = true
        GlobalScope.launch {
            permissionRunnable = object : Runnable {
                override fun run() {
                    //Subscribed permission status data is sent to scan callback
                    getPermissionStatus(true)
                    permissionHandler.postDelayed(this, permissionSubscriptionTimeout)
                }
            }
            if (startPermissionSubscription) {
                permissionHandler.postDelayed(permissionRunnable, 0)
            }
        }
    }

    fun stopPermissionSubscription() {
        permissionHandler.removeCallbacks(permissionRunnable)
        startPermissionSubscription = false
    }

    fun requestPermission(permissionName: String, callback: (data: Any?) -> Unit) {
        permissionRequestCallback = callback
        PermissionLocator.requestPermission(permissionName, callback)
    }

    fun getScanPermissionStatus(): Flow<Boolean> = flow {
        while (startPermissionSubscription) {
            val permissionStatus = getPermissionStatus()
            val bluetoothPermissionStatus = permissionStatus[GGPermissionType.BLUETOOTH_SWITCH]
            val nearbyDevicePermissionState = permissionStatus[GGPermissionType.NEARBY_DEVICE]
            val locationPermissionState = permissionStatus[GGPermissionType.LOCATION]
            val isScanPermissionGranted = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                when {
                    (nearbyDevicePermissionState == GGPermissionState.ENABLED && bluetoothPermissionStatus == GGPermissionState.ENABLED) -> true
                    else -> false
                }
            } else {
                when {
                    (locationPermissionState == GGPermissionState.ENABLED && bluetoothPermissionStatus == GGPermissionState.ENABLED) -> true
                    else -> false
                }
            }
            emit(isScanPermissionGranted)
            delay(scanPermissionSubscriptionTimeout)
        }
    }

    private fun getPermissionStatus(shouldSubscribe: Boolean = false): GGPermissionStatusMap {
        val appPermission = AppPermissions.getValue(mAppType)
        val permissionStatusMap = mutableMapOf<String, String>()

        appPermission.forEach { permissionType ->
            val updatedState = PermissionLocator.getStatus(permissionName = permissionType)
            permissionStatusMap[permissionType] =
                updatedState ?: GGPermissionState.NOT_DETERMINED
        }
        if (shouldSubscribe) {
            respondPermissionCallback(permissionStatusMap)
        }
        return permissionStatusMap
    }

    private fun respondPermissionRequestCallback() {
        val permissionStatus = getPermissionStatus()
        permissionRequestCallback?.invoke(permissionStatus)
    }

    private fun respondPermissionCallback(data: GGPermissionStatusMap) {
        try {
            scanCallback(
                GGScanResponse.Permission(
                    GGScanResponseType.PERMISSION_STATUS, data
                )
            )
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
        }
    }

    private fun log(message: String) {
        tag(tag).i(message)
    }
}