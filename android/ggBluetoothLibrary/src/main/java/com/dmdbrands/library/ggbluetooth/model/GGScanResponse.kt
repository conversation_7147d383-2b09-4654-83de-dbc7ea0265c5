package com.dmdbrands.library.ggbluetooth.model

import com.dmdbrands.library.ggbluetooth.enums.GGScanResponseType

sealed class GGScanResponse {
    data class DeviceDetail(val type: GGScanResponseType, val data: GGDeviceDetail) :
        GGScanResponse()

    data class Permission(val type: GGScanResponseType, val data: GGPermissionStatusMap) :
        GGScanResponse()

    data class Entry(val type: GGScanResponseType, val data: List<GGEntry>) : GGScanResponse()
    data object None : GGScanResponse()
}

