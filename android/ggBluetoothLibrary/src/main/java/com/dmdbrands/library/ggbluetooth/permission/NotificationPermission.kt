package com.dmdbrands.library.ggbluetooth.permission

import android.Manifest
import android.app.Activity
import android.os.Build
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionState
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionType

class NotificationPermission(activity: Activity) : Permission(activity) {

    override var permissionType = GGPermissionType.NOTIFICATION

    override var isSwitchAvailable = false

    init {
        getPermissionStatusPreference()
    }

    override fun getStatus(): String {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
            return GGPermissionState.ENABLED
        }
        val status = getPermissionState(Manifest.permission.POST_NOTIFICATIONS)
        if (!(permissionMap[permissionType] == GGPermissionState.NOT_REQUESTED && status == GGPermissionState.DISABLED)) {
            setPermissionStatusPreference(permissionType, status)
        }
        return status
    }

    override fun requestPermission(permissionRequestCallback: (data: Any?) -> Unit) {
        val initialPermissionStatus = getStatus()
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
            return
        }
        when (initialPermissionStatus) {
            GGPermissionState.NOT_DETERMINED,
            GGPermissionState.NOT_REQUESTED,
            GGPermissionState.DISABLED
            -> {
                if (permissionMap[permissionType] == GGPermissionState.NOT_REQUESTED || permissionMap[permissionType] == GGPermissionState.NOT_DETERMINED) {
                    launchSinglePermission(Manifest.permission.POST_NOTIFICATIONS)
                } else if (permissionMap[permissionType] == GGPermissionState.DISABLED) {
                    permissionRequestCallback.invoke(initialPermissionStatus)
                    gotoAppSettings()
                }
            }

            else -> {
                return
            }
        }
    }

}
