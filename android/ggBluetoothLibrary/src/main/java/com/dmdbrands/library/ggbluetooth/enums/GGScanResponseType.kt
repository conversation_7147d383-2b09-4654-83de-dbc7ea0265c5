package com.dmdbrands.library.ggbluetooth.enums

enum class GGScanResponseType {
    KNOWN_DEVICE,
    NEW_DEVICE,
    DEVICE_CONNECTED,
    DEVICE_CONNECTION_FAILED,
    DEVICE_DISCONNECTED,
    SINGLE_ENTRY,
    <PERSON>U<PERSON>I_ENTRIES,
    WIFI_STATUS_UPDATE,
    DEVICE_WAKE_UP,
    DEVICE_INFO_UPDATE,
    DEVICE_MEMORY_FULL,
    DEVICE_DUPLICATE_USER,
    PERMISSION_STATUS,
    ERROR,
    NONE
}