package com.dmdbrands.library.ggbluetooth.model

data class GGBTSetting(
    val key: String,
    val value: GGBTSettingValue
)

sealed class GGBTSettingValue {
    data class Boolean(val value : kotlin.Boolean) : GGBTSettingValue()
    data class String(val value: kotlin.String) : GGBTSettingValue()
    data class Int(val value: Int) : GGBTSettingValue()
    companion object {
        fun getSettingValue(setting: GGBTSetting): Any {
            return when (val value = setting.value) {
                is Boolean -> value.value
                is Int -> value.value
                is String -> value.value
            }
        }
    }
}

sealed class GGBTSettingValueException(message: String) : Exception(message)

class UnknownSettingTypeException(value: Any?) : GGBTSettingValueException(
    "Encountered unknown setting value type: $value"
)

