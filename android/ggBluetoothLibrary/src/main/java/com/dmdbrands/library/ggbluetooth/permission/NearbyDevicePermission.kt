package com.dmdbrands.library.ggbluetooth.permission

import android.Manifest
import android.app.Activity
import android.os.Build
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionState
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionType

class NearbyDevicePermission(activity: Activity) : Permission(activity) {

    override var permissionType = GGPermissionType.NEARBY_DEVICE

    override var isSwitchAvailable = false

    init {
        getPermissionStatusPreference()
    }

    override fun getStatus(): String {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            return GGPermissionState.NOT_DETERMINED
        }

        val bluetoothScanStatus = getPermissionState(Manifest.permission.BLUETOOTH_SCAN)
        val bluetoothConnectStatus = getPermissionState(Manifest.permission.BLUETOOTH_CONNECT)

        val status = if (bluetoothScanStatus == GGPermissionState.ENABLED && bluetoothConnectStatus == GGPermissionState.ENABLED)
        { GGPermissionState.ENABLED }
        else if (bluetoothScanStatus == GGPermissionState.NOT_DETERMINED && bluetoothConnectStatus == GGPermissionState.NOT_DETERMINED)
        { GGPermissionState.NOT_DETERMINED }
        else { GGPermissionState.DISABLED }

        if(! (permissionMap[permissionType] == GGPermissionState.NOT_REQUESTED && status == GGPermissionState.DISABLED)){
            setPermissionStatusPreference(permissionType,status)
        }
        return status
    }

    override fun requestPermission(permissionRequestCallback: (data: Any?) -> Unit) {
        val nearbyDevicePermissionStatus = getStatus()
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            return
        }
        when (nearbyDevicePermissionStatus) {
            GGPermissionState.NOT_DETERMINED,
            GGPermissionState.NOT_REQUESTED,
            GGPermissionState.DISABLED -> {
                if (permissionMap[permissionType] == GGPermissionState.NOT_REQUESTED ||
                    permissionMap[permissionType] == GGPermissionState.NOT_DETERMINED
                ) {
                    launchMultiplePermission(
                        arrayOf(
                            Manifest.permission.BLUETOOTH_SCAN,
                            Manifest.permission.BLUETOOTH_CONNECT
                        )
                    )
                } else if (permissionMap[permissionType] == GGPermissionState.DISABLED) {
                    permissionRequestCallback.invoke(nearbyDevicePermissionStatus)
                    gotoAppSettings()
                }
            }

            else -> return
        }
    }
}