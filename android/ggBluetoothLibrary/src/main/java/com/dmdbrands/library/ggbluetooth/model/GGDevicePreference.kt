package com.dmdbrands.library.ggbluetooth.model

data class GGDevicePreference(
    val tzOffset: Int? = null,
    val timeFormat: String? = null,
    val displayMetrics: List<String>? = null,
    val displayName: String? = null,
    val shouldMeasurePulse: Boolean? = null,
    val shouldMeasureImpedance: Boolean? = null,
    val shouldFactoryReset: Boolean? = null,
    val wifiFotaScheduleTime: Long? = null
)