package com.dmdbrands.library.ggbluetooth.helper

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.google.gson.Gson
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "permission_preferences")

class PermissionStatusPreference(context: Context) {

    private val dataStore = context.dataStore

    companion object;

    suspend fun setValue(key: String, value: String) {
        dataStore.edit { preferences ->
            preferences[stringPreferencesKey(key)] = Gson().toJson(value)
        }
    }

    val getValue
        get() =
            dataStore.data


    suspend fun getFirstValue(key: Preferences.Key<String>): String? {
        val preferences = dataStore.data.map { preferences ->
            preferences[key]
        }.first()
        return preferences
    }

    suspend fun clearValue(key: Preferences.Key<String>) {
        dataStore.edit { prefs ->
            prefs.remove(key)
        }
    }
}