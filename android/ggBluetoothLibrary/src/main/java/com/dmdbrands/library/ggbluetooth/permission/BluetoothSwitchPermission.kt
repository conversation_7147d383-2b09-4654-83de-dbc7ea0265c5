package com.dmdbrands.library.ggbluetooth.permission

import android.app.Activity
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.provider.Settings
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionState
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionType

class BluetoothSwitchPermission(activity: Activity) : Permission(activity) {

    private val bluetoothManager =
        getActivity()?.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
    private val bluetoothAdapter: BluetoothAdapter? = bluetoothManager.adapter
    override var permissionType = GGPermissionType.BLUETOOTH_SWITCH

    override var isSwitchAvailable = true

    init {
        getPermissionStatusPreference()
    }

    override fun getStatus(): String {
        val nearbyDeviceStatus = PermissionLocator.getStatus(GGPermissionType.NEARBY_DEVICE)
        val locationStatus = PermissionLocator.getStatus(GGPermissionType.LOCATION)
        val status = when {
            (bluetoothAdapter?.isEnabled == true) -> GGPermissionState.ENABLED
            ((Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) && nearbyDeviceStatus == GGPermissionState.ENABLED) -> GGPermissionState.NOT_DETERMINED
            ((Build.VERSION.SDK_INT < Build.VERSION_CODES.S) && locationStatus == GGPermissionState.ENABLED) -> GGPermissionState.NOT_DETERMINED
            else -> GGPermissionState.DISABLED
        }
        setPermissionStatusPreference(permissionType, status)
        return status
    }

    override fun requestPermission(permissionRequestCallback: (data: Any?) -> Unit) {
        val bluetoothSwitchStatus = getStatus()

        when (bluetoothSwitchStatus) {
            GGPermissionState.ENABLED -> return
            GGPermissionState.NOT_DETERMINED -> {
                val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                launchIntentPermission(enableBtIntent)
            }

            else -> {
                permissionRequestCallback.invoke(bluetoothSwitchStatus)
                goToBluetoothSettings()
            }
        }
    }

    private fun goToBluetoothSettings() {
        val bluetoothSettingsIntent = Intent().apply {
            action = Settings.ACTION_BLUETOOTH_SETTINGS
        }
        getActivity()?.startActivity(bluetoothSettingsIntent)
    }

}
