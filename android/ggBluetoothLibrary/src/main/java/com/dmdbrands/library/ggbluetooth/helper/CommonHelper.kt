package com.dmdbrands.library.ggbluetooth.helper

import android.icu.util.Calendar
import com.dmdbrands.library.ggbluetooth.enums.BodyMetrics
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.dmdbrands.library.ggbluetooth.enums.OperationType
import com.dmdbrands.library.ggbluetooth.model.GGBGMEntry
import com.dmdbrands.library.ggbluetooth.model.GGBPMEntry
import com.dmdbrands.library.ggbluetooth.model.GGDeviceDetail
import com.dmdbrands.library.ggbluetooth.model.GGPulseEntry
import com.dmdbrands.library.ggbluetooth.model.GGScaleEntry
import com.dmdbrands.library.ggbluetooth.model.GGTemperatureEntry
import com.dmdbrands.library.ggbluetooth.model.GGWeightEntry
import com.google.gson.GsonBuilder
import com.greatergoods.ggbluetoothsdk.external.enums.BgmUnit
import com.greatergoods.ggbluetoothsdk.external.enums.GGBLEDeviceType
import com.greatergoods.ggbluetoothsdk.external.enums.GGDeviceProtocolType
import com.greatergoods.ggbluetoothsdk.external.enums.GGSwitchState
import com.greatergoods.ggbluetoothsdk.external.enums.GGWeighingScaleDisplayDetail
import com.greatergoods.ggbluetoothsdk.external.enums.GGWifiState
import com.greatergoods.ggbluetoothsdk.external.enums.TemperatureUnit
import com.greatergoods.ggbluetoothsdk.external.enums.WeightUnit
import com.greatergoods.ggbluetoothsdk.external.models.GGIBloodGlucoseInfo
import com.greatergoods.ggbluetoothsdk.external.models.GGIBloodPressureInfo
import com.greatergoods.ggbluetoothsdk.external.models.GGIBodyMetricsInfo
import com.greatergoods.ggbluetoothsdk.external.models.GGIDeviceInfo
import com.greatergoods.ggbluetoothsdk.external.models.GGIHealthTemperatureInfo
import com.greatergoods.ggbluetoothsdk.external.models.GGIPulseOximeterInfo
import com.greatergoods.ggbluetoothsdk.external.models.GGIWeightInfo

object CommonHelper {
    fun <T : Any> convertToObject(data: String, className: Class<T>): T {
        val gson = GsonBuilder().create()
        return gson.fromJson(data, className)
    }

    fun convertStringToPreference(metrics: List<String>): ArrayList<GGWeighingScaleDisplayDetail> {
        val preferenceList = mutableListOf<GGWeighingScaleDisplayDetail>()
        metrics.forEach { BodyMetrics[it]?.let { it1 -> preferenceList.add(it1) } }
        return preferenceList as ArrayList<GGWeighingScaleDisplayDetail>
    }

    fun getWeightUnit(unit: String): WeightUnit {
        return when (unit) {
            "kg" -> WeightUnit.WEIGHT_KILOGRAMS
            "lb" -> WeightUnit.WEIGHT_POUNDS
            "lb_oz" -> WeightUnit.WEIGHT_LB_OZ
            "oz" -> WeightUnit.WEIGHT_OUNCE
            "g" -> WeightUnit.WEIGHT_GRAMS
            "ml" -> WeightUnit.WEIGHT_MILLI_LITRE_WATER
            "ml_milk" -> WeightUnit.WEIGHT_MILLI_LITRE_MILK
            "fl_oz" -> WeightUnit.WEIGHT_FLUID_OUNCE_WATER
            "fl_oz_milk" -> WeightUnit.WEIGHT_FLUID_OUNCE_MILK
            else -> WeightUnit.WEIGHT_POUNDS
        }
    }

    fun getTemperatureUnit(unit: String): TemperatureUnit {
        return when (unit) {
            "\u2103" -> TemperatureUnit.TEMPERATURE_CELSIUS
            "\u2109" -> TemperatureUnit.TEMPERATURE_FAHRENHEIT
            else -> TemperatureUnit.TEMPERATURE_CELSIUS
        }
    }

    fun getBgmUnit(unit: String): BgmUnit {
        return when (unit) {
            "mg/dl" -> BgmUnit.BGM_MG
            "mmol/L" -> BgmUnit.BGM_MMOL
            else -> BgmUnit.BGM_MG
        }
    }

    fun getTemperatureMuteMode(mode: String): GGSwitchState {
        return when (mode) {
            "On" -> GGSwitchState.GG_SWITCH_STATE_ON
            else -> GGSwitchState.GG_SWITCH_STATE_OFF
        }
    }

    fun convertToEntry(
        ggDeviceInfo: GGIDeviceInfo,
        ggBodyMetric: GGIBodyMetricsInfo
    ): GGScaleEntry {
        return GGScaleEntry(
            bmi = ggBodyMetric.bmi,
            bmr = ggBodyMetric.bmr.toInt(),
            bodyFat = ggBodyMetric.bodyFatPercent,
            water = ggBodyMetric.bodyWaterPercent,
            boneMass = ggBodyMetric.boneMass,
            metabolicAge = ggBodyMetric.metabolicAge,
            muscleMass = ggBodyMetric.musclePercent,
            proteinPercent = ggBodyMetric.protein,
            skeletalMusclePercent = ggBodyMetric.skeletalMuscle,
            subcutaneousFatPercent = ggBodyMetric.subcutaneousFat,
            unit = getUnitType(ggBodyMetric.unit),
            visceralFatLevel = ggBodyMetric.visceralFat.toInt(),
            weight = ggBodyMetric.weightInLbs,
            weightInKg = ggBodyMetric.weightInKg,
            impedance = ggBodyMetric.impedance,
            pulse = ggBodyMetric.heartRate,
            protocolType = getProtocolType(ggDeviceInfo.protocolType),
            broadcastId = ggDeviceInfo.broadcastId,
            broadcastIdString = ggDeviceInfo.broadcastId,
            operationType = OperationType.CREATE,
            date = (ggBodyMetric.timeStamp * 1000).toLong()
        )
    }

    fun convertToWeightEntry(
        ggDeviceInfo: GGIDeviceInfo,
        ggBodyMetric: GGIWeightInfo
    ): GGWeightEntry {
        return GGWeightEntry(
            unit = getUnitType(ggBodyMetric.unit),
            weight = ggBodyMetric.weight,
            weightInKg = ggBodyMetric.weightInKg,
            weightInMg = ggBodyMetric.weight,
            protocolType = getProtocolType(ggDeviceInfo.protocolType),
            broadcastId = ggDeviceInfo.broadcastId,
            broadcastIdString = ggDeviceInfo.broadcastId,
            operationType = OperationType.CREATE,
            date = (ggBodyMetric.timeStamp * 1000).toLong()
        )
    }

    fun covertToBPMEntry(
        ggDeviceInfo: GGIDeviceInfo,
        ggBodyMetric: GGIBloodPressureInfo
    ): GGBPMEntry {
        return GGBPMEntry(
            operationType = OperationType.CREATE,
            systolic = ggBodyMetric.systolicValue,
            diastolic = ggBodyMetric.diastolicValue,
            pulse = ggBodyMetric.pulseRate,
            meanPressure = ggBodyMetric.meanPressureValue,
            date = ggBodyMetric.timeStamp * 1000,
            userNumber = ggBodyMetric.user,
            protocolType = getProtocolType(ggDeviceInfo.protocolType),
            broadcastId = ggDeviceInfo.broadcastId,
            broadcastIdString = ggDeviceInfo.broadcastId
        )
    }

    fun convertToTemperatureEntry(
        ggDeviceInfo: GGIDeviceInfo,
        ggBodyMetric: GGIHealthTemperatureInfo
    ): GGTemperatureEntry {
        return GGTemperatureEntry(
            operationType = OperationType.CREATE,
            temperature = ggBodyMetric.temperatureValue,
            temperatureInFahrenheit = convertTemperatureToFahrenheit(ggBodyMetric.temperatureValue),
            date = ggBodyMetric.timeStamp * 1000,
            protocolType = getProtocolType(ggDeviceInfo.protocolType),
            broadcastId = ggDeviceInfo.broadcastId,
            broadcastIdString = ggDeviceInfo.broadcastId,
            unit = getRpmUnit(ggBodyMetric.temperatureUnit)
        )
    }

    fun convertToBGMEntry(
        ggDeviceInfo: GGIDeviceInfo,
        ggBodyMetric: GGIBloodGlucoseInfo
    ): GGBGMEntry {
        return GGBGMEntry(
            operationType = OperationType.CREATE,
            bgm = ggBodyMetric.value,
            date = ggBodyMetric.timeStamp * 1000,
            protocolType = getProtocolType(ggDeviceInfo.protocolType),
            broadcastId = ggDeviceInfo.broadcastId,
            broadcastIdString = ggDeviceInfo.broadcastId,
            unit = getRpmUnit(ggBodyMetric.bgmUnit)
        )
    }

    fun convertToPulseEntry(
        ggDeviceInfo: GGIDeviceInfo,
        ggBodyMetric: GGIPulseOximeterInfo
    ): GGPulseEntry {
        return GGPulseEntry(
            operationType = OperationType.CREATE,
            date = Calendar.getInstance().timeInMillis,
            protocolType = getProtocolType(ggDeviceInfo.protocolType),
            broadcastId = ggDeviceInfo.broadcastId,
            broadcastIdString = ggDeviceInfo.broadcastId,
            unit = "%",
            spO2 = ggBodyMetric.spO2,
            pr = ggBodyMetric.pr,
            pulseAmplitudeIndex = ggBodyMetric.pulseAmplitudeIndex
        )
    }

    private fun getUnitBasedOnType(deviceInfo: GGIDeviceInfo): String? {
        return when (deviceInfo.deviceType) {
            GGBLEDeviceType.GG_BGM_DEVICE, GGBLEDeviceType.GG_PULSE_OXIMETER_DEVICE, GGBLEDeviceType.GG_HEALTH_THERMOMETER_DEVICE -> getRpmUnit(
                deviceInfo.bgmUnit
            )

            GGBLEDeviceType.GG_WEIGHING_SCALE_DEVICE, GGBLEDeviceType.GG_SMART_KITCHEN_SCALE -> getUnitType(
                deviceInfo.deviceUnit
            )

            else -> null
        }
    }

    fun GGIDeviceInfo.getAppType(appType: String?): String? {
        if (appType != GGAppType.ALL) {
            return appType
        }
        return when (this.deviceType) {
            GGBLEDeviceType.GG_SMART_KITCHEN_SCALE -> GGAppType.SAGE
            GGBLEDeviceType.GG_HEALTH_THERMOMETER_DEVICE,
            GGBLEDeviceType.GG_PULSE_OXIMETER_DEVICE,
            GGBLEDeviceType.GG_BGM_DEVICE -> GGAppType.RPM

            GGBLEDeviceType.GG_WEIGHING_SCALE_DEVICE -> {
                if (this.deviceName.contains("0222") || this.deviceName.contains("0220"))
                    GGAppType.SMART_BABY
                else
                    GGAppType.WEIGHT_GURUS
            }

            GGBLEDeviceType.GG_BPM_DEVICE -> GGAppType.BALANCE_HEALTH
            else -> null
        }
    }


    fun convertToDeviceDetail(
        ggDeviceInfo: GGIDeviceInfo,
        muteMode: Boolean? = null,
        error: String? = null
    ): GGDeviceDetail {
        return GGDeviceDetail(
            systemID = ggDeviceInfo.systemID,
            manufacturerName = ggDeviceInfo.manufacturerName,
            modelNumber = ggDeviceInfo.modelNumber,
            serialNumber = ggDeviceInfo.serialNumber,
            softwareRevision = ggDeviceInfo.softwareRevision,
            hardwareRevision = ggDeviceInfo.hardwareRevision,
            firmwareRevision = ggDeviceInfo.firmwareRevision,
            deviceName = ggDeviceInfo.deviceName,
            broadcastId = ggDeviceInfo.broadcastId,
            broadcastIdString = ggDeviceInfo.broadcastId,
            password = ggDeviceInfo.password,
            macAddress = ggDeviceInfo.macAddress,
            wifiMacAddress = ggDeviceInfo.wifiMacAddress,
            identifier = ggDeviceInfo.broadcastId,
            protocolType = getProtocolType(ggDeviceInfo.protocolType),
            isWifiConfigured = ggDeviceInfo.wifiSetupState == GGWifiState.GG_WIFI_STATE_CONNECTED,
            sessionImpedanceSwitchState = ggDeviceInfo.sessionImpedanceMeasurementState == GGSwitchState.GG_SWITCH_STATE_ON,
            impedanceSwitchState = ggDeviceInfo.impedanceMeasurementState == GGSwitchState.GG_SWITCH_STATE_ON,
            startAnimationState = ggDeviceInfo.startAnimationState == GGSwitchState.GG_SWITCH_STATE_ON,
            endAnimationState = ggDeviceInfo.endAnimationState == GGSwitchState.GG_SWITCH_STATE_ON,
            batteryLevel = ggDeviceInfo.batteryLevel,
            muteMode = muteMode,
            unit = getUnitBasedOnType(ggDeviceInfo),
            error = error
        )
    }

    fun getWifiState(wifiState: GGWifiState): String {
        return when (wifiState) {
            GGWifiState.GG_WIFI_STATE_CANCELLED -> "GG_WIFI_STATE_CANCELLED"
            GGWifiState.GG_WIFI_STATE_CONNECTED -> "GG_WIFI_STATE_CONNECTED"
            GGWifiState.GG_WIFI_STATE_NOT_CONNECTED -> "GG_WIFI_STATE_NOT_CONNECTED"
            GGWifiState.GG_WIFI_STATE_DISCONNECTED -> "GG_WIFI_STATE_DISCONNECTED"
            else -> "GG_WIFI_STATE_UNSET"
        }
    }

    private fun getProtocolType(ggProtocolType: GGDeviceProtocolType): String {
        return when (ggProtocolType) {
            GGDeviceProtocolType.GG_DEVICE_PROTOCOL_A3 -> "A3"
            GGDeviceProtocolType.GG_DEVICE_PROTOCOL_A6 -> "A6"
            GGDeviceProtocolType.GG_DEVICE_PROTOCOL_R4 -> "R4"
            else -> ""
        }
    }

    fun getUnitType(unit: WeightUnit): String {
        return when (unit) {
            WeightUnit.WEIGHT_KILOGRAMS -> "kg"
            WeightUnit.WEIGHT_POUNDS -> "lb"
            WeightUnit.WEIGHT_GRAMS -> "g"
            WeightUnit.WEIGHT_OUNCE -> "oz"
            WeightUnit.WEIGHT_LB_OZ, WeightUnit.WEIGHT_POUND_OUNCE -> "lb:oz"
            WeightUnit.WEIGHT_MILLI_LITRE_WATER -> "ml"
            WeightUnit.WEIGHT_MILLI_LITRE_MILK -> "ml - m"

            WeightUnit.WEIGHT_FLUID_OUNCE_WATER -> "fl\'oz"
            WeightUnit.WEIGHT_FLUID_OUNCE_MILK -> "fl\'oz - m"

            else -> "lb"
        }
    }

    fun <T> getRpmUnit(unit: T): String {
        return when (unit) {
            TemperatureUnit.TEMPERATURE_CELSIUS -> "\u2103"
            TemperatureUnit.TEMPERATURE_FAHRENHEIT -> "\u2109"
            BgmUnit.BGM_MG -> "mg/dl"
            BgmUnit.BGM_MMOL -> "mmol/L"
            else -> ""
        }
    }

    private fun convertTemperatureToFahrenheit(temperature: Float): Float {
        val fahrenheit = (temperature * 1.8) + 32
        return String.format("%.1f", fahrenheit).toFloat()
    }

}
