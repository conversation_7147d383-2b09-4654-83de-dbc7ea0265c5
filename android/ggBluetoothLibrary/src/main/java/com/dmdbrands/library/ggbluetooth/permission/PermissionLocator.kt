package com.dmdbrands.library.ggbluetooth.permission

import android.app.Activity
import android.content.Intent
import android.os.Build
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionType

//@SuppressLint("StaticFieldLeak")
object PermissionLocator {
    private lateinit var singlePermissionRequestLauncher: ActivityResultLauncher<String>
    private lateinit var multiplePermissionRequestLauncher: ActivityResultLauncher<Array<String>>
    private lateinit var intentRequestLauncher: ActivityResultLauncher<Intent>
    private lateinit var intentSenderRequestLauncher: ActivityResultLauncher<IntentSenderRequest>

    private lateinit var bluetoothSwitchPermission: BluetoothSwitchPermission
    private lateinit var nearbyDevicePermission: NearbyDevicePermission
    private lateinit var locationPermission: LocationPermission
    private lateinit var locationSwitchPermission: LocationSwitchPermission
    private lateinit var notificationPermission: NotificationPermission
    private lateinit var cameraPermission: CameraPermission


    fun initialize(
        activity: Activity,
        requestSinglePermission: ActivityResultLauncher<String>,
        requestMultiplePermission: ActivityResultLauncher<Array<String>>,
        intentLauncher: ActivityResultLauncher<Intent>,
        intentSenderLauncher: ActivityResultLauncher<IntentSenderRequest>
    ) {
        singlePermissionRequestLauncher = requestSinglePermission
        multiplePermissionRequestLauncher = requestMultiplePermission
        intentRequestLauncher = intentLauncher
        intentSenderRequestLauncher = intentSenderLauncher

        //Creating instance of individual permission class
        bluetoothSwitchPermission = BluetoothSwitchPermission(activity)
        nearbyDevicePermission = NearbyDevicePermission(activity)
        locationPermission = LocationPermission(activity)
        locationSwitchPermission = LocationSwitchPermission(activity)
        notificationPermission = NotificationPermission(activity)
        cameraPermission = CameraPermission(activity)
    }

    fun getStatus(permissionName: String): String? {
        val permission = getPermission(permissionName)
        return permission?.getStatus()
    }

    fun requestPermission(permissionName: String, requestCallback: (data: Any?) -> Unit ) {
        val permission = getPermission(permissionName)
        permission?.setSinglePermissionLauncher(singlePermissionRequestLauncher)
        permission?.setMultipleRequestLauncher(multiplePermissionRequestLauncher)
        permission?.setIntentRequestLauncher(intentRequestLauncher)
        permission?.setIntentSenderRequestLauncher(intentSenderRequestLauncher)
        permission?.requestPermission(requestCallback)
    }

    fun handlePermanentlyDeniedStatus(status: Map<String, Boolean>) {
        if( status.keys.contains(android.Manifest.permission.CAMERA) )
        {
            val permissionStatus = getStatus(GGPermissionType.CAMERA)
            cameraPermission.handlePermanentlyDeniedPermissionRequest(
                GGPermissionType.CAMERA, permissionStatus, status[android.Manifest.permission.CAMERA] == false)
        } else if(status.keys.contains(android.Manifest.permission.ACCESS_FINE_LOCATION) )
        {
            val permissionStatus = getStatus(GGPermissionType.LOCATION)
            locationPermission.handlePermanentlyDeniedPermissionRequest(
                GGPermissionType.LOCATION, permissionStatus, status[android.Manifest.permission.ACCESS_FINE_LOCATION] == false)
        }
    }

    private fun getPermission(permissionName: String): Permission? {
        return when (permissionName) {
            GGPermissionType.BLUETOOTH_SWITCH -> bluetoothSwitchPermission
            GGPermissionType.NEARBY_DEVICE -> nearbyDevicePermission
            GGPermissionType.LOCATION -> locationPermission
            GGPermissionType.LOCATION_SWITCH -> locationSwitchPermission
            GGPermissionType.NOTIFICATION -> notificationPermission
            GGPermissionType.CAMERA -> cameraPermission
            else -> null
        }
    }

}