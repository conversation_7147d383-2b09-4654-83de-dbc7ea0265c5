package com.dmdbrands.library.ggbluetooth


import com.dmdbrands.library.ggbluetooth.enums.ClearDataType
import com.dmdbrands.library.ggbluetooth.enums.GGUserActionResponseType
import com.dmdbrands.library.ggbluetooth.helper.GGBluetoothSDKHelper
import com.dmdbrands.library.ggbluetooth.helper.GGPermissionHandler
import com.dmdbrands.library.ggbluetooth.model.BluetoothContext
import com.dmdbrands.library.ggbluetooth.model.GGBTDevice
import com.dmdbrands.library.ggbluetooth.model.GGBTSetting
import com.dmdbrands.library.ggbluetooth.model.GGBTUserProfile
import com.dmdbrands.library.ggbluetooth.model.GGBTWifiConfig
import com.dmdbrands.library.ggbluetooth.model.GGDeviceDetail
import com.dmdbrands.library.ggbluetooth.model.GGDeviceLogResponse
import com.dmdbrands.library.ggbluetooth.model.GGLiveDataResponse
import com.dmdbrands.library.ggbluetooth.model.GGScaleUserResponse
import com.dmdbrands.library.ggbluetooth.model.GGScanResponse
import com.dmdbrands.library.ggbluetooth.model.GGWifiResponse
import com.dmdbrands.library.ggbluetooth.model.GGWifiSetupResponse
import timber.log.Timber.Forest.tag


class GGBluetooth(private val activity: BluetoothContext) {
    private val tag = "GGBluetooth"
    private var permissionHandler: GGPermissionHandler = GGPermissionHandler()

    init {
        permissionHandler.setContext(activity)
    }

    fun scan(profile: GGBTUserProfile, appType: String, callback: (data: GGScanResponse) -> Unit) {
        try {
            GGBluetoothSDKHelper.initializeScan(
                activity,
                profile,
                appType,
                permissionHandler,
                callback
            )
        } catch (ex: Exception) {
            log(ex.message.toString())
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun confirmPair(
        device: GGBTDevice,
        callback: (data: GGUserActionResponseType) -> Unit
    ) {
        try {
            GGBluetoothSDKHelper.checkAndPair(device, callback)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun deleteUser(
        device: GGBTDevice,
        disconnect: Boolean,
        callback: (data: GGUserActionResponseType) -> Unit
    ) {
        try {
            GGBluetoothSDKHelper.deleteAccount(device, disconnect, callback)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun getWifiList(device: GGBTDevice, callback: (data: GGWifiResponse) -> Unit) {
        try {
            GGBluetoothSDKHelper.getWifiList(device, callback)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun getUsers(device: GGBTDevice, callback: (data: GGScaleUserResponse) -> Unit) {
        try {
            GGBluetoothSDKHelper.getUserList(device, callback)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun getWifiMacAddress(device: GGBTDevice, callback: (data: String) -> Unit) {
        try {
            GGBluetoothSDKHelper.getWifiMacAddress(device, callback)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun getDeviceInfo(device: GGBTDevice, callback: (data: GGDeviceDetail?) -> Unit) {
        try {
            GGBluetoothSDKHelper.getDeviceInfo(device, callback)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun getDeviceLogs(device: GGBTDevice, callback: (data: GGDeviceLogResponse) -> Unit) {
        try {
            GGBluetoothSDKHelper.getDeviceLogs(device, callback)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun setupWifi(
        device: GGBTDevice,
        wifiConfig: GGBTWifiConfig,
        callback: (data: GGWifiSetupResponse) -> Unit
    ) {
        try {
            GGBluetoothSDKHelper.setupWifi(device, wifiConfig, callback)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
        }
    }

    fun cancelWifi(device: GGBTDevice, callback: (data: Boolean) -> Unit) {
        try {
            GGBluetoothSDKHelper.cancelWifi(device, callback)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun updateProfile(profile: GGBTUserProfile, callback: (data: Boolean) -> Unit) {
        try {
            GGBluetoothSDKHelper.updateProfile(profile, callback)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun updateAccount(
        device: GGBTDevice,
        callback: (data: GGUserActionResponseType) -> Unit
    ) {
        try {
            GGBluetoothSDKHelper.updateAccount(device, callback)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun restoreAccount(
        device: GGBTDevice,
        accountName: String,
        callback: (data: GGUserActionResponseType) -> Unit
    ) {
        try {
            GGBluetoothSDKHelper.restoreAccount(device, accountName, callback)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }


    fun getConnectedWifiSSID(device: GGBTDevice, callback: (data: String) -> Unit) {
        try {
            GGBluetoothSDKHelper.getConnectedWifiSSID(device, callback)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun getMeasurementLiveData(device: GGBTDevice, callback: (data: GGLiveDataResponse) -> Unit) {
        try {
            GGBluetoothSDKHelper.getMeasurementLiveData(device, callback)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun startFirmwareUpdate(device: GGBTDevice, timeStamp: Long) {
        try {
            GGBluetoothSDKHelper.startFirmwareUpdate(device, timeStamp)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun stop() {
        try {
            GGBluetoothSDKHelper.stopScan(forceStop = true, canReinitialization = true)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun resumeScan(clearOnlyPairing: Boolean) {
        try {
            GGBluetoothSDKHelper.resumeScan(clearOnlyPairing)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun scanForPairing() {
        try {
            GGBluetoothSDKHelper.scanForPairing()
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun pauseScan() {
        try {
            GGBluetoothSDKHelper.pauseScan()
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun syncAccount(accounts: Map<String, GGBTUserProfile>) {
        try {
            GGBluetoothSDKHelper.setAccounts(accounts)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun syncDevices(devices: List<GGBTDevice>) {
        try {
            GGBluetoothSDKHelper.setPairedDevices(devices)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun clearDevices() {
        try {
            GGBluetoothSDKHelper.clearPairedDevices()
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun updateSetting(device: GGBTDevice, settings: List<GGBTSetting>) {
        try {
            GGBluetoothSDKHelper.updateSetting(settings, device)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun disconnectDevice(broadcastId: String) {
        try {
            GGBluetoothSDKHelper.disconnectDevice(broadcastId)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun skipDevice(broadcastId: String) {
        try {
            GGBluetoothSDKHelper.skipDevice(broadcastId)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun tare(device: GGBTDevice) {
        try {
            GGBluetoothSDKHelper.tare(device)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun subscribeToLiveData(device: GGBTDevice) {
        try {
            GGBluetoothSDKHelper.subscribeLiveData(device)
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }

    fun clearData(device: GGBTDevice, clearType: ClearDataType, callback: (data: String) -> Unit) {

        try {
            GGBluetoothSDKHelper.clearData(device, clearType, callback)
        } catch (ex: Exception) {
            ex.printStackTrace()
            throw ex
        }
    }

    fun requestPermission(permission: String) {
        try {
            permissionHandler.requestPermission(permission) { data ->
                // Handle the callback result here
                log("Permission result: $data")
            }
        } catch (ex: Exception) {
            log(ex.stackTraceToString())
            throw ex
        }
    }
    
    fun log(message: String) {
        tag(tag).i(message)
    }

}