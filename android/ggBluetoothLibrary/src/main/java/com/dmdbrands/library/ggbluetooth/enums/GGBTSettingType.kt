package com.dmdbrands.library.ggbluetooth.enums

object GGBTSettingType {
    const val HEART_RATE = "HEART_RATE"
    const val IMPEDANCE = "IMPEDANCE"
    const val SESSION_IMPEDANCE = "SESSION_IMPEDANCE"
    const val RESTORE_FACTORY = "RESTORE_FACTORY"
    const val INITIAL_LOGO_ANIM = "INITIAL_LOGO_ANIM"
    const val FINAL_LOGO_ANIM = "FINAL_LOGO_ANIM"
    const val TIME_FORMAT = "TIME_FORMAT"
    const val RESET_FIRMWARE = "RESET_FIRMWARE"
    const val UNIT = "UNIT"
    const val TEMPERATURE_UNIT = "TEMPERATURE_UNIT"
    const val BGM_UNIT = "BGM_UNIT"
    const val TARE = "TARE"
    const val MUTE_MODE = "MUTE_MODE"
    const val MUTE_MODE_ON = "MUTE MODE ON"
    const val MUTE_MODE_OFF = "MUTE MODE OFF"
}
