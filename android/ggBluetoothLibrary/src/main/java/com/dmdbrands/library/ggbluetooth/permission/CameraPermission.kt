package com.dmdbrands.library.ggbluetooth.permission

import android.Manifest
import android.app.Activity
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionState
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionType

class CameraPermission(activity: Activity) : Permission(activity) {

    override var permissionType = GGPermissionType.CAMERA

    override var isSwitchAvailable = false

    init {
        getPermissionStatusPreference()
    }

    override fun getStatus(): String {
        val status = getPermissionState(Manifest.permission.CAMERA)
        if (!(permissionMap[permissionType] == GGPermissionState.NOT_REQUESTED && status == GGPermissionState.DISABLED)) {
            setPermissionStatusPreference(permissionType, status)
        }
        return status
    }

    override fun requestPermission(permissionRequestCallback: (data: Any?) -> Unit) {
        val cameraPermissionStatus = getStatus()
        when (cameraPermissionStatus) {
            GGPermissionState.NOT_DETERMINED,
            GGPermissionState.NOT_REQUESTED,
            GGPermissionState.DISABLED
            -> {
                if( permissionMap[permissionType] == GGPermissionState.NOT_REQUESTED || permissionMap[permissionType] == GGPermissionState.NOT_DETERMINED
                    || permissionMap[permissionType] == GGPermissionState.DISABLED){
                    launchSinglePermission(Manifest.permission.CAMERA)
                } else if (permissionMap[permissionType] == GGPermissionState.DISABLED) {
                    permissionRequestCallback.invoke(cameraPermissionStatus)
                    gotoAppSettings()
                }
            }

            else -> {
                return
            }
        }
    }

}
