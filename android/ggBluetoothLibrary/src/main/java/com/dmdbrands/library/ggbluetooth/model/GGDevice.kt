package com.dmdbrands.library.ggbluetooth.model

import com.greatergoods.ggbluetoothsdk.external.devices.GGIBLEDevice
import com.greatergoods.ggbluetoothsdk.external.models.GGIDeviceInfo
import com.dmdbrands.library.ggbluetooth.enums.ConnectionStatus

data class GGDevice(
    var ggDevice: GGIDeviceInfo,
    var bleDevice: GGIBLEDevice? = null,
    var token: String? = null,
    val existingDevice: Boolean = false,
    var userNumber: Int? = null,
    var connectionStatus: ConnectionStatus = ConnectionStatus.DISCONNECTED,
    val syncAllData: Boolean? = null
)
