package com.dmdbrands.library.ggbluetooth.model

data class GGBPMEntry(
    override val operationType: String? = null,
    val systolic: Number,
    val diastolic: Number,
    val pulse: Number,
    override val date: Long,
    override val broadcastId: String,
    override val broadcastIdString: String,
    override val protocolType: String,
    val userNumber: Number,
    val meanPressure: Number,
    override val unit: String? = null
) : GGEntry()