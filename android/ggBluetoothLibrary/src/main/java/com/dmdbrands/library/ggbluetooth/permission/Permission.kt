package com.dmdbrands.library.ggbluetooth.permission

import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.core.app.ActivityCompat.shouldShowRequestPermissionRationale
import androidx.datastore.preferences.core.stringPreferencesKey
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionState
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionType
import com.dmdbrands.library.ggbluetooth.helper.PermissionStatusPreference
import com.dmdbrands.library.ggbluetooth.model.GGPermissionStatusMap
import com.google.gson.Gson
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import timber.log.Timber
import java.lang.ref.WeakReference

sealed class Permission(activity: Activity) {
    private val activityRef = WeakReference(activity)

    protected fun getActivity(): Activity? {
        return activityRef.get()
    }

    private lateinit var requestSinglePermission: ActivityResultLauncher<String>
    private lateinit var requestMultiplePermission: ActivityResultLauncher<Array<String>>
    private lateinit var intentRequestLauncher: ActivityResultLauncher<Intent>
    private lateinit var intentSenderRequestLauncher: ActivityResultLauncher<IntentSenderRequest>
    private val tag = "Permission"
    protected open var isSwitchAvailable = false
    var isLocationPermissionStatusUpdating = false
    var isCameraPermissionStatusUpdating = false
    var permissionMap: GGPermissionStatusMap = mutableMapOf(
        GGPermissionType.NOTIFICATION to GGPermissionState.NOT_REQUESTED,
        GGPermissionType.CAMERA to GGPermissionState.NOT_REQUESTED,
        GGPermissionType.NEARBY_DEVICE to GGPermissionState.NOT_REQUESTED,
        GGPermissionType.LOCATION to GGPermissionState.NOT_REQUESTED,
        GGPermissionType.BLUETOOTH_SWITCH to GGPermissionState.NOT_REQUESTED,
        GGPermissionType.LOCATION_SWITCH to GGPermissionState.NOT_REQUESTED
    )
    private var permissionStatusPreference: PermissionStatusPreference =
        PermissionStatusPreference(activity)
    open lateinit var permissionType: String

    abstract fun getStatus(): String

    abstract fun requestPermission(permissionRequestCallback: (data: Any?) -> Unit)


    companion object {
        private const val STATUS_KEY_PREFIX = "permission_"
    }

    fun setSinglePermissionLauncher(launcher: ActivityResultLauncher<String>) {
        requestSinglePermission = launcher
    }

    fun setMultipleRequestLauncher(launcher: ActivityResultLauncher<Array<String>>) {
        requestMultiplePermission = launcher
    }

    fun setIntentRequestLauncher(intentLauncher: ActivityResultLauncher<Intent>) {
        intentRequestLauncher = intentLauncher
    }

    fun setIntentSenderRequestLauncher(intentSenderLauncher: ActivityResultLauncher<IntentSenderRequest>) {
        intentSenderRequestLauncher = intentSenderLauncher
    }

    fun launchSinglePermission(permissionName: String) {
        requestSinglePermission.launch(permissionName)
    }

    fun launchMultiplePermission(permissions: Array<String>) {
        requestMultiplePermission.launch(permissions)
    }

    fun launchIntentPermission(intent: Intent) {
        intentRequestLauncher.launch(intent)
    }

    fun launchIntentSenderPermission(intent: IntentSenderRequest) {
        intentSenderRequestLauncher.launch(intent)
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun setPermissionStatusPreference(
        key: String,
        value: String = GGPermissionState.NOT_REQUESTED
    ) {
        GlobalScope.launch(Dispatchers.IO) {
            val preferenceKey = STATUS_KEY_PREFIX + key
            permissionStatusPreference.setValue(preferenceKey, value)
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun getPermissionStatusPreference() {
        val permissionType: String = permissionType
        GlobalScope.launch(Dispatchers.IO) {
            permissionStatusPreference.getValue.collect { value ->
                val preferenceKey = value[stringPreferencesKey(STATUS_KEY_PREFIX + permissionType)]
                //if preferenceKey is null, it means there's no value associated with the specified key,
                // and the lambda inside collect is returned from,
                // effectively skipping the rest of the lambda's execution for this particular iteration.
                val stringValue = preferenceKey ?: return@collect
                permissionMap[permissionType] =
                    Gson().fromJson(stringValue, String::class.java)
            }
        }
    }

    fun getPermissionState(permission: String): String {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            return GGPermissionState.NOT_DETERMINED
        }
        if (getActivity() == null) {
            return GGPermissionState.NOT_DETERMINED
        }
        val activity = getActivity()
        return if (
            getActivity()?.checkSelfPermission(permission) == PackageManager.PERMISSION_GRANTED
        ) {
            GGPermissionState.ENABLED
        } else if (shouldShowRequestPermissionRationale(activity!!, permission)) {
            GGPermissionState.NOT_DETERMINED
        } else {
            GGPermissionState.DISABLED
        }
    }

    fun handlePermanentlyDeniedPermissionRequest(
        permission: String,
        permissionStatus: String?,
        isPermissionDenied: Boolean
    ) {
        when {
            (permission === GGPermissionType.CAMERA) || (permission === GGPermissionType.LOCATION) -> {
                if(permissionStatus == GGPermissionState.DISABLED && isPermissionDenied && permissionMap[permission] !== GGPermissionState.PERMANENTLY_DENIED) {
                    when(permission) {
                        (GGPermissionType.CAMERA) -> isCameraPermissionStatusUpdating = true
                        (GGPermissionType.LOCATION) -> isLocationPermissionStatusUpdating = true
                    }
                    setPermissionStatusPreference(permission, GGPermissionState.PERMANENTLY_DENIED)
                }
            }
        }
    }

    fun gotoAppSettings() {
        if (getActivity() == null) {
            return
        }
        val activity = getActivity()!!
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
        val uri = Uri.fromParts("package", activity.packageName, null)
        intent.setData(uri)
        activity.startActivity(intent)
    }

    fun log(message: String) {
        Timber.tag(tag).i(message)
    }
}