package com.dmdbrands.library.ggbluetooth.model

data class GGDeviceDetail(
    val systemID: String? = null,
    val manufacturerName: String? = null,
    val modelNumber: String? = null,
    val serialNumber: String? = null,
    val softwareRevision: String? = null,
    val hardwareRevision: String? = null,
    val firmwareRevision: String? = null,
    val deviceName: String,
    val broadcastId: String? = null,
    val broadcastIdString: String? = null,
    val password: String? = null,
    val macAddress: String,
    val wifiMacAddress: String? = null,
    val identifier: String,
    val protocolType: String? = null,
    val isWifiConfigured: Boolean? = null,
    val sessionImpedanceSwitchState: Boolean? = null,
    val impedanceSwitchState: Boolean? = null,
    val startAnimationState: Boolean? = null,
    val endAnimationState: Boolean? = null,
    val muteMode: Boolean? = null,
    val error: String? = null,
    val unit: String? = null,
    val batteryLevel: Int? = null
)
