package com.dmdbrands.library.ggbluetooth.model

data class GGWeightEntry(
    override val unit: String,
    val weight: Float,
    val weightInKg: Float? = null,
    val weightInMg: Float,
    override val date: Long,
    override val broadcastId: String,
    override val broadcastIdString: String,
    override val protocolType: String = "WELLAND_KITCHEN",
    override val operationType: String? = null
) : GGEntry()