package com.dmdbrands.library.ggbluetooth.model


data class GGBTUserProfile(
    val name: String,
    val age: Int? = null,
    val sex: String? = null,
    val unit: String? = null,
    val height: Double? = null,
    val weight: Double? = null,
    val goalWeight: Double? = null,
    val isAthlete: Boolean? = null,
    val goalType: String? = null,
    val metrics: List<GGBTMetricConfig> = mutableListOf()
)