package com.dmdbrands.library.ggbluetooth.permission

import android.app.Activity
import android.content.Context
import android.content.IntentSender
import android.location.LocationManager
import androidx.activity.result.IntentSenderRequest
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionState
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionType
import com.google.android.gms.common.api.ResolvableApiException
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.LocationSettingsResponse
import com.google.android.gms.location.LocationSettingsStatusCodes
import com.google.android.gms.location.Priority
import com.google.android.gms.tasks.Task

class LocationSwitchPermission(activity: Activity) : Permission(activity) {

    private val locationManager =
        activity.getSystemService(Context.LOCATION_SERVICE) as LocationManager
    override var permissionType = GGPermissionType.LOCATION_SWITCH

    override var isSwitchAvailable = true

    init {
        getPermissionStatusPreference()
    }

    override fun getStatus(): String {
        val status = when {
            (locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) -> GGPermissionState.ENABLED
            else -> GGPermissionState.DISABLED
        }
        setPermissionStatusPreference(permissionType, status)
        return status
    }

    override fun requestPermission(permissionRequestCallback: (data: Any?) -> Unit) {
        if (getActivity() == null) {
            return
        }
        val activity = getActivity()!!
        val locationSwitchStatus = getStatus()
        if (locationSwitchStatus == GGPermissionState.DISABLED) {
            val locationSettingsClient = LocationServices.getSettingsClient(activity)
            val locationRequest =
                LocationRequest.Builder(Priority.PRIORITY_HIGH_ACCURACY, 1000).build()
            val locationRequestBuilder =
                LocationSettingsRequest.Builder().addLocationRequest(locationRequest)
            val gpsSettingTask: Task<LocationSettingsResponse> =
                locationSettingsClient.checkLocationSettings(locationRequestBuilder.build())
            gpsSettingTask.addOnFailureListener { exception ->
                val statusCode = (exception as ResolvableApiException).statusCode
                if (statusCode == LocationSettingsStatusCodes.RESOLUTION_REQUIRED) {
                    try {
                        val locationSwitchIntent =
                            IntentSenderRequest.Builder(exception.resolution).build()
                        launchIntentSenderPermission(locationSwitchIntent)
                    } catch (sendEx: IntentSender.SendIntentException) {
                        log(sendEx.message.toString())
                    }
                }
            }
        } else {
            return
        }
    }
}

