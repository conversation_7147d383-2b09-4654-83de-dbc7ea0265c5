package com.dmdbrands.library.ggbluetooth.model

import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionType

typealias GGPermissionStatusMap = MutableMap<String, String>

val AppPermissions = mutableMapOf(
    GGAppType.WEIGHT_GURUS to listOf(
        GGPermissionType.NOTIFICATION,
        GGPermissionType.NEARBY_DEVICE,
        GGPermissionType.BLUETOOTH_SWITCH,
        GGPermissionType.LOCATION_SWITCH,
        GGPermissionType.LOCATION,
        GGPermissionType.CAMERA,
        GGPermissionType.ALL
    ),
    GGAppType.BALANCE_HEALTH to listOf(
        GGPermissionType.NEARBY_DEVICE,
        GGPermissionType.BLUETOOTH_SWITCH,
        GGPermissionType.LOCATION_SWITCH,
        GGPermissionType.LOCATION,
        GGPermissionType.ALL
    ),
    GGAppType.SMART_BABY to listOf(
        GGPermissionType.NOTIFICATION, GGPermissionType.NEARBY_DEVICE,
        GGPermissionType.BLUETOOTH_SWITCH, GGPermissionType.LOCATION, GGPermissionType.ALL
    ),
    GGAppType.SAGE to listOf(
        GGPermissionType.NOTIFICATION,
        GGPermissionType.NEARBY_DEVICE,
        GGPermissionType.BLUETOOTH_SWITCH,
        GGPermissionType.LOCATION_SWITCH,
        GGPermissionType.LOCATION,
        GGPermissionType.CAMERA,
        GGPermissionType.ALL
    ),
    GGAppType.ALL to listOf(
        GGPermissionType.NOTIFICATION,
        GGPermissionType.NEARBY_DEVICE,
        GGPermissionType.BLUETOOTH_SWITCH,
        GGPermissionType.LOCATION_SWITCH,
        GGPermissionType.LOCATION,
        GGPermissionType.CAMERA,
        GGPermissionType.ALL
    ),
    GGAppType.RPM to listOf(
        GGPermissionType.NOTIFICATION,
        GGPermissionType.NEARBY_DEVICE,
        GGPermissionType.BLUETOOTH_SWITCH,
        GGPermissionType.LOCATION_SWITCH,
        GGPermissionType.LOCATION,
        GGPermissionType.CAMERA,
        GGPermissionType.ALL
    )
)
