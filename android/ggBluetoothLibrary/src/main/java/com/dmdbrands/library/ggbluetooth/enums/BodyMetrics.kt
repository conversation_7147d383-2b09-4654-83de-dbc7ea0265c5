package com.dmdbrands.library.ggbluetooth.enums

import com.greatergoods.ggbluetoothsdk.external.enums.GGWeighingScaleDisplayDetail

val BodyMetrics =
    mapOf(
        "bmi" to GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BMI,
        "bodyFatPercent" to GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BODY_FAT,
        "musclePercent" to GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_MUSCLE,
        "bodyWaterPercent" to
                GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BODY_WATER,
        "bonePercent" to GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BONE_MASS,
        "heartRate" to GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_HEART_RATE,
        "visceralFatLevel" to
                GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_VISCERAL_FAT,
        "subcutaneousFatPercent" to
                GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_SUBCUTANEOUS_FAT,
        "proteinPercent" to GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_PROTEIN,
        "skeletalMusclePercent" to
                GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_SKELETAL_MUSCLE,
        "bmr" to GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BMR,
        "metabolicAge" to GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_BODY_AGE,
        "goalProgress" to GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_TARGET,
        "dailyAverage" to
                GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_DAILY_AVERAGE,
        "weeklyAverage" to
                GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_WEEKLY_AVERAGE,
        "monthlyAverage" to
                GGWeighingScaleDisplayDetail.GG_WEIGHING_SCALE_DISPLAY_MONTHLY_AVERAGE,
    )
