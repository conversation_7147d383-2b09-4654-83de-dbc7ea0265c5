package com.dmdbrands.library.ggbluetooth.model

import com.dmdbrands.library.ggbluetooth.enums.GGLiveDataResponseType

sealed class GGLiveDataResponse {
    data class Success(
        val data: GGEntry,
        val type: GGLiveDataResponseType = GGLiveDataResponseType.SUCCESS,
    ) :
        GGLiveDataResponse()

    data class Entry(
        val data: GGEntry,
        val type: GGLiveDataResponseType = GGLiveDataResponseType.ENTRY,
    ) : GGLiveDataResponse()

    data class Update<T>(
        val data: T,
        val type: GGLiveDataResponseType = GGLiveDataResponseType.UPDATE,
    ) : GGLiveDataResponse()

    data object None : GGLiveDataResponse()
}