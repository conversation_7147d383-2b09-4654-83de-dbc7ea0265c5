package com.dmdbrands.library.ggbluetooth.model

data class GGScaleEntry(
    val bmi: Float,
    val bmr: Int,
    val bodyFat: Float,
    val water: Float,
    val boneMass: Float,
    val metabolicAge: Int,
    val muscleMass: Float,
    val proteinPercent: Float,
    val skeletalMusclePercent: Float,
    val subcutaneousFatPercent: Float,
    override val unit: String,
    val visceralFatLevel: Int,
    val weight: Float,
    val weightInKg: Float,
    override val date: Long,
    val impedance: Float,
    val pulse: Int,
    override val broadcastId: String,
    override val broadcastIdString: String,
    override val protocolType: String = "btWifiR4",
    override val operationType: String? = null
) : GGEntry()
