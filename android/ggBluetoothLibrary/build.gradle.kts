plugins {
    id(libs.plugins.android.library.get().pluginId)
    id(libs.plugins.jetbrains.kotlin.android.get().pluginId)
    id("maven-publish")
}

android {
    namespace = "com.dmdbrands.library.ggbluetooth"
    compileSdk = 34

    defaultConfig {
        minSdk = 24

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }

    packaging {
        resources.excludes += "META-INF/gc_sl_release.kotlin_module"
        resources.pickFirsts += "**/*.so"
    }
    sourceSets {
        getByName("main") {
            jniLibs.srcDirs("libs", "jniLibs")
        }
    }
}

publishing {
    publications {
        create<MavenPublication>("gpr") {
            run {
                groupId = "com.dmdbrands.lib"
                artifactId = "gg-bluetooth-android"
                version = "1.0.0-alpha15"
                artifact("build/outputs/aar/ggBluetoothLibrary-release.aar")
            }
        }
    }
    repositories {
        maven {
            name = "GitHubPackages"
            url =
                uri("https://maven.pkg.github.com/dmdbrands/ggBluetoothNativeLibrary") // Github Package
            credentials {
                username = System.getenv("GPR_USER") ?: "VivekGG"
                password =
                    System.getenv("GPR_API_KEY") ?: "****************************************"
            }
        }
    }
}

dependencies {
    api(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))
    api(libs.gson)
    api(libs.timber)
    api(libs.androidx.activity.ktx)


    // DataStore
    api(libs.androidx.datastore.preferences)
    api(libs.androidx.datastore.preferences.core)

    //Device Location
    api(libs.play.services.location)

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}