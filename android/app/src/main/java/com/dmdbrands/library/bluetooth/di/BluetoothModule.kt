package com.dmdbrands.library.bluetooth.di

import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
class BluetoothModule {
    /*@Provides
    @Singleton
    fun provideGGBluetooth(@ApplicationContext context: Context): GGBluetooth {
        return GGBluetooth(BluetoothContext.ComposeActivity(context.applicationContext as ComponentActivity))
    }*/
}