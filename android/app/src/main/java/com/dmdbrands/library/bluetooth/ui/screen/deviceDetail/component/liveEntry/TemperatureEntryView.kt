package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.liveEntry

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.VolumeMute
import androidx.compose.material.icons.automirrored.filled.VolumeUp
import androidx.compose.material.icons.filled.BluetoothConnected
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.PowerSettingsNew
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.R
import com.dmdbrands.library.bluetooth.data.sample.Unit.TemperatureModeList
import com.dmdbrands.library.bluetooth.data.sample.Unit.TemperatureUnitList
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.HistoryData
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.DeviceDetailViewmodel
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.ViewWithIndicator
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.shared.component.UnitPicker
import com.dmdbrands.library.bluetooth.ui.theme.BackgroundSecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.BorderColor
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.dmdbrands.library.ggbluetooth.enums.GGBTSettingType
import com.example.utilities.modal.Modal
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.theme.PrimaryLightShadeColor

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TemperatureEntryView(
    latestEntry: HistoryData? = null,
    device: BLEDevice?,
    error: String? = null,
    viewModel: DeviceDetailViewmodel = androidx.lifecycle.viewmodel.compose.viewModel()
) {

    val enabled = error == null && latestEntry != null
    Card(
        shape = RoundedCornerShape(
            20.dp
        ),
        colors = CardDefaults.cardColors(containerColor = BackgroundSecondaryColor),
        elevation = CardDefaults.cardElevation(12.dp)
    ) {
        ViewWithIndicator(isLive = false) {
            Card(
                shape = RoundedCornerShape(100.dp),
                elevation = CardDefaults.cardElevation(6.dp),
                colors = CardDefaults.cardColors(containerColor = Color.LightGray)
            ) {
                Column(modifier = Modifier.padding(vertical = 32.dp, horizontal = 40.dp)) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 10.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            painter = painterResource(R.drawable.gg_ic),
                            contentDescription = null,
                            modifier = Modifier.size(32.dp),
                            tint = White
                        )
                    }
                    Column(
                        modifier = Modifier
                            .fillMaxHeight(0.25f)
                            .clip(RoundedCornerShape(20.dp))
                            .background(PrimaryLightShadeColor)
                            .padding(10.dp)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth(),
                            horizontalAlignment = Alignment.Start
                        ) {
                            Icon(imageVector = Icons.Default.Person, contentDescription = null)
                            if (device?.device?.muteMode == true) {
                                Icon(
                                    imageVector = Icons.AutoMirrored.Filled.VolumeMute,
                                    contentDescription = null
                                )
                            } else {
                                Icon(
                                    imageVector = Icons.AutoMirrored.Filled.VolumeUp,
                                    contentDescription = null
                                )
                            }
                        }
                        Box(
                            modifier = Modifier
                                .fillMaxWidth(),
                            contentAlignment = Alignment.Center
                        ) {
                            if (enabled) {
                                Text(
                                    text = latestEntry.getDisplayValue(),
                                    fontSize = 24.sp,
                                    fontFamily = OpenSansFont
                                )
                            } else {
                                Text(
                                    text = "Syncing Data....",
                                    fontSize = 16.sp,
                                    fontFamily = OpenSansFont,
                                )
                            }
                        }
                        Box(
                            modifier = Modifier.fillMaxWidth(),
                            contentAlignment = Alignment.BottomEnd
                        ) {
                            Column {
                                if (latestEntry != null && latestEntry.unit != null) {
                                    Text(
                                        text = latestEntry.unit,
                                        modifier = Modifier.align(Alignment.CenterHorizontally),
                                        fontSize = 12.sp,
                                        fontFamily = OpenSansFont
                                    )
                                }
                                Icon(
                                    imageVector = Icons.Default.BluetoothConnected,
                                    contentDescription = null
                                )
                            }
                        }
                    }
                    Spacer(modifier = Modifier.height(20.dp))
                }
            }
            Spacer(modifier = Modifier.height(20.dp))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                AppButton(
                    cardModifier = Modifier.weight(1f), onClick = {
                        viewModel.setModal(
                            Modal(
                                onDismiss = {
                                    viewModel.setModal(null)
                                },
                                content = {
                                    UnitPicker(
                                        value = latestEntry?.unit,
                                        unitList = TemperatureUnitList
                                    ) {
                                        viewModel.changeUnit(
                                            it,
                                            key = GGBTSettingType.TEMPERATURE_UNIT
                                        )
                                        viewModel.setModal(null)
                                    }
                                }
                            )
                        )
                    },
                    shape = RectangleShape,
                    enabled = enabled,
                    containerColor = BackgroundSecondaryColor,
                    border = BorderStroke(1.dp, BorderColor)
                ) {
                    Text(
                        text = "Change Type",
                        color = TextPrimaryColor,
                        fontFamily = OpenSansFont,
                        fontSize = 10.sp,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
                AppButton(
                    cardModifier = Modifier.weight(1f), onClick = {
                        viewModel.setModal(
                            Modal(
                                onDismiss = {
                                    viewModel.setModal(null)
                                },
                                content = {
                                    UnitPicker(unitList = TemperatureModeList) {
                                        viewModel.changeMode(it)
                                        viewModel.setModal(null)
                                    }
                                }
                            )
                        )
                    },
                    shape = RectangleShape,
                    enabled = enabled,
                    containerColor = BackgroundSecondaryColor,
                    border = BorderStroke(1.dp, BorderColor)
                ) {
                    Text(
                        text = "Change Mode",
                        color = TextPrimaryColor,
                        fontFamily = OpenSansFont,
                        fontSize = 10.sp,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            Spacer(modifier = Modifier.height(40.dp))
            AppButton(
                onClick = {
                    viewModel.startLiveMeasurement(device = device!!)
                },
                shape = CircleShape,
                containerColor = PrimaryLightShadeColor,
                border = BorderStroke(1.dp, BorderColor)
            ) {
                Icon(
                    imageVector = Icons.Default.PowerSettingsNew,
                    contentDescription = null,
                    modifier = Modifier.size(50.dp),
                    tint = TextPrimaryColor
                )
            }
        }
        Spacer(modifier = Modifier.height(40.dp))
    }
}

@PreviewScreens
@Composable
private fun TemperatureEntryViewPreview() {
    GGBluetoothLibraryTheme {
        TemperatureEntryView(latestEntry = null, device = null)
    }
}
