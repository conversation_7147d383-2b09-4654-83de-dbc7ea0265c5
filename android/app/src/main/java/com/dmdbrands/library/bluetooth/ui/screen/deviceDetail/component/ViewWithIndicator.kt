package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.dmdbrands.library.bluetooth.ui.shared.component.LiveIndicator

@Composable
fun ViewWithIndicator(
    isLive: Boolean,
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth(0.75f)
            .padding(10.dp)
            .then(modifier),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        LiveIndicator(isLive = isLive, modifier = Modifier.align(Alignment.End))
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            content()
        }
    }
}