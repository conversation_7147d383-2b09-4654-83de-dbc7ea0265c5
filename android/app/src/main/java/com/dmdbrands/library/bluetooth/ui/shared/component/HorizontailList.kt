package com.dmdbrands.library.bluetooth.ui.shared.component

import androidx.compose.foundation.gestures.ScrollableDefaults
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@Composable

fun <T> HorizontalList(
    data: List<T>,
    state: LazyListState = rememberLazyListState(),
    modifier: Modifier = Modifier,
    userScrollEnabled: Boolean = true,
    content: @Composable (data: T) -> Unit,
) {
    LazyRow(
        modifier = modifier
            .wrapContentHeight()
            .fillMaxWidth(),
        userScrollEnabled = userScrollEnabled,
        state = state,
        horizontalArrangement = Arrangement.SpaceEvenly,
        flingBehavior = ScrollableDefaults.flingBehavior()
    ) {
        items(data) { data ->
            content(data)
        }
    }
}

