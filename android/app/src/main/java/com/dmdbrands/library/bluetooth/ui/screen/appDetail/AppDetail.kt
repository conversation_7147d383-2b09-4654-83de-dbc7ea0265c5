package com.dmdbrands.library.bluetooth.ui.screen.appDetail

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.rememberNavController
import com.dmdbrands.library.bluetooth.config.AppRoute
import com.dmdbrands.library.bluetooth.model.AppDetailBottomNavItem
import com.dmdbrands.library.bluetooth.ui.screen.appDetail.component.AppDetailNavGraph
import com.dmdbrands.library.bluetooth.ui.screen.home.component.BottomNavigation
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.shared.component.NavigationObserver
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.SurfaceBackgroundColor
import com.dmdbrands.library.ggbluetooth.enums.GGAppType

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppDetail(appType: String) {
    val navController = rememberNavController()
    val viewmodel = hiltViewModel<AppDetailViewmodel, AppDetailViewmodel.Factory> { factory ->
        factory.create(appType)
    }
    val state by viewmodel.state.collectAsState()
    var title by remember { mutableStateOf("") }
    NavigationObserver(
        viewmodel.appUtility.navigationIntent,
        navController,
        AppRoute.AppDetail
    )
    val bottomNavItem = if (appType == GGAppType.SMART_BABY)
        listOf(
            AppDetailBottomNavItem.History,
            AppDetailBottomNavItem.Settings
        )
    else {
        listOf(
            AppDetailBottomNavItem.Devices,
            AppDetailBottomNavItem.History,
            AppDetailBottomNavItem.Settings
        )
    }
    Scaffold(bottomBar = {
        BottomNavigation(
            bottomNavItems = bottomNavItem, navController
        )
    }) {
        LaunchedEffect(Unit) {
            viewmodel.setFabPadding(it.calculateBottomPadding() - 16.dp)
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(SurfaceBackgroundColor)
                .padding(bottom = it.calculateBottomPadding())
        ) {
            AppDetailNavGraph(
                appType,
                state = state,
                navController,
                startDestination = if (appType == GGAppType.SMART_BABY) AppRoute.AppHistory else AppRoute.AppDevices,
                onSelected = {
                    viewmodel.handleIntent(AppDetailIntent.SetSubUser(it))
                }
            ) {
                title = it
            }
        }
    }
}


@PreviewScreens
@Composable
private fun PairedDevicesViewPreview() {
    GGBluetoothLibraryTheme {
        AppRoute.AppDetail(GGAppType.BALANCE_HEALTH)
    }
}



