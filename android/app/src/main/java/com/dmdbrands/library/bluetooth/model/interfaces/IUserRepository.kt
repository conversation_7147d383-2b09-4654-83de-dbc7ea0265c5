package com.dmdbrands.library.bluetooth.model.interfaces

import com.dmdbrands.library.bluetooth.data.db.entity.UserEntity
import com.dmdbrands.library.bluetooth.model.AppStatus
import com.dmdbrands.library.bluetooth.model.api.LoginRequest
import com.dmdbrands.library.bluetooth.model.api.LoginResponse
import com.dmdbrands.library.bluetooth.model.api.ScaleTokenResponse
import com.dmdbrands.library.bluetooth.model.api.SubUser
import com.dmdbrands.library.bluetooth.model.api.User
import kotlinx.coroutines.flow.Flow
import retrofit2.Response

interface IUserRepository {
    suspend fun login(loginRequest: LoginRequest, appType: String): Response<out LoginResponse>?
    suspend fun logout(appType: String)
    suspend fun subscribeUsers(): Flow<List<UserEntity>>
    suspend fun subscribeAppStatus(): Flow<List<AppStatus>>
    suspend fun getUserDetails(appType: String): Response<out User>?
    suspend fun getSubUserDetails(appType: String): Response<out List<SubUser>>?
    suspend fun setSubUser(subUser: SubUser, appType: String)
    suspend fun getUser(appType: String): UserEntity?
    suspend fun getScaleToken(appType: String): Response<ScaleTokenResponse>?
    val users: List<UserEntity>?
    val appStatus: List<AppStatus>?
}