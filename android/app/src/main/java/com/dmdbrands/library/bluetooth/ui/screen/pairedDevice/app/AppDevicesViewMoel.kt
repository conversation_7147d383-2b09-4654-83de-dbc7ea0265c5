package com.dmdbrands.library.bluetooth.ui.screen.pairedDevice.app

import androidx.lifecycle.viewModelScope
import com.dmdbrands.library.bluetooth.model.interfaces.IDeviceRepository
import com.dmdbrands.library.bluetooth.ui.shared.DeviceIntent
import com.dmdbrands.library.bluetooth.ui.shared.ScanDeviceState
import com.example.utilities.services.viewmodel.BaseViewModel
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch

@HiltViewModel(assistedFactory = AppDevicesViewModel.AppDeviceViewModelFactory::class)

class AppDevicesViewModel @AssistedInject constructor(
    @Assisted private val appType: String,
    private val deviceRepository: IDeviceRepository,
) : BaseViewModel<ScanDeviceState, DeviceIntent>(
    initialState = ScanDeviceState(),
    reducer = AppDeviceReducer()
) {

    @AssistedFactory
    interface AppDeviceViewModelFactory {
        fun create(id: String): AppDevicesViewModel
    }

    init {
        loadDevices()
    }

    fun loadDevices() {
        handleIntent(DeviceIntent.AlterLocalRefreshState(true))
        viewModelScope.launch {
            val devices = deviceRepository.getAppDevices(appType)
            handleIntent(DeviceIntent.AddDevice(devices))
            handleIntent(DeviceIntent.AlterLocalRefreshState(false))
        }

    }

    override fun handleIntent(intent: DeviceIntent) {
        super.handleIntent(intent)
    }
}