package com.dmdbrands.library.bluetooth.data.db

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.dmdbrands.library.bluetooth.data.db.dao.DeviceDao
import com.dmdbrands.library.bluetooth.data.db.dao.HistoryDao
import com.dmdbrands.library.bluetooth.data.db.dao.UserDao
import com.dmdbrands.library.bluetooth.data.db.entity.DeviceEntity
import com.dmdbrands.library.bluetooth.data.db.entity.HistoryEntity
import com.dmdbrands.library.bluetooth.data.db.entity.PreferencesEntity
import com.dmdbrands.library.bluetooth.data.db.entity.UserEntity
import com.dmdbrands.library.bluetooth.service.Converters

@Database(
    entities = [DeviceEntity::class, UserEntity::class, HistoryEntity::class, PreferencesEntity::class],
    version = 4,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class AppDatabase : RoomDatabase() {
    abstract fun deviceDao(): DeviceDao
    abstract fun userDao(): UserDao
    abstract fun historyDao(): HistoryDao


    companion object {
        /*The value of a volatile variable will never be cached, and all writes and reads will be done to and from the main memory.
        This helps make sure the value of INSTANCE is always up-to-date and the same for all execution threads.
        It means that changes made by one thread to INSTANCE are visible to all other threads immediately.*/
        @Volatile
        private var INSTANCE: AppDatabase? = null

        fun getInstance(context: Context): AppDatabase {
            // only one thread of execution at a time can enter this block of code
            synchronized(this) {
                var instance = INSTANCE

                if (instance == null) {
                    instance = Room.databaseBuilder(
                        context.applicationContext,
                        AppDatabase::class.java,
                        "TestApp"
                    ).fallbackToDestructiveMigration().build()

                    INSTANCE = instance
                }
                return instance
            }
        }
    }
}