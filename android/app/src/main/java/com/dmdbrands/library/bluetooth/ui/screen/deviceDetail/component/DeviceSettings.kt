package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Restore
import androidx.compose.material.icons.filled.Upgrade
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.data.sample.Unit.WeightGurusWeightUnitList
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.DetailItem
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.DeviceDetailReducer
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.shared.component.DetailContainer
import com.dmdbrands.library.bluetooth.ui.shared.component.UnitPicker
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.ggbluetooth.enums.ClearDataType
import com.dmdbrands.library.ggbluetooth.enums.GGBTSettingType
import com.dmdbrands.library.ggbluetooth.enums.TimeFormat
import com.dmdbrands.library.ggbluetooth.model.GGBTSetting
import com.dmdbrands.library.ggbluetooth.model.GGBTSettingValue
import com.dmdbrands.library.ggbluetooth.model.GGBTUser
import com.example.utilities.modal.Modal
import com.example.utilities.modal.PickerItem
import com.example.utilities.services.component.picker.PickerPopup
import com.example.utilities.services.component.picker.PickerSelectionMode
import com.example.utilities.util.CalendarUtil

@Composable
fun DeviceSettings(
    device: BLEDevice?,
    userList: List<GGBTUser> = listOf(),
    handleIntent: (DeviceDetailReducer.Intent) -> Unit = {},
    showCustom: (Modal?) -> Unit,
    showDialog: (
        message: String,
        confirmText: String,
        loaderMessage: String?,
        onConfirm: suspend () -> Unit
    ) -> Unit = { _, _, _, _ -> }
) {
    val timeFormats = listOf(
        PickerItem("12H", TimeFormat.TWELVE),
        PickerItem("24H", TimeFormat.TWENTY_FOUR)
    )
    val clearDataOptions = enumValues<ClearDataType>().map {
        PickerItem(it.name.replace("_", " ").lowercase().replaceFirstChar(Char::uppercase), it)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {

        // Preferences
        DetailContainer(
            "Preferences", listOf(
                pickerItem(
                    title = "Unit",
                    value = device?.device?.unit,
                    icon = Icons.Default.ArrowDropDown
                ) {
                    showCustom(Modal {
                        UnitPicker(device?.device?.unit, WeightGurusWeightUnitList) {
                            handleIntent(
                                DeviceDetailReducer.Intent.updateSettings(
                                    GGBTSetting(GGBTSettingType.UNIT, GGBTSettingValue.String(it))
                                )
                            )
                        }
                    })
                },
                pickerItem(
                    title = "Time Format",
                    value = if (device?.preferences?.timeFormat == TimeFormat.TWENTY_FOUR) "24H" else "12H",
                    icon = Icons.Default.ArrowDropDown
                ) {
                    showCustom(Modal {
                        PickerPopup(
                            label = "Time Format",
                            items = listOf(timeFormats),
                            value = listOf(timeFormats.find { it.value == device?.preferences?.timeFormat }
                                ?: timeFormats.first()),
                            buttonText = AppLang.SAVE,
                            key = PickerItem<String>::label
                        ) {
                            handleIntent(
                                DeviceDetailReducer.Intent.updateSettings(
                                    GGBTSetting(
                                        GGBTSettingType.TIME_FORMAT,
                                        GGBTSettingValue.Boolean(it.first().value == TimeFormat.TWELVE)
                                    )
                                )
                            )
                        }
                    })
                }
            ))

        // Animations
        DetailContainer(
            "Animations", listOf(
                switchItem("Start Animation", device?.device?.startAnimationState == true) {
                    handleIntent(
                        DeviceDetailReducer.Intent.updateSettings(
                            GGBTSetting(
                                GGBTSettingType.INITIAL_LOGO_ANIM,
                                GGBTSettingValue.Boolean(it)
                            )
                        )
                    )
                },
                switchItem("End Animation", device?.device?.endAnimationState == true) {
                    handleIntent(
                        DeviceDetailReducer.Intent.updateSettings(
                            GGBTSetting(
                                GGBTSettingType.FINAL_LOGO_ANIM,
                                GGBTSettingValue.Boolean(it)
                            )
                        )
                    )
                }
            ))

        // Data
        val bodyMetricsEnabled =
            userList.find { it.token == device?.token }?.isBodyMetricsEnabled == true
        val impedanceEnabled = device?.device?.impedanceSwitchState == false

        DetailContainer(
            "Data", listOf(
                pickerItem("Clear Data", icon = Icons.Default.ChevronRight) {
                    showCustom(Modal {
                        PickerPopup(
                            label = "Clear Data",
                            items = listOf(clearDataOptions),
                            buttonText = AppLang.SELECT,
                            selectionMode = PickerSelectionMode.RADIO,
                            key = PickerItem<ClearDataType>::label
                        ) {
                            handleIntent(DeviceDetailReducer.Intent.clearData(it.first().value))
                        }
                    })
                },
                switchItem(
                    "Session Impedance",
                    checked = device?.device?.sessionImpedanceSwitchState == true && bodyMetricsEnabled && impedanceEnabled,
                    enabled = bodyMetricsEnabled && impedanceEnabled
                ) {
                    handleIntent(
                        DeviceDetailReducer.Intent.updateSettings(
                            GGBTSetting(
                                GGBTSettingType.SESSION_IMPEDANCE,
                                GGBTSettingValue.Boolean(it)
                            )
                        )
                    )
                }
            ))

        // Firmware
        DetailContainer(
            "Firmware", listOf(
                pickerItem("Start Firmware Upgrade", icon = Icons.Default.Upgrade) {
                    showDialog(
                        "Are you sure you want to start the firmware upgrade?",
                        "START", "Starting upgrade..."
                    ) {
                        handleIntent(DeviceDetailReducer.Intent.startUpgrade(CalendarUtil.getCurrentDate().timeInMillis))
                    }
                },
                pickerItem("Restore Firmware", icon = Icons.Default.Restore) {
                    showDialog(
                        "Are you sure you want to restore the firmware?",
                        "RESTORE",
                        "Restoring firmware..."
                    ) {
                        handleIntent(
                            DeviceDetailReducer.Intent.updateSettings(
                                GGBTSetting(
                                    GGBTSettingType.RESET_FIRMWARE,
                                    GGBTSettingValue.Boolean(true)
                                )
                            )
                        )
                    }
                },
                pickerItem("Restore Factory Settings", icon = Icons.Default.Restore) {
                    showDialog(
                        "Are you sure you want to restore factory settings?",
                        "RESTORE",
                        "Restoring factory settings..."
                    ) {
                        handleIntent(
                            DeviceDetailReducer.Intent.updateSettings(
                                GGBTSetting(
                                    GGBTSettingType.RESTORE_FACTORY,
                                    GGBTSettingValue.Boolean(true)
                                )
                            )
                        )
                    }
                }
            ))
    }
}

@Composable
fun switchItem(
    title: String,
    checked: Boolean,
    enabled: Boolean = true,
    onChecked: (Boolean) -> Unit
) = DetailItem(title, action = {
    Switch(
        checked = checked,
        enabled = enabled,
        modifier = Modifier
            .size(24.dp)
            .padding(end = 24.dp),
        colors = SwitchDefaults.colors(checkedTrackColor = PrimaryColor),
        onCheckedChange = onChecked
    )
})

fun pickerItem(
    title: String,
    value: String? = null,
    icon: ImageVector? = null,
    onClick: () -> Unit
) =
    DetailItem(title, value = value, imageVector = icon, onClick = onClick)

@PreviewScreens
@Composable
private fun DeviceSettingsPreview() {
    GGBluetoothLibraryTheme {
        DeviceSettings(null, listOf(), {}, {})
    }
}