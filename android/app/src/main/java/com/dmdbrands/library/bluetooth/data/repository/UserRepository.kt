package com.dmdbrands.library.bluetooth.data.repository

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import com.dmdbrands.library.bluetooth.config.AppConfig
import com.dmdbrands.library.bluetooth.data.api.IUserAPI
import com.dmdbrands.library.bluetooth.data.db.dao.DeviceDao
import com.dmdbrands.library.bluetooth.data.db.dao.UserDao
import com.dmdbrands.library.bluetooth.data.db.entity.UserEntity
import com.dmdbrands.library.bluetooth.model.AppStatus
import com.dmdbrands.library.bluetooth.model.api.LoginRequest
import com.dmdbrands.library.bluetooth.model.api.LoginResponse
import com.dmdbrands.library.bluetooth.model.api.ScaleTokenResponse
import com.dmdbrands.library.bluetooth.model.api.SubUser
import com.dmdbrands.library.bluetooth.model.api.User
import com.dmdbrands.library.bluetooth.model.interfaces.IAppConfiguration
import com.dmdbrands.library.bluetooth.model.interfaces.IDeviceRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IUserRepository
import com.dmdbrands.library.bluetooth.service.Converters
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.example.utilities.util.CalendarUtil
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import retrofit2.Response
import javax.inject.Inject

class UserRepository @Inject constructor(
    private val userAPI: IUserAPI,
    private val userDao: UserDao,
    private val deviceDao: DeviceDao,
    private val deviceRepository: IDeviceRepository,
    private val appConfiguration: IAppConfiguration
) : IUserRepository {

    private val _users = MutableLiveData<List<UserEntity>>(listOf())
    override val users: List<UserEntity>?
        get() = _users.value
    private val _appStatus =
        MutableLiveData<List<AppStatus>>(
            AppConfig.getAppsWithServer()
                .map { AppStatus(it) })

    override val appStatus: List<AppStatus>?
        get() = _appStatus.value


    override suspend fun login(
        loginRequest: LoginRequest,
        appType: String
    ): Response<out LoginResponse>? {  // Removed nullable return type
        val response = when (appType) {
            GGAppType.BALANCE_HEALTH -> userAPI.bhLogin(loginRequest)
            GGAppType.WEIGHT_GURUS -> userAPI.wgLogin(loginRequest)
            GGAppType.SMART_BABY -> userAPI.sbLogin(loginRequest)
            else -> null
        }
        response?.body()?.let { body ->
            if (response.isSuccessful) {
                withContext(IO) {
                    val token = body.token
                    if (!userDao.checkUserAvailable(token)) {
                        userDao.insertUser(
                            body.user?.convertToUserEntity(
                                token,
                                appType
                            ) ?: body.convertToUser()?.convertToUserEntity(
                                token,
                                appType
                            )!!
                        )
                    }
                }
                fetchUsers()
                fetchAppStatus()
                updateDeviceLinkStatus(appType, false)
            }
        }
        return response
    }

    override suspend fun getSubUserDetails(appType: String): Response<out List<SubUser>>? {
        appConfiguration.setAppType(appType)
        return when (appType) {
            GGAppType.SMART_BABY -> userAPI.getSBSubUser()
            else -> null
        }
    }

    override suspend fun setSubUser(subUser: SubUser, appType: String) {
        withContext(IO) {
            val user = userDao.getUserByAppType(appType)
            val convertedCategory = Converters().fromPair(Pair(true, subUser.id))!!
            userDao.setUserCategory(user!!.id, convertedCategory)
        }
    }

    override suspend fun getUser(appType: String): UserEntity? {
        return withContext(IO) {
            userDao.getUserByAppType(appType)
        }
    }

    override suspend fun logout(appType: String) {
        withContext(IO) {
            val user = userDao.getUserByAppType(appType)
            if (user != null) {
                userDao.deleteUserById(user.id)
                deviceDao.updatePrimaryTokenByAppType(user.appType, null)
            }
        }
        fetchUsers()
        fetchAppStatus()
        updateDeviceLinkStatus(appType, null)
    }

    override suspend fun subscribeUsers(): Flow<List<UserEntity>> {
        fetchUsers()
        return _users.asFlow()
    }

    override suspend fun subscribeAppStatus(): Flow<List<AppStatus>> {
        fetchAppStatus()
        return _appStatus.asFlow()
    }

    override suspend fun getUserDetails(appType: String): Response<out User>? {
        appConfiguration.setAppType(appType)
        val response = when (appType) {
            GGAppType.BALANCE_HEALTH -> {
                val apiResponse = userAPI.getBHDetails()
                Response.success(apiResponse.body()?.user)
            }

            GGAppType.WEIGHT_GURUS -> {
                val apiResponse = userAPI.getWGDetails()
                Response.success(apiResponse.body())
            }

            GGAppType.SMART_BABY -> {
                val apiResponse = userAPI.getSBDetails()
                val babyResponse = userAPI.getSBSubUser()
                val baby = babyResponse.body()!!.first()
                val calender = CalendarUtil.timeStringToCalender(baby.birthdate)

                Response.success(
                    apiResponse.body()
                        ?.copy(
                            dob = CalendarUtil.calendarToString(calender),
                            gender = baby.sex,
                            lastName = "",
                            firstName = baby.name,
                            id = baby.id,
                        )
                )
            }

            else -> null
        }
        return response
    }

    override suspend fun getScaleToken(appType: String): Response<ScaleTokenResponse>? {
        appConfiguration.setAppType(appType)
        val response = when (appType) {
            GGAppType.WEIGHT_GURUS -> userAPI.getScaleToken()
            else -> null
        }
        return response
    }

    private suspend fun fetchUsers() {
        _users.value = withContext(IO) {
            userDao.getAllUsers()
        }
    }

    private suspend fun fetchAppStatus() {
        _appStatus.value = withContext(IO) {
            _appStatus.value!!.map {
                it.copy(token = userDao.getUserByAppType(it.type)?.token)
            }
        }
    }

    private suspend fun updateDeviceLinkStatus(appType: String, status: Boolean?) {
        deviceRepository.updateLinkStatusBasedOnAppType(appType, status)
    }

}