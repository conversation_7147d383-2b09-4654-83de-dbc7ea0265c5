package com.dmdbrands.library.bluetooth.model.interfaces

import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.DeviceAction
import com.dmdbrands.library.ggbluetooth.model.GGBTUser
import kotlinx.coroutines.flow.Flow
import retrofit2.Response

interface IDeviceRepository {
    suspend fun subscribeNewDevices(): Flow<List<BLEDevice>>
    suspend fun subscribePairedDevices(): Flow<List<BLEDevice>>
    suspend fun subscribeRemoteDevices(): Flow<List<BLEDevice>>
    suspend fun subscribeAllPairedDevices(): Flow<List<BLEDevice>>
    suspend fun fetchDevices()
    suspend fun checkDeviceAvailable(device: BLEDevice): Boolean
    fun getDevice(broadCastId: String): BLEDevice?
    suspend fun subscribeDevice(broadCastId: String): Flow<BLEDevice?>
    suspend fun onDeviceAction(device: BLEDevice, action: DeviceAction)
    suspend fun updateToken(device: BLEDevice, user: GGBTU<PERSON>?, isRefresh: Boolean = false)
    suspend fun refresh()
    suspend fun onPostConnect(device: BLEDevice?)
    suspend fun updatePreference(device: BLEDevice)
    suspend fun removeToken(device: BLEDevice, isRemote: Boolean = false)

    suspend fun getAppDevices(appType: String): List<BLEDevice>
    suspend fun addDeviceToRemote(appType: String, device: BLEDevice): Response<Boolean>
    suspend fun updateLinkStatusBasedOnAppType(appType: String, isLinked: Boolean?)
    suspend fun deleteRemoteDevice(appType: String, device: BLEDevice): Response<Boolean>

}