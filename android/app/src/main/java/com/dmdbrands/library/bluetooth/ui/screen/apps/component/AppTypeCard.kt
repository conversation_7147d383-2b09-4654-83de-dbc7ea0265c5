package com.dmdbrands.library.bluetooth.ui.screen.apps.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.model.AppStatus
import com.dmdbrands.library.bluetooth.ui.theme.BackgroundSecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.TextSecondaryColor
import com.dmdbrands.library.bluetooth.util.DeviceUtil.getAppIcon
import com.example.utilities.util.camelCase
import com.example.utilities.util.onlyString


@Composable
fun AppTypeCard(app: AppStatus, onCLick: () -> Unit) {
    Row(
        modifier = Modifier
            .clickable {
                onCLick()
            }
            .background(BackgroundSecondaryColor)
            .padding(16.dp)
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(10.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(getAppIcon(app.type)),
            contentDescription = "app icon",
            modifier = Modifier.size(36.dp)
        )
        Text(
            text = app.type.onlyString().camelCase(),
            fontFamily = OpenSansFont,
            fontSize = 22.sp,
            fontWeight = FontWeight.Bold,
            color = TextPrimaryColor,
            modifier = Modifier
                .weight(1f)
        )
        
        Column(
            modifier = Modifier.fillMaxHeight(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Bottom
        ) {
            Text(
                text = if (app.token != null) "Logged in" else "Not logged in",
                fontFamily = OpenSansFont,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = TextSecondaryColor,
            )
        }
    }
}


