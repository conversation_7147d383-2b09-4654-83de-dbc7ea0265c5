package com.dmdbrands.library.bluetooth.service

import android.util.Log
import com.dmdbrands.library.bluetooth.model.interfaces.IAppConfiguration
import okhttp3.Interceptor
import okhttp3.Response
import okio.IOException
import javax.inject.Inject

class BaseUrlInterceptor @Inject constructor(private val appConfiguration: IAppConfiguration) :
    Interceptor {

    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()

        val originalUrl = originalRequest.url
        val originalHost = originalUrl.host
        val originalPath = originalUrl.encodedPath

        val newBaseUrl = appConfiguration.getBaseUrl()
        val newPath = appConfiguration.getApiVersionPath()

        Log.d("BaseUrlInterceptor", "Original URL: $originalUrl")
        Log.d("BaseUrlInterceptor", "New Base URL: $newBaseUrl, New Path: $newPath")

        if (originalHost != newBaseUrl || originalPath != newPath) {
            val newUrl = originalUrl.newBuilder()
                .host(newBaseUrl)
                .encodedPath(newPath + originalPath.substring(1))
                .build()

            Log.d("BaseUrlInterceptor", "New Request URL: $newUrl")

            val newRequest = originalRequest.newBuilder()
                .url(newUrl)
                .build()

            return chain.proceed(newRequest)
        }

        return chain.proceed(originalRequest)
    }
}
