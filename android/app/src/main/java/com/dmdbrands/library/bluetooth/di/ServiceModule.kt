package com.dmdbrands.library.bluetooth.di

import com.example.bluetoothwrapper.service.GGBLEService
import com.example.bluetoothwrapper.service.GGDeviceService
import com.example.bluetoothwrapper.service.GGPermissionService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class ServiceModule {

    @Provides
    @Singleton
    fun provideBLEService(): GGBLEService {
        return GGBLEService()
    }
    
    @Provides
    @Singleton
    fun providePermissionService(bleService: GGBLEService): GGPermissionService {
        return GGPermissionService(bleService)
    }

    @Provides
    @Singleton
    fun provideDeviceService(bleService: GGBLEService): GGDeviceService {
        return GGDeviceService(bleService)
    }


}