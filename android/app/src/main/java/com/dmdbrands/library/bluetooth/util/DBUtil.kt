package com.dmdbrands.library.bluetooth.util

import com.dmdbrands.library.bluetooth.data.db.entity.DeviceEntity
import com.dmdbrands.library.bluetooth.data.db.entity.PreferencesEntity
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.api.Preferences
import com.dmdbrands.library.ggbluetooth.model.GGDeviceDetail
import kotlin.random.Random

object DBUtil {

    fun PreferencesEntity.convertToPreferences(): Preferences {
        return Preferences(
            id = this.id,
            tzOffset = this.tzOffset,
            timeFormat = this.timeFormat,
            displayName = this.displayName,
            displayMetrics = this.displayMetrics,
            shouldMeasurePulse = this.shouldMeasurePulse,
            shouldMeasureImpedance = this.shouldMeasureImpedance,
            shouldFactoryReset = this.shouldFactoryReset,
            wifiFotaScheduleTime = this.wifiFotaScheduleTime
        )
    }

    fun DeviceEntity.convertBleDevice(preferences: Preferences? = null): BLEDevice {
        return BLEDevice(
            device = GGDeviceDetail(
                systemID = this.systemId,
                deviceName = this.deviceName,
                broadcastId = this.broadcastId,
                identifier = this.identifier,
                macAddress = this.macAddress,
                password = this.password
            ),
            alreadyPaired = true,
            userNumber = this.userNumber,
            createdAt = this.createdAt,
            isLinked = this.isLinked,
            token = this.token,
            primaryToken = this.primaryToken,
            preferences = preferences
        )
    }

    fun BLEDevice.convertDeviceEntity(): DeviceEntity {
        return DeviceEntity(
            deviceName = this.device.deviceName,
            broadcastId = this.device.broadcastId ?: Random.nextInt().toString(),
            identifier = this.device.identifier,
            macAddress = this.device.macAddress,
            appType = this.getAppType(),
            userNumber = this.userNumber,
            password = this.device.password.toString(),
            createdAt = this.createdAt,
            isLinked = this.isLinked,
            systemId = this.device.systemID,
            primaryToken = this.primaryToken,
            token = this.token,
        )
    }

    fun BLEDevice.convertToPreferenceEntity(): PreferencesEntity {
        return PreferencesEntity(
            broadcastId = this.device.broadcastId ?: Random.nextInt().toString(),
            id = this.preferences?.id ?: Random.nextLong(),
            tzOffset = preferences?.tzOffset,
            timeFormat = preferences?.timeFormat,
            displayName = preferences?.displayName,
            displayMetrics = preferences?.displayMetrics,
            shouldMeasurePulse = preferences?.shouldMeasurePulse,
            shouldMeasureImpedance = preferences?.shouldMeasureImpedance,
            shouldFactoryReset = preferences?.shouldFactoryReset,
            wifiFotaScheduleTime = preferences?.wifiFotaScheduleTime,
        )

    }
}