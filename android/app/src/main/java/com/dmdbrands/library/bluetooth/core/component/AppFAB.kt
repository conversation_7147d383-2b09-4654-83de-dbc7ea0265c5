package com.dmdbrands.library.bluetooth.core.component

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.BluetoothSearching
import androidx.compose.material.icons.filled.Block
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.dmdbrands.library.bluetooth.ui.theme.DisabledIconColor
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.example.utilities.services.component.animation.RadarAnimation
import com.example.utilities.services.theme.HiddenBackgroundColor
import kotlinx.coroutines.flow.Flow

@Composable
fun AppFab(
    paddingFlow: Flow<Dp>,
    isScanning: Boolean,
    enabled: Boolean,
    onClick: () -> Unit
) {
    var bottomPadding by remember { mutableStateOf(0.dp) }
    LaunchedEffect(Unit) {
        paddingFlow.collect {
            bottomPadding = it
        }
    }
    val color =
        if (isScanning == false && enabled) PrimaryColor else if (isScanning == true && enabled) HiddenBackgroundColor else DisabledIconColor

    FloatingActionButton(
        onClick = onClick,
        containerColor = color,
        modifier = Modifier.padding(bottom = bottomPadding),
    ) {
        if (isScanning == true && enabled) {
            RadarAnimation(
                icon = Icons.Filled.Stop,
            )
        } else {
            val icon =
                if (isScanning == false && enabled) Icons.AutoMirrored.Filled.BluetoothSearching else Icons.Filled.Block
            Icon(
                imageVector = icon,
                contentDescription = "scan",
                tint = White,
                modifier = Modifier.size(24.dp)
            )
        }
    }
}