package com.dmdbrands.library.bluetooth.config.assests

import com.dmdbrands.library.bluetooth.ui.shared.component.GGErrorCode

object GGErrorCodes {
    val WgA3ScaleErrors = listOf(
        GGErrorCode(
            code = "Lo or dim display",
            title = "LOW BATTERY",
            description = "When you see this symbol, the batteries need to be replaced. Open the battery door on the back of the scale, and insert 4 new AAA batteries."
        ),
        GGErrorCode(
            code = "----",
            title = "OVERLOAD",
            description = "There is too much weight on the scale (Capacity: 400lb / 180kg). Please remove the weight on the scale to protect the scale sensors."
        ),
        GGErrorCode(
            code = "Err",
            title = "MEASURING ERROR",
            description = "If the scale has trouble measuring your weight, it will show 'Err' and turn off. This usually happens if you don’t settle your feet on the scale fast enough. Make sure that the scale is placed on a flat, hard surface (not carpet), and you’re distributing your weight evenly while holding still."
        ),
        GGErrorCode(
            code = "E1",
            title = "PAIRING FAILURE",
            description = "Your scale timed out while attempting to pair with the app. Try again, and contact us if you get the same error. (There are many potential causes, so it’s usually easier to troubleshoot in person.)"
        ),
        GGErrorCode(
            code = "Your scale only shows weight data, and then turns off after a few seconds.",
            title = "COULDN’T CALCULATE BODY COMPOSITION",
            description = "Make sure you’re standing on the scale with bare feet (no socks), and you’ve properly set up a user profile on the scale. If you have, manually select your user number before weighing in again by waking your scale up, using the arrows to select your user number, tapping SEL, and then stepping on to weigh. Your scale shows U2, U3, etc., instead of calculating your body composition."
        ),
        GGErrorCode(
            code = "Your scale shows U2, U3, etc., instead of calculating your body composition.",
            title = "AUTO-DETECTION CONFLICT",
            description = "If two or more users are within 10lbs of each other, an auto-detection conflict will occur. There are two possible solutions: Select your user profile manually by waking the scale up, using the arrows to select your profile, and tapping SEL before stepping on the scale. The scale will now proceed measuring for the profile you have selected. Alternatively, delete the conflicting user profile by following the instructions above."
        )
    )

    val WgA6ScaleErrors = listOf(
        GGErrorCode(
            code = "Lo or dim display",
            title = "LOW BATTERY",
            description = "When you see this symbol, the batteries need to be replaced. Open the battery door on the back of the scale, and insert 4 new AAA batteries."
        ),
        GGErrorCode(
            code = "----",
            title = "OVERLOAD",
            description = "There is too much weight on the scale (Capacity: 400lb / 180kg). Please remove the weight on the scale to protect the scale sensors."
        ),
        GGErrorCode(
            code = "Battery full",
            title = "MEASUREMENT SUCCESSFUL",
            description = "Your scale successfully took a measurement. As soon as it completes syncing to your device, this icon will disappear. If the icon remains on the screen until your scale turns off, your measurement failed to sync."
        ),
        GGErrorCode(
            code = "Bluetooth symbol",
            title = "BLUETOOTH IS CONNECTED",
            description = "Your scale is connected to your device's Bluetooth and is attempting to sync your measurement."
        )
    )

    val R4ScaleErrors = listOf(
        // Router errors
        GGErrorCode(code = "E0000", description = "No error"),
        GGErrorCode(
            code = "E1001",
            title = "WIFI_REASON_NO_AP_FOUND",
            description = "The hotspot connected to the device does not exist"
        ),
        GGErrorCode(
            code = "E1002",
            title = "WIFI_REASON_AUTH_FAIL",
            description = "The hotspot password of the device connection may be incorrect"
        ),
        GGErrorCode(
            code = "E1003",
            title = "WIFI_REASON_ASSOC_FAIL",
            description = "The hotspot password of the device connection may be incorrect"
        ),
        GGErrorCode(
            code = "E1002",
            title = "WIFI_REASON_4WAY_HANDSHAKE_TIMEOUT",
            description = "Wrong password"
        ),
        GGErrorCode(
            code = "E1004",
            title = "WIFI_REASON_AUTH_FAIL",
            description = "The hotspot password of the device connection may be incorrect"
        ),
        GGErrorCode(
            code = "E1005",
            description = "Hotspot does not exist/ wrong password. The device is not in the router Whitelist"
        ),

        // HTTP Errors
        GGErrorCode(
            code = "E2001",
            title = "EAI_NONAME",
            description = "Domain name resolution error"
        ),
        GGErrorCode(code = "E2002", title = "EAI_SERVICE", description = "Domain name is wrong"),
        GGErrorCode(
            code = "E2003",
            title = "EAI_FAIL",
            description = "Domain name Port Number is wrong"
        ),
        GGErrorCode(
            code = "E2004",
            title = "HOST_NOT_FOUND",
            description = "Domain name does not exist"
        ),
        GGErrorCode(code = "E2005", description = "Socket error"),
        GGErrorCode(
            code = "E2006",
            description = "Connect interface returns when there is no network"
        ),

        // HTTPS Errors
        GGErrorCode(code = "E3001", description = "Device connected hotspot No network"),
        GGErrorCode(code = "E3002", description = "Failed to connect to server"),
        GGErrorCode(code = "E3003", description = "Network connection failed"),
        GGErrorCode(code = "E3004", description = "Network connection failed"),
        GGErrorCode(code = "E3005", description = "Network exception"),
        GGErrorCode(code = "E3006", description = "Network exception"),
        GGErrorCode(
            code = "E3007",
            title = "MBEDTLS_ERR_SSL_FATAL_ALERT_MESSAGE",
            description = "Network exception"
        ),

        // Server Errors
        GGErrorCode(code = "E4001", description = "Failed to register server"),
        GGErrorCode(code = "E4002", description = "Server returns error - 'Token is invalid'"),
        GGErrorCode(code = "E4003", description = "The server returned an error - 'Bad Request'"),
        GGErrorCode(code = "E4004", description = "The server returned an error - 'Unauthorized'"),
        GGErrorCode(code = "E4005", description = "Server returns error - 'Scale not available'"),
        GGErrorCode(
            code = "E4006",
            description = "The server returned an error - 'error': 'Missing required value XX'"
        ),
        GGErrorCode(
            code = "E4007",
            title = "MBEDTLS_ERR_SSL_FATAL_ALERT_MESSAGE",
            description = "The server returned an error - 'Multiple rows were not expected.'"
        ),

        // Equipment Errors
        GGErrorCode(
            code = "E5001",
            title = "WIFI_CONFIG_REGISTER_CONNECT_TIMEOUT",
            description = "Distribution network connection router timed out"
        ),
        GGErrorCode(
            code = "E5002",
            title = "WIFI_CONFIG_REGISTER_SEVER_TIMEOUT",
            description = "Distribution network registration server returns timeout"
        ),
        GGErrorCode(code = "E5003", description = "WiFi password not set for distribution network"),
        GGErrorCode(
            code = "E5004",
            description = "The WiFi account and password are not set in the distribution network"
        ),
        GGErrorCode(
            code = "E5005",
            description = "The distribution network did not obtain the WiFi list"
        ),
        GGErrorCode(
            code = "E5006",
            description = "The cloud did not return the upgrade component link"
        ),
        GGErrorCode(code = "E5007", description = "Length error"),
        GGErrorCode(code = "E5008", description = "CRC error"),
        GGErrorCode(code = "E5009", description = "Timeout of first frame of data frame"),
        GGErrorCode(code = "E50010", description = "Inconsistent data frame number"),
        GGErrorCode(code = "E50011", description = "Data frame reception timeout"),
        GGErrorCode(code = "E50012", description = "Timed out requesting upgrade file")
    )

    val bhMonitorErrors = listOf(
        GGErrorCode(
            code = "Display doesn’t light up",
            title = "No power",
            description = "Check to see if the batteries are dead or inserted incorrectly or charge the device"
        ),
        GGErrorCode(
            code = "Lo or dim display",
            title = "Low batteries",
            description = "Open the battery door on the back of the monitor, and insert 2 new AAA batteries or charge the device"
        ),
        GGErrorCode(
            code = "E1 shows on screen",
            title = "The cuff is too tight or too loose",
            description = "Refasten the cuff and then measure again"
        ),
        GGErrorCode(
            code = "E2 shows on screen",
            title = "Monitor detected motion while inflating",
            description = "Remain still while the monitor is taking a measurement"
        ),
        GGErrorCode(
            code = "E3 shows on screen",
            title = "Pulse not detected",
            description = "Remove clothing covering the wrist or tighten the cuff and then measure again"
        ),
        GGErrorCode(
            code = "E4 shows on screen",
            title = "Measured incorrectly",
            description = "Refasten the cuff and then measure again"
        ),
        GGErrorCode(
            code = "EExx shows on screen",
            title = "A calibration error occurred",
            description = "Retake the measurement. If the problem persists, contact our customer service department for further assistance"
        ),
        GGErrorCode(
            code = "'Out' shows on screen",
            title = "Measurement is out of range",
            description = "Relax for a moment and retake the measurement. If the problem persists, contact your physician"
        )
    )
    val pulseOximeterErrors = listOf(
        GGErrorCode(
            code = "The oximeter won't power on.",
            title = "The batteries are drained or almost drained.",
            description = "Replace the batteries"
        ),
        GGErrorCode(
            code = "The oximeter won't power on.",
            title = "The batteries are installed incorrectly.",
            description = "Reinstall the batteries correctly."
        ),
        GGErrorCode(
            code = "The oximeter won't power on.",
            title = "The device is broken.",
            description = "Contact customer service for help."
        ),
        GGErrorCode(
            code = "The SpO2 and PR are not displayed normally.",
            title = "The finger is too big or too small.",
            description = "Select a different sized finger suitable for measurement."
        ),
        GGErrorCode(
            code = "The SpO2 and PR are not displayed normally.",
            title = "There is excessive ambient light.",
            description = "Find somewhere with less ambient light to measure."
        ),
        GGErrorCode(
            code = "The SpO2 and PR are not displayed normally.",
            title = "User's blood perfusion is very low.",
            description = "Warm the finger and try again."
        ),
        GGErrorCode(
            code = "The display suddenly turns off.",
            title = "The oximeter is designed to turn off in 10 seconds when a finger is not inserted.",
            description = "This is normal."
        ),
        GGErrorCode(
            code = "The display suddenly turns off.",
            title = "The batteries are almost drained.",
            description = "Replace the batteries."
        ),
        GGErrorCode(
            code = "The SpO2 and PR are not displayed stably.",
            title = "The finger is not inserted deep enough.",
            description = "Replace the finger and try again."
        ),
        GGErrorCode(
            code = "The SpO2 and PR are not displayed stably.",
            title = "The finger is shaking, or the body is moving.",
            description = "Try to keep still."
        ),
        GGErrorCode(
            code = "The SpO2 and PR are not displayed stably..",
            title = "The oximeter is not being used in a proper working environment.",
            description = "Please use the oximeter in a normal working environment (i.e. indoors, warm)."
        ),
        GGErrorCode(
            code = "The SpO2 and PR are not displayed stably.",
            title = "The device is broken.",
            description = "Contact customer service for help."
        )
    )
    val glucoseMeterErrors = listOf(
        GGErrorCode(
            code = "E0",
            description = "Please replace with a new strip and test again."
        ),
        GGErrorCode(
            code = "E1",
            description = "Please add samples after the interface indicating 'waiting to add blood' appears."
        ),
        GGErrorCode(
            code = "E2",
            description = "The test strip has been used. Please replace it with a new strip and test again."
        ),
        GGErrorCode(
            code = "E3",
            description = "Blood glucose test strip does not match. Please use a compatible blood glucose test strip."
        ),
        GGErrorCode(
            code = "E4",
            description = "The sample is abnormal. Only human blood or associated quality control fluid can be used for testing."
        ),
        GGErrorCode(
            code = "E5",
            description = "Move to a suitable temperature, wait for 20 minutes, and retest."
        ),
        GGErrorCode(
            code = "E6",
            description = "The device is abnormal. Please restart the blood glucose meter and use a new test strip."
        ),
        GGErrorCode(
            code = "E7",
            description = "The test sample is insufficient. Please replace the test strip with a new one and test again."
        ),
        GGErrorCode(
            code = "E8",
            description = "Please unplug the charging cord of the glucose meter and switch it on again for testing."
        ),
        GGErrorCode(
            code = "E9",
            description = "Ensure the sample is sufficient, then replace the test strip with a new one and test again."
        ),
        GGErrorCode(
            code = "E10",
            description = "Ensure the sample is sufficient, then replace the test strip with a new one and test again."
        )
    )

    val basicErrors= listOf(
        GGErrorCode(
            code = "Lo or dim display",
            title = "LOW BATTERY",
            description = "When you see this symbol, the batteries need to be replaced. Open the battery door on the back of the scale, and insert new batteries."
        ),
        GGErrorCode(
            code = "----",
            title = "OVERLOAD",
            description = "There is too much weight on the scale (Capacity: 400lb / 180kg). Please remove the weight on the scale to protect the scale sensors."
        )
    )

    val thermometerErrors= listOf(
        GGErrorCode(
            code = "HI",
            title = "Temperature Too High.",
            description = "The measured temperature exceeds the maximum measurable value. Ensure the thermometer is within the recommended range and retake the measurement."
        ),
        GGErrorCode(
            code = "LOW",
            title = "Temperature Too Low.",
            description = "The measured temperature is below the minimum measurable value. Ensure the thermometer is within the recommended range and retake the measurement."
        ),
        GGErrorCode(
            code = "OUT_OF_RANGE",
            title = "Temperature Out of Range.",
            description = "The temperature is out of the measurable range. Verify the thermometer is being used correctly and retake the measurement."
        ),
        GGErrorCode(
            code = "Lo",
            title = "The battery level is critically low.",
            description = "Replace the thermometer's battery and try again."
        )
    )

}