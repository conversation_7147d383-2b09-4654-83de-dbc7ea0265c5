package com.dmdbrands.library.bluetooth.ui.shared.component

import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarColors
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.SurfaceBackgroundColor

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppTopBar(
    title: String? = null,
    modifier: Modifier = Modifier,
    titleComposable: @Composable (() -> Unit)? = null,
    actions: @Composable (RowScope.() -> Unit)? = { },
    navigationIcon: @Composable (() -> Unit)? = {},
    expandedHeight: Dp = TopAppBarDefaults.TopAppBarExpandedHeight,
    windowInsets: WindowInsets = TopAppBarDefaults.windowInsets,
    colors: TopAppBarColors = TopAppBarDefaults.topAppBarColors(containerColor = SurfaceBackgroundColor)
) {
    CenterAlignedTopAppBar(
        modifier = modifier,
        title = {
            Row(
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (titleComposable != null) {
                    titleComposable()
                }
                if (title != null) {
                    Text(
                        text = title,
                        fontFamily = OpenSansFont,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.basicMarquee()
                    )
                }
            }

        },
        navigationIcon = {
            if (navigationIcon != null) {
                navigationIcon()
            }
        },
        actions = {

            if (actions != null) {
                actions()
            }
        },
        colors = colors,
        windowInsets = windowInsets,
        expandedHeight = expandedHeight
    )
}