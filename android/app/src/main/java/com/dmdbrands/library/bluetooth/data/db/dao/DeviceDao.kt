package com.dmdbrands.library.bluetooth.data.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Update
import com.dmdbrands.library.bluetooth.data.db.entity.DeviceEntity
import com.dmdbrands.library.bluetooth.data.db.entity.PreferencesEntity

@Dao
interface DeviceDao {
    @Insert
    fun addDevice(device: DeviceEntity)

    @Insert
    fun addDevicePreference(devicePreference: PreferencesEntity)

    @Update
    fun updatePreference(devicePreference: PreferencesEntity)

    @Query("Select * from device_preferences where broadcast_id = :broadcastId")
    fun getPreferencesForDevice(broadcastId: String): PreferencesEntity?


    @Query("Select * from device_details where broadcast_id = :broadcastId")
    fun getDevice(broadcastId: String): DeviceEntity?

    @Query("Update device_details SET system_id = :systemId  WHERE broadcast_id = :broadcastId ")
    fun updateDeviceSystemId(systemId: String?, broadcastId: String)

    @Query("UPDATE device_details SET is_Linked = :isLink WHERE broadcast_id = :broadcastId")
    fun updateDevice(
        broadcastId: String,
        isLink: Boolean
    )

    @Query("UPDATE device_details SET primaryToken = :primaryToken WHERE broadcast_id = :broadcastId")
    fun updatePrimaryToken(
        broadcastId: String,
        primaryToken: String?
    )

    @Query("UPDATE device_details SET primaryToken = :primaryToken WHERE app_type = :appType")
    fun updatePrimaryTokenByAppType(appType: String, primaryToken: String?)

    @Query("UPDATE device_details SET token = :token WHERE broadcast_id = :broadcastId")
    fun updateToken(
        broadcastId: String,
        token: String?
    )


    @Query("UPDATE device_preferences SET display_name = :userName WHERE broadcast_id = :broadcastId")
    fun updateUserName(
        broadcastId: String,
        userName: String?
    )


    @Query("UPDATE device_details SET is_Linked = :isLink WHERE app_type = :appType")
    fun updateDeviceBasedOnAppType(
        appType: String,
        isLink: Boolean?
    )

    @Query("DELETE FROM device_details WHERE broadcast_id = :broadcastId")
    fun deleteDevice(broadcastId: String)

    @Query("Select * from device_details")
    fun getDevices(): List<DeviceEntity>

    @Query("Select 1 from device_details where mac_address = :macAddress and user_number = :userNumber")
    fun checkDeviceAvailable(macAddress: String, userNumber: Int?): Boolean


}