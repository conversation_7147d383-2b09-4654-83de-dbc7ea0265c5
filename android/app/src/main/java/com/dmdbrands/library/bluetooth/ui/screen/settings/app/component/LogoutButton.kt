package com.dmdbrands.library.bluetooth.ui.screen.settings.app.component

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.ui.screen.settings.viewmodel.ProfileViewModel
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.example.utilities.modal.ActionButton
import com.example.utilities.modal.Dialog
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.theme.HiddenBackgroundColor

@Composable
fun LogoutButton(utility: ProfileViewModel, navController: NavController, appType: String) {
    AppButton(containerColor = Transparent, onClick = {
        utility.setDialog(
            logOutAlert(onDismiss = {
                utility.setDialog(null)
            }, onLogout = {
                utility.logout(appType)
                navController.navigateUp()
            })
        )
    }) {
        Text(
            AppLang.LOGOUT.uppercase(),
            fontFamily = OpenSansFont,
            fontSize = 14.sp,
            color = HiddenBackgroundColor,
            fontWeight = FontWeight.Bold,
            modifier = Modifier
                .padding(2.dp),
            textAlign = TextAlign.Center,
        )
    }
}


private fun logOutAlert(onDismiss: () -> Unit, onLogout: () -> Unit): Dialog {
    return Dialog(
        title = AppLang.ALERT,
        message = AppLang.Dialog.LOGOUT_INFO,
        confirmButton = ActionButton(
            text = AppLang.LOGOUT,
            action = {
                onLogout()
                onDismiss()
            }
        ),
        dismissButton = ActionButton(
            text = AppLang.Dialog.CANCEL,
            action = onDismiss
        )
    )
}