package com.dmdbrands.library.bluetooth.data.db.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

@Entity(
    tableName = "device_details",
    indices = [Index(value = ["broadcast_id"], unique = true)] // 👈 Enforce uniqueness
)
data class DeviceEntity(
    @PrimaryKey(autoGenerate = true) val id: Long = 0,  // Primary key for Room
    @ColumnInfo(name = "device_name") val deviceName: String,
    @ColumnInfo(name = "broadcast_id") val broadcastId: String,
    @ColumnInfo(name = "mac_address") val macAddress: String,
    @ColumnInfo(name = "identifier") val identifier: String,
    @ColumnInfo(name = "password") val password: String,
    @ColumnInfo(name = "app_type") val appType: String,
    @ColumnInfo(name = "createdAt") val createdAt: String,
    @ColumnInfo(name = "primaryToken") val primaryToken: String? = null,
    @ColumnInfo(name = "token") val token: String? = null,
    @ColumnInfo(name = "user_number") val userNumber: Int? = null,
    @ColumnInfo(name = "system_id") val systemId: String? = null,
    @ColumnInfo(name = "is_Linked") val isLinked: Boolean? = null
)