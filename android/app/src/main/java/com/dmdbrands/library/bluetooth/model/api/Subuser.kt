package com.dmdbrands.library.bluetooth.model.api

sealed class SubUser {
    abstract val id: String
    abstract val name: String
}

data class Baby(
    override val id: String,
    val isOwnedByAccount: Boolean,
    val babyPermissions: Int, // 1, 2, or 3
    val birthdate: String, // 'YYYY-MM-DD' format
    val createdAt: String, // Automatically set timestamp with timezone
    val dueDate: String, // 'YYYY-MM-DD' format
    val isBorn: Boolean,
    override val name: String,
    val sex: String?, // 'male', 'female', 'private', or null
    val birthWeightGrams: Int,
    val birthLengthMillimeters: Int
) : SubUser()
