package com.dmdbrands.library.bluetooth.ui.screen.settings.app

import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.dmdbrands.library.bluetooth.config.LocalNavController
import com.dmdbrands.library.bluetooth.model.api.SubUser
import com.dmdbrands.library.bluetooth.ui.screen.appDetail.AppDetailState
import com.dmdbrands.library.bluetooth.ui.screen.scanDevice.component.AppTypeDropDown
import com.dmdbrands.library.bluetooth.ui.screen.settings.app.component.LogoutButton
import com.dmdbrands.library.bluetooth.ui.screen.settings.app.component.ProfileForm
import com.dmdbrands.library.bluetooth.ui.screen.settings.viewmodel.ProfileViewModel
import com.dmdbrands.library.bluetooth.ui.shared.component.AppScaffold
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.example.utilities.services.component.AppButton
import com.example.utilities.util.onlyString
import com.example.utilities.util.single
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppSettingsScreen(
    appType: String,
    parentState: AppDetailState? = null,
    setSubUser: (SubUser) -> Unit = {}
) {
    val viewmodel = hiltViewModel<ProfileViewModel, ProfileViewModel.Factory> { factory ->
        factory.create(appType)
    }
    val state by viewmodel.state.collectAsState()
    val userForm by state.getUserForm().form.collectAsState()
    val scope = rememberCoroutineScope()

    LaunchedEffect(parentState?.subUser) {
        viewmodel.fetchUserDetails()
    }

    AppScaffold(
        title = if (parentState?.subUsers?.isEmpty() == true) "${
            appType.onlyString().single()
        } - HISTORY" else null,
        actions = {
            AppButton(
                onClick = {
                    viewmodel.fetchUserDetails()
                },
                containerColor = Transparent,
                disabledContainerColor = Transparent
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "Refresh",
                    tint = PrimaryColor,
                    modifier = Modifier.size(26.dp)
                )
            }
        },
        navigationIcon = {
            LogoutButton(viewmodel, LocalNavController.current, appType)
        }, titleComposable =
            if (parentState?.subUsers?.isNotEmpty() == true) {
                {
                    AppTypeDropDown(
                        items = parentState.subUsers.map { it.name },
                        selected = parentState.subUser?.name
                    ) { selected ->
                        scope.launch {
                            setSubUser(parentState.subUsers.find { it.name == selected }!!)
                            viewmodel.fetchUserDetails()
                        }
                    }
                }
            } else null) {
        PullToRefreshBox(isRefreshing = state.isRefreshing, onRefresh = {
            viewmodel.fetchUserDetails()
        }) {
            ProfileForm(state.user, userForm)
        }
    }
}
