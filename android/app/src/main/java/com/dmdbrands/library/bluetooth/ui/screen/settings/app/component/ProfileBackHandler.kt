package com.dmdbrands.library.bluetooth.ui.screen.settings.app.component

import androidx.activity.compose.BackHandler
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.config.AppRoute
import com.example.utilities.modal.ActionButton
import com.example.utilities.modal.Dialog
import com.example.utilities.services.viewmodel.UtilitiesViewmodel
import com.example.utilities.util.Form

@Composable
fun ProfileBackHandler(utility: UtilitiesViewmodel, userForm: Form, onReset: () -> Unit) {
    val keyboardController = LocalSoftwareKeyboardController.current
    fun setAlert(): Dialog {
        return Dialog(
            title = AppLang.ALERT,
            message = AppLang.Dialog.DISCARD_CHANGES,
            confirmButton = ActionButton(
                text = AppLang.Dialog.DISCARD,
                action = {
                    onReset()
                    utility.navigateBack(baseRoute = AppRoute.AppDevices)
                }
            ),
            dismissButton = ActionButton(
                text = AppLang.Dialog.CANCEL,
                action = {
                    utility.setDialog(dialog = null)
                }
            )
        )
    }

    fun onDismissRequest() {
        if (userForm.isFormDirty || userForm.isFormTouched) {
            utility.setDialog(dialog = setAlert())
        } else {
            utility.navigateBack(baseRoute = AppRoute.AppDetail)
        }
        keyboardController?.hide()
    }

    BackHandler {
        onDismissRequest()
    }
}