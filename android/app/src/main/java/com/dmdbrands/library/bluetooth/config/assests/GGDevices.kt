package com.dmdbrands.library.bluetooth.config.assests

import com.dmdbrands.library.bluetooth.R
import com.dmdbrands.library.bluetooth.ui.shared.component.BLEDeviceInfo
import com.dmdbrands.library.ggbluetooth.enums.GGAppType

val ggDevices = listOf(
    BLEDeviceInfo(
        name = "Kitchen Scale",
        sku = "0480",
        image = R.drawable.ic_kitchen_scale,
        protocolType = "Welland",
        manufacturer = "Welland",
        app = GGAppType.SAGE,
        errorCodes = GGErrorCodes.basicErrors
    ),
    BLEDeviceInfo(
        name = "Bluetooth smart scale",
        sku = "0375",
        image = R.drawable.ic_weight_gurus,
        protocolType = "A3",
        manufacturer = "Welland",
        app = GGAppType.WEIGHT_GURUS,
        errorCodes = GGErrorCodes.WgA3ScaleErrors
    ),
    BLEDeviceInfo(
        name = "Bluetooth smart scale",
        sku = "0376",
        image = R.drawable._376,
        protocolType = "A3",
        manufacturer = "Welland",
        app = GGAppType.WEIGHT_GURUS,
        errorCodes = GGErrorCodes.WgA3ScaleErrors
    ),
    BLEDeviceInfo(
        name = "Bluetooth smart scale",
        sku = "0380",
        image = R.drawable._380,
        protocolType = "A3",
        manufacturer = "Welland",
        app = GGAppType.WEIGHT_GURUS,
        errorCodes = GGErrorCodes.WgA3ScaleErrors
    ),
    BLEDeviceInfo(
        name = "Bluetooth smart scale",
        sku = "0382",
        protocolType = "A3",
        image = R.drawable._382,
        manufacturer = "Welland",
        app = GGAppType.WEIGHT_GURUS,
        errorCodes = GGErrorCodes.WgA3ScaleErrors
    ),
    BLEDeviceInfo(
        name = "Bluetooth smart scale",
        sku = "0378",
        protocolType = "A6",
        image = R.drawable._376,
        manufacturer = "Welland",
        app = GGAppType.WEIGHT_GURUS,
        errorCodes = GGErrorCodes.WgA6ScaleErrors
    ),
    BLEDeviceInfo(
        name = "Bluetooth smart scale",
        sku = "0383",
        protocolType = "A6",
        image = R.drawable._383,
        manufacturer = "Welland",
        app = GGAppType.WEIGHT_GURUS,
        errorCodes = GGErrorCodes.WgA6ScaleErrors
    ),
    BLEDeviceInfo(
        name = "AccuCheck Verve Smart Scale",
        sku = "0412",
        protocolType = "R4",
        image = R.drawable.ic_weight_gurus,
        manufacturer = "Unique",
        app = GGAppType.WEIGHT_GURUS,
        errorCodes = GGErrorCodes.R4ScaleErrors
    ),
    BLEDeviceInfo(
        name = "Smart Wrist Blood Pressure Monitor",
        sku = "0603",
        protocolType = "A3",
        image = R.drawable._603,
        manufacturer = "Transtek",
        app = GGAppType.BALANCE_HEALTH,
        errorCodes = GGErrorCodes.bhMonitorErrors
    ),
    BLEDeviceInfo(
        name = "Smart Blood Pressure Monitor",
        sku = "0604/0664",
        protocolType = "A3",
        image = R.drawable._604,
        manufacturer = "Transtek",
        app = GGAppType.BALANCE_HEALTH,
        errorCodes = GGErrorCodes.bhMonitorErrors
    ),
    BLEDeviceInfo(
        name = "Smart Pro-Series Blood Pressure Monitor",
        sku = "0634",
        image = R.drawable._634,
        protocolType = "A3",
        manufacturer = "Transtek",
        app = GGAppType.BALANCE_HEALTH,
        errorCodes = GGErrorCodes.bhMonitorErrors
    ),
    BLEDeviceInfo(
        name = "All-In-One Bluetooth Blood Pressure Monitor",
        sku = "0636/0639",
        protocolType = "A3",
        image = R.drawable._636,
        manufacturer = "Transtek",
        app = GGAppType.BALANCE_HEALTH,
        errorCodes = GGErrorCodes.bhMonitorErrors
    ),
    BLEDeviceInfo(
        name = "Smart Blood Pressure Monitor",
        sku = "0663/0665",
        image = R.drawable._604,
        protocolType = "A6",
        manufacturer = "Transtek",
        app = GGAppType.BALANCE_HEALTH,
        errorCodes = GGErrorCodes.bhMonitorErrors
    ),
    BLEDeviceInfo(
        name = "Smart Blood Pressure Monitor",
        sku = "0661",
        image = R.drawable._661,
        protocolType = "A6",
        manufacturer = "Transtek",
        app = GGAppType.BALANCE_HEALTH,
        errorCodes = GGErrorCodes.bhMonitorErrors
    ),
    BLEDeviceInfo(
        name = "Pulse Oxi meter",
        sku = "0003",
        protocolType = "0003",
        image = R.drawable.pulseoximeter,
        manufacturer = "Transtek",
        app = GGAppType.BALANCE_HEALTH,
        errorCodes = GGErrorCodes.pulseOximeterErrors
    ),
    BLEDeviceInfo(
        name = "Blood Glucose Meter",
        sku = "Transtek",
        protocolType = "Transtek",
        image = R.drawable.bgm_1234,
        manufacturer = "Transtek",
        app = GGAppType.BALANCE_HEALTH,
        errorCodes = GGErrorCodes.glucoseMeterErrors
    ),
    BLEDeviceInfo(
        name = "Thermometer",
        sku = "0062",
        protocolType = "Avita",
        image = R.drawable._062,
        manufacturer = "Avita",
        app = GGAppType.ALL,
        errorCodes = GGErrorCodes.thermometerErrors
    ),
    BLEDeviceInfo(
        name = "Baby Scale",
        sku = "0222",
        image = R.drawable.babyscale,
        protocolType = "A6",
        manufacturer = "WellLand",
        app = GGAppType.SMART_BABY,
        errorCodes = GGErrorCodes.basicErrors
    ),
    BLEDeviceInfo(
        name = "Smart Baby Scale",
        sku = "0220",
        protocolType = "A6",
        image = R.drawable.smartbabyscale,
        manufacturer = "Wellland",
        app = GGAppType.SMART_BABY,
        errorCodes = GGErrorCodes.WgA6ScaleErrors
    )
)
