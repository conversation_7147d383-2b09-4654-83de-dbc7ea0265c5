package com.dmdbrands.library.bluetooth.ui.screen.pairedDevice.app

import com.dmdbrands.library.bluetooth.ui.shared.DeviceIntent
import com.dmdbrands.library.bluetooth.ui.shared.ScanDeviceState
import com.example.utilities.modal.interfaces.IReducer


class AppDeviceReducer : IReducer<ScanDeviceState, DeviceIntent> {
    override fun reduce(state: ScanDeviceState, intent: DeviceIntent): ScanDeviceState? {

        return when (intent) {
            is DeviceIntent.AddDevice -> {
                state.copy(localDevices = intent.devices)
            }

            is DeviceIntent.AlterLocalRefreshState -> {
                state.copy(localRefreshing = intent.isRefreshing)
            }

            else -> {
                state
            }
        }
    }
}