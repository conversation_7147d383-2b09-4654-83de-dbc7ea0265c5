package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail

import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.DeviceLogs
import com.dmdbrands.library.bluetooth.model.HistoryData
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.sheet.DeviceMode
import com.dmdbrands.library.ggbluetooth.enums.ClearDataType
import com.dmdbrands.library.ggbluetooth.model.GGBTSetting
import com.dmdbrands.library.ggbluetooth.model.GGBTUser
import com.dmdbrands.library.ggbluetooth.model.GGDeviceLog
import com.dmdbrands.library.ggbluetooth.model.GGEntry
import com.example.utilities.modal.interfaces.IReducer
import com.greatergoods.ggbluetoothsdk.external.models.GGWifiInfo

class DeviceDetailReducer : IReducer<DeviceDetailReducer.State, DeviceDetailReducer.Intent> {

    data class State(
        val device: BLEDevice? = null,
        val deviceLog: DeviceLogs = DeviceLogs(),
        val users: List<GGBTUser> = listOf(),
        val historyData: List<HistoryData>? = null,
        val wifiList: List<GGWifiInfo>? = null,
        val connectedWifiMacAddress: String? = null,
        val liveEntry: GGEntry? = null,
        val error: String? = null,
        val latestEntry: HistoryData? = null,
    ) :
        IReducer.State

    sealed interface Intent : IReducer.Intent {
        //init
        data class setDevice(val device: BLEDevice) : Intent
        data class saveDeviceLogs(val logs: List<GGDeviceLog>) : Intent
        data class saveDeviceLogFetchStatus(val percentage: Float) : Intent
        data class saveLiveEntry(val liveEntry: GGEntry?) : Intent
        data class saveLastestEntry(val historyData: HistoryData?) : Intent
        data class saveUsers(val users: List<GGBTUser>) : Intent
        data class saveError(val error: String?) : Intent
        data class saveHistoryData(val historyData: List<HistoryData>?) : Intent

        //account
        data object linkToAccount : Intent
        data object unlinkFromAccount : Intent

        //logs
        data object fetchLogs : Intent
        data object saveLogsToMemory : Intent

        //wifi
        data class saveWifiList(val wifiList: List<GGWifiInfo>?) : Intent
        data object clearWifiList : Intent
        data object onCopytoClipBoard : Intent
        data class connectWifi(val wifiInfo: GGWifiInfo) : Intent
        data object getWifiList : Intent
        data class onSaveMetrics(val metrics: List<String>, val mode: DeviceMode) : Intent
        data object getConnectedWifiMacAddress : Intent
        data class saveConnectedWifiMacAddress(val macAddress: String) : Intent

        //settings
        data class startUpgrade(val timeInMillis: Long) : Intent
        data class updateSettings(val settings: GGBTSetting) : Intent
        data class clearData(val type: ClearDataType) : Intent
        data object removeProfile : Intent
    }

    override fun reduce(
        state: State,
        intent: Intent
    ): State? {
        return when (intent) {
            is Intent.setDevice -> {
                state.copy(device = intent.device)
            }

            is Intent.saveConnectedWifiMacAddress -> {
                state.copy(connectedWifiMacAddress = intent.macAddress)
            }

            is Intent.saveDeviceLogs -> {
                state.copy(deviceLog = state.deviceLog.copy(logs = intent.logs))
            }

            is Intent.saveDeviceLogFetchStatus -> {
                state.copy(deviceLog = state.deviceLog.copy(fetchStatus = intent.percentage))
            }

            is Intent.saveLiveEntry -> {
                state.copy(liveEntry = intent.liveEntry)
            }

            is Intent.saveError -> {
                state.copy(error = intent.error)
            }

            is Intent.saveWifiList -> {
                state.copy(wifiList = intent.wifiList)
            }

            is Intent.clearWifiList -> {
                state.copy(wifiList = null)
            }

            is Intent.saveHistoryData -> {
                state.copy(historyData = intent.historyData)
            }

            is Intent.saveUsers -> {
                state.copy(users = intent.users)
            }

            is Intent.saveLastestEntry -> {
                state.copy(latestEntry = intent.historyData)
            }

            else -> state

        }
    }

}