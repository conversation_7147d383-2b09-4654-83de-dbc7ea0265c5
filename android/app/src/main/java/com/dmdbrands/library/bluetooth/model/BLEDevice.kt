package com.dmdbrands.library.bluetooth.model

import com.dmdbrands.library.bluetooth.model.api.Preferences
import com.dmdbrands.library.bluetooth.util.DeviceUtil
import com.dmdbrands.library.bluetooth.util.DeviceUtil.getAppType
import com.dmdbrands.library.bluetooth.util.DeviceUtil.getSKU
import com.dmdbrands.library.bluetooth.util.Source
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.dmdbrands.library.ggbluetooth.model.GGDeviceDetail
import com.example.utilities.modal.PickerItem
import com.example.utilities.util.CalendarUtil

enum class BLEStatus {
    CONNECTED, DISCONNECTED, CONNECTING, DISCONNECTING
}

data class BLEDevice(
    val device: GGDeviceDetail,
    val connectionStatus: BLEStatus = BLEStatus.DISCONNECTED,
    val sku: String = device.getSKU(),
    val nickname: String = device.deviceName,
    val alreadyPaired: Boolean = false,
    val userNumber: Int? = 0,
    val source: String = Source.BLUETOOTH,
    val preferences: Preferences? = if (sku == "0412") Preferences(
        shouldMeasureImpedance = device.impedanceSwitchState,
        displayMetrics = DeviceUtil.initialMetrics
    ) else null,
    val primaryToken: String? = null,
    val token: String? = null,
    val wifiSSID: String? = null,
    val isLinked: Boolean? = null,
    val createdAt: String = CalendarUtil.getCurrentDate().timeInMillis.toString(),
) {
    fun getAppType(): String {
        return device.getAppType(this.sku)
    }

    fun containsUsers(): Boolean {
        return !(device.getAppType() == GGAppType.SAGE || device.getAppType() == GGAppType.SMART_BABY || ((device.getAppType() == GGAppType.WEIGHT_GURUS) && (device.protocolType == "A6" || device.deviceName.contains(
            "0351"
        ) || sku.contains("0344") || sku.contains("0412"))) || device.getAppType() == GGAppType.RPM)
    }

    fun getUserList(): List<PickerItem<Int>> {
        return DeviceUtil.getUserList(this)
    }

    fun getUserLabel(): String {
        return DeviceUtil.getUserLabel(this)
    }
}
