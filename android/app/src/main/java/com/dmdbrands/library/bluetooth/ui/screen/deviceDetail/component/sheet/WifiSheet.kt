package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.sheet

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Wifi
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.DeviceDetailReducer
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.WifiIconAnimation
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.theme.BackgroundSecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.DividerColor
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.example.utilities.services.component.list.StaticList
import com.example.utilities.services.theme.HiddenBackgroundColor
import com.example.utilities.services.theme.OpenSansFont
import com.example.utilities.services.theme.PrimaryColor
import com.greatergoods.ggbluetoothsdk.external.models.GGWifiInfo

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WifiSheet(
    ssid: String? = null,
    wifiList: List<GGWifiInfo>? = null,
    handleIntent: (DeviceDetailReducer.Intent) -> Unit,
    onDismiss: () -> Unit
) {
    val filteredWifiList = wifiList?.filter { it.ssid != ssid }
    DetailViewSheet(
        title = "Connect to Wi-Fi",
        onDismiss = onDismiss
    ) {
        if (filteredWifiList != null) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)

            ) {
                Column(
                    modifier = Modifier
                        .clip(RoundedCornerShape(16.dp))
                        .background(BackgroundSecondaryColor)
                        .padding(18.dp)
                ) {

                    Text(
                        "If you have multiple Wi-Fi networks, pick the 2.4 GHz network closest to your scale.",
                        fontFamily = OpenSansFont,
                        fontSize = 14.sp,
                        color = TextPrimaryColor
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    if (ssid.isNullOrBlank()) {
                        Text(
                            text = "No Connected Network",
                            fontFamily = OpenSansFont,
                            fontWeight = FontWeight.Bold,
                            fontSize = 14.sp,
                            color = HiddenBackgroundColor,
                        )
                    } else {
                        Text(
                            text = "Connected Network",
                            fontFamily = OpenSansFont,
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp,
                            color = TextPrimaryColor,
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        WIFICard(ssid, connected = true)
                    }
                    Spacer(modifier = Modifier.height(10.dp))
                    if (filteredWifiList.isEmpty()) {
                        Text(
                            text = "No Available Networks",
                            fontFamily = OpenSansFont,
                            fontWeight = FontWeight.Bold,
                            fontSize = 14.sp,
                            color = HiddenBackgroundColor
                        )
                    } else {
                        Text(
                            text = "Available Networks",
                            fontFamily = OpenSansFont,
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp,
                            color = TextPrimaryColor,
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        StaticList(
                            list = filteredWifiList,
                            scrollBarEnabled = true
                        ) { index, item ->
                            WIFICard(item.ssid) {
                                handleIntent(
                                    DeviceDetailReducer.Intent.connectWifi(item)
                                )
                            }
                            HorizontalDivider(color = DividerColor, thickness = 2.dp)
                        }
                    }
                }
            }
        } else {
            Column(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    "Gathering Networks ...",
                    fontFamily = OpenSansFont,
                    fontSize = 28.sp,
                    color = TextPrimaryColor
                )
                WifiIconAnimation(
                    circleSizeRatio = 0.5f
                )
            }
        }
    }
}


@Composable
private fun WIFICard(ssid: String, connected: Boolean = false, onClick: (() -> Unit)? = null) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable {
                onClick?.invoke()
            }
            .padding(vertical = 14.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = Icons.Default.Wifi,
            contentDescription = null,
            tint = PrimaryColor
        )
        Text(
            modifier = Modifier.weight(1f),
            text = ssid,
            fontFamily = OpenSansFont,
            fontSize = 14.sp,
            color = TextPrimaryColor
        )
        if (!connected)
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = null,
                tint = PrimaryColor,
            )
    }
}

@PreviewScreens
@Composable
private fun WifiSheetPreview() {
    WifiSheet(handleIntent = {}, wifiList = listOf()) { }
}