package com.dmdbrands.library.bluetooth.ui.screen.appDetail

import androidx.lifecycle.viewModelScope
import com.dmdbrands.library.bluetooth.model.api.SubUser
import com.dmdbrands.library.bluetooth.model.interfaces.IUserRepository
import com.example.utilities.services.viewmodel.BaseViewModel
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch

@HiltViewModel(assistedFactory = AppDetailViewmodel.Factory::class)
class AppDetailViewmodel @AssistedInject constructor(
    @Assisted private val appType: String,
    private val userRepository: IUserRepository
) : BaseViewModel<AppDetailState, AppDetailIntent>(
    initialState = AppDetailState(),
    reducer = AppDetailReducer()
) {
    init {
        updateSubUsers()
    }

    @AssistedFactory
    interface Factory {
        fun create(appType: String): AppDetailViewmodel
    }

    private fun setSubUser(subUser: SubUser) {
        viewModelScope.launch {
            userRepository.setSubUser(subUser, appType)
        }
    }

    private fun updateSubUsers() {
        viewModelScope.launch {
            val response = userRepository.getSubUserDetails(appType)?.body()
            if (response != null) {
                handleIntent(AppDetailIntent.UpdateSubUsers(response))
                val user = userRepository.getUser(appType)
                val subUser =
                    if (user != null && user.subCategory.first && user.subCategory.second == null) {
                        userRepository.setSubUser(response.first(), appType)
                        response.first()
                    } else response.find { it.id == user?.subCategory?.second }
                if (subUser != null) {
                    handleIntent(AppDetailIntent.SetSubUser(subUser))
                }
            }
        }
    }

    override fun handleIntent(intent: AppDetailIntent) {
        super.handleIntent(intent)
        when (intent) {
            is AppDetailIntent.SetSubUser -> {
                setSubUser(intent.subUser)
            }

            else -> null

        }
    }
}