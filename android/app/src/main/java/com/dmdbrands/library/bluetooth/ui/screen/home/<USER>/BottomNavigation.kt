package com.dmdbrands.library.bluetooth.ui.screen.home.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.dmdbrands.library.bluetooth.model.BottomNavItem
import com.dmdbrands.library.bluetooth.model.HomeBottomNavItem
import com.dmdbrands.library.bluetooth.ui.theme.IconSecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import kotlin.collections.listOf


@Composable
fun BottomNavigation(
    bottomNavItems: List<BottomNavItem>,
    navController: NavHostController,
) {

    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route
    var selectedItem by remember { mutableStateOf(bottomNavItems[0]) }


    LaunchedEffect(currentRoute) {
        if (currentRoute != null) {
            selectedItem = bottomNavItems.find { it.route::class.qualifiedName == currentRoute }
                ?: bottomNavItems[0]
        }
    }
    NavigationBar(
        containerColor = White
    ) {

        Row(
            modifier = Modifier.fillMaxWidth()
        ) {
            bottomNavItems.forEach { item ->
                val isSelected = selectedItem.route == item.route
                val color = if (isSelected) White else IconSecondaryColor

                NavigationBarItem(
                    colors = NavigationBarItemDefaults.colors(indicatorColor = PrimaryColor),
                    icon = {
                        if (item.icon != null) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                Icon(
                                    imageVector = item.icon!!,
                                    contentDescription = item.label,
                                    tint = color
                                )
                            }
                        }
                    },
                    label = {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                        ) {
                            Text(
                                text = item.label,
                                color = if (isSelected) PrimaryColor else IconSecondaryColor,
                                fontSize = 8.sp,
                                textAlign = TextAlign.Center
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            HorizontalDivider(
                                color = if (isSelected) PrimaryColor else Transparent,
                                thickness = 2.dp,
                                modifier = Modifier.fillMaxWidth(0.65f)
                            )
                        }
                    },
                    selected = isSelected,
                    onClick = {
                        navController.navigate(item.route) {
                            popUpTo(bottomNavItems[0].route) { saveState = true }
                            launchSingleTop = true
                            restoreState = true
                        }
                    })
            }

        }
    }
}

@Composable
@Preview(showBackground = true)
fun HomeBottomNavigationPreview() {
    BottomNavigation(
        bottomNavItems = listOf(
            HomeBottomNavItem.ScanDevice,
            HomeBottomNavItem.PairedDevice,
            HomeBottomNavItem.Apps,
            HomeBottomNavItem.History,
            HomeBottomNavItem.Devices,
            HomeBottomNavItem.Settings,
        ), navController = rememberNavController()
    )
}



