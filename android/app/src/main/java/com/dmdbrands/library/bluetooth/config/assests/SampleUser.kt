package com.dmdbrands.library.bluetooth.config.assests

import com.dmdbrands.library.bluetooth.model.UserDetails
import com.example.utilities.util.CalendarUtil

val sampleUserDetail = UserDetails(
    name = "Guest",
    email = "<EMAIL>",
    gender = "Male",
    height = "150",
    weight = "80",
    goalWeight = "60",
    birthday = CalendarUtil.getCurrentDate(),
    goalType = "Maintain Weight",
    unitType = "kgs and cm",
    isAthlete = false,
)