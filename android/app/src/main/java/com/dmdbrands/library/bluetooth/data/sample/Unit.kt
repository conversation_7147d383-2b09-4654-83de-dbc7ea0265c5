package com.dmdbrands.library.bluetooth.data.sample

import com.dmdbrands.library.bluetooth.model.UnitData
import com.dmdbrands.library.ggbluetooth.enums.GGBTSettingType
import com.greatergoods.ggbluetoothsdk.external.enums.TemperatureUnit
import com.greatergoods.ggbluetoothsdk.external.enums.WeightUnit

object Unit {
    val WeightUnitList: List<UnitData<WeightUnit>> = listOf(
        UnitData("lb_oz", WeightUnit.WEIGHT_POUND_OUNCE, "lb:oz"),
        UnitData("g", WeightUnit.WEIGHT_GRAMS, "g"),
        UnitData("ml", WeightUnit.WEIGHT_MILLI_LITRE_WATER, "ml"),
        UnitData("ml_milk", WeightUnit.WEIGHT_MILLI_LITRE_MILK, "ml - m"),
        UnitData("fl_oz", WeightUnit.WEIGHT_FLUID_OUNCE_WATER, "fl'oz"),
        UnitData("fl_oz_milk", WeightUnit.WEIGHT_FLUID_OUNCE_MILK, "fl'oz - m")
    )
    val SmartBabyWeightUnitList: List<UnitData<WeightUnit>> = listOf(
        UnitData("lb_oz", WeightUnit.WEIGHT_LB_OZ, "lb:oz"),
        UnitData("lb", WeightUnit.WEIGHT_POUNDS, "lb"),
        UnitData("kg", WeightUnit.WEIGHT_KILOGRAMS, "kg"),
    )

    val WeightGurusWeightUnitList: List<UnitData<WeightUnit>> = listOf(
        UnitData("lb", WeightUnit.WEIGHT_POUNDS, "lb"),
        UnitData("kg", WeightUnit.WEIGHT_KILOGRAMS, "kg")
    )

    val TemperatureUnitList: List<UnitData<TemperatureUnit>> = listOf(
        UnitData("\u2109", TemperatureUnit.TEMPERATURE_FAHRENHEIT, "\u2109"),
        UnitData("\u2103", TemperatureUnit.TEMPERATURE_CELSIUS, "\u2103")
    )

    val TemperatureModeList: List<UnitData<String>> = listOf(
        UnitData("On", GGBTSettingType.MUTE_MODE_ON, "Off"),
        UnitData("Off", GGBTSettingType.MUTE_MODE_OFF, "On")
    )


}