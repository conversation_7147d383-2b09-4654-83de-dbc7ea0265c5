package com.dmdbrands.library.bluetooth.data.repository

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import com.dmdbrands.library.bluetooth.data.api.IOperationAPI
import com.dmdbrands.library.bluetooth.data.db.dao.HistoryDao
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.HistoryData
import com.dmdbrands.library.bluetooth.model.api.BHOperationRequest
import com.dmdbrands.library.bluetooth.model.interfaces.IAppConfiguration
import com.dmdbrands.library.bluetooth.model.interfaces.IDeviceRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IHistoryRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IUserRepository
import com.dmdbrands.library.bluetooth.util.CommonUtil.toHistoryData
import com.dmdbrands.library.bluetooth.util.HistoryUtil.toBHEntryRequest
import com.dmdbrands.library.bluetooth.util.HistoryUtil.toReformedDetailItem
import com.dmdbrands.library.bluetooth.util.HistoryUtil.toWGEntryRequest
import com.dmdbrands.library.bluetooth.util.OperationUtil
import com.dmdbrands.library.bluetooth.util.Source
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.dmdbrands.library.ggbluetooth.model.GGEntry
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.ResponseBody
import retrofit2.Response
import javax.inject.Inject

class HistoryRepository @Inject constructor(
    private val operationAPI: IOperationAPI,
    private val historyDao: HistoryDao,
    private val userRepository: IUserRepository,
    private val deviceRepository: IDeviceRepository,
    private val appConfiguration: IAppConfiguration
) :
    IHistoryRepository {
    private val _localHistoryList = MutableLiveData<List<HistoryData>>(listOf())
    private val _remoteHistoryList = MutableLiveData<List<HistoryData>>()

    init {
        CoroutineScope(Dispatchers.Main).launch {
            fetchLocalHistory()
        }
    }

    override suspend fun subscribeRemoteHistory(): Flow<List<HistoryData>> {
        return _remoteHistoryList.asFlow()
    }

    override suspend fun subscribeLatestEntry(broadCastId: String): Flow<HistoryData?> {
        return _localHistoryList.asFlow()
            .map { history ->
                // Check in both lists
                val latestEntry = history
                    .filter { it.broadcastId == broadCastId }.maxByOrNull { it.entryTimestamp }
                latestEntry?.copy(
                    detailItems = latestEntry.detailItems.toReformedDetailItem(
                        latestEntry.appType,
                        source = Source.BLUETOOTH,
                        unit = latestEntry.unit
                    )
                )
            }
    }

    override suspend fun subscribeHistory(broadCastId: String): Flow<List<HistoryData>> {
        return _localHistoryList.asFlow()
            .map { local ->
                val filteredEntries =
                    local.filter { it.broadcastId == broadCastId }.map { historyData ->
                        historyData.copy(
                            detailItems = historyData.detailItems.toReformedDetailItem(
                                historyData.appType,
                                source = Source.BLUETOOTH,
                                unit = historyData.unit
                            )
                        )
                    }
                filteredEntries
            }
    }

    override suspend fun subscribeLocalHistory(): Flow<List<HistoryData>> {
        return _localHistoryList.asFlow().map { historyList ->
            historyList.map { historyData ->
                historyData.copy(
                    detailItems = historyData.detailItems.toReformedDetailItem(
                        historyData.appType,
                        source = Source.BLUETOOTH,
                        unit = historyData.unit
                    )
                )
            }
        }
    }


    override suspend fun addHistory(historyData: HistoryData) {
        val currentDevice = deviceRepository.getDevice(historyData.broadcastId!!)
        val currentList = _localHistoryList.value!!.toMutableList()
        val updatedData = historyData.copy(appType = currentDevice?.getAppType() ?: GGAppType.NONE)
        currentList.add(updatedData)
        _localHistoryList.value = currentList
        withContext(IO) {
            historyDao.addHistory(historyData.toHistoryEntity())
        }
    }

    override suspend fun addBulkHistory(
        historyData: List<GGEntry>,
        device: (BLEDevice?) -> Unit
    ) {
        val currentList = _localHistoryList.value!!.toMutableList()
        var currentDevice: BLEDevice? = null

        val updatedList = withContext(IO) {
            historyData.mapNotNull { newData ->
                currentDevice = deviceRepository.getDevice(newData.broadcastId)
                val exists =
                    currentList.any { it.entryTimestamp == newData.date.toString() && it.broadcastId == newData.broadcastId }

                if (exists) return@mapNotNull null
                newData.toHistoryData(
                    currentDevice?.getAppType() ?: GGAppType.NONE,
                    sku = currentDevice?.sku
                )
            }
        }
        device(currentDevice)
        currentList.addAll(updatedList)
        _localHistoryList.value = currentList
        withContext(IO) {
            historyDao.addBulkHistory(updatedList.map { it.toHistoryEntity() })
        }
        if (currentDevice?.source == Source.API || currentDevice?.isLinked == true) {
            sendDataToApi(updatedList)
        }

    }

    override suspend fun removeHistory(historyId: Long) {
        _localHistoryList.value = _localHistoryList.value?.filterNot { it.id == historyId }
        withContext(IO) {
            historyDao.removeHistory(historyId)
        }
    }

    override suspend fun refresh() {
        _localHistoryList.value = listOf()
        _remoteHistoryList.value = listOf()
        fetchLocalHistory()
    }

    override suspend fun fetchLocalHistory() {
        val localHistories = withContext(IO) {
            historyDao.getLocalHistory().map { it.toHistoryData() }
        }
        _localHistoryList.value = localHistories
    }

    override suspend fun getAppHistory(appType: String): List<HistoryData> {
        appConfiguration.setAppType(appType)
        val response = when (appType) {
            GGAppType.BALANCE_HEALTH -> {
                val apiResponse = operationAPI.bhOperations("")
                if (apiResponse.isSuccessful && apiResponse.body() != null) {
                    apiResponse.body()
                } else null
            }

            GGAppType.WEIGHT_GURUS -> {
                val apiResponse = operationAPI.wgOperations("")
                if (apiResponse.isSuccessful && apiResponse.body() != null) {
                    apiResponse.body()
                } else null
            }

            GGAppType.SMART_BABY -> {
                val sbUser = userRepository.getUser(appType)
                if (sbUser?.subCategory?.second == null) {
                    return listOf()
                }
                val apiResponse = operationAPI.getSBOperation(sbUser.subCategory.second!!)
                if (apiResponse.isSuccessful && apiResponse.body() != null) {
                    apiResponse.body()
                } else null
            }

            else -> null
        }
        val histories = response?.operations?.map {
            it.toHistoryData(appType)
        } ?: listOf()

        _remoteHistoryList.value = withContext(IO) {
            val existingHistories =
                _remoteHistoryList.value?.filter { it.appType != appType } ?: listOf()

            // Process new histories
            val newHistories = histories.groupBy { it.entryId ?: it.entryTimestamp }
                .filterValues { entries ->
                    entries.all { it.operation != "delete" }
                }.values.flatten()
                .map {
                    it.copy(
                        unit = it.unit ?: OperationUtil.getMainUnit(appType),
                        detailItems = it.detailItems.toReformedDetailItem(
                            appType,
                            Source.API,
                            it.unit
                        )
                    )
                }

            // Merge histories, replacing entries with same app type
            (existingHistories + newHistories)
                .groupBy { it.appType }
                .flatMap { (_, histories) ->
                    // For each app type, take the most recent entries
                    histories.distinctBy { it.entryTimestamp }
                }
                .sortedByDescending { it.opTimestamp }

        }

        return _remoteHistoryList.value?.toList()?.filter {
            it.appType == appType
        } ?: listOf()

    }

    private suspend fun sendDataToApi(historyData: List<HistoryData>): Response<Boolean>? {
        if (historyData.isEmpty()) {
            return Response.error(400, ResponseBody.create(null, "No history data provided"))
        }

        val appType = historyData.first().appType
        appConfiguration.setAppType(appType)

        val userId = userRepository.users?.find { it.appType == appType }?.id
        if (userId == null) {
            return Response.error(400, ResponseBody.create(null, "User ID not found"))
        }

        return when (appType) {
            GGAppType.BALANCE_HEALTH -> {
                val historyRequest = historyData.map { it.toBHEntryRequest(userId) }
                val apiResponse = operationAPI.addBHOperation(
                    BHOperationRequest(operations = historyRequest)
                )

                if (apiResponse.isSuccessful) {
                    Response.success(true)
                } else {
                    Response.error(
                        apiResponse.code(),
                        apiResponse.errorBody() ?: ResponseBody.create(
                            null,
                            "Error adding BH operation"
                        )
                    )
                }
            }

            GGAppType.WEIGHT_GURUS -> {
                val historyRequest = historyData.first().copy(
                    detailItems = historyData.first().detailItems.toReformedDetailItem(
                        appType,
                        Source.SEND
                    )
                ).toWGEntryRequest()

                val apiResponse = operationAPI.addWGOperation(historyRequest)

                if (apiResponse.isSuccessful) {
                    Response.success(true)
                } else {
                    Response.error(
                        apiResponse.code(),
                        apiResponse.errorBody() ?: ResponseBody.create(
                            null,
                            "Error adding WG operation"
                        )
                    )
                }
            }

            else -> null
        }
    }
}
