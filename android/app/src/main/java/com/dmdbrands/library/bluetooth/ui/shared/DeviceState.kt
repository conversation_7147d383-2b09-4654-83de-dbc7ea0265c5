package com.dmdbrands.library.bluetooth.ui.shared

import android.util.Log
import androidx.compose.runtime.Immutable
import com.dmdbrands.library.bluetooth.config.AppConfig
import com.dmdbrands.library.bluetooth.model.AppStatus
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.example.utilities.modal.interfaces.IReducer

@Immutable
sealed class DeviceState(
    open val localDevices: List<BLEDevice>? = null,
    open val localRefreshing: Boolean = false,
    open val appType: String? = AppConfig.APPS_LIST[0],
) : IReducer.State {
    fun getFilteredLocalDevices(): List<BLEDevice>? {
        return if (appType == GGAppType.ALL) {
            localDevices
        } else {
            localDevices?.filter {
                it.getAppType() == appType
            }
        }
    }
}

data class PairedDeviceState(
    override val localDevices: List<BLEDevice>? = null,
    val remoteDevices: List<BLEDevice>? = null,
    override val localRefreshing: Boolean = false,
    val remoteRefreshing: Boolean = false,
    val appStatus: List<AppStatus> = listOf(),
    override val appType: String? = AppConfig.APPS_LIST[0]
) : DeviceState(localDevices, localRefreshing, appType) {
    fun getFilteredRemoteDevices(): List<BLEDevice>? {
        return if (appType == GGAppType.ALL) {
            remoteDevices
        } else {
            remoteDevices?.filter {
                it.getAppType() == appType
            }
        }
    }
}

data class ScanDeviceState(
    override val localDevices: List<BLEDevice>? = null,
    override val localRefreshing: Boolean = false,
    override val appType: String? = AppConfig.APPS_LIST[0]
) : DeviceState(localDevices, localRefreshing, appType)
