package com.dmdbrands.library.bluetooth.data

import android.net.Uri
import androidx.lifecycle.MutableLiveData
import com.dmdbrands.library.bluetooth.config.AppConfig
import com.dmdbrands.library.bluetooth.model.interfaces.IAppConfiguration
import com.dmdbrands.library.ggbluetooth.enums.GGAppType

class AppConfiguration : IAppConfiguration {
    private val _currentAppType = MutableLiveData<String?>(null)

    override fun setAppType(appType: String?) {
        _currentAppType.value = appType
    }

    override fun getAppType(): String {
        return _currentAppType.value ?: GGAppType.NONE
    }

    override fun getBaseUrl(): String {
        val url = Uri.parse(getAPIkey())
        return url?.host ?: throw IllegalArgumentException("Invalid URL: ${getAPIkey()}")
    }

    override fun getApiVersionPath(): String {
        val url = Uri.parse(getAPIkey())
        return url?.encodedPath ?: throw IllegalArgumentException("Invalid URL: ${getAPIkey()}")
    }

    override fun getScheme(): String {
        val url = Uri.parse(getAPIkey())
        return url?.scheme ?: throw IllegalArgumentException("Invalid URL: ${getAPIkey()}")
    }

    private fun getAPIkey(): String {
        return when (_currentAppType.value) {
            GGAppType.BALANCE_HEALTH -> AppConfig.BH_API_URL
            GGAppType.SMART_BABY -> AppConfig.SB_API_URL
            GGAppType.WEIGHT_GURUS -> AppConfig.WG_API_URL
            else -> AppConfig.WG_API_URL
        }
    }
}
