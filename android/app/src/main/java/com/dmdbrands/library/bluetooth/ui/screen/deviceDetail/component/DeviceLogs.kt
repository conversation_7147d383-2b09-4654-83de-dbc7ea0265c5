package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Download
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.model.DeviceLogs
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.DeviceDetailReducer
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.sheet.DetailViewSheet
import com.dmdbrands.library.bluetooth.ui.theme.DisabledIconColor
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.component.list.StaticList

@Composable
fun DeviceLogs(
    deviceLog: DeviceLogs,
    onDismiss: () -> Unit,
    handleIntent: (DeviceDetailReducer.Intent) -> Unit
) {
    LaunchedEffect(Unit) {
        if (deviceLog.fetchStatus < 100.0f)
            handleIntent(DeviceDetailReducer.Intent.fetchLogs)
    }
    DetailViewSheet(title = "Device Logs", onDismiss = onDismiss, action = {
        AppButton(
            onClick = {
                handleIntent(DeviceDetailReducer.Intent.saveLogsToMemory)
            },
            enabled = deviceLog.fetchStatus == 100.0f,
            containerColor = Transparent,
            disabledContainerColor = Transparent
        ) { enabled ->
            Icon(
                imageVector = Icons.Default.Download,
                contentDescription = null,
                tint = if (enabled) PrimaryColor else DisabledIconColor,
            )
        }
    }) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            if (deviceLog.fetchStatus < 100.0f) {
                LinearProgressIndicator(
                    modifier = Modifier.height(6.dp),
                    progress = { deviceLog.fetchStatus / 100 },
                    color = PrimaryColor,
                    gapSize = (-6).dp,
                    drawStopIndicator = { false }
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "Fetching logs: ${deviceLog.fetchStatus.toInt()}%",
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(6.dp),
                    fontSize = 16.sp,
                    fontFamily = OpenSansFont,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )
            } else {
                Box(
                    modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.CenterEnd
                ) {

                }
                StaticList(deviceLog.logs.mapNotNull { it.log }.reversed()) { index, item ->
                    Text(
                        text = item,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(6.dp),
                        fontSize = 12.sp,
                        fontFamily = OpenSansFont,
                    )
                }
            }
        }
    }
}