package com.dmdbrands.library.bluetooth.model

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Apps
import androidx.compose.material.icons.outlined.BluetoothConnected
import androidx.compose.material.icons.outlined.DeviceHub
import androidx.compose.material.icons.outlined.Devices
import androidx.compose.material.icons.outlined.Search
import androidx.compose.material.icons.outlined.Settings
import androidx.compose.material.icons.outlined.WorkHistory
import androidx.compose.ui.graphics.vector.ImageVector
import com.dmdbrands.library.bluetooth.config.AppRoute

sealed class BottomNavItem(
    open val route: Any,
    open val icon: ImageVector?,
    open val label: String,
    open val baseRoute: Any = AppRoute.Home
)

sealed class HomeBottomNavItem(
    override val route: AppRoute,
    override val icon: ImageVector,
    override val label: String,
    override val baseRoute: AppRoute = AppRoute.Home
) : BottomNavItem(route, icon, label, baseRoute) {
    data object ScanDevice :
        HomeBottomNavItem(AppRoute.ScanDevices, Icons.Outlined.Search, "Scan")

    data object Devices : HomeBottomNavItem(AppRoute.Devices, Icons.Outlined.DeviceHub, "Devices")
    data object PairedDevice :
        HomeBottomNavItem(AppRoute.PairedDevice, Icons.Outlined.BluetoothConnected, "Paired Device")

    data object History : HomeBottomNavItem(AppRoute.History, Icons.Outlined.WorkHistory, "History")
    data object Settings : HomeBottomNavItem(AppRoute.Settings, Icons.Outlined.Settings, "Settings")
    data object Apps : HomeBottomNavItem(AppRoute.Apps, Icons.Outlined.Apps, "Apps")

}


open class DeviceBottomNavItem(
    override val route: Int,
    override val label: String,
    override val icon: ImageVector? = null,
    override val baseRoute: AppRoute = AppRoute.Home
) : BottomNavItem(route, icon, label, baseRoute) {
    data object BasicDetail :
        DeviceBottomNavItem(0, "Info")

    data object History :
        DeviceBottomNavItem(1, "History")

    data object Users :
        DeviceBottomNavItem(2, "Users")

    data object LiveMeasurement :
        DeviceBottomNavItem(3, "Measurement")

    data object DeviceSettings :
        DeviceBottomNavItem(4, "Settings")

}

sealed class AppDetailBottomNavItem(
    override val route: Any,
    override val label: String,
    override val icon: ImageVector? = null,
    override val baseRoute: Any = AppRoute.AppDetail
) : BottomNavItem(route, icon, label, baseRoute) {
    data object Devices : HomeBottomNavItem(AppRoute.AppDevices, Icons.Outlined.Devices, "Devices")
    data object History :
        HomeBottomNavItem(AppRoute.AppHistory, Icons.Outlined.WorkHistory, "History")

    data object Settings :
        HomeBottomNavItem(AppRoute.AppSettings, Icons.Outlined.Settings, "Settings")
}