package com.dmdbrands.library.bluetooth.core.component

import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.text.font.FontWeight
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.example.utilities.services.component.AppButton

@ExperimentalMaterial3Api
@Composable
fun SetAppTypeAlert(onDismiss: () -> Unit) {
    AlertDialog(
        onDismissRequest = onDismiss,
        confirmButton = {
            AppButton(onClick = onDismiss, containerColor = Transparent) {
                Text("OK")
            }
        },
        title = {
            Text(
                text = "Set App Type",
                fontWeight = FontWeight.Bold,
                fontFamily = OpenSansFont,
            )
        },
        text = {
            Text(
                text = "App type is required to start scanning and fetch devices.",
                fontFamily = OpenSansFont,
                fontWeight = FontWeight.Medium
            )
        }
    )
}