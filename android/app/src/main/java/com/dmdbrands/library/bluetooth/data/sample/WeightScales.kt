package com.dmdbrands.library.bluetooth.data.sample

data class WeightScale(
    val productName: String,
    val sku: String,
    val imgPath: String? = null, // Optional field, can be null
    val setupType: String,
    val bodyComp: Boolean
)

val weightScales = listOf(
    WeightScale(productName = "Bluetooth Smart Scale", sku = "0375", setupType = "bluetooth", bodyComp = false),
    WeightScale(productName = "Bluetooth Smart Scale", sku = "0376", setupType = "bluetooth", bodyComp = false),
    WeightScale(productName = "Bluetooth Smart Scale", sku = "0380", setupType = "bluetooth", bodyComp = false),
    WeightScale(productName = "Bluetooth Smart Scale", sku = "0382", setupType = "bluetooth", bodyComp = true)
)
