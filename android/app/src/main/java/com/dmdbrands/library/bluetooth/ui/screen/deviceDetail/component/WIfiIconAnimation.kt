package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component

import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Wifi
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor

@Composable
fun WifiIconAnimation(
    boxSize: Dp = 200.dp,              // Outer box size
    circleSizeRatio: Float = 0.3f   // Inner circle size relative to outer
) {
    val infiniteTransition = rememberInfiniteTransition()

    // Pulse animation values
    val pulseProgress by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = 1.5f,
        animationSpec = infiniteRepeatable(
            animation = tween(1800, easing = LinearOutSlowInEasing),
            repeatMode = RepeatMode.Restart
        )
    )

    val pulseAlpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 0f,
        animationSpec = infiniteRepeatable(
            animation = tween(1800),
            repeatMode = RepeatMode.Restart
        )
    )

    // Blinking central circle
    val blinkAlpha by infiniteTransition.animateFloat(
        initialValue = 0.6f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000),
            repeatMode = RepeatMode.Reverse
        )
    )

    Box(
        modifier = Modifier.size(boxSize),
        contentAlignment = Alignment.Center
    ) {
        // Pulse Circle Animation (Canvas for precise drawing)
        Canvas(modifier = Modifier.matchParentSize()) {
            val center = Offset(size.width / 2, size.height / 2)
            val innerRadius = size.minDimension * circleSizeRatio / 2
            val animatedRadius = innerRadius * pulseProgress

            drawCircle(
                color = PrimaryColor.copy(alpha = pulseAlpha),
                radius = animatedRadius,
                center = center
            )
        }

        // Blinking solid circle with icon
        val innerSize = boxSize * circleSizeRatio
        Box(
            modifier = Modifier
                .size(innerSize)
                .background(PrimaryColor.copy(alpha = blinkAlpha), CircleShape),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.Wifi,
                contentDescription = "Wi-Fi Icon",
                tint = Color.White,
                modifier = Modifier.size(innerSize * 0.5f)
            )
        }
    }
}


@PreviewScreens
@Composable
private fun WifiIconAnimationPreview() {
    WifiIconAnimation()
}
