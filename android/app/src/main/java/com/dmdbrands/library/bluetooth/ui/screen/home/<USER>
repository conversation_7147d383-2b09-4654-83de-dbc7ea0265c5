package com.dmdbrands.library.bluetooth.ui.screen.home

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.rememberNavController
import com.dmdbrands.library.bluetooth.config.AppRoute
import com.dmdbrands.library.bluetooth.model.HomeBottomNavItem
import com.dmdbrands.library.bluetooth.ui.screen.home.component.BottomNavigation
import com.dmdbrands.library.bluetooth.ui.screen.home.component.HomeNavGraph
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.shared.component.NavigationObserver
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.SurfaceBackgroundColor
import com.example.utilities.services.viewmodel.UtilitiesViewmodel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(isScanning: Boolean = false) {
    val navController = rememberNavController()
    val viewmodel = hiltViewModel<UtilitiesViewmodel>()

    NavigationObserver(viewmodel.appUtility.navigationIntent, navController, AppRoute.Home)
    Scaffold(
        bottomBar = {
            BottomNavigation(
                listOf(
                    HomeBottomNavItem.ScanDevice,
                    HomeBottomNavItem.PairedDevice,
                    HomeBottomNavItem.Apps,
                    HomeBottomNavItem.History,
                    HomeBottomNavItem.Devices,
                    HomeBottomNavItem.Settings,
                ), navController
            )
        },
    ) {
        Column(
            Modifier
                .fillMaxSize()
                .background(SurfaceBackgroundColor)
                .padding(bottom = it.calculateBottomPadding())
        ) {
            viewmodel.setFabPadding(it.calculateBottomPadding() - 16.dp)
            HomeNavGraph(isScanning = isScanning, navController = navController)

        }
    }
}

@PreviewScreens
@Composable
fun HomeScreenPreview() {
    GGBluetoothLibraryTheme {
        HomeScreen()
    }
}
