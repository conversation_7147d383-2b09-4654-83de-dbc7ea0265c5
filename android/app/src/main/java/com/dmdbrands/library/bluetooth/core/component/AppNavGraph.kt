package com.dmdbrands.library.bluetooth.core.component

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import com.dmdbrands.library.bluetooth.config.AppRoute
import com.dmdbrands.library.bluetooth.ui.screen.appDetail.AppDetail
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.DeviceDetail
import com.dmdbrands.library.bluetooth.ui.screen.deviceInfo.DeviceInfoScreen
import com.dmdbrands.library.bluetooth.ui.screen.home.HomeScreen
import com.dmdbrands.library.bluetooth.ui.screen.settings.local.component.PermissionSection
import com.dmdbrands.library.bluetooth.ui.screen.settings.local.component.UserSection


@Composable
fun AppNavGraph(
    isScanning: Boolean = false,
    navController: NavHostController,
    startDestination: Any = AppRoute.Home
) {
    NavHost(navController = navController, startDestination = startDestination) {
        composable<AppRoute.Home> {
            HomeScreen(isScanning)
        }
        composable<AppRoute.AppDetail> { backStackEntry ->
            backStackEntry.toRoute<AppRoute.AppDetail>().also {
                AppDetail(it.appType)
            }
        }

        composable<AppRoute.DeviceDetail> { backStackEntry ->
            backStackEntry.toRoute<AppRoute.DeviceDetail>().also {
                DeviceDetail(it.broadcastId)
            }
        }
        composable<AppRoute.DeviceInfo> { backStackEntry ->
            backStackEntry.toRoute<AppRoute.DeviceInfo>().also {
                DeviceInfoScreen(it.id)
            }
        }
        composable<AppRoute.Profile> {
            UserSection()
        }

        composable<AppRoute.Permission> {
            PermissionSection()
        }

    }
}