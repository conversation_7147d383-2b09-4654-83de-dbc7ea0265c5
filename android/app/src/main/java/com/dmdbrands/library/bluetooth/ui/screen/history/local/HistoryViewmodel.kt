package com.dmdbrands.library.bluetooth.ui.screen.history.local

import androidx.lifecycle.viewModelScope
import com.dmdbrands.library.bluetooth.model.AppStatus
import com.dmdbrands.library.bluetooth.model.interfaces.IAppConfiguration
import com.dmdbrands.library.bluetooth.model.interfaces.IHistoryRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IUserRepository
import com.dmdbrands.library.bluetooth.ui.screen.history.HistoryIntent
import com.dmdbrands.library.bluetooth.ui.screen.history.HistoryReducer
import com.dmdbrands.library.bluetooth.ui.screen.history.HistoryState
import com.example.utilities.services.viewmodel.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class HistoryViewmodel @Inject constructor(
    private val historyRepository: IHistoryRepository,
    private val userRepository: IUserRepository,
    private val appConfiguration: IAppConfiguration
) :
    BaseViewModel<HistoryState, HistoryIntent>(
        HistoryState(),
        HistoryReducer()
    ) {
    init {
        fetchLocalHistory()
        fetchRemoteHistory()
        fetchAppStatus()
    }

    private fun fetchLocalHistory() {
        handleIntent(HistoryIntent.AlterLocalRefreshing(true))
        viewModelScope.launch {
            historyRepository.subscribeLocalHistory().collect {
                handleIntent(HistoryIntent.AddBulkHistory(it))
                handleIntent(HistoryIntent.AlterLocalRefreshing(false))
            }
        }
    }

    private fun fetchRemoteHistory() {
        viewModelScope.launch {
            historyRepository.subscribeRemoteHistory().collect {
                handleIntent(HistoryIntent.AddRemoteHistory(it))
            }
        }
    }

    private fun fetchAppStatus() {
        viewModelScope.launch {
            handleIntent(HistoryIntent.AlterRemoteRefreshing(true))
            userRepository.subscribeAppStatus().collect {
                if (!state.value.remoteRefreshing) {
                    handleIntent(HistoryIntent.AlterRemoteRefreshing(true))
                }
                handleIntent(HistoryIntent.ModifyAppStatus(it.filter { it.token != null }))
                loadAPIHistory(it.filter { it.token != null })
                handleIntent(HistoryIntent.AlterRemoteRefreshing(false))
            }
        }
    }

    private suspend fun loadAPIHistory(filter: List<AppStatus>) {
        val previousAppType = appConfiguration.getAppType()
        filter.forEach {
            appConfiguration.setAppType(it.type)
            historyRepository.getAppHistory(it.type)
        }
        appConfiguration.setAppType(previousAppType)
    }

    fun removeHistory(historyId: Long) {
        viewModelScope.launch {
            historyRepository.removeHistory(historyId)
        }
    }

    override fun handleIntent(intent: HistoryIntent) {
        super.handleIntent(intent)
    }

    fun refresh() {
        handleIntent(HistoryIntent.AlterLocalRefreshing(true))
        handleIntent(HistoryIntent.AlterRemoteRefreshing(true))
        viewModelScope.launch {
            historyRepository.refresh()
            handleIntent(HistoryIntent.AlterLocalRefreshing(false))
        }
        viewModelScope.launch {
            loadAPIHistory(state.value.appStatus)
            handleIntent(HistoryIntent.AlterRemoteRefreshing(false))
        }
    }


}