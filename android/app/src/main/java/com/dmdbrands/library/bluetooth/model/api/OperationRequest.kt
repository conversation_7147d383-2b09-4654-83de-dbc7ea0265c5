package com.dmdbrands.library.bluetooth.model.api

data class BHEntryRequest(
    val diastolic: Int,
    val entryTimestamp: String,
    val operation: String,
    val pulse: Int,
    val systolic: Int,
    val type: String,
    val userId: String
)

data class BHOperationRequest(
    val operations: List<BHEntryRequest>
)

data class WGOperationRequest(
    val operationType: String,
    val entryTimestamp: String,  // ISO date string
    val weight: Int,
    val bodyFat: Int?,
    val muscleMass: Int?,
    val water: Int?,
    val bmi: Int?,
    val source: String  // Values: 'wifi scale', 'bluetooth scale', 'lcbt', or 'manual'
)
