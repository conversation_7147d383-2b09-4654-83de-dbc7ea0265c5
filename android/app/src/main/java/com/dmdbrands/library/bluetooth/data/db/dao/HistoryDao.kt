package com.dmdbrands.library.bluetooth.data.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import com.dmdbrands.library.bluetooth.data.db.entity.HistoryEntity

@Dao
interface HistoryDao {

    @Query("SELECT * FROM history")
    suspend fun getLocalHistory(): List<HistoryEntity>

    @Query("DELETE FROM history WHERE id = :id")
    suspend fun removeHistory(id: Long)

    @Insert
    suspend fun addHistory(historyEntity: HistoryEntity)

    @Insert
    suspend fun addBulkHistory(historyEntities: List<HistoryEntity>)
}