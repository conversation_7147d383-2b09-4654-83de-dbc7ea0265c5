package com.dmdbrands.library.bluetooth.util

import com.dmdbrands.library.bluetooth.model.DetailItem
import com.dmdbrands.library.bluetooth.model.HistoryData
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.dmdbrands.library.ggbluetooth.model.GGEntry
import com.example.utilities.services.viewmodel.UtilitiesViewmodel
import com.example.utilities.util.CalendarUtil
import retrofit2.Response
import kotlin.reflect.KProperty1
import kotlin.reflect.full.memberProperties

object CommonUtil {

    fun <T : Any> T.toDetailItems(
        excludedProperties: Set<String> = setOf(),
        includedProperties: Set<String>? = null,
        valueMappers: Map<String, (Any?) -> Any?> = emptyMap()
    ): List<DetailItem> {
        return this::class.memberProperties
            .filterIsInstance<KProperty1<T, *>>()
            .filter { property ->
                includedProperties?.contains(property.name)
                    ?: (property.name !in excludedProperties)
            }
            .mapNotNull { property ->
                try {
                    val value = property.get(this)

                    DetailItem(
                        name = property.name,
                        value = valueMappers[property.name]?.invoke(value) ?: value,
                    )
                } catch (e: Exception) {
                    println("Error accessing property '${property.name}': ${e.message}")
                    null
                }
            }
    }


    fun <T : GGEntry> T.toHistoryData(appType: String, sku: String? = null): HistoryData? {
        val detailItems = this.toDetailItems(
            excludedProperties = setOf(
                "date",
                "operationType",
                "unit"
            )
        )
        val timeStamp =
            if (this.date != 0L && appType != GGAppType.SMART_BABY) this.date else CalendarUtil.getCurrentDate().timeInMillis
        val weight = detailItems.find { it.name == "weight" }?.value as Float?
        return if (weight != null && weight <= 0f) {
            null
        } else {
            HistoryData(
                appType = appType,
                unit = this.unit ?: OperationUtil.getMainUnit(appType),
                sku = sku,
                entryTimestamp = timeStamp.toString(),
                opTimestamp = timeStamp.toString(),
                operation = operationType.toString(),
                broadcastId = broadcastId,
                detailItems = detailItems
            )
        }
    }

    suspend fun <T> UtilitiesViewmodel.responseWrapper(
        response: Response<T>?,
        onSuccess: suspend () -> Unit = {}
    ) {
        if (response != null && response.isSuccessful) {
            onSuccess()
        } else {
            if (response?.code() == 401) {
                this.setAlert("Try Again!")
            } else
                this.setToastMessage(response?.message().toString())
        }
    }
}
