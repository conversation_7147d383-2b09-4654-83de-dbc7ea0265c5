package com.dmdbrands.library.bluetooth.util

import com.dmdbrands.library.bluetooth.model.DetailItem
import com.dmdbrands.library.bluetooth.model.HistoryData
import com.dmdbrands.library.bluetooth.model.api.BHEntryRequest
import com.dmdbrands.library.bluetooth.model.api.WGOperationRequest
import com.dmdbrands.library.bluetooth.util.DeviceUtil.displayMetricProperty
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.example.utilities.util.CalendarUtil

object HistoryUtil {

    fun getDisplayValue(appType: String, unit: String? = null): List<String> {
        return when (appType) {
            GGAppType.SAGE -> listOf("weight")
            GGAppType.BALANCE_HEALTH -> listOf("systolic", "diastolic")
            GGAppType.WEIGHT_GURUS -> if (unit == "kg") listOf("weightInKg") else listOf("weight")
            GGAppType.RPM -> if (unit == "\u2103") listOf("temperature") else listOf(
                "temperatureInFahrenheit",
                "bgm",
                "spO2",
                "pr"
            )

            GGAppType.SMART_BABY -> if (unit == "kg") listOf(
                "weightInKg",
                "babyWeightDecigrams"
            ) else listOf(
                "weight",
            )

            else -> listOf()
        }
    }

    fun getDisplayDetail(appType: String): List<String> {
        return when (appType) {
            GGAppType.SAGE -> listOf()
            GGAppType.BALANCE_HEALTH -> listOf("pulse")
            GGAppType.WEIGHT_GURUS -> displayMetricProperty
            GGAppType.RPM -> listOf("pulseAmplitudeIndex")
            else -> listOf()
        }

    }

    fun List<DetailItem>.toReformedDetailItem(
        appType: String = GGAppType.NONE,
        source: String = Source.BLUETOOTH,
        unit: String? = null
    ): List<DetailItem> {
        val propertyUnit = OperationUtil.unitMappers(appType)
        val propertyIcons = OperationUtil.iconMappers(appType)
        val valueMappers = OperationUtil.getMapper(appType, source, unit)
        return this.map {
            it.copy(
                unit = propertyUnit[it.name] ?: it.unit,
                icon = propertyIcons[it.name] ?: it.icon,
                value = valueMappers[it.name]?.invoke(it.value) ?: it.value
            )
        }
    }

    fun HistoryData.toBHEntryRequest(userId: String): BHEntryRequest {
        return BHEntryRequest(
            diastolic = (this.detailItems.find { it.name == "diastolic" }?.value ?: 0) as Int,
            systolic = (this.detailItems.find { it.name == "systolic" }?.value ?: 0) as Int,
            pulse = (this.detailItems.find { it.name == "pulse" }?.value ?: 0) as Int,
            operation = "create",
            type = "device",
            entryTimestamp = this.entryTimestamp,
            userId = userId,
        )
    }

    fun HistoryData.toWGEntryRequest(): WGOperationRequest {
        return WGOperationRequest(
            weight = (this.detailItems.find { it.name == "weight" }?.value ?: 0) as Int,
            operationType = "create",
            source = "bluetooth scale",
            entryTimestamp = CalendarUtil.dateStringFromTimeStamp(
                timeStamp = this.entryTimestamp,
                pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
            ),
            bodyFat = (this.detailItems.find { it.name == "bodyFat" }?.value ?: 0) as Int?,
            muscleMass = this.detailItems.find { it.name == "muscleMass" }?.value as Int?,
            water = this.detailItems.find { it.name == "water" }?.value as Int?,
            bmi = this.detailItems.find { it.name == "bmi" }?.value as Int?,
        )
    }

}



