package com.dmdbrands.library.bluetooth.model.api

import com.dmdbrands.library.bluetooth.model.HistoryData
import com.dmdbrands.library.bluetooth.util.CommonUtil.toDetailItems
import com.example.utilities.util.CalendarUtil
import com.google.gson.annotations.SerializedName


sealed interface Entry {
    fun toHistoryData(
        appType: String,
    ): HistoryData
}

data class BHEntry(
    val diastolic: Int,
    val entryTimestamp: String,
    val meanArterial: String?,
    val note: String?,
    val opTimestamp: String,
    val operation: String,
    val pulse: Int,
    val systolic: Int,
    val type: String,
    val userId: String
) : Entry {
    override fun toHistoryData(
        appType: String,
    ): HistoryData {
        return HistoryData(
            appType = appType,
            entryId = entryTimestamp,
            entryTimestamp = entryTimestamp,
            opTimestamp = opTimestamp,
            operation = operation,
            detailItems = this.toDetailItems(
                excludedProperties = setOf("entryTimestamp", "opTimestamp", "operation"),
            )
        )
    }
}


data class WGEntry(
    val operationType: String,
    val entryTimestamp: String,
    val serverTimestamp: String,
    val weight: Int,
    val bodyFat: Int?,
    val muscleMass: Int?,
    val water: Int?,
    val bmi: Int?,
    val source: String
) : Entry {

    override fun toHistoryData(
        appType: String,
    ): HistoryData {
        return HistoryData(
            appType = appType,
            entryId = entryTimestamp,
            entryTimestamp = CalendarUtil.stringToCalender(entryTimestamp).timeInMillis.toString(),
            opTimestamp = CalendarUtil.stringToCalender(serverTimestamp).timeInMillis.toString(),
            unit = "lbs",
            operation = operationType,
            detailItems = this.toDetailItems(
                excludedProperties = setOf("entryTimestamp", "serverTimestamp", "operationType"),
            )
        )
    }
}


data class BabyEntry(
    @SerializedName("id")
    val id: String,

    @SerializedName("entryId")
    val entryId: String,

    @SerializedName("babyId")
    val babyId: String,

    @SerializedName("babyLengthMillimeters")
    val babyLengthMillimeters: Int?, // Nullable if the value is null in JSON

    @SerializedName("babyWeightDecigrams")
    val babyWeightDecigrams: Int?, // Nullable if the value is null in JSON

    @SerializedName("diaperType")
    val diaperType: String?, // Nullable if the value is null in JSON

    @SerializedName("entryNote")
    val entryNote: String,

    @SerializedName("entryTimestamp")
    val entryTimestamp: String, // Date with time (in ISO 8601 format)

    @SerializedName("entryType")
    val entryType: String,

    @SerializedName("feedingTimeSecondsLeft")
    val feedingTimeSecondsLeft: Int?, // Nullable if the value is null in JSON

    @SerializedName("feedingTimeSecondsRight")
    val feedingTimeSecondsRight: Int?, // Nullable if the value is null in JSON

    @SerializedName("feedingMilliliters")
    val feedingMilliliters: Int?, // Nullable if the value is null in JSON

    @SerializedName("operationType")
    val operationType: String,

    @SerializedName("photo")
    val photo: String?, // Nullable if the value is null in JSON

    @SerializedName("source")
    val source: String,

    @SerializedName("serverTimestamp")
    val serverTimestamp: String, // Date with time (in ISO 8601 format)

    @SerializedName("sleepTimeMinutes")
    val sleepTimeMinutes: Int? // Nullable if the value is null in JSON
) : Entry {
    override fun toHistoryData(appType: String): HistoryData {
        return HistoryData(
            appType = appType,
            entryId = entryId,
            entryTimestamp = convertToTimeStamp(entryTimestamp.toString()),
            opTimestamp = convertToTimeStamp(serverTimestamp.toString()),
            operation = operationType,
            unit = "kg",
            detailItems = this.toDetailItems(
                excludedProperties = setOf("entryTimestamp", "serverTimestamp", "operationType"),
            )
        )
    }

    private fun convertToTimeStamp(value: String): String {
        val calender = CalendarUtil.stringToCalender(
            value
        )
        return calender.timeInMillis.toString()
    }
}
