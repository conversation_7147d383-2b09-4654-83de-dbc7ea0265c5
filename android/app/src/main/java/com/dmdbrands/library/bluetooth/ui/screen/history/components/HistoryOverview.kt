package com.dmdbrands.library.bluetooth.ui.screen.history.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.util.DeviceUtil.getAppOutlinedIcon
import com.example.utilities.modal.ExpandedCardState
import com.example.utilities.services.theme.OpenSansFont
import com.example.utilities.services.theme.SurfaceBackgroundColor
import com.example.utilities.services.theme.TextPrimaryColor
import com.example.utilities.util.CalendarUtil

@Composable
fun HistoryOverview(
    id: Long,
    opTimestamp: String,
    sku: String? = null,
    appType: String,
    unit: String,
    value: String = "",
    state: ExpandedCardState,
) {
    val primaryColor = if (state.value.contains(id)) SurfaceBackgroundColor else TextPrimaryColor
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(end = 10.dp)
            .padding(vertical = 10.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceAround
    ) {
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.Start
        ) {
            Text(
                text = CalendarUtil.timestampToMonthYear(
                    opTimestamp.toLong(),
                    "MMM dd yyyy"
                ),
                color = primaryColor,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                fontFamily = OpenSansFont
            )
            Spacer(modifier = Modifier.size(4.dp))
            Text(
                text = CalendarUtil.timestampToMonthYear(
                    opTimestamp.toLong(),
                    "hh:mm a"
                ),
                color = primaryColor,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                fontFamily = OpenSansFont
            )
        }
        Icon(
            painter = painterResource(getAppOutlinedIcon(appType, sku)),
            contentDescription = null,
            modifier = Modifier.size(30.dp),
            tint = primaryColor
        )
        Row(
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.End,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = value, color = primaryColor,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                fontFamily = OpenSansFont
            )
            Spacer(modifier = Modifier.size(4.dp))
            Text(
                text = unit, color = primaryColor,
                fontSize = 12.sp,
                fontFamily = OpenSansFont,
                fontWeight = FontWeight.Medium
            )
        }
    }
}