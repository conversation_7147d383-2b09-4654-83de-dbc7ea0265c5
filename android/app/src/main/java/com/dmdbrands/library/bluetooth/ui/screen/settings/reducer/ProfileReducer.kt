package com.dmdbrands.library.bluetooth.ui.screen.settings.reducer

import com.dmdbrands.library.bluetooth.model.api.User
import com.dmdbrands.library.bluetooth.model.api.sampleBHUser
import com.example.utilities.modal.interfaces.IReducer
import com.example.utilities.util.CalendarUtil
import com.example.utilities.util.FormBuilder
import com.example.utilities.util.FormField
import com.example.utilities.util.FormValidations

enum class EUser {
    NAME, EMAIL, BIRTHDAY, GENDER, ZIPCODE
}

data class ProfileState(val user: User = sampleBHUser, val isRefreshing: Boolean = false) :
    IReducer.State {
    fun getUserForm(): FormBuilder = FormBuilder(
        formFields = mutableMapOf<String, FormField<Any>>(
            EUser.NAME.name to FormField(
                value = (user.firstName) + " " + (user.lastName),
                validations = listOf(
                    FormValidations.required()
                )
            ),
            EUser.EMAIL.name to FormField(
                value = user.email,
                validations = listOf(
                    FormValidations.required(),
                    FormValidations.email()
                )
            ),
            EUser.ZIPCODE.name to FormField(
                value = user.zipcode
            )
        ).apply {
            user.dob?.let {
                put(
                    EUser.BIRTHDAY.name,
                    FormField(value = CalendarUtil.stringToCalender(it))
                )
            }
            user.gender?.let { put(EUser.GENDER.name, FormField(value = it)) }
        }
    )
}

sealed interface ProfileIntent : IReducer.Intent {
    data class setUser(val user: User) : ProfileIntent
    data class AlterRefreshing(val isRefreshing: Boolean) : ProfileIntent
}

class ProfileScreenReducer : IReducer<ProfileState, ProfileIntent> {
    override fun reduce(state: ProfileState, intent: ProfileIntent): ProfileState? {
        return when (intent) {
            is ProfileIntent.setUser -> {
                state.copy(user = intent.user)
            }

            is ProfileIntent.AlterRefreshing -> {
                state.copy(isRefreshing = intent.isRefreshing)
            }

            else -> state

        }
    }

}