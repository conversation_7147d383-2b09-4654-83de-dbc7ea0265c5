package com.dmdbrands.library.bluetooth.model

import androidx.compose.runtime.Immutable
import com.dmdbrands.library.bluetooth.data.db.entity.HistoryEntity
import com.dmdbrands.library.bluetooth.util.HistoryUtil
import com.google.gson.GsonBuilder
import kotlin.random.Random

@Immutable
data class HistoryData(
    val id: Long = Random.nextLong(),
    val appType: String,
    val sku: String? = null,
    val entryId: String? = null,
    val entryTimestamp: String,
    val opTimestamp: String = entryTimestamp,
    val unit: String? = null,
    val operation: String,
    val broadcastId: String? = null,
    val detailItems: List<DetailItem> = listOf()
) {
    fun toHistoryEntity(): HistoryEntity {
        val gson = GsonBuilder()
            .excludeFieldsWithoutExposeAnnotation()
            .create()
        return HistoryEntity(
            id = id,
            appType = appType,
            sku = sku,
            entryTimestamp = entryTimestamp,
            unit = unit,
            opTimestamp = opTimestamp,
            operation = operation,
            broadcastId = broadcastId,
            details = gson.toJson(detailItems)
        )
    }

    fun getDisplayValue(): String {
        val showValue = HistoryUtil.getDisplayValue(this.appType, this.unit)
        val mainDetailItem =
            this.detailItems.filter {
                it.name.lowercase() in showValue.map { it.lowercase() }
            }.sortedBy { showValue.indexOf(it.name) }

        return mainDetailItem.map {
            it.value
        }.joinToString("/")

    }

    fun getDisplayDetail(): List<DetailItem> {
        val showDetail = HistoryUtil.getDisplayDetail(this.appType)
        return this.detailItems.filter { showDetail.contains(it.name) && it.value != null }
    }
}
