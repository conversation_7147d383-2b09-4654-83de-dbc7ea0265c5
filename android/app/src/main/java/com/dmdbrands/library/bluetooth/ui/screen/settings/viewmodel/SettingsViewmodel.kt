package com.dmdbrands.library.bluetooth.ui.screen.settings.viewmodel

import androidx.lifecycle.viewModelScope
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.model.Permission
import com.dmdbrands.library.bluetooth.model.interfaces.IBaseRepository
import com.dmdbrands.library.bluetooth.ui.screen.settings.reducer.SettingsScreenReducer
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionType
import com.example.bluetoothwrapper.service.GGPermissionService
import com.example.utilities.services.viewmodel.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SettingsViewmodel @Inject constructor(
    private val ggPermissionService: GGPermissionService,
    private val baseRepository: IBaseRepository
) :
    BaseViewModel<SettingsScreenReducer.State, SettingsScreenReducer.Intent>(
        initialState = SettingsScreenReducer.State(),
        SettingsScreenReducer()
    ) {

    init {
        getPermissionStatus()
        getUserDetails()
    }

    fun requestPermission(permissionType: String) {
        ggPermissionService.requestPermission(permissionType)
    }

    private fun getUserDetails() {
        viewModelScope.launch {
            baseRepository.subscribeUserDetails().collect {
                handleIntent(SettingsScreenReducer.Intent.setUser(it))
            }
        }
    }

    private fun getPermissionStatus() {
        viewModelScope.launch {
            baseRepository.subscribePermissions().collect {
                val permissions = it.filter { it.key != GGPermissionType.ALL }.map {
                    Permission(type = it.key, description = it.key, status = it.value)
                }
                handleIntent(SettingsScreenReducer.Intent.updatePermissions(permissions))

            }
        }
    }

    override fun handleIntent(intent: SettingsScreenReducer.Intent) {
        super.handleIntent(intent)
        when (intent) {
            is SettingsScreenReducer.Intent.onSave -> {
                setLoader(AppLang.Loader.SAVING)
                viewModelScope.launch {
                    baseRepository.setUserDetails(intent.user)
                    setLoader(null)
                }
            }

            else -> null
        }
    }
}