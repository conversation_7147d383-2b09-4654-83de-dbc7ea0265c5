package com.dmdbrands.library.bluetooth.util

import com.dmdbrands.library.bluetooth.R
import com.dmdbrands.library.bluetooth.util.DeviceUtil.displayMetricProperty
import com.dmdbrands.library.ggbluetooth.enums.GGAppType

object Source {
    const val BLUETOOTH = "bluetooth"
    const val SEND = "send"
    const val API = "api"
    const val SHARED = "shared"
}

object OperationUtil {
    // Generic numeric conversion function
    fun <T : Number> convertValue(value: Any?, transform: (Float) -> T): T? {
        return when (value) {
            is Float -> transform(value)
            is Double -> transform(value.toFloat())
            is Int -> transform(value.toFloat())
            is String -> value.toFloatOrNull()?.let(transform)
            else -> null
        }
    }

    fun getMapper(
        appType: String,
        source: String,
        unit: String? = null
    ): Map<String, (Any?) -> Any?> {
        return when {
            appType == GGAppType.WEIGHT_GURUS && source == Source.API ->
                displayMetricProperty.associateWith {
                    { value: Any? -> convertValue(value) { it / 10f } }
                } + mapOf(
                    "weight" to { value: Any? -> convertValue(value) { it / 10f } }
                )

            appType == GGAppType.SMART_BABY && source == Source.API -> mapOf(
                "babyWeightDecigrams" to { value -> convertValue(value) { it / 10000f } },
            )

            appType == GGAppType.WEIGHT_GURUS && source == Source.SEND ->
                displayMetricProperty.associateWith {
                    { value: Any? -> convertValue(value) { (it * 10f).toInt() } }
                } + mapOf(
                    "weight" to { value -> convertValue(value) { (it * 10f).toInt() } }
                )

            appType == GGAppType.SMART_BABY && source == Source.BLUETOOTH -> mapOf(
                "weight" to { value ->
                    if (unit == "lb") {
                        convertValue(value) { it / 10f }
                    } else {
                        convertLbToLbOz(convertValue(value) { it / 10f }!!)
                    }
                }
            )

            appType == GGAppType.BALANCE_HEALTH -> mapOf()

            else -> emptyMap()
        }
    }

    fun convertLbToLbOz(weight: Float): String {
        val lbs = weight.toInt() // Whole pounds
        val ounces = (weight - lbs) * 16 // Ounces as a float
        return "$lbs : ${"%.1f".format(ounces)}" // Format ounces as a float with 1 decimal point
    }

    fun checkNotZero(value: Any?): Boolean {
        return when (value) {
            is Float -> (value != 0f)
            is Long -> (value != 0L)
            is Double -> (value != 0.0)
            is Int -> (value != 0)
            is String -> (value != "0")
            else -> false
        }
    }

    fun unitMappers(appType: String): Map<String, String> {
        return when (appType) {
            GGAppType.WEIGHT_GURUS -> {
                mapOf(
                    "bodyFat" to "%",
                    "muscleMass" to "%",
                    "water" to "%",
                    "weight" to "lbs",
                    "bodyFat" to "%",
                    "boneMass" to "%",
                    "metabolicAge" to "yrs",
                    "proteinPercent" to "%",
                    "skeletalMusclePercent" to "%",
                    "subcutaneousFatPercent" to "%",
                )
            }

            GGAppType.BALANCE_HEALTH -> {
                mapOf(
                    "pulse" to "bpm",
                    "systolic" to "mmHg",
                )
            }

            else -> emptyMap()
        }
    }

    fun getMainUnit(appType: String): String {
        return when (appType) {
            GGAppType.BALANCE_HEALTH -> "mmHg"
            else -> ""
        }
    }

    fun iconMappers(appType: String): Map<String, Int> {
        return when (appType) {
            GGAppType.WEIGHT_GURUS -> {
                mapOf(
                    "bmi" to R.drawable.bmi_ic,
                    "bodyFat" to R.drawable.bodyfat_ic,
                    "muscleMass" to R.drawable.muscle_mass_ic,
                    "water" to R.drawable.water_ic
                )
            }

            else -> emptyMap()
        }
    }
}