package com.dmdbrands.library.bluetooth.ui.screen.settings.local.component

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.config.LocalNavController
import com.dmdbrands.library.bluetooth.ui.screen.settings.reducer.EUserDetails
import com.dmdbrands.library.bluetooth.ui.screen.settings.reducer.SettingsScreenReducer
import com.dmdbrands.library.bluetooth.ui.screen.settings.viewmodel.SettingsViewmodel
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.shared.component.AppScaffold
import com.dmdbrands.library.bluetooth.ui.theme.BackgroundSecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.dmdbrands.library.bluetooth.util.UserUtil.getUserDetails
import com.example.utilities.modal.ActionButton
import com.example.utilities.modal.Dialog
import com.example.utilities.modal.DropDownItem
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.component.input.DatePickerType
import com.example.utilities.services.component.input.Input
import com.example.utilities.services.component.input.InputType
import com.example.utilities.services.component.input.TextFieldType

@OptIn(ExperimentalMaterial3Api::class, ExperimentalLayoutApi::class)
@Composable
fun UserSection() {

    val viewModel: SettingsViewmodel = hiltViewModel()
    val state by viewModel.state.collectAsState()
    val userForm by state.getUserForm().form.collectAsState()
    var heightMeasure by remember { mutableStateOf("ft") }
    var weightMeasure by remember { mutableStateOf("lbs") }
    val keyboardController = LocalSoftwareKeyboardController.current
    val navController = LocalNavController.current

    fun setAlert(): Dialog {
        return Dialog(
            title = AppLang.ALERT,
            message = AppLang.Dialog.DISCARD_CHANGES,
            confirmButton = ActionButton(
                text = AppLang.Dialog.DISCARD,
                action = {
                    userForm.resetForm(state.getUserForm().form.value)
                    navController.navigateUp()
                }
            ),
            dismissButton = ActionButton(
                text = AppLang.Dialog.CANCEL,
                action = {
                    viewModel.setDialog(dialog = null)
                }
            )
        )
    }

    fun onDismissRequest() {
        if (userForm.getUserDetails() != state.user) {
            viewModel.setDialog(dialog = setAlert())
        } else {
            navController.navigateUp()
        }
        keyboardController?.hide()
    }

    BackHandler {
        onDismissRequest()
    }
    AppScaffold(
        setFabPadding = {
            viewModel.setFabPadding(0.dp)
        },
        topBarExpandedHeight = TopAppBarDefaults.TopAppBarExpandedHeight * 0.75f,
        topAppBarColors = TopAppBarDefaults.topAppBarColors(containerColor = BackgroundSecondaryColor),
        title = AppLang.Settings.PROFILE.uppercase(),
        navigationIcon = {
            AppButton(containerColor = Transparent, onClick = {
                onDismissRequest()
            }) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = null,
                    tint = PrimaryColor
                )
            }
        },
    ) {
        HorizontalDivider(modifier = Modifier.fillMaxWidth(), color = TextPrimaryColor)
        Column(
            modifier = Modifier
                .verticalScroll(state = rememberScrollState())
                .imePadding()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(22.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Input(
                type = InputType.TEXT,
                label = "Name",
                name = EUserDetails.NAME.name,
                form = userForm,
                imeAction = ImeAction.Next,
                textFieldType = TextFieldType.SIMPLE,
            )
            Input(
                type = InputType.EMAIL,
                label = "Email",
                name = EUserDetails.EMAIL.name,
                form = userForm,
                imeAction = ImeAction.Next,
                textFieldType = TextFieldType.SIMPLE,
            )
            Input(
                type = InputType.NUMBER,
                label = "Height ${heightMeasure}",
                form = userForm,
                name = EUserDetails.HEIGHT.name,
                imeAction = ImeAction.Next,
                textFieldType = TextFieldType.SIMPLE
            )
            Input(
                type = InputType.NUMBER,
                label = "Weight ${weightMeasure}",
                form = userForm,
                name = EUserDetails.WEIGHT.name,
                imeAction = ImeAction.Next,
                textFieldType = TextFieldType.SIMPLE
            )
            Input(
                type = InputType.NUMBER,
                label = "Goal Weight",
                form = userForm,
                name = EUserDetails.GOAL_WEIGHT.name,
                imeAction = ImeAction.Next,
                textFieldType = TextFieldType.SIMPLE
            )
            Input(
                type = InputType.DATE_PICKER,
                label = "Birthday",
                form = userForm,
                datePickerType = DatePickerType.BIRTHDAY,
                name = EUserDetails.BIRTHDAY.name,
                textFieldType = TextFieldType.SIMPLE
            )
            Input(
                type = InputType.DROP_DOWN,
                label = "Gender",
                form = userForm,
                menuItems = listOf(
                    DropDownItem("Male"),
                    DropDownItem("Female"),
                    DropDownItem("Other")
                ),
                name = EUserDetails.GENDER.name,
            )
            Input(
                value = if (userForm.getValue<Boolean>(EUserDetails.BODY_TYPE.name) == true) "Athlete" else "Normal",
                type = InputType.DROP_DOWN,
                label = "Body Type",
                form = userForm,
                menuItems = listOf(DropDownItem("Normal"), DropDownItem("Athlete")),
                name = EUserDetails.BODY_TYPE.name,
                onValueChange = { userForm.update(EUserDetails.BODY_TYPE.name, it == "Athlete") }
            )
            Input(
                type = InputType.DROP_DOWN,
                label = "Goal Type",
                form = userForm,
                menuItems = listOf(
                    DropDownItem("Gain Weight"),
                    DropDownItem("Maintain Weight"),
                    DropDownItem("Lose Weight")
                ),
                name = EUserDetails.GOAL_TYPE.name,
            )
            Input(
                type = InputType.DROP_DOWN,
                label = "Unit Type",
                form = userForm,
                menuItems = listOf(DropDownItem("kgs and cm"), DropDownItem("lbs & ft")),
                name = EUserDetails.UNIT_TYPE.name,
                onValueChange = {
                    var currentHeight =
                        userForm.getValue<String>(EUserDetails.HEIGHT.name)?.toInt()?.toDouble()!!
                    var currentWeight =
                        userForm.getValue<String>(EUserDetails.WEIGHT.name)?.toInt()?.toDouble()!!
                    var goalWeight =
                        userForm.getValue<String>(EUserDetails.GOAL_WEIGHT.name)?.toInt()
                            ?.toDouble()!!
                    if (it == "lbs & ft") {
                        currentWeight = currentWeight * 2.2f // kgs to lbs
                        currentHeight = currentHeight * 0.3f // cm to ft
                        goalWeight = goalWeight * 2.2f // kgs to lbs
                    } else {
                        currentWeight = currentWeight / 2.2f // lbs to kgs
                        currentHeight = currentHeight / 0.3f // ft to cm
                        goalWeight = goalWeight / 2.2f // lbs
                    }
                    userForm.update(EUserDetails.HEIGHT.name, currentHeight.toString())
                    userForm.update(EUserDetails.WEIGHT.name, currentWeight.toString())
                    userForm.update(EUserDetails.GOAL_WEIGHT.name, goalWeight.toString())
                    userForm.update(EUserDetails.UNIT_TYPE.name, it)
                }
            )
            AppButton(
                modifier = Modifier
                    .fillMaxWidth(0.8f),
                onClick = {
                    viewModel.handleIntent(
                        SettingsScreenReducer.Intent.onSave(
                            userForm.getUserDetails()
                        )
                    )
                    navController.navigateUp()
                },
                enabled = userForm.getUserDetails() != state.user,
                shape = RoundedCornerShape(20.dp)
            ) {
                Text(
                    AppLang.SAVE.uppercase(),
                    fontFamily = OpenSansFont,
                    fontSize = 18.sp,
                    color = White,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(2.dp),
                    textAlign = TextAlign.Center
                )
            }
            Spacer(modifier = Modifier.height(60.dp))
        }
    }
}


@PreviewScreens
@Composable
private fun UserSectionPreview() {
    GGBluetoothLibraryTheme {
        UserSection(
        )
    }
}