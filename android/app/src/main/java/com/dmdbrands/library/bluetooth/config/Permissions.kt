package com.dmdbrands.library.bluetooth.config

import com.dmdbrands.library.bluetooth.model.Permission
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionState
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionType


val PermissionList: List<Permission> = listOf(
//    Permission(GGPermissionType.BLUETOOTH, "Bluetooth", GGPermissionState.NOT_REQUESTED),
    Permission(
        GGPermissionType.BLUETOOTH_SWITCH,
        "Bluetooth Switch",
        GGPermissionState.NOT_REQUESTED
    ),
    Permission(GGPermissionType.LOCATION, "Location", GGPermissionState.NOT_REQUESTED),
    Permission(
        GGPermissionType.LOCATION_SWITCH,
        "Location Switch",
        GGPermissionState.NOT_REQUESTED
    ),
    Permission(GGPermissionType.NEARBY_DEVICE, "Nearby", GGPermissionState.NOT_REQUESTED),
    Permission(GGPermissionType.NOTIFICATION, "Notification", GGPermissionState.NOT_REQUESTED),
    Permission(GGPermissionType.CAMERA, "Camera", GGPermissionState.NOT_REQUESTED),
)
