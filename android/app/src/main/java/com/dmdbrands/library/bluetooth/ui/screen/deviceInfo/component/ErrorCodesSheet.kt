package com.dmdbrands.library.bluetooth.ui.screen.deviceInfo.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.ui.shared.component.GGErrorCode
import com.dmdbrands.library.bluetooth.ui.theme.BackgroundSecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.BorderColor
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.example.utilities.services.component.list.StaticList

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ErrorCodeSheet(errorCodes: List<GGErrorCode>, onDismiss: () -> Unit) {
    ModalBottomSheet(
        onDismiss,
        modifier = Modifier.statusBarsPadding(),
        containerColor = BackgroundSecondaryColor,
    ) {
        StaticList(
            modifier = Modifier.padding(vertical = 10.dp),
            list = errorCodes
        ) { index, errorCode ->
            ErrorCodeContent(errorCode)
        }
    }
}

@Composable
private fun ErrorCodeContent(errorCode: GGErrorCode) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(6.dp)
    ) {
        Text(
            text = errorCode.code,
            modifier = Modifier.weight(1f),
            fontFamily = OpenSansFont,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )
        VerticalDivider(
            modifier = Modifier.fillMaxHeight(),
            thickness = 2.dp,
            color = BorderColor
        )
        Column(
            modifier = Modifier.weight(1f),
        ) {
            if (errorCode.title != null) {
                Text(
                    text = errorCode.title, fontFamily = OpenSansFont,
                    fontWeight = FontWeight.Medium,
                    fontSize = 14.sp,
                    modifier = Modifier.fillMaxWidth()
                )
            }
            if (errorCode.description != null) {
                Text(
                    text = errorCode.description,
                    fontFamily = OpenSansFont,
                    textAlign = TextAlign.Justify,
                    fontSize = 12.sp,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }

    }

}