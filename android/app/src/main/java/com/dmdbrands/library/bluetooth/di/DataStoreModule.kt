package com.dmdbrands.library.bluetooth.di

import android.content.Context
import com.dmdbrands.library.bluetooth.data.datastore.UserDataStore
import com.dmdbrands.library.bluetooth.data.datastore.interfaces.IUserDataStore
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class DataStoreModule {
    @Singleton
    @Provides
    fun provideUserDataStore(@ApplicationContext context: Context): IUserDataStore {
        return UserDataStore(context)
    }
}