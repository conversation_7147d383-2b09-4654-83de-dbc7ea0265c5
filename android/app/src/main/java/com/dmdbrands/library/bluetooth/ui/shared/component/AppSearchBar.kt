package com.dmdbrands.library.bluetooth.ui.shared.component

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.ui.theme.SurfaceBackgroundColor
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.component.input.Input
import com.example.utilities.services.component.input.TextFieldType

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppSearchBar(onClear: () -> Unit = {}, onSearch: (String) -> Unit = {}) {
    var query by remember { mutableStateOf("") }
    CenterAlignedTopAppBar(
        colors = TopAppBarDefaults.topAppBarColors(containerColor = SurfaceBackgroundColor),
        title = {
            Input(
                value = query,
                name = "",
                label = "Search",
                modifier = Modifier.fillMaxWidth(),
                onValueChange = {
                    if (it is String) {
                        onSearch(it)
                        query = it
                    }
                },
                textStyle = LocalTextStyle.current.copy(
                    fontSize = 14.sp,
                ),
                trailingIcon = {
                    AppButton(containerColor = Transparent, onClick = onClear) {
                        Icon(imageVector = Icons.Default.Clear, contentDescription = "Clear")
                    }
                },
                textFieldType = TextFieldType.FILLED,
                imeAction = ImeAction.Search,

                onSearch = {
                    onSearch(query)
                }
            )
        })
}