package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.liveEntry


import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import com.dmdbrands.library.bluetooth.data.sample.Unit.SmartBabyWeightUnitList
import com.dmdbrands.library.bluetooth.data.sample.Unit.WeightGurusWeightUnitList
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.DeviceDetailReducer
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.DeviceDetailViewmodel
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.dmdbrands.library.ggbluetooth.enums.GGBTSettingType
import com.dmdbrands.library.ggbluetooth.model.GGPulseEntry
import com.dmdbrands.library.ggbluetooth.model.GGScaleEntry
import com.dmdbrands.library.ggbluetooth.model.GGWeightEntry

@Composable
fun LiveEntryView(
    state: DeviceDetailReducer.State,
    viewModel: DeviceDetailViewmodel = hiltViewModel()
) {
    val appType = state.device?.getAppType() ?: GGAppType.ALL
    val sku = state.device?.sku
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        when {
            appType == GGAppType.SAGE -> {
                ScaleEntryView(
                    liveEntry = state.liveEntry,
                    weight = (state.liveEntry as GGWeightEntry?)?.weight.toString(),
                    viewModel = viewModel
                )
            }

            appType == GGAppType.RPM && sku == "0003" -> {
                PulseEntryView(state.liveEntry as GGPulseEntry?)
            }

            appType == GGAppType.RPM && sku == "0005" -> {
                BGMEntryView(latestEntry = state.latestEntry) {
                    viewModel.changeUnit(it, GGBTSettingType.BGM_UNIT)
                }
            }

            appType == GGAppType.RPM && sku == "0062" -> {
                TemperatureEntryView(
                    state.latestEntry,
                    device = state.device,
                    viewModel = viewModel,
                    error = state.error
                )
            }

            appType == GGAppType.SMART_BABY -> {
                ScaleEntryView(
                    latestEntry = state.latestEntry,
                    unitList = SmartBabyWeightUnitList,
                    viewModel = viewModel
                )
            }

            appType == GGAppType.WEIGHT_GURUS -> {
                ScaleEntryView(
                    liveEntry = state.liveEntry,
                    weight = if (state.liveEntry?.unit == "kg") (state.liveEntry as GGScaleEntry).weightInKg.toString() else (state.liveEntry as GGScaleEntry?)?.weight.toString(),
                    unitList = WeightGurusWeightUnitList,
                    tareRequired = false,
                    viewModel = viewModel
                )
            }

            else -> {
                Text(text = state.liveEntry?.unit.toString())
            }
        }
    }
}

