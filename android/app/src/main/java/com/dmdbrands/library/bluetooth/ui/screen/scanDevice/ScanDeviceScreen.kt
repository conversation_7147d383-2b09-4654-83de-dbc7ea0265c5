package com.dmdbrands.library.bluetooth.ui.screen.scanDevice

import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.dmdbrands.library.bluetooth.ui.screen.scanDevice.component.AppTypeDropDown
import com.dmdbrands.library.bluetooth.ui.shared.DeviceIntent
import com.dmdbrands.library.bluetooth.ui.shared.ScanDeviceIntent
import com.dmdbrands.library.bluetooth.ui.shared.ScanDeviceState
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.shared.component.AppScaffold
import com.dmdbrands.library.bluetooth.ui.shared.component.device.DeviceList
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.example.utilities.services.component.AppButton


@Composable
fun ScanDeviceScreen(isScanning: Boolean) {
    val viewModel: ScanDeviceViewModel = hiltViewModel()
    val state by viewModel.state.collectAsState()
    ScanDevicesContent(
        state,
        isScanning,
        viewModel::handleIntent,
    )

}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ScanDevicesContent(
    state: ScanDeviceState,
    isScanning: Boolean,
    sendIntent: (DeviceIntent) -> Unit,
) {

    AppScaffold(
        titleComposable = {
            AppTypeDropDown(selected = state.appType, enabled = !isScanning) {
                sendIntent(ScanDeviceIntent.setAppType(it))
            }
        },
        actions = {
            AppButton(
                onClick = {
                    sendIntent(DeviceIntent.Refresh)
                },
                containerColor = Transparent,
                disabledContainerColor = Transparent
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "Refresh",
                    tint = PrimaryColor,
                    modifier = Modifier.size(26.dp)
                )
            }
        },
    ) {
        PullToRefreshBox(state.localRefreshing, {
            sendIntent(DeviceIntent.Refresh)
        }) {
            DeviceList(state.getFilteredLocalDevices(), sendIntent = sendIntent)
        }
    }

}

@PreviewScreens
@Composable
fun ScanDeviceScreenPreview() {
    GGBluetoothLibraryTheme {
        ScanDevicesContent(
            state = ScanDeviceState(),
            false,
        ) {}
    }
}