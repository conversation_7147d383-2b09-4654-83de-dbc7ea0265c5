package com.dmdbrands.library.bluetooth.ui.screen.settings.local.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.config.LocalNavController
import com.dmdbrands.library.bluetooth.ui.screen.settings.viewmodel.SettingsViewmodel
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.shared.component.AppScaffold
import com.dmdbrands.library.bluetooth.ui.shared.component.PermissionCard
import com.dmdbrands.library.bluetooth.ui.theme.BackgroundSecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.example.utilities.services.component.AppButton


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PermissionSection(
) {
    val viewModel = hiltViewModel<SettingsViewmodel>()
    val state by viewModel.state.collectAsState()
    val navController = LocalNavController.current
    AppScaffold(
        setFabPadding = {
            viewModel.setFabPadding(0.dp)
        },
        topBarExpandedHeight = TopAppBarDefaults.TopAppBarExpandedHeight * 0.75f,
        topAppBarColors = TopAppBarDefaults.topAppBarColors(containerColor = BackgroundSecondaryColor),
        title = AppLang.Settings.PERMISSIONS.uppercase(),
        navigationIcon = {
            AppButton(containerColor = Transparent, onClick = {
                navController.navigateUp()
            }) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = null,
                    tint = PrimaryColor
                )
            }
        },
    ) {
        HorizontalDivider(modifier = Modifier.fillMaxWidth(), color = TextPrimaryColor)
        if (state.permissions.isEmpty()) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "A scan is required to collect and display the current permissions. Initiate the scan now.",
                    color = PrimaryColor,
                    fontFamily = OpenSansFont,
                    textAlign = TextAlign.Center,
                )
            }
        } else {
            LazyColumn(
                modifier = Modifier.padding(horizontal = 16.dp),
            ) {
                items(state.permissions) {
                    Spacer(modifier = Modifier.height(10.dp))
                    PermissionCard(
                        permission = it,
                        onClick = {
                            viewModel.requestPermission(it.type)
                        }
                    )
                }
            }
        }
    }
}

@Composable
@PreviewScreens
private fun PreviewPermissionSection() {
    GGBluetoothLibraryTheme {
        PermissionSection()
    }
}