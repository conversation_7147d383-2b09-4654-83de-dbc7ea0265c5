package com.dmdbrands.library.bluetooth.ui.screen.apps.component

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.component.animation.DotsPulsing
import com.example.utilities.services.component.input.Input
import com.example.utilities.services.component.input.InputType
import com.example.utilities.services.component.input.TextFieldType
import com.example.utilities.util.onlyString

@Composable
fun LoginDialog(
    appType: String,
    enabled: Boolean,
    onDismiss: () -> Unit = {},
    onLogin: (String, String) -> Unit
) {
    Card(colors = CardDefaults.cardColors(containerColor = White)) {
        var email by remember { mutableStateOf("") }
        var password by remember { mutableStateOf("") }
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "Close",
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .clickable {
                        onDismiss()
                    }
            )
            Column(
                modifier = Modifier
                    .fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Spacer(modifier = Modifier.height(10.dp))
                Text(
                    appType.onlyString(),
                    fontFamily = OpenSansFont,
                    fontSize = 22.sp,
                    color = PrimaryColor,
                    fontWeight = FontWeight.ExtraBold,
                )
                Spacer(modifier = Modifier.height(6.dp))
                Text(
                    AppLang.Dialog.LOGIN_TO_YOUR_ACCOUNT,
                    fontFamily = OpenSansFont,
                    fontSize = 16.sp,
                    color = TextPrimaryColor,
                    fontWeight = FontWeight.Bold,
                )
                Spacer(modifier = Modifier.height(30.dp))
                Input(name = "email", label = "Email", value = email, onValueChange = {
                    email = it.toString()
                }, textFieldType = TextFieldType.SIMPLE, type = InputType.EMAIL)
                Spacer(modifier = Modifier.height(16.dp))
                Input(name = "password", label = "Password", value = password, onValueChange = {
                    password = it.toString()
                }, textFieldType = TextFieldType.SIMPLE, type = InputType.PASSWORD)
                Spacer(modifier = Modifier.height(26.dp))
                AppButton(
                    onClick = {
                        onLogin(email, password)
                    },
                    enabled = enabled,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(2.dp),
                    disabledContainerColor = Transparent
                ) {
                    if (enabled)
                        Text(
                            AppLang.LOGIN.uppercase(),
                            fontFamily = OpenSansFont,
                            fontSize = 18.sp,
                            color = White,
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.Center
                        )
                    else
                        DotsPulsing(numberOfDots = 5)
                }
                Spacer(modifier = Modifier.height(10.dp))
            }
        }
    }
}