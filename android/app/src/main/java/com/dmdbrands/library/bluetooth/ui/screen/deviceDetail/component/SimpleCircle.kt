package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Composable
fun SimpleCircle(circleSize : Dp  = 80.dp , containerColor : Color = Color.White , outlineColor : Color = Color.Black) {
    Canvas(
        modifier = Modifier.size(circleSize)
    ) {
        val center = Offset(size.width / 2, size.height / 2)
        val radius = size.minDimension / 2 - 10 // Leave some padding

        // Draw the circle
        drawCircle(
            color = containerColor,  // Blue color
            center = center,
            radius = radius,
            style = androidx.compose.ui.graphics.drawscope.Fill
        )

        // Draw outline
        drawCircle(
            color = outlineColor,  // Darker blue for outline
            center = center,
            radius = radius,
            style = androidx.compose.ui.graphics.drawscope.Stroke(width = 4f)
        )
    }
}