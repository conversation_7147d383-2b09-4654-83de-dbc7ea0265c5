package com.dmdbrands.library.bluetooth.ui.screen.history

import com.dmdbrands.library.bluetooth.config.AppConfig
import com.dmdbrands.library.bluetooth.model.AppStatus
import com.dmdbrands.library.bluetooth.model.HistoryData
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.example.utilities.modal.interfaces.IReducer

data class HistoryState(
    val historyList: List<HistoryData> = listOf(),
    val remoteHistoryList: List<HistoryData> = listOf(),
    val localRefreshing: Boolean = false,
    val remoteRefreshing: Boolean = false,
    val appStatus: List<AppStatus> = listOf(),
    val appType: String = AppConfig.APPS_LIST[0]
) : IReducer.State {
    fun getFilteredHistories(): List<HistoryData> {
        return if (appType == GGAppType.ALL) {
            historyList
        } else {
            historyList.filter {
                it.appType == appType
            }
        }.sortedByDescending {
            it.entryTimestamp
        }
    }

    fun getFilteredRemoteHistories(): List<HistoryData> {
        return if (appType == GGAppType.ALL) {
            remoteHistoryList
        } else {
            remoteHistoryList.filter {
                it.appType == appType
            }
        }.sortedByDescending {
            it.entryTimestamp
        }
    }

}

sealed interface HistoryIntent : IReducer.Intent {
    data class AddHistory(val historyData: HistoryData) : HistoryIntent
    data class AddBulkHistory(val historyData: List<HistoryData>) : HistoryIntent
    data class AddRemoteHistory(val historyData: List<HistoryData>) : HistoryIntent
    data object ClearHistory : HistoryIntent
    data class RemoveHistory(val historyData: HistoryData) : HistoryIntent
    data class AlterLocalRefreshing(val isRefreshing: Boolean) : HistoryIntent
    data class AlterRemoteRefreshing(val isRefreshing: Boolean) : HistoryIntent
    data class ChangeAppType(val appType: String) : HistoryIntent
    data class ModifyAppStatus(val appStatus: List<AppStatus>) : HistoryIntent

}

class HistoryReducer : IReducer<HistoryState, HistoryIntent> {
    override fun reduce(state: HistoryState, intent: HistoryIntent): HistoryState {
        return when (intent) {
            is HistoryIntent.AddHistory -> {
                state.copy(historyList = state.historyList + intent.historyData)
            }

            is HistoryIntent.AddBulkHistory -> {
                state.copy(historyList = intent.historyData)
            }

            is HistoryIntent.AddRemoteHistory -> {
                state.copy(remoteHistoryList = intent.historyData)
            }

            is HistoryIntent.AlterLocalRefreshing -> {
                state.copy(localRefreshing = intent.isRefreshing)
            }

            is HistoryIntent.AlterRemoteRefreshing -> {
                state.copy(remoteRefreshing = intent.isRefreshing)
            }

            is HistoryIntent.ModifyAppStatus -> {
                state.copy(appStatus = intent.appStatus)
            }

            is HistoryIntent.ChangeAppType -> {
                state.copy(appType = intent.appType)
            }

            is HistoryIntent.RemoveHistory -> {
                state.copy(historyList = state.historyList - intent.historyData)
            }

            is HistoryIntent.ClearHistory -> {
                state.copy(historyList = listOf())
            }

            else -> state

        }
    }
}
