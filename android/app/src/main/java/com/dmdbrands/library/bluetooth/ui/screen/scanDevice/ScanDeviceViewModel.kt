package com.dmdbrands.library.bluetooth.ui.screen.scanDevice

import androidx.lifecycle.viewModelScope
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.BLEStatus
import com.dmdbrands.library.bluetooth.model.DeviceAction
import com.dmdbrands.library.bluetooth.model.interfaces.IBaseRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IDeviceRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IUserRepository
import com.dmdbrands.library.bluetooth.ui.shared.DeviceIntent
import com.dmdbrands.library.bluetooth.ui.shared.DeviceViewModel
import com.dmdbrands.library.bluetooth.ui.shared.ScanDeviceIntent
import com.dmdbrands.library.bluetooth.ui.shared.ScanDeviceState
import com.dmdbrands.library.bluetooth.util.DeviceUtil.convertToGGBTDevice
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.dmdbrands.library.ggbluetooth.enums.GGUserActionResponseType
import com.dmdbrands.library.ggbluetooth.model.GGDeviceDetail
import com.example.utilities.util.camelCase
import com.example.utilities.util.onlyString
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
class ScanDeviceViewModel @Inject constructor(
    override var deviceRepository: IDeviceRepository,
    override var userRepository: IUserRepository,
    override var baseRepository: IBaseRepository
) :
    DeviceViewModel<ScanDeviceState, DeviceIntent>(
        initialState = ScanDeviceState(appType = null),
        reducer = ScanDevicesReducer(),

        ) {

    init {
        subscribeAppType()
        subscribeDevices()
    }

    private fun subscribeDevices() {
        viewModelScope.launch {
            deviceRepository.subscribeNewDevices().collect {
                handleIntent(
                    DeviceIntent.AddDevice(it)
                )
            }
        }
    }

    private fun subscribeAppType() {
        viewModelScope.launch {
            baseRepository.subscribeAppType().collect {
                handleIntent(DeviceIntent.ChangeAppType(it))
            }
        }
    }

    private suspend fun connectDevice(device: BLEDevice) {
        if (deviceRepository.checkDeviceAvailable(device)) {
            this.setLoader("Checking for existing devices")
            this.setToastMessage(device.device.deviceName + " is already paired")
            deviceRepository.onDeviceAction(device, DeviceAction.REMOVE)
            setLoader(null)
        } else {
            updateDevice(device, connectionStatus = BLEStatus.CONNECTING)
            var updatedDevice: BLEDevice? = device
            val ggBTDevice = device.convertToGGBTDevice()
            ggDeviceService.pairDevice(ggBTDevice) {
                this.setToastMessage(
                    "${device.device.deviceName.onlyString()}|${
                        it.name.onlyString().camelCase()
                    }"
                )

                if (it == GGUserActionResponseType.CREATION_COMPLETED) {
                    ggDeviceService.getDeviceInfo(ggBTDevice) { deviceInfo ->
                        if (deviceInfo != null) {
                            updatedDevice = updatedDevice?.copy(
                                device = deviceInfo,
                            )
                            getLiveMeasurement(updatedDevice!!)
                            checkForWifiScales(updatedDevice!!)
                            viewModelScope.launch {
                                deviceRepository.onPostConnect(updatedDevice)
                            }
                        }
                    }
                } else {
                    updateDevice(device, connectionStatus = BLEStatus.DISCONNECTED)
                    updatedDevice = null
                }
            }
            if (updatedDevice != null) {
                handleA6Scales(device)
            }
        }
    }

    private fun checkForWifiScales(device: BLEDevice) {
        viewModelScope.launch {
            if (device.sku == "0412") {
                ggDeviceService.getConnectedWifiSSID(device.convertToGGBTDevice()) {
                    updateDevice(
                        deviceInfo = device.device,
                        wifiSSID = it
                    )
                }
            }
        }
    }

    private fun handleA6Scales(device: BLEDevice) {
        viewModelScope.launch {
            if ((device.device.protocolType == "A6") && (device.getAppType() == GGAppType.WEIGHT_GURUS || device.getAppType() == GGAppType.SMART_BABY)) {
                deviceRepository.onPostConnect(device)
                updateDevice(
                    device,
                    connectionStatus = BLEStatus.DISCONNECTED,
                    alreadyPaired = true
                )
            }
        }
    }

    private fun getLiveMeasurement(device: BLEDevice) {
        if (device.getAppType() == GGAppType.SAGE || device.getAppType() == GGAppType.RPM) {
            ggDeviceService.subscribeToLiveData(device.convertToGGBTDevice()) {}
        }
    }

    private fun disconnectDevice(device: BLEDevice) {
        updateDevice(device, connectionStatus = BLEStatus.CONNECTING)
        val ggBTDevice = device.convertToGGBTDevice()
        ggDeviceService.disconnectDevice(ggBTDevice)

    }

    private fun updateDevice(
        bleDevice: BLEDevice? = null, deviceInfo: GGDeviceDetail? = null,
        connectionStatus: BLEStatus? = null, alreadyPaired: Boolean? = null,
        wifiSSID: String? = null
    ) {
        val currentDevice = bleDevice ?: deviceRepository.getDevice(deviceInfo?.broadcastId!!)
        if (currentDevice != null) {
            viewModelScope.launch {
                deviceRepository.onDeviceAction(
                    currentDevice.copy(
                        connectionStatus = connectionStatus ?: currentDevice.connectionStatus,
                        alreadyPaired = alreadyPaired ?: currentDevice.alreadyPaired,
                        wifiSSID = wifiSSID ?: currentDevice.wifiSSID
                    ),
                    DeviceAction.UPDATE,
                )
            }
        }
    }

    private fun handleDeviceUsers(device: BLEDevice) {
        setLoader("Fetching users")
        ggDeviceService.getUsers(device.convertToGGBTDevice()) { response ->
            setLoader(null)
            showPicker(
                response.user,
                device
            )
        }
    }

    override fun handleIntent(intent: DeviceIntent) {
        super.handleIntent(intent)
        when (intent) {
            is DeviceIntent.AddDevice -> {
            }

            is ScanDeviceIntent.onConnectAction -> {
                viewModelScope.launch {
                    if (intent.device.connectionStatus == BLEStatus.DISCONNECTED) {
                        connectDevice(intent.device)
                    } else {
                        disconnectDevice(intent.device)
                    }
                }
            }

            is ScanDeviceIntent.onDeviceAction -> {
                viewModelScope.launch {
                    deviceRepository.onDeviceAction(intent.device, intent.action)
                }
            }

            is ScanDeviceIntent.setAppType -> {
                viewModelScope.launch {
                    baseRepository.setAppType(intent.appType)
                }
            }

            is DeviceIntent.Refresh -> {
                viewModelScope.launch {
                    super.handleIntent(DeviceIntent.AlterLocalRefreshState(true))
                    delay(1000)
                    deviceRepository.refresh()
                    super.handleIntent(DeviceIntent.AlterLocalRefreshState(false))
                }
            }

            is DeviceIntent.addProfile -> {
                handleDeviceUsers(intent.device)
            }

            else -> null
        }
    }
}
