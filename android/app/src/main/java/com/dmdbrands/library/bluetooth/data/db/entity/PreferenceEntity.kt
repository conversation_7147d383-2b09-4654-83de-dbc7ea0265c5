package com.dmdbrands.library.bluetooth.data.db.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

@Entity(
    tableName = "device_preferences",
    foreignKeys = [ForeignKey(
        entity = DeviceEntity::class,
        parentColumns = ["broadcast_id"],   // references DeviceEntity.broadcast_id
        childColumns = ["broadcast_id"],    // matches PreferencesEntity.broadcast_id
        onDelete = ForeignKey.CASCADE
    )],
    indices = [Index("broadcast_id")]
)
data class PreferencesEntity(
    @PrimaryKey(autoGenerate = true) val id: Long = 0,

    @ColumnInfo(name = "broadcast_id") val broadcastId: String,  // Foreign key reference

    @ColumnInfo(name = "tz_offset") val tzOffset: Int? = null,
    @ColumnInfo(name = "time_format") val timeFormat: String? = null,
    @ColumnInfo(name = "display_name") val displayName: String? = null,
    @ColumnInfo(name = "display_metrics") val displayMetrics: List<String>? = null,
    @ColumnInfo(name = "should_measure_pulse") val shouldMeasurePulse: Boolean? = null,
    @ColumnInfo(name = "should_measure_impedance") val shouldMeasureImpedance: Boolean? = null,
    @ColumnInfo(name = "should_factory_reset") val shouldFactoryReset: Boolean? = null,
    @ColumnInfo(name = "wifi_fota_schedule_time") val wifiFotaScheduleTime: Long? = null
)


