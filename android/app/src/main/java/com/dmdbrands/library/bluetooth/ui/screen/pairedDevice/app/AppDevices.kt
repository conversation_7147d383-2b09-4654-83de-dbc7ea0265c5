package com.dmdbrands.library.bluetooth.ui.screen.pairedDevice.app

import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.dmdbrands.library.bluetooth.ui.shared.component.AppScaffold
import com.dmdbrands.library.bluetooth.ui.shared.component.BackNavigation
import com.dmdbrands.library.bluetooth.ui.shared.component.device.DeviceList
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.example.utilities.services.component.AppButton
import com.example.utilities.util.onlyString
import com.example.utilities.util.single

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppDevicesScreen(appType: String) {
    val viewmodel =
        hiltViewModel<AppDevicesViewModel, AppDevicesViewModel.AppDeviceViewModelFactory> { factory ->
            factory.create(appType)
        }
    val state by viewmodel.state.collectAsState()

    AppScaffold(title = "${appType.onlyString().single()} - DEVICES", navigationIcon = {
        BackNavigation()
    }, actions = {
        AppButton(
            onClick = {
                viewmodel.loadDevices()
            },
            containerColor = Transparent,
            disabledContainerColor = Transparent
        ) {
            Icon(
                imageVector = Icons.Default.Refresh,
                contentDescription = "Refresh",
                tint = PrimaryColor,
                modifier = Modifier.size(26.dp)
            )
        }
    }) {
        DeviceList(state.localDevices, isRefreshing = state.localRefreshing)
    }
}