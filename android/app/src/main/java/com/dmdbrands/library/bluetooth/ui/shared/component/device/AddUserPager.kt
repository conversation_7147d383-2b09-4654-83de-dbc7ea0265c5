package com.dmdbrands.library.bluetooth.ui.shared.component.device

import android.annotation.SuppressLint
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.ggbluetooth.model.GGBTUser
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.theme.OpenSansFont
import kotlinx.coroutines.launch

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun AddUserPager(
    users: List<GGBTUser> = listOf(),
    isPrimaryTokenAvailable: Boolean = true,
    onCreateLocalUser: (String) -> Unit,
    onAppUserAdd: () -> Unit,
    onDismiss: () -> Unit
) {
    val pagerState = rememberPagerState { 2 }
    val scope = rememberCoroutineScope()

    Card(
        colors = CardDefaults.cardColors(containerColor = White),
        modifier = Modifier.wrapContentSize()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Header with close button and dynamic title
            Box(modifier = Modifier.fillMaxWidth()) {
                AppButton(
                    onClick = onDismiss,
                    containerColor = Transparent,
                    modifier = Modifier.align(Alignment.TopEnd)
                ) {
                    Icon(imageVector = Icons.Default.Close, contentDescription = "Close")
                }

                Text(
                    text = if (pagerState.currentPage == 0) "Add New User" else "Create Local User",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    fontFamily = OpenSansFont,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.align(Alignment.Center)
                )
            }

            if (isPrimaryTokenAvailable) {
                AddUserNameContent(
                    users = users,
                    onCreate = onCreateLocalUser,
                    onBack = {
                        scope.launch {
                            pagerState.animateScrollToPage(0)
                        }
                    }
                )
            } else {
                BoxWithConstraints {
                    val maxH = maxHeight / 5
                    HorizontalPager(
                        state = pagerState,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(maxH)
                    ) { page ->
                        when (page) {
                            0 -> AddUserTypeSelectionContent(
                                onLocalUserAdd = {
                                    scope.launch {
                                        pagerState.animateScrollToPage(1)
                                    }
                                },
                                onAppUserAdd = onAppUserAdd
                            )

                            1 -> AddUserNameContent(
                                users = users,
                                onCreate = onCreateLocalUser,
                                onBack = {
                                    scope.launch {
                                        pagerState.animateScrollToPage(0)
                                    }
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun AddUserTypeSelectionContent(
    onLocalUserAdd: () -> Unit,
    onAppUserAdd: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(10.dp),
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = "Select the type of account to create",
            fontSize = 14.sp,
            fontFamily = OpenSansFont,
            textAlign = TextAlign.Center
        )

        AppButton(onClick = onLocalUserAdd, cardModifier = Modifier.fillMaxWidth()) {
            Text(
                "Local".uppercase(),
                fontSize = 18.sp,
                modifier = Modifier.fillMaxWidth(),
                fontWeight = FontWeight.Medium,
                fontFamily = OpenSansFont,
                textAlign = TextAlign.Center,
                color = White
            )
        }
        AppButton(onClick = onAppUserAdd, cardModifier = Modifier.fillMaxWidth()) {
            Text(
                "App".uppercase(),
                fontSize = 18.sp,
                modifier = Modifier.fillMaxWidth(),
                fontWeight = FontWeight.Medium,
                fontFamily = OpenSansFont,
                textAlign = TextAlign.Center,
                color = White
            )
        }
    }
}

@Composable
fun AddUserNameContent(
    name: String = "",
    users: List<GGBTUser> = listOf(),
    onBack: (() -> Unit)? = null,
    onCreate: (String) -> Unit
) {
    var username by remember { mutableStateOf(name) }
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp),
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = "Enter a username for the new local user",
            fontSize = 14.sp,
            fontFamily = OpenSansFont,
            textAlign = TextAlign.Center
        )

        OutlinedTextField(
            value = username,
            onValueChange = {
                username = it
            },
            label = { Text("Username") },
            modifier = Modifier.fillMaxWidth()
        )

        Row(horizontalArrangement = Arrangement.spacedBy(12.dp)) {
            if (onBack != null) {
                AppButton(onClick = onBack, cardModifier = Modifier.weight(1f)) {
                    Text(
                        "Back".uppercase(),
                        fontSize = 16.sp,
                        modifier = Modifier.fillMaxWidth(),
                        fontWeight = FontWeight.Medium,
                        fontFamily = OpenSansFont,
                        textAlign = TextAlign.Center,
                        color = White
                    )
                }
            }
            AppButton(
                onClick = {
                    onCreate(username)
                },
                cardModifier = Modifier.weight(1f),
                enabled = username.isNotBlank() && users.none { it.name == username }
            ) {
                Text(
                    "Create".uppercase(),
                    fontSize = 16.sp,
                    modifier = Modifier.fillMaxWidth(),
                    fontWeight = FontWeight.Medium,
                    fontFamily = OpenSansFont,
                    textAlign = TextAlign.Center,
                    color = White
                )
            }
        }
    }
}

@PreviewScreens
@Composable
private fun AddUserPagerPreview() {
    GGBluetoothLibraryTheme {
        AddUserPager(onAppUserAdd = {}, onCreateLocalUser = {}) { }
    }
}