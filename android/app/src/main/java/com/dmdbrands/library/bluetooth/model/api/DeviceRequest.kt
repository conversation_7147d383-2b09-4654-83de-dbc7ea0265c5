package com.dmdbrands.library.bluetooth.model.api


data class BHDeviceRequest(
    val name: String,
    val sku: String,
    val type: String,
    val broadcastId: String,
    val peripheralIdentifier: String,
    val mac: String,
    val password: String?,
    val userNumber: Int,
    val nickname: String,
)

data class WGDeviceRequest(
    val name: String,
    val sku: String,
    val type: String,
    val broadcastId: Long,
    val peripheralIdentifier: String,
    val mac: String,
    val password: Long?,
    val userNumber: Int,
    val nickname: String,
    val scaleToken: String?
)
