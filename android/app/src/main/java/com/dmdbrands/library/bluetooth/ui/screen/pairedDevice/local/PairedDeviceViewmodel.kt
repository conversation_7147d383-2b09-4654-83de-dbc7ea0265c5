package com.dmdbrands.library.bluetooth.ui.screen.pairedDevice.local

import androidx.lifecycle.viewModelScope
import com.dmdbrands.library.bluetooth.model.AppStatus
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.DeviceAction
import com.dmdbrands.library.bluetooth.model.interfaces.IAppConfiguration
import com.dmdbrands.library.bluetooth.model.interfaces.IDeviceRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IUserRepository
import com.dmdbrands.library.bluetooth.ui.shared.DeviceIntent
import com.dmdbrands.library.bluetooth.ui.shared.DeviceViewModel
import com.dmdbrands.library.bluetooth.ui.shared.PairedDeviceIntent
import com.dmdbrands.library.bluetooth.ui.shared.PairedDeviceState
import com.dmdbrands.library.bluetooth.util.DeviceUtil.convertToGGBTDevice
import com.dmdbrands.library.ggbluetooth.enums.GGUserActionResponseType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class PairedDeviceViewmodel @Inject constructor(
    override var deviceRepository: IDeviceRepository,
    override var userRepository: IUserRepository,
    private val appConfiguration: IAppConfiguration,
) :
    DeviceViewModel<PairedDeviceState, DeviceIntent>(
        initialState = PairedDeviceState(),
        reducer = PairedDeviceReducer(),
    ) {
    init {
        fetchLocalDevices()
        fetchRemoteDevices()
        fetchAppStatus()

    }

    private fun fetchLocalDevices() {
        handleIntent(DeviceIntent.AlterLocalRefreshState(true))
        viewModelScope.launch {
            deviceRepository.subscribePairedDevices().collect {
                if (it.isNotEmpty() && !state.value.localRefreshing) {
                    handleIntent(DeviceIntent.AlterLocalRefreshState(true))
                }
                handleIntent(DeviceIntent.AddDevice(it))
                handleIntent(DeviceIntent.AlterLocalRefreshState(false))
            }
        }
    }

    private fun fetchAppStatus() {
        viewModelScope.launch {
            userRepository.subscribeAppStatus().collect {
                handleIntent(PairedDeviceIntent.ModifyAppStatus(it.filter { it.token != null }))
            }
        }
    }

    private fun fetchRemoteDevices() {
        handleIntent(PairedDeviceIntent.AlterRemoteRefreshState(true))
        viewModelScope.launch {
            deviceRepository.subscribeRemoteDevices().collect {
                if (it.isNotEmpty() && !state.value.remoteRefreshing) {
                    handleIntent(PairedDeviceIntent.AlterRemoteRefreshState(true))
                }
                handleIntent(PairedDeviceIntent.AddRemoteDevice(it))
                handleIntent(PairedDeviceIntent.AlterRemoteRefreshState(false))
            }
        }
    }

    private suspend fun loadRemoteDevices(filter: List<AppStatus>) {
        filter.forEach {
            appConfiguration.setAppType(it.type)
            deviceRepository.getAppDevices(it.type)
        }
    }

    private fun refresh() {
        handleIntent(DeviceIntent.AlterLocalRefreshState(true))
        handleIntent(PairedDeviceIntent.AlterRemoteRefreshState(true))
        viewModelScope.launch {
            delay(100)
            deviceRepository.refresh()
            handleIntent(DeviceIntent.AlterLocalRefreshState(false))
        }
        viewModelScope.launch {
            loadRemoteDevices(state.value.appStatus)
            handleIntent(PairedDeviceIntent.AlterRemoteRefreshState(false))
        }
    }

    private fun handleDeviceUsers(device: BLEDevice) {
        setLoader("Fetching users")
        ggDeviceService.getUsers(device.convertToGGBTDevice()) { response ->
            setLoader(null)
            showPicker(
                response.user,
                device
            )
        }
    }

    override fun handleIntent(intent: DeviceIntent) {
        super.handleIntent(intent)
        when (intent) {
            is DeviceIntent.Refresh -> {
                refresh()
            }

            is DeviceIntent.RemoveDevice -> {
                viewModelScope.launch {
                    deviceRepository.onDeviceAction(intent.device, DeviceAction.DELETE)
                    ggDeviceService.deleteAccount(
                        intent.device.convertToGGBTDevice(),
                    ) {
                        if (it == GGUserActionResponseType.DELETE_COMPLETED) {
                            ggDeviceService.disconnectDevice(intent.device.convertToGGBTDevice())
                        }
                    }
                }
            }

            is DeviceIntent.addProfile -> {
                handleDeviceUsers(intent.device)
            }

            is PairedDeviceIntent.RemoveRemoteDevice -> {
                setLoader("Deleting ..")
                viewModelScope.launch {
                    deviceRepository.deleteRemoteDevice(intent.device.getAppType(), intent.device)
                    setLoader(null)
                }

            }

            else -> null
        }
    }
}