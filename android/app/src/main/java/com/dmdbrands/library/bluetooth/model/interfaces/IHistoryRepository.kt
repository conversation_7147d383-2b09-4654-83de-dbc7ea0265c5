package com.dmdbrands.library.bluetooth.model.interfaces

import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.HistoryData
import com.dmdbrands.library.ggbluetooth.model.GGEntry
import kotlinx.coroutines.flow.Flow

interface IHistoryRepository {
    suspend fun subscribeLocalHistory(): Flow<List<HistoryData>>
    suspend fun subscribeRemoteHistory(): Flow<List<HistoryData>>
    suspend fun subscribeLatestEntry(broadCastId: String): Flow<HistoryData?>
    suspend fun subscribeHistory(broadCastId: String): Flow<List<HistoryData>>
    suspend fun addHistory(historyData: HistoryData)
    suspend fun addBulkHistory(historyData: List<GGEntry>, device: (BLEDevice?) -> Unit)
    suspend fun removeHistory(historyId: Long)

    suspend fun getAppHistory(appType: String): List<HistoryData>
    suspend fun fetchLocalHistory()
    suspend fun refresh()
}