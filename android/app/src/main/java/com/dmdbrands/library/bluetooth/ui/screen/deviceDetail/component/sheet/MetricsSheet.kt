package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.sheet

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.DeviceDetailReducer
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.theme.BackgroundSecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.BorderColor
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.SurfaceBackgroundColor
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.TextSecondaryColor
import com.dmdbrands.library.bluetooth.util.DeviceUtil
import com.example.utilities.services.component.AppButton
import com.example.utilities.util.normalize


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MetricsSheet(
    metrics: List<String>,
    mode: DeviceMode,
    handleIntent: (DeviceDetailReducer.Intent) -> Unit,
    onDismiss: () -> Unit
) {
    var tempMetricsList by remember {
        mutableStateOf(metrics)
    }
    val tempMode = remember { mutableStateOf(mode) }
    val bodyMetrics = when (tempMode.value) {
        DeviceMode.WEIGHT_ONLY -> DeviceUtil.initialMetrics.filter { it == "bmi" }
        DeviceMode.FULL_WITH_PULSE -> DeviceUtil.displayMetrics
        DeviceMode.FULL -> DeviceUtil.initialMetrics
    }
    val otherMetrics = DeviceUtil.initialMetrics.filter {
        it.contains("average", ignoreCase = true) ||
                it.contains("progress", ignoreCase = true)
    }
    val remainingMetrics = bodyMetrics.filter { it !in otherMetrics }
    val saveButtonEnabled = tempMetricsList.toSet() != metrics.toSet() || tempMode.value != mode
            || tempMode.value != mode
    DetailViewSheet(title = "Metrics", onDismiss = onDismiss, action = {
        AppButton(
            enabled = saveButtonEnabled,
            containerColor = Transparent,
            disabledContainerColor = Transparent,
            onClick = {
                handleIntent(
                    DeviceDetailReducer.Intent.onSaveMetrics(
                        tempMetricsList,
                        tempMode.value
                    )
                )
                onDismiss()
            }
        ) {
            Text(
                AppLang.SAVE.uppercase(),
                fontFamily = OpenSansFont,
                fontWeight = FontWeight.Bold,
                fontSize = 18.sp,
                color = if (saveButtonEnabled) PrimaryColor else TextSecondaryColor,
            )
        }
    }) {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(bottom = 32.dp)
        ) {
            item {
                Text(
                    "Preset Modes",
                    fontFamily = OpenSansFont,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(BackgroundSecondaryColor)
                        .border(0.5.dp, BorderColor)
                        .padding(horizontal = 16.dp, vertical = 10.dp),
                )
            }

            item {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 10.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    mapOf(
                        DeviceMode.FULL to "Full Metrics",
                        DeviceMode.FULL_WITH_PULSE to "With Pulse",
                        DeviceMode.WEIGHT_ONLY to "Weight Only"
                    ).forEach { (value, label) ->
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .weight(1f)
                        ) {
                            RadioButton(
                                selected = tempMode.value == value,
                                enabled = tempMode.value != value,
                                colors = RadioButtonDefaults.colors(
                                    disabledSelectedColor = PrimaryColor
                                ),
                                onClick = {
                                    if (value == DeviceMode.FULL_WITH_PULSE && !tempMetricsList.contains(
                                            "heartRate"
                                        )
                                    ) {
                                        tempMetricsList += ("heartRate")
                                    } else if (tempMetricsList.contains(
                                            "heartRate"
                                        )
                                    ) {
                                        tempMetricsList -= ("heartRate")
                                    }
                                    tempMode.value = value
                                }
                            )
                            Text(
                                text = label,
                                fontFamily = OpenSansFont,
                                fontSize = 16.sp,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }
                    }
                }
            }

            item {
                Text(
                    "Body Metrics",
                    fontFamily = OpenSansFont,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(BackgroundSecondaryColor)
                        .border(0.5.dp, BorderColor)
                        .padding(horizontal = 16.dp, vertical = 10.dp),
                )
            }

            items(remainingMetrics) { metric ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 32.dp, vertical = 10.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = metric.normalize(),
                        modifier = Modifier.weight(1f),
                        fontFamily = OpenSansFont,
                        color = if (tempMetricsList.contains(metric)) TextPrimaryColor else TextSecondaryColor,
                        fontSize = 16.sp,
                    )

                    Switch(
                        checked = tempMetricsList.contains(metric),
                        onCheckedChange = { isChecked ->
                            if (isChecked) {
                                if (!tempMetricsList.contains(metric)) tempMetricsList += (metric)
                            } else {
                                tempMetricsList -= (metric)
                            }
                        },
                        colors = SwitchDefaults.colors(
                            checkedIconColor = PrimaryColor,
                            checkedTrackColor = PrimaryColor,
                            uncheckedTrackColor = SurfaceBackgroundColor
                        ),
                        modifier = Modifier.size(24.dp)
                    )
                }
                HorizontalDivider()
            }

            item {
                Text(
                    "Other Metrics",
                    fontFamily = OpenSansFont,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(BackgroundSecondaryColor)
                        .border(0.5.dp, BorderColor)
                        .padding(horizontal = 16.dp, vertical = 10.dp),
                )
            }

            items(otherMetrics) { metric ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 32.dp, vertical = 10.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = metric.normalize(),
                        modifier = Modifier.weight(1f),
                        fontFamily = OpenSansFont,
                        color = if (tempMetricsList.contains(metric)) TextPrimaryColor else TextSecondaryColor,
                        fontSize = 16.sp,
                    )

                    Switch(
                        checked = tempMetricsList.contains(metric),
                        onCheckedChange = { isChecked ->
                            if (isChecked) {
                                if (!tempMetricsList.contains(metric)) tempMetricsList += (metric)
                            } else {
                                tempMetricsList -= (metric)
                            }
                        },
                        colors = SwitchDefaults.colors(
                            checkedIconColor = PrimaryColor,
                            checkedTrackColor = PrimaryColor,
                            uncheckedTrackColor = SurfaceBackgroundColor
                        ),
                        modifier = Modifier.size(24.dp)
                    )
                }
                HorizontalDivider()
            }
        }
    }
}

enum class DeviceMode {
    FULL, FULL_WITH_PULSE, WEIGHT_ONLY
}


@PreviewScreens
@Composable
private fun MetricsSheetsPreview() {
    GGBluetoothLibraryTheme {
        MetricsSheet(listOf(), DeviceMode.FULL_WITH_PULSE, {}) { }
    }
}