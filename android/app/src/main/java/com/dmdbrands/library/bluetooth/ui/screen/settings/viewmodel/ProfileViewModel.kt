package com.dmdbrands.library.bluetooth.ui.screen.settings.viewmodel

import androidx.lifecycle.viewModelScope
import com.dmdbrands.library.bluetooth.model.interfaces.IUserRepository
import com.dmdbrands.library.bluetooth.ui.screen.settings.reducer.ProfileIntent
import com.dmdbrands.library.bluetooth.ui.screen.settings.reducer.ProfileScreenReducer
import com.dmdbrands.library.bluetooth.ui.screen.settings.reducer.ProfileState
import com.dmdbrands.library.bluetooth.util.CommonUtil.responseWrapper
import com.example.utilities.services.viewmodel.BaseViewModel
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch

@HiltViewModel(assistedFactory = ProfileViewModel.Factory::class)
class ProfileViewModel @AssistedInject constructor(
    @Assisted private val appType: String,
    private val userRepository: IUserRepository,
) :
    BaseViewModel<ProfileState, ProfileIntent>(
        initialState = ProfileState(),
        reducer = ProfileScreenReducer()
    ) {
        

    @AssistedFactory
    interface Factory {
        fun create(appType: String): ProfileViewModel
    }

    fun fetchUserDetails() {
        handleIntent(ProfileIntent.AlterRefreshing(true))
        viewModelScope.launch {
            val response = userRepository.getUserDetails(appType)
            responseWrapper(response = response) {
                handleIntent(ProfileIntent.setUser(response?.body()!!))
            }
            handleIntent(ProfileIntent.AlterRefreshing(false))
        }
    }

    fun logout(appType: String) {
        viewModelScope.launch {
            userRepository.logout(appType)
        }
    }
}