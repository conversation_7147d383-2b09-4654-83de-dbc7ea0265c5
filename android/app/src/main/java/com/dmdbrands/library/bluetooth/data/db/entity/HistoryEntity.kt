package com.dmdbrands.library.bluetooth.data.db.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.dmdbrands.library.bluetooth.model.DetailItem
import com.dmdbrands.library.bluetooth.model.HistoryData
import com.google.gson.GsonBuilder
import com.google.gson.ToNumberPolicy

@Entity(tableName = "history")
data class HistoryEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    @ColumnInfo(name = "app_type")
    val appType: String,
    @ColumnInfo(name = "sku")
    val sku: String? = null,
    @ColumnInfo(name = "unit")
    val unit: String?,
    @ColumnInfo(name = "entry_timestamp")
    val entryTimestamp: String,
    @ColumnInfo(name = "op_timestamp")
    val opTimestamp: String,
    @ColumnInfo(name = "operation")
    val operation: String,
    @ColumnInfo(name = "broadcast_id")
    val broadcastId: String?,
    @ColumnInfo(name = "details")
    val details: String // JSON string for detailItems
) {
    fun toHistoryData(): HistoryData {
        val gson = GsonBuilder()
            .setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE)
            .create()
        val detailItemsList =
            gson.fromJson(details, Array<DetailItem>::class.java).toList().applyDefaults()

        return HistoryData(
            id = id,
            appType = appType,
            sku = sku,
            entryTimestamp = entryTimestamp,
            unit = unit,
            opTimestamp = opTimestamp,
            operation = operation,
            broadcastId = broadcastId,
            detailItems = detailItemsList
        )
    }
}

fun List<DetailItem>.applyDefaults(): List<DetailItem> {
    return this.map { it.copy(enabled = true) }
}