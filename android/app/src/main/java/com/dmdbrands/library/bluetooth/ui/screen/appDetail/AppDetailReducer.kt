package com.dmdbrands.library.bluetooth.ui.screen.appDetail

import com.dmdbrands.library.bluetooth.model.api.SubUser
import com.example.utilities.modal.interfaces.IReducer

data class AppDetailState(
    val subUser: SubUser? = null,
    val subUsers: List<SubUser> = listOf()
) : IReducer.State

sealed interface AppDetailIntent : IReducer.Intent {
    data class SetSubUser(val subUser: SubUser) : AppDetailIntent
    data class UpdateSubUsers(val subUsers: List<SubUser>) : AppDetailIntent
}

class AppDetailReducer : IReducer<AppDetailState, AppDetailIntent> {
    override fun reduce(
        state: AppDetailState,
        intent: AppDetailIntent
    ): AppDetailState? {
        return when (intent) {
            is AppDetailIntent.SetSubUser -> {
                state.copy(subUser = intent.subUser)
            }

            is AppDetailIntent.UpdateSubUsers -> {
                state.copy(subUsers = intent.subUsers)
            }


        }
    }

}