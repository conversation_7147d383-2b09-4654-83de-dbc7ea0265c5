package com.dmdbrands.library.bluetooth.config

object AppLang {
    object ScanDevices {
        const val TITLE = "Scan Devices"
        const val SCAN = "Scan"
        const val STOP = "Stop"
        const val DENIED = "Denied"
    }

    object Settings {
        const val TITLE = "Settings"
        const val PROFILE = "Profile"
        const val GUEST = "Guest"
        const val EDIT = "Edit"
        const val PERMISSIONS = "Permissions"
        const val APP_SETTINGS = "App Settings"
    }

    object PairedDevice {
        const val TITLE = "Paired Devices"
        const val NO_FILTER = "All"
        const val APP = "Apps"
    }

    object Dialog {
        const val DELETE = "Delete"
        const val SWITCH = "Switch"
        const val DISCARD = "Discard"
        const val CANCEL = "Cancel"
        const val CREATE = "Create"
        const val REMOVE = "Remove"
        const val DELETE_INFO =
            "Deleting this device will disconnect it and you will lose access to its details."
        const val DELETE_USER =
            "Are you sure you want to delete this user? This action cannot be undone."
        const val SWITCH_USER = "" +
                "Are you sure you want to switch to another user?"
        const val CREATE_ACCOUNT_INFO =
            "Are you sure you want to create a new account?"
        const val DISCARD_CHANGES =
            "Are you sure you want to discard the changes?"
        const val LOGIN_TO_YOUR_ACCOUNT = "Login to your account"
        const val LOGOUT_INFO = "Are you sure you want to logout?"
    }

    const val ALERT = "Alert"

    object Toast {
        const val CONNECTED = "Connected"
        const val DISCONNECTED = "Disconnected"
        const val CONNECTION_FAILED = "Connection Failed"
        const val DEVICE_NOT_FOUND = "Device Not Found"
        const val DEVICE_ALREADY_CONNECTED = "Device Already Connected"
        const val HISTORY_ADDED = "History Added"
        const val HISTORY_SENT = "History Sent"

    }

    object Loader {
        const val SAVING = "Saving"
    }

    const val CONNECT = "Connect"
    const val DISCONNECT = "Disconnect"
    const val SAVE = "Save"
    const val SELECT = "Select"
    const val LOGIN = "Login"
    const val LOGOUT = "Logout"

}