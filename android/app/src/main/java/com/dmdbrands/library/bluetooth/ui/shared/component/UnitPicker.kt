package com.dmdbrands.library.bluetooth.ui.shared.component

import androidx.compose.runtime.Composable
import com.dmdbrands.library.bluetooth.model.UnitData
import com.example.utilities.modal.PickerItem
import com.example.utilities.services.component.picker.PickerPopup

@Composable
fun <T> UnitPicker(
    value: String? = null,
    unitList: List<UnitData<T>>,
    onUpdate: (String) -> Unit
) {

    val units = unitList.map { PickerItem(it.unit, it.type, it.label) }
    val type = if (value != null) unitList.find { it.unit == value }?.type else null
    val label = if (value != null) unitList.find { it.unit == value }?.label else null
    PickerPopup(
        label = "UNIT",
        buttonText = "UPDATE",
        value = listOf(
            PickerItem(
                value ?: units.first().label,
                type ?: units.first().value,
                label ?: units.first().description
            )
        ),
        key = PickerItem<T>::label,
        items = listOf(units),
    ) {
        onUpdate(it.first().description)
    }
}