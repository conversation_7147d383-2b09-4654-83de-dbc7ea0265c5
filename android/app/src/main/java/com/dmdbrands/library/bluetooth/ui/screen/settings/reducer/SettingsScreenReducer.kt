package com.dmdbrands.library.bluetooth.ui.screen.settings.reducer

import androidx.compose.runtime.Immutable
import com.dmdbrands.library.bluetooth.config.assests.sampleUserDetail
import com.dmdbrands.library.bluetooth.model.Permission
import com.dmdbrands.library.bluetooth.model.UserDetails
import com.example.utilities.modal.interfaces.IReducer
import com.example.utilities.util.FormBuilder
import com.example.utilities.util.FormField
import com.example.utilities.util.FormValidations

enum class EUserDetails {
    NAME, EMAIL, HEIGHT, WEIGHT, GOAL_WEIGHT, BIRTHDAY, GENDER, BODY_TYPE, GOAL_TYPE, UNIT_TYPE
}


class SettingsScreenReducer : IReducer<SettingsScreenReducer.State, SettingsScreenReducer.Intent> {
    @Immutable
    data class State(
        val permissions: List<Permission> = listOf(),
        val user: UserDetails = sampleUserDetail,
    ) : IReducer.State {
        fun getUserForm(): FormBuilder = FormBuilder(
            formFields = mapOf(
                EUserDetails.NAME.name to FormField(
                    value = user.name,
                    validations = listOf(
                        FormValidations.required()
                    )
                ),
                EUserDetails.EMAIL.name to FormField(
                    value = user.email,
                    validations = listOf(
                        FormValidations.required(),
                        FormValidations.email()
                    )
                ),
                EUserDetails.HEIGHT.name to FormField(
                    value = user.height,
                    validations = listOf(
                        FormValidations.required()
                    )
                ),
                EUserDetails.WEIGHT.name to FormField(
                    value = user.weight ?: "",
                    validations = listOf(
                        FormValidations.required()
                    )
                ),
                EUserDetails.GOAL_WEIGHT.name to FormField(
                    value = user.goalWeight ?: "",
                    validations = listOf(
                        FormValidations.required()
                    )
                ),
                EUserDetails.BIRTHDAY.name to FormField(
                    value = user.birthday
                ),
                EUserDetails.GENDER.name to FormField(
                    value = user.gender
                ),
                EUserDetails.BODY_TYPE.name to FormField(
                    value = user.isAthlete == true
                ),
                EUserDetails.GOAL_TYPE.name to FormField(
                    value = user.goalType ?: ""
                ),
                EUserDetails.UNIT_TYPE.name to FormField(
                    value = user.unitType
                )
            )
        )
    }

    @Immutable
    sealed class Intent : IReducer.Intent {
        data class updatePermissions(val permission: List<Permission>) : Intent()
        data class setUser(val user: UserDetails) : Intent()
        data class onSave(val user: UserDetails) : Intent()
    }

    override fun reduce(
        state: State,
        intent: Intent
    ): State? {
        return when (intent) {
            is Intent.updatePermissions -> {
                state.copy(permissions = intent.permission)
            }

            is Intent.setUser -> {
                state.copy(user = intent.user)
            }

            else -> state
        }
    }
}