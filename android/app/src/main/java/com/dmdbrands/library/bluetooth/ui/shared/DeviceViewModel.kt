package com.dmdbrands.library.bluetooth.ui.shared

import androidx.lifecycle.viewModelScope
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.interfaces.IBaseRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IDeviceRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IUserRepository
import com.dmdbrands.library.bluetooth.ui.shared.component.ProfilePopup
import com.dmdbrands.library.bluetooth.ui.shared.component.UserConflictPager
import com.dmdbrands.library.bluetooth.ui.shared.component.device.AddUserPager
import com.dmdbrands.library.bluetooth.util.DeviceUtil.convertToGGBTDevice
import com.dmdbrands.library.ggbluetooth.enums.GGUserActionResponseType
import com.dmdbrands.library.ggbluetooth.model.GGBTUser
import com.example.bluetoothwrapper.service.GGDeviceService
import com.example.utilities.config.GG_TOAST_DELIMITER
import com.example.utilities.modal.ActionButton
import com.example.utilities.modal.Dialog
import com.example.utilities.modal.Modal
import com.example.utilities.modal.interfaces.IReducer
import com.example.utilities.services.viewmodel.BaseViewModel
import com.example.utilities.util.CalendarUtil
import com.example.utilities.util.onlyString
import kotlinx.coroutines.launch
import java.security.SecureRandom
import javax.inject.Inject

open class DeviceViewModel<State : IReducer.State, Intent : IReducer.Intent>(
    initialState: State,
    val reducer: IReducer<State, Intent>
) : BaseViewModel<State, Intent>(initialState, reducer) {

    @Inject
    open lateinit var deviceRepository: IDeviceRepository

    @Inject
    lateinit var ggDeviceService: GGDeviceService

    @Inject
    open lateinit var baseRepository: IBaseRepository

    @Inject
    open lateinit var userRepository: IUserRepository

    internal fun showPicker(users: List<GGBTUser>, device: BLEDevice) {
        viewModelScope.launch {
            setModal(
                Modal(
                    onDismiss = {
                        setModal(null)
                    },
                ) {
                    ProfilePopup(
                        users = users,
                        addNewProfile = device.primaryToken == null,
                        createAccount = {
                            createNewAccountPopup(device, users = users)
                        }
                    ) { userName ->
                        val user = users.find { userName == it.name }
                        setLoader("Updating account details")
                        if (user != null) {
                            viewModelScope.launch {
                                deviceRepository.updateToken(
                                    device,
                                    user
                                )
                                ggDeviceService.updateAccount(
                                    device.convertToGGBTDevice(
                                        scaleToken = user.token,
                                        preferences = device.preferences?.copy(displayName = user.name)
                                    )
                                ) {}
                                setModal(null)
                                setLoader(null)
                            }
                        }
                    }
                }
            )
        }
    }

    internal fun createNewAccountPopup(device: BLEDevice, users: List<GGBTUser>) {
        viewModelScope.launch {
            val user = userRepository.getUser(device.getAppType())
            val isPrimaryTokenAvailable = device.primaryToken != null || user == null

            setModal(
                Modal {
                    AddUserPager(
                        users = users,
                        isPrimaryTokenAvailable = isPrimaryTokenAvailable,
                        onCreateLocalUser = { username ->
                            createNewAccount(device, false, username, users)
                        },
                        onAppUserAdd = {
                            createNewAccount(device, true, users = users)
                        },
                        onDismiss = { setModal(null) }
                    )
                }
            )
        }
    }


    internal fun handleAccountUpdateResult(
        result: GGUserActionResponseType,
        isRemote: Boolean,
        userName: String,
        scaleToken: String,
        device: BLEDevice,
        users: List<GGBTUser> = listOf()
    ) {
        setToastMessage(device.device.deviceName + GG_TOAST_DELIMITER + result.name.onlyString())
        setModal(null)
        setLoader(null)

        when (result) {
            GGUserActionResponseType.CREATION_COMPLETED -> {
                viewModelScope.launch {
                    val ggBTUser = GGBTUser(
                        name = userName,
                        token = scaleToken,
                        lastActive = CalendarUtil.getCurrentDate().timeInMillis,
                        isBodyMetricsEnabled = true
                    )
                    deviceRepository.updateToken(device, ggBTUser, isRemote)
                }
            }

            GGUserActionResponseType.DUPLICATE_USER_ERROR -> {
                setModal(
                    Modal {
                        UserConflictPager(
                            userName,
                            users,
                            onEditConfirmed = { editedName ->
                                setLoader("Updating account...")
                                // You can call updateAccount again with the new name
                                val updated = device.copy(
                                    token = scaleToken,
                                    preferences = device.preferences?.copy(displayName = editedName)
                                )
                                ggDeviceService.updateAccount(updated.convertToGGBTDevice()) {
                                    handleAccountUpdateResult(
                                        it,
                                        isRemote,
                                        editedName,
                                        scaleToken,
                                        device,
                                        users
                                    )
                                }
                            },
                            onRestore = {
                                setLoader("Restoring account...")
                                val updated = device.copy(
                                    token = scaleToken,
                                    preferences = device.preferences?.copy(displayName = userName)
                                )
                                ggDeviceService.restoreAccount(
                                    updated.convertToGGBTDevice(),
                                    userName
                                ) {
                                    handleAccountUpdateResult(
                                        it,
                                        isRemote,
                                        userName,
                                        scaleToken,
                                        device,
                                        users
                                    )
                                }
                            },
                            onDismiss = {
                                setModal(null)
                            }
                        )
                    }
                )
            }

            GGUserActionResponseType.MEMORY_FULL -> {
                setModal(
                    Modal {
                        ProfilePopup(
                            users = users,
                            label = "Max Users Reached",
                            buttonText = "Replace",
                            addNewProfile = false
                        ) { user ->
                            if (user != null) {
                                setLoader("Replacing user")
                                val updated = device.copy(
                                    token = scaleToken,
                                    preferences = device.preferences?.copy(displayName = userName)
                                )
                                val ggBTUser = users.find { it.name == user }
                                if (ggBTUser != null && ggBTUser.token == device.primaryToken) {
                                    viewModelScope.launch {
                                        deviceRepository.removeToken(device, true)
                                    }
                                }
                                ggDeviceService.restoreAccount(
                                    updated.convertToGGBTDevice(),
                                    user
                                ) {
                                    handleAccountUpdateResult(
                                        it,
                                        isRemote,
                                        userName,
                                        scaleToken,
                                        device,
                                        users
                                    )


                                }
                            }
                        }
                    }
                )
            }

            else -> Unit
        }
    }

    fun createNewAccount(
        device: BLEDevice,
        isRemote: Boolean,
        userName: String? = null,
        users: List<GGBTUser> = listOf()
    ) {
        setLoader("Checking user...")
        viewModelScope.launch {
            val response = userRepository.getUserDetails(device.getAppType())
            val remoteUser = if (response?.isSuccessful == true) {
                response.body()?.convertToUserDetail()
            } else null
            setLoader(null)
            if (remoteUser != null) {
                fetchTokenAndProceed(userName ?: remoteUser.name, device, isRemote, users)
            } else {
                // Only the token generation is split and handled via dialog
                promptToGenerateLocalToken { localToken ->
                    val displayName = userName ?: "Local User"
                    proceedWithAccountCreation(device, isRemote, displayName, users, localToken)
                }
            }
        }
    }

    private fun fetchTokenAndProceed(
        name: String,
        device: BLEDevice,
        isRemote: Boolean,
        users: List<GGBTUser>
    ) {
        viewModelScope.launch {
            val tokenResponse = userRepository.getScaleToken(device.getAppType())
            if (tokenResponse?.code() == 401) {
                setLoader(null)
                setToastMessage("Try Again!|Invalid token.")
                return@launch
            }

            val scaleToken = tokenResponse?.body()?.token
            if (scaleToken == null) {
                setLoader(null)
                return@launch
            }

            proceedWithAccountCreation(device, isRemote, name, users, scaleToken)
        }
    }

    private fun promptToGenerateLocalToken(onTokenGenerated: (String) -> Unit) {
        showConfirmationDialog(
            message = "No user found. Do you want to create a local token? You won't be able to change Wi-Fi settings later.",
            confirmText = "Create",
            loaderMessage = "Creating account...",
            onConfirm = {
                val localToken = generateLocalToken()
                onTokenGenerated(localToken)
            },
            onDismiss = {
                setDialog(null)
            }
        )
    }

    private fun proceedWithAccountCreation(
        device: BLEDevice,
        isRemote: Boolean,
        userName: String,
        users: List<GGBTUser>,
        scaleToken: String
    ) {
        setLoader("Creating account...")
        val updatedDevice = device.convertToGGBTDevice(
            scaleToken,
            preferences = device.preferences?.copy(displayName = userName)
        )

        ggDeviceService.updateAccount(updatedDevice) { result ->
            handleAccountUpdateResult(result, isRemote, userName, scaleToken, device, users)
        }
    }

    private fun generateLocalToken(byteLength: Int = 32): String {
        val random = SecureRandom()
        val bytes = ByteArray(byteLength)
        random.nextBytes(bytes)
        return bytes.joinToString("") { "%02x".format(it) }
    }

    internal fun showConfirmationDialog(
        message: String,
        confirmText: String,
        onConfirm: suspend () -> Unit,
        title: String = AppLang.ALERT,
        dismissText: String = AppLang.Dialog.CANCEL,
        loaderMessage: String? = null,
        onDismiss: () -> Unit = { setDialog(null) }
    ) {
        viewModelScope.launch {
            setDialog(
                Dialog(
                    title = title,
                    message = message,
                    confirmButton = ActionButton(
                        text = confirmText,
                        action = {
                            if (loaderMessage != null) setLoader(loaderMessage)
                            viewModelScope.launch {
                                onConfirm()
                                setLoader(null)
                                setDialog(null)
                            }
                        }
                    ),
                    dismissButton = ActionButton(
                        text = dismissText,
                        action = onDismiss
                    )
                )
            )
        }
    }

}