package com.dmdbrands.library.bluetooth.ui.shared.component.device

import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.example.utilities.modal.PickerItem
import com.example.utilities.services.component.picker.PickerPopup

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserNumberPopup(
    onDismissRequest: () -> Unit = {},
    device: BLEDevice,
    setUser: (Int) -> Unit = {},
) {
    PickerPopup(
        label = "Select User",
        value = listOf(device.getUserList().first()),
        items = listOf(device.getUserList()),
        buttonText = "Set User",
        key = PickerItem<Int>::label,
        onUpdate = {
            setUser(it.first().value)
            onDismissRequest()
        }
    )
}


//@PreviewScreens
//@Composable
//private fun UserNumberPopupPreview() {
//    GGBluetoothLibraryTheme {
//        UserNumberPopup()
//    }
//}
