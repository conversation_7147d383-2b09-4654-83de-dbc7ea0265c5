package com.dmdbrands.library.bluetooth.model

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.ui.graphics.vector.ImageVector
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

@Immutable
data class DetailItem(
    @Expose
    @SerializedName("name")
    val name: String,

    @Expose
    @SerializedName("value")
    val value: Any? = null,

    @Expose
    @SerializedName("unit")
    val unit: String? = null,

    @Transient
    val imageVector: ImageVector? = null,

    @Transient
    val enabled: Boolean = true,

    @Transient
    val icon: Int? = null,

    @Transient
    val action: (@Composable () -> Unit)? = null,

    @Transient
    val onClick: (() -> Unit)? = null

)
