package com.dmdbrands.library.bluetooth.data.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import com.dmdbrands.library.bluetooth.data.db.entity.UserEntity

@Dao
interface UserDao {

    @Insert
    fun insertUser(user: UserEntity)

    @Query("SELECT * FROM users WHERE id = :userId")
    fun getUserById(userId: String): UserEntity?

    @Query("SELECT * FROM users WHERE appType = :appType")
    fun getUserByAppType(appType: String): UserEntity?

    @Query("UPDATE users SET subCategory = :category WHERE id = :userId")
    fun setUserCategory(userId: String, category: String)

    @Query("DELETE FROM users WHERE id = :userId")
    fun deleteUserById(userId: String)

    @Query("DELETE FROM users WHERE token = :token")
    fun deleteUserByToken(token: String)

    @Query("Select 1 from users where token = :token")
    fun checkUserAvailable(token: String): Boolean

    @Query("Select * from users")
    fun getAllUsers(): List<UserEntity>
}