package com.dmdbrands.library.bluetooth.ui.shared.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.RowScope
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.dmdbrands.library.bluetooth.ui.theme.DividerColor
import com.example.utilities.modal.DraggableListState
import com.example.utilities.services.component.list.DraggableList
import com.example.utilities.services.component.list.StaticList
import com.example.utilities.services.component.list.rememberDraggableListState

@Composable
fun <T> AppList(
    modifier: Modifier = Modifier,
    draggableListState: DraggableListState = rememberDraggableListState(),
    list: List<T>,
    isDraggable: Boolean = false,
    actionModifier: Modifier = Modifier,
    actionContent: @Composable (RowScope.(Int, T, Modifier) -> Unit) = { _, _, _ -> },
    staticContent: (@Composable (Int, T) -> Unit)? = null,
    contentPadding: PaddingValues = PaddingValues(0.dp),
    content: @Composable ((T) -> Unit)
) {
    if (isDraggable) {
        DraggableList(
            modifier = modifier,
            draggableListState = draggableListState,
            list = list,
            iconWidth = 54.dp,
            actionModifier = actionModifier,
            actionContent = actionContent,
            staticContent = staticContent,
            contentPadding = contentPadding
        ) { item, index ->
            Column {
                content(item)
                if (index != list.size - 1) {
                    HorizontalDivider(color = DividerColor, thickness = 1.dp)
                }
            }
        }
    } else {
        StaticList(
            modifier = modifier,
            list = list,
            contentPadding = contentPadding,
            staticContent = staticContent
        ) { index, item ->
            Column {
                content(item)
                if (index != list.size - 1) {
                    HorizontalDivider(color = DividerColor, thickness = 1.dp)
                }
            }
        }
    }
}
