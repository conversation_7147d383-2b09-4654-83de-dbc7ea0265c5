package com.dmdbrands.library.bluetooth.ui.shared.component.device

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.dmdbrands.library.bluetooth.config.AppRoute
import com.dmdbrands.library.bluetooth.config.LocalNavController
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.ui.screen.scanDevice.component.ConnectButton
import com.dmdbrands.library.bluetooth.ui.shared.DeviceIntent
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.theme.BackgroundSecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.ggbluetooth.model.GGDeviceDetail
import com.example.utilities.services.viewmodel.UtilitiesViewmodel

@Composable
fun DeviceCard(
    device: BLEDevice,
    modifier: Modifier = Modifier,
    utility: UtilitiesViewmodel = hiltViewModel<UtilitiesViewmodel>(),
    sendIntent: (DeviceIntent) -> Unit = {},
) {


    val navController = LocalNavController.current

    Row(
        modifier = Modifier
            .background(BackgroundSecondaryColor)
            .fillMaxWidth()
            .clickable(enabled = device.alreadyPaired) {
                navController.navigate(AppRoute.DeviceDetail(device.device.broadcastId ?: "")) {
                    launchSingleTop = true
                    restoreState = true
                }
            }
            .padding(12.dp)
            .then(modifier),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        DeviceSummary(modifier = Modifier.weight(1f), device = device)
        ConnectButton(
            device = device,
            utility = utility,
            sendIntent = sendIntent
        )
    }
}

@PreviewScreens
@Composable
fun DeviceCardPreview() {
    GGBluetoothLibraryTheme {
        DeviceCard(
            BLEDevice(
                GGDeviceDetail(
                    deviceName = "Food scale",
                    macAddress = "12:12:45:15:15:15",
                    broadcastId = "12451115",
                    identifier = "12121"
                ),
            )
        ) {}
    }
}