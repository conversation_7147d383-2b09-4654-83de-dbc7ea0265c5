package com.dmdbrands.library.bluetooth.ui.screen.apps

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import androidx.hilt.navigation.compose.hiltViewModel
import com.dmdbrands.library.bluetooth.config.AppRoute
import com.dmdbrands.library.bluetooth.config.LocalNavController
import com.dmdbrands.library.bluetooth.ui.screen.apps.component.AppTypeCard
import com.dmdbrands.library.bluetooth.ui.screen.apps.component.LoginDialog
import com.dmdbrands.library.bluetooth.ui.shared.component.AppScaffold
import com.example.utilities.modal.Modal
import com.example.utilities.services.component.list.StaticList
import com.example.utilities.services.theme.DividerColor

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppsScreen() {
    val viewmodel: AppsScreenViewmodel = hiltViewModel()
    val state by viewmodel.state.collectAsState()
    var enabled by remember { mutableStateOf(true) }
    val navController = LocalNavController.current
    AppScaffold(title = "APPS") {
        StaticList(
            list = state.appsList,
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .clip(RoundedCornerShape(10.dp)),
        ) { index, app ->
            AppTypeCard(app) {
                if (app.isValidToken()) {
                    viewmodel.selectApp(app.type)
                    navController.navigate(AppRoute.AppDetail(app.type))
                } else {
                    viewmodel.setModal(
                        Modal(
                            onDismiss = {
                                viewmodel.setModal(null)
                            },
                            properties = DialogProperties(
                                dismissOnBackPress = false,
                                dismissOnClickOutside = false
                            )
                        ) {
                            LoginDialog(app.type, enabled = enabled, onDismiss = {
                                viewmodel.setModal(null)
                            }) { email, password ->
                                viewmodel.login(email, password, app.type) {
                                    enabled = it
                                }
                            }
                        })
                }
            }
            if (index != state.appsList.lastIndex)
                HorizontalDivider(color = DividerColor, thickness = 1.dp)

        }
    }
}