package com.dmdbrands.library.bluetooth.ui.screen.history.local

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.dmdbrands.library.bluetooth.ui.screen.history.HistoryIntent
import com.dmdbrands.library.bluetooth.ui.screen.history.components.HistoryList
import com.dmdbrands.library.bluetooth.ui.screen.scanDevice.component.AppTypeDropDown
import com.dmdbrands.library.bluetooth.ui.shared.component.AppScaffold
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.sage.ui.shared.component.tab.TabTitle
import com.dmdbrands.sage.ui.shared.component.tab.TabType
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.component.tab.Tab

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HistoryScreen() {
    val historyViewmodel: HistoryViewmodel = hiltViewModel()
    val state by historyViewmodel.state.collectAsState()
    val tabs = listOf(
        TabTitle.TextTitle("In Device"),
        TabTitle.TextTitle("App"),
    )
    val pagerState = rememberPagerState(0) { tabs.size }


    AppScaffold(titleComposable = {
        AppTypeDropDown(state.appType, enabled = true) {
            historyViewmodel.handleIntent(
                HistoryIntent.ChangeAppType(it)
            )
        }
    }, actions = {
        AppButton(
            onClick = {
                historyViewmodel.refresh()
            },
            containerColor = Transparent,
            disabledContainerColor = Transparent
        ) {
            Icon(
                imageVector = Icons.Default.Refresh,
                contentDescription = "Refresh",
                tint = PrimaryColor,
                modifier = Modifier.size(26.dp)
            )
        }
    }) {
        if (state.appStatus.isNotEmpty()) {
            Tab(
                type = TabType.BOXED,
                userScrollEnabled = false,
                modifier = Modifier.fillMaxWidth(0.7f),
                pagerState = pagerState,
                titles = tabs,
            ) {
                when (it) {

                    0 -> {
                        HistoryList(
                            list = state.getFilteredHistories(),
                            state.localRefreshing,
                        ) {
                            historyViewmodel.removeHistory(it)
                        }
                    }

                    1 -> {
                        HistoryList(
                            list = state.getFilteredRemoteHistories(),
                            state.remoteRefreshing,
                            isDraggable = false
                        )
                    }
                }

            }
        } else {
            HistoryList(list = state.getFilteredHistories()) {
                historyViewmodel.removeHistory(it)
            }
        }
    }
}

@PreviewScreenSizes
@Composable
private fun HistoryPreview() {
    HistoryScreen()
}