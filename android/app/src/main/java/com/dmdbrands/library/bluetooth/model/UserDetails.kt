package com.dmdbrands.library.bluetooth.model

import com.example.utilities.util.CalendarUtil
import java.util.Calendar

data class UserDetails(
    val name: String = "",
    val email: String = "",
    val gender: String = "",
    val height: String = "",
    val weight: String? = "",
    val goalWeight: String? = "",
    val goalType: String? = "",
    val isAthlete: Boolean = false,
    val zipCode: String = "",
    val birthday: Calendar = CalendarUtil.getCurrentDate(),
    val unitType: String = "",
    val metrics: List<String> = listOf()
) {
    fun getAge(): Int {
        var age = CalendarUtil.getCurrentDate().get(Calendar.YEAR) - birthday.get(Calendar.YEAR)
        if (CalendarUtil.getCurrentDate()
                .get(Calendar.DAY_OF_YEAR) < birthday.get(Calendar.DAY_OF_YEAR)
        ) {
            age--
        }
        return age
    }
}
