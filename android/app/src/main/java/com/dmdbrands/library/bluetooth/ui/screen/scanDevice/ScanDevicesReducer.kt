package com.dmdbrands.library.bluetooth.ui.screen.scanDevice

import com.dmdbrands.library.bluetooth.ui.shared.DeviceIntent
import com.dmdbrands.library.bluetooth.ui.shared.ScanDeviceState
import com.example.utilities.modal.interfaces.IReducer


class ScanDevicesReducer : IReducer<ScanDeviceState, DeviceIntent> {

    override fun reduce(
        state: ScanDeviceState,
        intent: DeviceIntent
    ): ScanDeviceState? {


        return when (intent) {

            is DeviceIntent.AddDevice -> {
                state.copy(localDevices = intent.devices)
            }

            is DeviceIntent.ChangeAppType -> {
                state.copy(appType = intent.appType)
            }

            is DeviceIntent.AlterLocalRefreshState -> {
                state.copy(localRefreshing = intent.isRefreshing)
            }

            else -> {
                state
            }
        }
    }
}
