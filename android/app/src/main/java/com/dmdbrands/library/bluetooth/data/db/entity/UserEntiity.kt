package com.dmdbrands.library.bluetooth.data.db.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "users")
data class UserEntity(
    @PrimaryKey @ColumnInfo(name = "id") val id: String,
    @ColumnInfo(name = "email") val email: String?,
    @ColumnInfo(name = "firstName") val firstName: String,
    @ColumnInfo(name = "lastName") val lastName: String,
    @ColumnInfo(name = "token") val token: String,
    @ColumnInfo(name = "refreshToken") val refreshToken: String,
    @ColumnInfo(name = "appType") val appType: String,
    @ColumnInfo(name = "subCategory") val subCategory: Pair<Boolean, String?> = Pair(false, null),
)