package com.dmdbrands.library.bluetooth.ui.shared

import com.dmdbrands.library.bluetooth.model.AppStatus
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.DeviceAction
import com.example.utilities.modal.interfaces.IReducer

sealed interface DeviceIntent : IReducer.Intent {
    data class AlterLocalRefreshState(val isRefreshing: Boolean) : PairedDeviceIntent
    data class AddDevice(val devices: List<BLEDevice>) : DeviceIntent
    data class RemoveDevice(val device: BLEDevice) : DeviceIntent
    data class ChangeAppType(val appType: String) : DeviceIntent
    data object Refresh : DeviceIntent
    data class addProfile(val device: BLEDevice) : DeviceIntent
}

sealed interface PairedDeviceIntent : DeviceIntent {
    data class AddRemoteDevice(val devices: List<BLEDevice>) : PairedDeviceIntent
    data class RemoveRemoteDevice(val device: BLEDevice) : PairedDeviceIntent
    data class AlterRemoteRefreshState(val isRefreshing: Boolean) : PairedDeviceIntent
    data class ModifyAppStatus(val appStatus: List<AppStatus>) : PairedDeviceIntent
}

sealed interface ScanDeviceIntent : DeviceIntent {
    data class setAppType(val appType: String) : ScanDeviceIntent
    data class onDeviceAction(val device: BLEDevice, val action: DeviceAction) : ScanDeviceIntent
    data class onConnectAction(val device: BLEDevice) : ScanDeviceIntent
}
