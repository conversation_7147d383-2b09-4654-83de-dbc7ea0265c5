package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.dmdbrands.library.bluetooth.config.assests.ggDevices
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.BLEStatus
import com.dmdbrands.library.bluetooth.model.DetailItem
import com.dmdbrands.library.bluetooth.model.DeviceLogs
import com.dmdbrands.library.bluetooth.model.api.Preferences
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.DeviceDetailReducer
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.sheet.DeviceMode
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.sheet.MacSheet
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.sheet.MetricsSheet
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.sheet.WifiSheet
import com.dmdbrands.library.bluetooth.ui.screen.deviceInfo.component.ErrorCodeSheet
import com.dmdbrands.library.bluetooth.ui.shared.component.DetailContainer
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.bluetooth.util.Source
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.example.utilities.util.CalendarUtil
import com.example.utilities.util.camelCase
import com.example.utilities.util.onlyString
import com.example.utilities.util.openInAppBrowser
import com.greatergoods.ggbluetoothsdk.external.models.GGWifiInfo

@RequiresApi(Build.VERSION_CODES.O)
@Composable
fun DeviceDetailView(
    modifier: Modifier = Modifier,
    wifiList: List<GGWifiInfo>?,
    deviceLog: DeviceLogs,
    device: BLEDevice?,
    wifiMacAddress: String?,
    handleIntent: (DeviceDetailReducer.Intent) -> Unit,
) {

    fun currentMode(preference: Preferences?): DeviceMode {
        return when {
            preference?.shouldMeasureImpedance == false -> DeviceMode.WEIGHT_ONLY
            preference?.shouldMeasurePulse == true -> DeviceMode.FULL_WITH_PULSE
            else -> DeviceMode.FULL
        }
    }

    val context = LocalContext.current
    val deviceImage = ggDevices.find { it.sku == device?.sku }?.image
    val errorCodes = ggDevices.find { it.sku == device?.sku }?.errorCodes ?: listOf()
    var isSheetOpen: String? by remember { mutableStateOf(null) }
    when (isSheetOpen) {
        "errorCodes" -> ErrorCodeSheet(errorCodes) {
            isSheetOpen = null
        }

        "wifi" -> WifiSheet(device?.wifiSSID, wifiList, handleIntent) {
            handleIntent(DeviceDetailReducer.Intent.clearWifiList)
            isSheetOpen = null
        }

        "mac" -> MacSheet(wifiMacAddress, handleIntent) {
            isSheetOpen = null
        }

        "metrics" -> MetricsSheet(
            device?.preferences?.displayMetrics ?: listOf(), currentMode(
                device?.preferences
            ), handleIntent
        ) {
            isSheetOpen = null
        }

        "logs" -> DeviceLogs(
            deviceLog = deviceLog,
            onDismiss = {
                isSheetOpen = null
            },
            handleIntent = handleIntent
        )
    }
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Card(
            modifier = Modifier.fillMaxSize(0.6f),
            colors = CardDefaults.cardColors(containerColor = Transparent)
        ) {
            if (deviceImage == null) return@Card
            Image(
                painter = painterResource(id = deviceImage),
                contentDescription = null,
                modifier = Modifier.fillMaxSize()
            )
        }
        DetailContainer(
            title = "Device Info", list = buildList {
                addAll(
                    listOf(
                        DetailItem("Scale Name", device?.device?.deviceName?.onlyString() ?: "--"),
                        DetailItem(
                            "Scale Type",
                            device?.getAppType()?.onlyString()?.camelCase() ?: "--"
                        ),
                        DetailItem("SKU", device?.sku ?: "--"),
                        DetailItem("User Number", device?.userNumber.toString()),
                        DetailItem("Broadcast Id", device?.device?.broadcastId ?: "--")
                    )
                )
                if (!device?.preferences?.displayMetrics.isNullOrEmpty()) {
                    add(
                        DetailItem(
                            "Metrics",
                            imageVector = Icons.Default.ChevronRight,
                            enabled = device.token != null
                        ) {
                            isSheetOpen = "metrics"
                        })
                }
                if (device?.sku == "0412")
                    add(
                        DetailItem(
                            "Logs",
                            imageVector = Icons.Default.ChevronRight,
                            enabled = device?.connectionStatus == BLEStatus.CONNECTED
                        ) {
                            isSheetOpen = "logs"
                        })
            }
        )
        DetailContainer(
            title = "Connection",
            list = buildList {
                // Bluetooth item
                add(
                    DetailItem(
                        name = "Bluetooth",
                        value = if (device?.connectionStatus == BLEStatus.CONNECTED) "Connected" else "Disconnected"
                    )
                )

                // Wi-Fi and MAC (only for specific connected device)
                if (device?.sku == "0412") {
                    add(
                        DetailItem(
                            name = "Wi-Fi",
                            value = if (device.wifiSSID.isNullOrBlank()) "Not Connected" else device.wifiSSID,
                            imageVector = if (device.wifiSSID.isNullOrBlank()) Icons.Default.ChevronRight else null,
                            enabled = device.connectionStatus == BLEStatus.CONNECTED
                        ) {
                            handleIntent(DeviceDetailReducer.Intent.getWifiList)
                            isSheetOpen = "wifi"
                        }
                    )
                    add(
                        DetailItem(
                            name = "Wi-Fi MacAddress",
                            imageVector = Icons.Default.ChevronRight,
                            enabled = device.wifiSSID != null
                        ) {
                            handleIntent(DeviceDetailReducer.Intent.getConnectedWifiMacAddress)
                            isSheetOpen = "mac"
                        }
                    )
                }

                // App Link toggle (conditionally shown)
                if (
                    device?.source != Source.API &&
                    device?.isLinked != null &&
                    device.getAppType() != GGAppType.SMART_BABY
                ) {
                    add(
                        DetailItem(
                            name = "App Link",
                            action = {
                                Switch(
                                    checked = device.isLinked,
                                    modifier = Modifier
                                        .size(24.dp)
                                        .padding(end = 24.dp),
                                    colors = SwitchDefaults.colors(checkedTrackColor = PrimaryColor),
                                    onCheckedChange = {
                                        handleIntent(
                                            if (device.isLinked)
                                                DeviceDetailReducer.Intent.unlinkFromAccount
                                            else
                                                DeviceDetailReducer.Intent.linkToAccount
                                        )
                                    },
                                )
                            },
                        )
                    )
                }
            }
        )


        DetailContainer(
            title = "Support",
            list = listOf(
                DetailItem(
                    "Date Paired",
                    CalendarUtil.dateStringFromTimeStamp(device?.createdAt ?: "--")
                ),
                DetailItem(
                    "Product Guide",
                    imageVector = Icons.Default.ChevronRight,
                ) {
                    openInAppBrowser(context, "https://greatergoods.com/service/${device?.sku}")
                },
                DetailItem(
                    "Error Codes",
                    imageVector = Icons.Default.ChevronRight,
                ) {
                    isSheetOpen = "errorCodes"
                },
            )
        )
        Spacer(modifier = Modifier.height(120.dp))
    }
}