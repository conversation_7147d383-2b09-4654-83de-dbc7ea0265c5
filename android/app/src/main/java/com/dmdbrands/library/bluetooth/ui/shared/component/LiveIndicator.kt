package com.dmdbrands.library.bluetooth.ui.shared.component

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme

@Composable
fun LiveIndicator(
    isLive: Boolean,
    modifier: Modifier = Modifier,
    deviceName: String? = null
) {
    val updatedModifier = (if(isLive) Modifier.clip(
        RoundedCornerShape(20.dp)
    ).background(White).padding(horizontal = 10.dp , vertical = 2.dp) else Modifier).then(modifier)

    if (isLive) {
        val infiniteTransition = rememberInfiniteTransition(label = "Unique Live Indicator")


        // Opacity animation
        val alphaAnimation by infiniteTransition.animateFloat(
            initialValue = 0.2f,
            targetValue = 1f,
            animationSpec = infiniteRepeatable(
                animation = tween(durationMillis = 1000, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            ),
            label = "Opacity Animation"
        )

        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .padding(bottom = 4.dp)
                .alpha(alphaAnimation)
                .then(updatedModifier)
        ) {
            // Animated Live Indicator
            Box(
                modifier = Modifier
                    .padding(end = 8.dp)
                    .size(10.dp)
                    .background(Color.Red, shape = CircleShape)
            )

            // Live Text with synchronized opacity
            Text(
                text = "LIVE",
                color = Color.Red,
                fontSize = 10.sp,
            )
        }
    } else {
        NotLiveIndicator(modifier = updatedModifier, deviceName = deviceName)
    }
}

// Example Usage
@PreviewScreens
@Composable
private fun LiveStreamScreen() {
    // Simulate live stream status
    val isCurrentlyLive = true // This could be a real state from your app
    GGBluetoothLibraryTheme {
        LiveIndicator(
            isLive = isCurrentlyLive,
            modifier = Modifier.padding(16.dp)
        )
    }
}