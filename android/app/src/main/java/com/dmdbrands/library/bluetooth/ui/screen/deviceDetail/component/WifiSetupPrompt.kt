package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Wifi
import androidx.compose.material.icons.filled.WifiPassword
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.ui.theme.BackgroundSecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.dmdbrands.library.ggbluetooth.model.GGBTWifiConfig
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.component.animation.DotsPulsing
import com.example.utilities.services.component.input.Input
import com.example.utilities.services.component.input.InputType
import com.example.utilities.services.component.input.TextFieldType

@Composable
fun WifiSetupPrompt(
    ssid: String,
    enabled: Boolean = true,
    onDismiss: () -> Unit = {},
    onClick: (GGBTWifiConfig) -> Unit
) {
    var password by remember { mutableStateOf("") }
    var isPasswordRequired: Boolean by remember { mutableStateOf(true) }


    Card(colors = CardDefaults.cardColors(containerColor = White)) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "Close",
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .clickable {
                        onDismiss()
                    }
            )
            Column(
                modifier = Modifier
                    .fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Spacer(modifier = Modifier.height(10.dp))
                Text(
                    "Enter Wi-fi Password",
                    fontFamily = OpenSansFont,
                    fontSize = 18.sp,
                    color = PrimaryColor,
                    fontWeight = FontWeight.ExtraBold,
                )

                Spacer(modifier = Modifier.height(30.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(10.dp))
                        .background(BackgroundSecondaryColor)
                        .padding(10.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = if (isPasswordRequired) Icons.Default.WifiPassword else Icons.Default.Wifi,
                        contentDescription = null,
                        tint = PrimaryColor
                    )
                    Text(
                        text = ssid,
                        fontFamily = OpenSansFont,
                        fontSize = 16.sp,
                        color = TextPrimaryColor,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
                Spacer(modifier = Modifier.height(30.dp))
                Card(
                    enabled = isPasswordRequired,
                    onClick = {},
                    colors = CardDefaults.cardColors(
                        containerColor = Transparent,
                        disabledContainerColor = Transparent
                    )
                ) {
                    Input(
                        name = "password",
                        label = "Password",
                        value = password,
                        enabled = isPasswordRequired,
                        readOnly = !isPasswordRequired,
                        onValueChange = {
                            password = it.toString()
                        },
                        textFieldType = TextFieldType.BOX,
                        type = InputType.PASSWORD
                    )
                }
                Spacer(modifier = Modifier.height(30.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Network has no password",
                        fontFamily = OpenSansFont,
                        fontSize = 16.sp,
                        color = TextPrimaryColor,
                        modifier = Modifier.weight(1f)
                    )
                    Switch(
                        checked = !isPasswordRequired,
                        onCheckedChange = {
                            isPasswordRequired = !it
                        },
                        colors = SwitchDefaults.colors(
                            uncheckedTrackColor = White,
                            checkedTrackColor = PrimaryColor
                        )
                    )
                }
                Spacer(modifier = Modifier.height(30.dp))
                AppButton(
                    onClick = {
                        val ggWifiConfig = GGBTWifiConfig(
                            ssid = ssid,
                            password = if (isPasswordRequired) password else ""
                        )
                        onClick(ggWifiConfig)
                    },
                    enabled = enabled,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(2.dp),
                    disabledContainerColor = Transparent
                ) {
                    if (enabled)
                        Text(
                            AppLang.CONNECT.uppercase(),
                            fontFamily = OpenSansFont,
                            fontSize = 18.sp,
                            color = White,
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.Center
                        )
                    else
                        DotsPulsing(numberOfDots = 5)
                }
                Spacer(modifier = Modifier.height(10.dp))
            }
        }
    }
}