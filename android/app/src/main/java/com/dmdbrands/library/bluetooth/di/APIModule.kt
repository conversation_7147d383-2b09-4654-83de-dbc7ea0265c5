package com.dmdbrands.library.bluetooth.di

import android.content.Context
import com.dmdbrands.library.bluetooth.BuildConfig
import com.dmdbrands.library.bluetooth.config.AppConfig
import com.dmdbrands.library.bluetooth.data.AppConfiguration
import com.dmdbrands.library.bluetooth.data.api.IDeviceApi
import com.dmdbrands.library.bluetooth.data.api.IOperationAPI
import com.dmdbrands.library.bluetooth.data.api.IUserAPI
import com.dmdbrands.library.bluetooth.data.db.dao.UserDao
import com.dmdbrands.library.bluetooth.model.interfaces.IAppConfiguration
import com.dmdbrands.library.bluetooth.service.BaseUrlInterceptor
import com.dmdbrands.library.bluetooth.service.NetworkInterceptor
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.example.utilities.modal.interfaces.IAppUtility
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.converter.scalars.ScalarsConverterFactory
import java.net.HttpURLConnection
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class APIModule {
    @Provides
    @Singleton
    fun provideAppConfiguration(): IAppConfiguration {
        return AppConfiguration()
    }

    @Provides
    @Singleton
    fun provideUserAPI(retrofit: Retrofit): IUserAPI {
        return retrofit.create(IUserAPI::class.java)
    }

    @Provides
    @Singleton
    fun provideDeviceAPI(retrofit: Retrofit): IDeviceApi {
        return retrofit.create(IDeviceApi::class.java)
    }

    @Provides
    @Singleton
    fun provideOperationAPI(retrofit: Retrofit): IOperationAPI {
        return retrofit.create(IOperationAPI::class.java)
    }


    @Provides
    @Singleton
    fun provideRetrofit(okHttpClient: OkHttpClient): Retrofit {

        return Retrofit.Builder()
            .addConverterFactory(ScalarsConverterFactory.create())
            .addConverterFactory(GsonConverterFactory.create())
            .baseUrl(AppConfig.EMPTY_URL)
            .client(okHttpClient)
            .build()
    }

    @Provides
    @Singleton
    fun provideOkHttpClient(
        interceptor: Interceptor,
        networkInterceptor: NetworkInterceptor,
        loggingInterceptor: HttpLoggingInterceptor,
        baseUrlInterceptor: BaseUrlInterceptor
    ): OkHttpClient {
        val okHttpClient = OkHttpClient.Builder()
        if (BuildConfig.DEBUG) {
            okHttpClient.addInterceptor(loggingInterceptor)
        }
        okHttpClient.addInterceptor(interceptor)
            .addInterceptor(networkInterceptor)
            .addInterceptor(baseUrlInterceptor)
        return okHttpClient.build()
    }


    @Provides
    @Singleton
    fun provideLogInterceptor(): HttpLoggingInterceptor {
        val loggingInterceptor = HttpLoggingInterceptor()
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY)
        return loggingInterceptor
    }

    @Provides
    @Singleton
    fun provideAuthInterceptor(
        appConfiguration: IAppConfiguration,
        userDao: UserDao
    ): Interceptor {
        return Interceptor {
            val request = it.request().newBuilder()
            val token = runBlocking(Dispatchers.IO) {
                userDao.getUserByAppType(appConfiguration.getAppType())?.token
            }
            if (token != null) {
                if (appConfiguration.getAppType() == GGAppType.WEIGHT_GURUS || appConfiguration.getAppType() == GGAppType.SMART_BABY) {
                    request.addHeader("Authorization", "Bearer $token")
                } else {
                    request.addHeader("x-access-token", token)
                }
            }
            val actualRequest = request.build()
            val response = it.proceed(actualRequest)

            if (response.code == HttpURLConnection.HTTP_UNAUTHORIZED) {
                val urlString = response.request.url.toString()
                val endpoints = listOf("login", "signup", "delete", "password")
                val noEndpointsPreset = endpoints.none { endPoint ->
                    urlString.contains(endPoint)
                }
                if (noEndpointsPreset) {
                    runBlocking(Dispatchers.IO) {
                        if (token != null)
                            userDao.deleteUserByToken(token)
                    }
                }
            }
            return@Interceptor response
        }
    }

    @Provides
    @Singleton
    fun provideBaseUrlInterceptor(appConfiguration: IAppConfiguration): BaseUrlInterceptor {
        return BaseUrlInterceptor(
            appConfiguration
        )

    }

    @Provides
    @Singleton
    fun provideNetworkInterceptor(
        @ApplicationContext context: Context,
        appUtility: IAppUtility
    ): NetworkInterceptor {
        return NetworkInterceptor(context, appUtility)
    }
}