package com.dmdbrands.library.bluetooth.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)


val PrimaryColor = Color(0xFF1565C0)
val SecondaryColor = Color(0xFF343F3B)
val TextPrimaryColor = Color(0xFF323F3B)
val SurfaceBackgroundColor = Color(0xFFFFFFFF)
val BorderColor = Color(0x323F3B33)
val TextSecondaryColor = Color(0xFF71756C)
val IconPrimaryColor = Color(0xFF424242)
val IconSecondaryColor = Color(0xFF707070)
val DisabledIconColor = Color(0xFFABABAB)
val DarkGreen = Color(0xFF323F3B)

val DividerColor = Color(0xFFE6E6E6)

val BackgroundSecondaryColor = Color(0xFFF5F5F5)
