package com.dmdbrands.library.bluetooth.ui.screen.device

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.dmdbrands.library.bluetooth.config.AppRoute
import com.dmdbrands.library.bluetooth.config.LocalNavController
import com.dmdbrands.library.bluetooth.config.assests.ggDevices
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.shared.component.AppList
import com.dmdbrands.library.bluetooth.ui.shared.component.AppScaffoldWithSearch
import com.dmdbrands.library.bluetooth.ui.shared.component.BLEDeviceInfo
import com.dmdbrands.library.bluetooth.ui.theme.BackgroundSecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.bluetooth.util.DeviceUtil.getAppIcon

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DevicesScreen() {
    var devices by remember { mutableStateOf(ggDevices) }
    AppScaffoldWithSearch(
        "DEVICES",
        onSearch = { query ->
            devices = if (query.isNotEmpty()) {
                ggDevices.filter {
                    it.name.contains(query, ignoreCase = true) || it.sku?.contains(
                        query,
                        ignoreCase = true
                    ) == true
                }
            } else {
                ggDevices
            }
        }) {
        if (devices.isEmpty()) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "No devices found",
                    fontFamily = OpenSansFont,
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center,
                    color = PrimaryColor
                )
            }
        } else {
            AppList(
                modifier = Modifier
                    .padding(horizontal = 16.dp),
                list = devices,
                contentPadding = PaddingValues(bottom = 100.dp)
            ) { item ->
                DeviceInfo(item)
            }
        }
    }
}


@Composable
private fun DeviceInfo(device: BLEDeviceInfo) {
    val navController = LocalNavController.current

    Row(
        modifier = Modifier
            .clickable {
                navController.navigate(AppRoute.DeviceInfo(device.id))
            }
            .background(BackgroundSecondaryColor)
            .fillMaxWidth()
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Box(modifier = Modifier.weight(1f)) {

            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(device.image) // Or device-specific image URL if dynamic
                    .crossfade(true)
                    .build(),
                contentDescription = "Scale Image",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .padding(start = 18.dp)
                    .size(60.dp)
            )
            Image(
                painter = painterResource(getAppIcon(device.app)),
                contentDescription = null,
                modifier = Modifier
                    .size(24.dp)
                    .clip(CircleShape)
            )

        }
        Column(
            modifier = Modifier
                .fillMaxHeight()
                .padding(horizontal = 14.dp)
                .weight(3f),
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = device.name,
                fontWeight = FontWeight.Bold,
                fontFamily = OpenSansFont,
                fontSize = 14.sp
            )
            if (device.sku != null) {
                Text(
                    text = device.sku,
                    fontWeight = FontWeight.Normal,
                    fontFamily = OpenSansFont,
                    fontSize = 12.sp
                )
            }
        }
        Icon(
            imageVector = Icons.Filled.ChevronRight,
            contentDescription = null,
            tint = PrimaryColor,
            modifier = Modifier
                .weight(1f)
        )
    }
}


@PreviewScreens
@Composable
fun DevicesScreenPreview() {
    GGBluetoothLibraryTheme {
        DevicesScreen()
    }
}