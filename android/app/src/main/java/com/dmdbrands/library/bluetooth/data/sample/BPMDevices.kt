package com.dmdbrands.library.bluetooth.data.sample

data class BPMMonitor(
    val name: String,
    val sku: String,
    val broadcastName: String,
    val hasNumericUsers: Boolean,
    val protocol: String,
    val toggleButton: Boolean,
    val darkColor: <PERSON>olean,
    val wristMonitor: <PERSON><PERSON>an,
    val hasStartButton: Boolean
)

val bpmMonitors = listOf(
    BPMMonitor(
        name = "Smart Wrist Blood Pressure Monitor",
        sku = "0603",
        broadcastName = "gG BPM 0603",
        hasNumericUsers = true,
        protocol = "A3",
        toggleButton = false,
        darkColor = false,
        wristMonitor = true,
        hasStartButton = true
    ),
    BPMMonitor(
        name = "Smart Blood Pressure Monitor",
        sku = "0604",
        broadcastName = "1490BT",
        hasNumericUsers = false,
        protocol = "A3",
        toggleButton = true,
        darkColor = false,
        wristMonitor = false,
        hasStartButton = true
    ),
    BPMMonitor(
        name = "Smart Pro-Series Blood Pressure Monitor",
        sku = "0634",
        broadcastName = "gG BPM 0634",
        hasNumericUsers = false,
        protocol = "A3",
        toggleButton = false,
        darkColor = true,
        wristMonitor = false,
        hasStartButton = true
    ),
    BPMMonitor(
        name = "All-In-One Bluetooth Blood Pressure Monitor",
        sku = "0636",
        broadcastName = "gG BPM 0636",
        hasNumericUsers = false,
        protocol = "A3",
        toggleButton = false,
        darkColor = true,
        wristMonitor = false,
        hasStartButton = false
    ),
    BPMMonitor(
        name = "Smart Blood Pressure Monitor",
        sku = "0663",
        broadcastName = "gG BPM 0663",
        hasNumericUsers = false,
        protocol = "A6",
        toggleButton = false,
        darkColor = false,
        wristMonitor = false,
        hasStartButton = true
    )
)