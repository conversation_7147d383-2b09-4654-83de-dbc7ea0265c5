package com.dmdbrands.library.bluetooth.ui.screen.settings.local

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.config.AppRoute
import com.dmdbrands.library.bluetooth.config.LocalNavController
import com.dmdbrands.library.bluetooth.model.DetailItem
import com.dmdbrands.library.bluetooth.ui.screen.settings.local.component.ProfileSection
import com.dmdbrands.library.bluetooth.ui.screen.settings.viewmodel.SettingsViewmodel
import com.dmdbrands.library.bluetooth.ui.shared.component.AppScaffold
import com.dmdbrands.library.bluetooth.ui.shared.component.DetailContainer


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen() {
    val lang = AppLang.Settings
    val navController = LocalNavController.current

    val detailItems = listOf(
        DetailItem(lang.PERMISSIONS, imageVector = Icons.Filled.ChevronRight) {
            navController.navigate(AppRoute.Permission)
        },
    )
    val viewModel: SettingsViewmodel = hiltViewModel()
    val state by viewModel.state.collectAsState()



    AppScaffold(title = lang.TITLE.uppercase()) {
        Column(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .fillMaxSize()
        ) {
            ProfileSection(state.user.name) {
                navController.navigate(AppRoute.Profile)
            }
            DetailContainer(lang.APP_SETTINGS, detailItems)
        }
    }

}

@Composable
@PreviewScreenSizes
private fun SettingsPreview() {
    SettingsScreen()
}