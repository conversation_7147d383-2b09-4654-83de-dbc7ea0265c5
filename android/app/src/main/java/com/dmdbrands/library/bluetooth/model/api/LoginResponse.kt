package com.dmdbrands.library.bluetooth.model.api

import com.google.gson.annotations.SerializedName

sealed class LoginResponse {
    abstract val token: String
    abstract val user: User?
    open fun convertToUser(): User? = null
}

data class WGLoginResponse(
    @SerializedName("account") override val user: WGUser,
    @SerializedName("accessToken") override val token: String,
    @SerializedName("refreshToken") val refreshToken: String,
    @SerializedName("expiresAt") val expiresAt: String
) : LoginResponse()

data class BHLoginResponse(
    @SerializedName("user") override val user: BHUser,
    @SerializedName("token") override val token: String,
) : LoginResponse()

data class SBLoginResponse(
    val email: String, // Unique email address
    val firstName: String,
    val id: String,
    val lastName: String,
    val zipcode: String,
    val measurementUnits: String, // Enum for metric, imperialLbOz, or imperialLbDecimal
    val willReceiveEmails: Boolean,
    val accountSettings: List<Map<String, Any>>, // A list of JSON objects representing account preferences
    val hasSeenAppReview: Boolean,
    val hasSeenScaleReview: Boolean,
    @SerializedName("accessToken") override val token: String,
    @SerializedName("refreshToken") val refreshToken: String,
    @SerializedName("tokenExpiresAt") val expiresAt: String,
    override val user: User? = null
) : LoginResponse() {
    override fun convertToUser(): User? {
        return SBUser(
            email = email,
            firstName = firstName,
            id = id,
            lastName = lastName,
            zipcode = zipcode,
            measurementUnits = measurementUnits,
            willReceiveEmails = willReceiveEmails,
            accountSettings = accountSettings,
            hasSeenAppReview = hasSeenAppReview,
            hasSeenScaleReview = hasSeenScaleReview
        )
    }
}
