package com.dmdbrands.library.bluetooth

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.dmdbrands.library.bluetooth.core.GGBluetoothApp
import com.example.bluetoothwrapper.service.GGBLEService
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    @Inject
    lateinit var ggbleService: GGBLEService
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        ggbleService.createInstance(this)
        setContent {
            GGBluetoothApp()
        }
    }
}