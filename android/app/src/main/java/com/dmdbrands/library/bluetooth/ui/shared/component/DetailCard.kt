package com.dmdbrands.library.bluetooth.ui.shared.component

import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.dmdbrands.library.bluetooth.model.DetailItem
import com.dmdbrands.library.bluetooth.ui.theme.BackgroundSecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.DisabledIconColor
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.TextSecondaryColor
import com.dmdbrands.library.bluetooth.util.OperationUtil
import com.example.utilities.util.normalize

@Composable
fun DetailCard(
    detailItem: DetailItem,
    modifier: Modifier = Modifier,
    bgColor: Color = BackgroundSecondaryColor,
) {
    if (detailItem.imageVector != null || detailItem.value != null || detailItem.icon != null || detailItem.action != null) {
        val value =
            if (OperationUtil.checkNotZero(detailItem.value)) detailItem.value.toString() else "--"
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(enabled = detailItem.onClick != null && detailItem.enabled) {
                    detailItem.onClick?.invoke()
                }
                .background(bgColor)
                .padding(horizontal = 16.dp, vertical = 10.dp)
                .then(modifier),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = detailItem.name.normalize(),
                fontWeight = FontWeight.Normal,
                fontFamily = OpenSansFont,
                color = if (detailItem.enabled) TextPrimaryColor else DisabledIconColor,
                modifier = Modifier,
                textAlign = TextAlign.Start
            )
            Row(
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 8.dp),
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (detailItem.value != null) {
                    Text(
                        text = value.toString(),
                        fontWeight = FontWeight.Normal,
                        fontFamily = OpenSansFont,
                        color = if (detailItem.enabled) TextSecondaryColor else DisabledIconColor,
                        modifier = Modifier
                            .weight(1f)
                            .basicMarquee(),
                        textAlign = TextAlign.End
                    )
                }
                if (detailItem.unit != null) {
                    Spacer(modifier = Modifier.width(2.dp))
                    Text(
                        text = detailItem.unit.toString(),
                        fontWeight = FontWeight.Normal,
                        fontFamily = OpenSansFont,
                        color = if (detailItem.enabled) TextSecondaryColor else DisabledIconColor,
                    )

                }
                if (detailItem.imageVector != null) {
                    Spacer(modifier = Modifier.width(4.dp))
                    Icon(
                        imageVector = detailItem.imageVector,
                        contentDescription = null,
                        tint = if (detailItem.enabled) TextSecondaryColor else DisabledIconColor,
                    )
                }
                if (detailItem.icon != null) {
                    Spacer(modifier = Modifier.width(4.dp))
                    Icon(
                        painter = painterResource(detailItem.icon),
                        contentDescription = null,
                        tint = if (detailItem.enabled) PrimaryColor else DisabledIconColor,
                    )
                }
                if (detailItem.action != null) {
                    Spacer(modifier = Modifier.width(4.dp))
                    detailItem.action()
                }
            }
        }
    }
}