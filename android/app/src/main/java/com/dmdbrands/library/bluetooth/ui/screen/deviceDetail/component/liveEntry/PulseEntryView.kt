package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.liveEntry

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.BatteryFull
import androidx.compose.material.icons.filled.BluetoothConnected
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.ViewWithIndicator
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.ggbluetooth.model.GGPulseEntry
import com.example.utilities.services.component.VerticalRotation
import com.example.utilities.services.component.animation.ArrowsPulsing
import com.example.utilities.services.component.rotateVertically
import com.example.utilities.services.theme.SecondaryLightShadeColor
import com.example.utilities.services.theme.TertiaryTextColor
import com.example.utilities.util.CalendarUtil
import kotlin.random.Random

@Composable
fun VerticalLabel(
    text: String,
    fontSize: Int = 22,
    color: Color = TertiaryTextColor,
    modifier: Modifier = Modifier
) {
    Text(
        text = text,
        modifier = modifier.rotateVertically(VerticalRotation.CLOCKWISE),
        fontSize = fontSize.sp,
        color = color,
        fontFamily = OpenSansFont
    )
}

@Composable
fun RotatedStatusIcon(
    icon: ImageVector,
    contentDescription: String?,
    modifier: Modifier = Modifier
) {
    Icon(
        imageVector = icon,
        contentDescription = contentDescription,
        modifier = modifier
            .rotateVertically(VerticalRotation.CLOCKWISE)
            .size(30.dp),
        tint = TertiaryTextColor
    )
}

@Composable
fun ValueWithLabel(
    value: String,
    label: String,
    valueFontSize: Int,
    textAlign: TextAlign = TextAlign.Start,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(start = 10.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = value,
            fontSize = valueFontSize.sp,
            color = SecondaryLightShadeColor,
            fontFamily = OpenSansFont,
            modifier = Modifier.weight(1f),
            textAlign = textAlign
        )
        VerticalLabel(text = label)
    }
}

@Composable
fun PulseEntryView(liveEntry: GGPulseEntry? = null) {
    ViewWithIndicator(isLive = liveEntry != null ) {
        Column(
            modifier = Modifier
                .background(Color.Black)
                .fillMaxWidth()
                .fillMaxHeight(0.5f),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (liveEntry != null) {
                // SPO2 Reading
                ValueWithLabel(
                    value = liveEntry.spO2.toString(),
                    label = buildAnnotatedString {
                        append("%SPO")
                        withStyle(style = SpanStyle(fontSize = 12.sp)) {
                            append("\u2082")
                        }
                    }.toString(),
                    valueFontSize = 120
                )

                Spacer(modifier = Modifier.weight(1f))

                // Status Icons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    RotatedStatusIcon(
                        icon = Icons.Default.BatteryFull,
                        contentDescription = "Battery Status"
                    )
                }
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    RotatedStatusIcon(
                        icon = Icons.Default.BluetoothConnected,
                        contentDescription = "Bluetooth Status"
                    )
                }

                Spacer(modifier = Modifier.weight(1f))

                // Pulse Rate
                ValueWithLabel(
                    value = liveEntry.pr.toString(),
                    label = "PR bpm",
                    valueFontSize = 80,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                Spacer(modifier = Modifier.weight(1f))

                // Heart Rate Indicator
                Row(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    ArrowsPulsing(
                        color = SecondaryLightShadeColor,
                        numberOfArrows = 6,
                        modifier = Modifier
                            .weight(1f)
                            .rotate(180f),
                        spaceBetween = 0.dp
                    )
                    Icon(
                        Icons.Filled.Favorite,
                        contentDescription = "Heart Rate",
                        modifier = Modifier.size(40.dp),
                        tint = TertiaryTextColor
                    )
                }
            } else {
                Text(
                    text = "Fetching live entry...",
                    fontSize = 14.sp,
                    color = SecondaryLightShadeColor,
                    modifier = Modifier.padding(horizontal = 6.dp),
                    fontFamily = OpenSansFont
                )
            }
        }
    }
}

@PreviewScreens
@Composable
private fun PulseEntryPreview() {
    GGBluetoothLibraryTheme {
        PulseEntryView(
            GGPulseEntry(
                spO2 = 98,
                pr = 80,
                pulseAmplitudeIndex = 83f,
                unit = null,
                operationType = "create",
                date = CalendarUtil.getCurrentDate().timeInMillis,
                broadcastId = Random.nextInt().toString(),
                broadcastIdString = Random.nextInt().toString(),
                protocolType = Random.nextInt().toString()
            )
        )
    }
}
