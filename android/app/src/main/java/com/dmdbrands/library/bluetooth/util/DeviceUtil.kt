package com.dmdbrands.library.bluetooth.util

import android.util.Log
import com.dmdbrands.library.bluetooth.R
import com.dmdbrands.library.bluetooth.config.assests.ggDevices
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.DeviceBottomNavItem
import com.dmdbrands.library.bluetooth.model.api.BHDeviceRequest
import com.dmdbrands.library.bluetooth.model.api.Preferences
import com.dmdbrands.library.bluetooth.model.api.PreferencesRequest
import com.dmdbrands.library.bluetooth.model.api.WGDeviceRequest
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.dmdbrands.library.ggbluetooth.model.GGBTDevice
import com.dmdbrands.library.ggbluetooth.model.GGDeviceDetail
import com.dmdbrands.library.ggbluetooth.model.GGDevicePreference
import com.example.utilities.modal.PickerItem

object DeviceUtil {
    val displayMetrics = listOf(
        "bmi",
        "bodyFatPercent",
        "musclePercent",
        "bodyWaterPercent",
        "bonePercent",
        "visceralFatLevel",
        "subcutaneousFatPercent",
        "proteinPercent",
        "skeletalMusclePercent",
        "bmr",
        "metabolicAge",
        "heartRate",
        "goalProgress",
        "dailyAverage",
        "weeklyAverage",
        "monthlyAverage"
    )
    val displayMetricProperty = listOf(
        "bmi",
        "bmr",
        "bodyFat",
        "water",
        "boneMass",
        "metabolicAge",
        "muscleMass",
        "proteinPercent",
        "skeletalMusclePercent",
        "subcutaneousFatPercent",
        "visceralFatLevel",
        "pulse"
    )


    val initialMetrics = displayMetrics.filter { it != "heartRate" }

    private val SKU_MAP = mapOf(
        "MY_SCALE" to "0480",
        "1490BT1" to "0604",
        "10376B" to "0376",
        "0376B" to "0376",
        "376B" to "0376",
        "0202B" to "0375",
        "1202B" to "0375",
        "202B" to "0375",
        "11251B" to "0380",
        "1251B" to "0380",
        "01251B" to "0380",
        "1270B" to "0380",
        "11270B" to "0380",
        "01270B" to "0380",
        "gG BS 0412" to "0412",
        "LS212-B" to "0383",
        "gG-RPM 0022" to "0383",
        "gG PulseOx 0003" to "0003",
        "gG-RPM 0040" to "0604",
        "gG BGM 0005" to "0005",
        "0062" to "0062",
        "gG BPM 0603" to "0603",
        "gG BS 0222" to "0222",
        "gG BS 0220" to "0220",
        "BS1711-B" to "0220",
        "Smart Blood Pressure Monitor" to "0663",
        "gG BPM 0667" to "0661",
        "gG BPM 0634" to "0634",
        "gG BS 0351" to "0375",
        "gG BS 0344" to "0344"
    )

    fun GGDeviceDetail.getSKU() = SKU_MAP[deviceName] ?: "0375"

    fun BLEDevice.convertToGGBTDevice(
        scaleToken: String? = null,
        preferences: Preferences? = null
    ) =
        GGBTDevice(
            name = device.deviceName,
            broadcastId = device.broadcastId ?: "",
            userNumber = userNumber,
            token = scaleToken ?: this.token,
            password = device.password.toString(),
            preference = (preferences ?: this.preferences)?.convertToGGDevicePreference()
        )

    fun Preferences.convertToGGDevicePreference(): GGDevicePreference {
        return GGDevicePreference(
            tzOffset = tzOffset,
            timeFormat = timeFormat,
            displayMetrics = displayMetrics,
            displayName = displayName,
            shouldFactoryReset = shouldFactoryReset,
            shouldMeasurePulse = shouldMeasurePulse,
            shouldMeasureImpedance = shouldMeasureImpedance,
            wifiFotaScheduleTime = wifiFotaScheduleTime?.toLong()
        )
    }

    fun Preferences.convertToPreferencesRequest(scaleId: String? = null): PreferencesRequest {
        return PreferencesRequest(
            tzOffset = tzOffset,
            timeFormat = timeFormat,
            displayMetrics = displayMetrics,
            displayName = displayName,
            shouldFactoryReset = shouldFactoryReset,
            shouldMeasurePulse = shouldMeasurePulse,
            shouldMeasureImpedance = shouldMeasureImpedance,
            wifiFotaScheduleTime = wifiFotaScheduleTime?.toLong(),
            scaleId = scaleId
        )
    }

    fun GGDeviceDetail.getAppType(sku: String? = null): String = when {
        deviceName.contains("gG-RPM") || deviceName.contains("gG BGM") ||
                deviceName.contains("0062") ||
                deviceName.contains("gG PulseOx 0003") -> GGAppType.RPM

        deviceName.contains("gG BPM") || broadcastId?.contains("62FFECE7") == true || deviceName.contains(
            "BT"
        ) == true -> GGAppType.BALANCE_HEALTH

        broadcastId?.matches(Regex("^D0")) == true -> GGAppType.SAGE
        deviceName.contains("LS212-B") || deviceName.contains("Scale") || deviceName.contains("0412") || deviceName.contains(
            "0351"
        ) || deviceName.contains("0344") -> GGAppType.WEIGHT_GURUS

        deviceName.contains("BS") -> GGAppType.SMART_BABY


        else -> {
            val sku = sku ?: getSKU()
            when (sku) {
                in listOf(
                    "0375",
                    "0376",
                    "0380",
                    "0382",
                    "0378",
                    "0383",
                    "0412"
                ) -> GGAppType.WEIGHT_GURUS

                in listOf("0222", "0220") -> GGAppType.SMART_BABY

                in listOf("0603", "0604", "0634", "0636", "0663") -> GGAppType.BALANCE_HEALTH
                in listOf("WellandKitchenScale", "0480") -> GGAppType.SAGE
                else -> GGAppType.NONE
            }
        }
    }

    fun getAppIcon(appType: String) = when (appType) {
        GGAppType.SAGE -> R.drawable.ic_sage
        GGAppType.BALANCE_HEALTH -> R.drawable.ic_bh
        GGAppType.SMART_BABY -> R.drawable.ic_sb
        GGAppType.WEIGHT_GURUS -> R.drawable.ic_wg
        else -> R.drawable.ic_test_app
    }

    fun getAppOutlinedIcon(appType: String, sku: String? = null) = when {
        sku == "0062" && appType == GGAppType.RPM -> R.drawable.temperature_ic
        sku == "0005" && appType == GGAppType.RPM -> R.drawable.bgmicon
        sku == "0003" && appType == GGAppType.RPM -> R.drawable.pulse_oxy_ic
        appType == GGAppType.BALANCE_HEALTH -> R.drawable.bpm_icon
        else -> R.drawable.wgicon
    }

    fun BLEDevice.getDeviceImage() =
        ggDevices.find { it.sku?.contains(sku) == true }?.image ?: when (sku) {
            GGAppType.SAGE -> R.drawable.ic_kitchen_scale
            GGAppType.WEIGHT_GURUS -> R.drawable.ic_weight_gurus
            GGAppType.BALANCE_HEALTH -> R.drawable._604
            else -> R.drawable.scale_default
        }

    fun getUserList(device: BLEDevice) = when (device.getAppType()) {
        GGAppType.BALANCE_HEALTH -> listOf(
            PickerItem("A", 1),
            PickerItem("B", 2)
        )

        else -> (1..8).map { PickerItem("U$it", it) }
    }

    fun getUserLabel(device: BLEDevice) = when (device.getAppType()) {
        GGAppType.BALANCE_HEALTH ->
            device.userNumber?.let { number ->
                if (number in 1..26) String(charArrayOf('A' + number - 1))
                else "Invalid"
            } ?: "Unknown"

        else -> device.userNumber.toString()
    }

    val deviceDetailBottomNavItems = listOf(
        DeviceBottomNavItem.BasicDetail,
        DeviceBottomNavItem.History,
        DeviceBottomNavItem.Users,
        DeviceBottomNavItem.LiveMeasurement,
        DeviceBottomNavItem.DeviceSettings,
    )

    fun BLEDevice.getDetailFeatures(): List<DeviceBottomNavItem> {
        val appType = getAppType()

        return deviceDetailBottomNavItems.filter {
            when {
                appType == GGAppType.WEIGHT_GURUS && sku == "0412" -> {
                    true
                }

                (appType == GGAppType.WEIGHT_GURUS &&
                        !(device.deviceName.contains("0351") || device.deviceName.contains("0344"))) ||
                        appType == GGAppType.BALANCE_HEALTH -> {
                    it !in listOf(
                        DeviceBottomNavItem.Users,
                        DeviceBottomNavItem.LiveMeasurement,
                        DeviceBottomNavItem.DeviceSettings,
                    )
                }

                else -> {
                    it !in listOf(
                        DeviceBottomNavItem.Users,
                        DeviceBottomNavItem.DeviceSettings,
                    )
                }
            }

        }
    }

    fun BLEDevice.convertToBHDeviceRequest(): BHDeviceRequest {
        return BHDeviceRequest(
            name = device.deviceName,
            broadcastId = device.broadcastId!!,
            userNumber = userNumber ?: 0,
            password = device.password,
            sku = sku,
            peripheralIdentifier = device.identifier,
            type = "bluetooth",
            mac = device.macAddress.toString(),
            nickname = nickname
        )
    }

    @OptIn(ExperimentalStdlibApi::class)
    fun BLEDevice.convertToWGDeviceRequest(): WGDeviceRequest {
        return WGDeviceRequest(
            name = device.deviceName,
            broadcastId = convertHexToInt(device.broadcastId)!!,
            userNumber = userNumber ?: 0,
            password = convertHexToInt(device.password),
            sku = sku,
            peripheralIdentifier = device.identifier,
            type = getScaleType(sku),
            mac = device.macAddress.toString(),
            nickname = nickname,
            scaleToken = token
        )

    }

    private fun getScaleType(sku: String?): String {
        return when (sku) {
            "0412" -> "btWifiR4"
            else -> "bluetooth"
        }
    }

    private fun convertHexToInt(value: String?): Long? {
        // Scales' broadcastIds and passwords are returned as hex strings, but need to be
        // stored as an integer
        Log.d("TAG", "convertHexToInt - converting value: $value")

        if (value.isNullOrBlank()) return null
        else {
            val convertedValue = value
                .chunked(2)
                .reversed()
                .joinToString("")
                .uppercase()
            return convertedValue.toLong(16)
        }
    }
}