package com.dmdbrands.library.bluetooth.data.datastore

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.dmdbrands.balancehealth.data.datastore.BaseDataStore
import com.dmdbrands.library.bluetooth.config.AppConfig
import com.dmdbrands.library.bluetooth.config.assests.sampleUserDetail
import com.dmdbrands.library.bluetooth.data.datastore.interfaces.IUserDataStore
import com.dmdbrands.library.bluetooth.model.UserDetails
import com.google.gson.Gson


class UserDataStore(context: Context) : BaseDataStore(context), IUserDataStore {
    companion object {
        private val Context.dataStore: DataStore<Preferences> by preferencesDataStore("user")
        private val SAVED_USER_KEY = stringPreferencesKey("user")
        private val SAVED_APPTYPE = stringPreferencesKey("appType")
    }


    override var dataStore: DataStore<Preferences> = context.dataStore

    override suspend fun getUser(): UserDetails {
        val data = getValue(SAVED_USER_KEY)
        return if (data != null) Gson().fromJson(
            data,
            UserDetails::class.java
        ) else sampleUserDetail
    }

    override suspend fun updateUser(user: UserDetails) {
        setValue(SAVED_USER_KEY, user)
    }


    override suspend fun getLiveUser(callback: (String?) -> Unit) {
        return getLiveValue(SAVED_USER_KEY, callback)
    }

    override suspend fun getAppType(): String {
        return getValue(SAVED_APPTYPE) ?: AppConfig.APPS_LIST[0]
    }

    override suspend fun updateAppType(appType: String) {
        setValue(SAVED_APPTYPE, appType)
    }


}