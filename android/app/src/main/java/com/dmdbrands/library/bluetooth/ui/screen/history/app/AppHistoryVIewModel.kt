package com.dmdbrands.library.bluetooth.ui.screen.history.app

import androidx.lifecycle.viewModelScope
import com.dmdbrands.library.bluetooth.model.interfaces.IHistoryRepository
import com.dmdbrands.library.bluetooth.ui.screen.history.HistoryIntent
import com.dmdbrands.library.bluetooth.ui.screen.history.HistoryReducer
import com.dmdbrands.library.bluetooth.ui.screen.history.HistoryState
import com.example.utilities.services.viewmodel.BaseViewModel
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch

@HiltViewModel(assistedFactory = AppHistoryViewModel.Factory::class)
class AppHistoryViewModel @AssistedInject constructor(
    @Assisted private val appType: String,
    private val historyRepository: IHistoryRepository,
) :
    BaseViewModel<HistoryState, HistoryIntent>(
        initialState = HistoryState(),
        reducer = HistoryReducer()
    ) {
        
    @AssistedFactory
    interface Factory {
        fun create(appType: String): AppHistoryViewModel
    }

    fun loadHistory() {
        handleIntent(HistoryIntent.AlterLocalRefreshing(true))
        viewModelScope.launch {
            val histories = historyRepository.getAppHistory(appType)
            handleIntent(HistoryIntent.ClearHistory)
            handleIntent(HistoryIntent.AddBulkHistory(histories))
            handleIntent(HistoryIntent.AlterLocalRefreshing(false))
        }
    }

    override fun handleIntent(intent: HistoryIntent) {
        super.handleIntent(intent)
    }
}