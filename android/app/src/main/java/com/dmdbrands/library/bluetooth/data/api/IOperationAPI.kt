package com.dmdbrands.library.bluetooth.data.api

import com.dmdbrands.library.bluetooth.model.api.BHOperationRequest
import com.dmdbrands.library.bluetooth.model.api.BHOperationResponse
import com.dmdbrands.library.bluetooth.model.api.SBOperationResponse
import com.dmdbrands.library.bluetooth.model.api.WGOperationRequest
import com.dmdbrands.library.bluetooth.model.api.WGOperationResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface IOperationAPI {

    @GET("operation")
    suspend fun bhOperations(@Query("startTime") startTime: String?): Response<BHOperationResponse>

    @GET("operation")
    suspend fun wgOperations(@Query("startTime") startTime: String?): Response<WGOperationResponse>

    @GET("operation/{id}")
    suspend fun getSBOperation(@Path("id") id: String): Response<SBOperationResponse>

    @POST("operation/bulk")
    suspend fun addBHOperation(@Body operations: BHOperationRequest): Response<String>

    @POST("operation")
    suspend fun addWGOperation(@Body operation: WGOperationRequest): Response<String>

    @GET("operation/csv")
    suspend fun exportCSV(): Response<String>

}