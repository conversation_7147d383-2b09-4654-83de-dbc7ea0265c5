package com.dmdbrands.library.bluetooth.model.interfaces

import com.dmdbrands.library.bluetooth.model.AppStatus
import kotlinx.coroutines.flow.Flow

interface IAppsRepository {
    fun subscribeAppStatus(): Flow<List<AppStatus>>
    fun checkAppType(appType: String): Boolean
    suspend fun addAppType(appType: String)
    suspend fun setAppToken(appType: String, token: String)
    suspend fun clearAppToken(appType: String)


}