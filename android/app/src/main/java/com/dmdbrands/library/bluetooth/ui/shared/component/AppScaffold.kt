package com.dmdbrands.library.bluetooth.ui.shared.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Search
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.TopAppBarColors
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.dmdbrands.library.bluetooth.ui.theme.SurfaceBackgroundColor
import com.example.utilities.services.component.AppButton

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppScaffold(
    title: String? = null,
    modifier: Modifier = Modifier,
    topBarExpandedHeight: Dp = TopAppBarDefaults.TopAppBarExpandedHeight,
    topAppBarColors: TopAppBarColors = TopAppBarDefaults.topAppBarColors(containerColor = SurfaceBackgroundColor),
    titleComposable: @Composable (() -> Unit)? = null,
    actions: @Composable RowScope.() -> Unit = {},
    navigationIcon: @Composable (() -> Unit)? = null,
    setFabPadding: ((Dp) -> Unit)? = null,
    bottomBar: (@Composable () -> Unit)? = null,
    content: @Composable ColumnScope.() -> Unit
) {
    Scaffold(modifier = Modifier.fillMaxSize(), topBar = {
        if (title != null || titleComposable != null)
            AppTopBar(
                title = title,
                titleComposable = titleComposable,
                actions = actions,
                navigationIcon = navigationIcon,
                colors = topAppBarColors,
                expandedHeight = topBarExpandedHeight
            )
    }, bottomBar = {
        if (bottomBar != null) {
            bottomBar()
        }
    }
    ) {
        if (setFabPadding != null) {
            setFabPadding(it.calculateBottomPadding() - 16.dp)
        }
        Column(
            modifier = modifier
                .padding(
                    top = it.calculateTopPadding(),
                    bottom = if (bottomBar != null) it.calculateBottomPadding() else 0.dp
                )
                .fillMaxSize()
                .background(SurfaceBackgroundColor)
        ) {
            content()
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppScaffoldWithSearch(
    title: String,
    onSearch: (String) -> Unit,
    modifier: Modifier = Modifier,
    navigationIcon: @Composable (() -> Unit)? = null,
    setFabPadding: ((Dp) -> Unit)? = null,
    bottomBar: (@Composable () -> Unit)? = null,
    content: @Composable ColumnScope.() -> Unit
) {
    var isVisible by remember { mutableStateOf(false) }

    Scaffold(modifier = Modifier.fillMaxSize(), topBar = {
        AnimatedVisibility(
            visible = isVisible,
            enter = fadeIn(animationSpec = tween(300)) + slideInVertically(),
            exit = fadeOut(animationSpec = tween(300)) + slideOutVertically()
        ) {
            AppSearchBar(onClear = {
                isVisible = false
            }) { query ->
                onSearch(query)
            }
        }
        AnimatedVisibility(
            visible = !isVisible,
            enter = fadeIn(animationSpec = tween(300)) + slideInVertically(),
            exit = fadeOut(animationSpec = tween(300)) + slideOutVertically()
        ) {
            AppTopBar(
                title = title,
                actions = {
                    AppButton(containerColor = Transparent, onClick = {
                        isVisible = !isVisible
                    }) {
                        Icon(imageVector = Icons.Outlined.Search, contentDescription = null)
                    }
                },
                navigationIcon = navigationIcon
            )
        }

    }, bottomBar = {
        if (bottomBar != null) {
            bottomBar()
        }
    }
    ) {
        if (setFabPadding != null) {
            setFabPadding(it.calculateBottomPadding() - 16.dp)
        }
        Column(
            modifier = modifier
                .background(SurfaceBackgroundColor)
                .padding(top = it.calculateTopPadding())
                .fillMaxSize()
        ) {
            content()
        }
    }
}