package com.dmdbrands.library.bluetooth.core

import com.dmdbrands.library.bluetooth.config.assests.sampleUserDetail
import com.dmdbrands.library.bluetooth.model.UserDetails
import com.dmdbrands.library.ggbluetooth.model.GGPermissionStatusMap
import com.example.utilities.modal.interfaces.IReducer

data class AppState(
    val isScanning: Boolean = false,
    val user: UserDetails = sampleUserDetail,
    val isEnabled: Boolean = true,
    val appType: String? = null,
    val permissions: GGPermissionStatusMap = mutableMapOf<String, String>(),
) : IReducer.State

interface AppIntent : IReducer.Intent {
    data class alterScanState(val isScanning: Boolean? = null, val isEnabled: Boolean? = null) :
        AppIntent

    data class onScanAction(val appType: String) : AppIntent
    data class onAppTypeChange(val appType: String?) : AppIntent
    data class setUser(val user: UserDetails) : AppIntent
    data class updatePermissions(val permissions: GGPermissionStatusMap) : AppIntent
}

class AppReducer : IReducer<AppState, AppIntent> {
    override fun reduce(
        state: AppState,
        intent: AppIntent
    ): AppState? {
        return when (intent) {
            is AppIntent.alterScanState -> {
                if (intent.isScanning != null) {
                    state.copy(isScanning = intent.isScanning)
                } else if (intent.isEnabled != null) {
                    state.copy(isEnabled = intent.isEnabled)
                } else {
                    state
                }
            }

            is AppIntent.updatePermissions -> {
                state.copy(permissions = intent.permissions)
            }

            is AppIntent.onAppTypeChange -> {
                state.copy(appType = intent.appType)
            }

            is AppIntent.setUser -> {
                state.copy(user = intent.user)
            }

            else -> state
        }
    }
}