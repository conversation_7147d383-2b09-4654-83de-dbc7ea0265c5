package com.dmdbrands.library.bluetooth.ui.shared.component

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.model.DetailItem
import com.dmdbrands.library.bluetooth.ui.theme.DividerColor
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.example.utilities.util.camelCase

@Composable
fun DetailContainer(
    title: String,
    list: List<DetailItem>,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = title.camelCase(),
            fontFamily = OpenSansFont,
            fontWeight = FontWeight.Bold,
            fontSize = 16.sp,
            color = TextPrimaryColor,
        )
        Spacer(modifier = Modifier.height(4.dp))
        Card(
            colors = CardDefaults.cardColors(containerColor = Transparent),
            shape = RoundedCornerShape(10.dp)
        ) {
            list.forEach { item ->
                DetailCard(
                    detailItem = item
                )
                HorizontalDivider(color = DividerColor, thickness = 1.dp)
            }
        }
    }
}