package com.dmdbrands.library.bluetooth.ui.shared.component

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.outlined.ArrowForwardIos
import androidx.compose.material.icons.outlined.Cancel
import androidx.compose.material.icons.outlined.CheckCircle
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.dmdbrands.library.bluetooth.model.Permission
import com.dmdbrands.library.bluetooth.ui.theme.BorderColor
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.TextSecondaryColor
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionState
import com.dmdbrands.library.ggbluetooth.enums.GGPermissionType

@Composable
fun PermissionCard(
    permission: Permission,
    onClick: (() -> Unit)? = null
) {
    var isPermissionGranted = permission.status == GGPermissionState.ENABLED
    LaunchedEffect(key1 = permission) {
        isPermissionGranted = permission.status == GGPermissionState.ENABLED
    }

    Card(
        onClick = {
            if (onClick != null && !isPermissionGranted) {
                onClick()
            }
        },
        modifier = Modifier.fillMaxWidth(),
        shape = CardDefaults.outlinedShape,
        border = BorderStroke(1.dp, BorderColor),
        colors = CardDefaults.cardColors(containerColor = Color.White),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(24.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = if (isPermissionGranted) Icons.Outlined.CheckCircle else Icons.Outlined.Cancel,
                    contentDescription = null,
                    modifier = Modifier.size(32.dp),
                    tint = if (isPermissionGranted) PrimaryColor else Color.Red
                )
                Text(
                    text = permission.description,
                    modifier = Modifier.weight(1f)
                )
                if (!isPermissionGranted) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Outlined.ArrowForwardIos,
                        contentDescription = null,
                        modifier = Modifier.size(24.dp),
                        tint = TextSecondaryColor,
                    )
                }
            }
        }
    }

}


@Preview(showSystemUi = true)
@Composable
fun PermissionCardPreview() {
    GGBluetoothLibraryTheme {
        PermissionCard(
            permission = Permission(
                status = GGPermissionState.DISABLED,
                description = "Enabled Text",
                type = GGPermissionType.BLUETOOTH
            )
        )
    }
}