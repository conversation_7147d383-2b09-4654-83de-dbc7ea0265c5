package com.dmdbrands.library.bluetooth.ui.screen.history.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.sharp.DeleteOutline
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.model.HistoryData
import com.dmdbrands.library.bluetooth.ui.shared.component.AppList
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.bluetooth.util.OperationUtil
import com.example.utilities.modal.ActionButton
import com.example.utilities.modal.Dialog
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.component.animation.DotsPulsing
import com.example.utilities.services.component.list.rememberDraggableListState
import com.example.utilities.services.component.rememberExpandedCardState
import com.example.utilities.services.theme.HiddenBackgroundColor
import com.example.utilities.services.viewmodel.UtilitiesViewmodel

@Composable
fun HistoryList(
    list: List<HistoryData>?,
    isRefreshing: Boolean = false,
    isDraggable: Boolean = true,
    onDelete: (Long) -> Unit = {}
) {
    val draggableListState = rememberDraggableListState()
    val expandedCardState = rememberExpandedCardState()
    val utility: UtilitiesViewmodel = hiltViewModel()
    fun dialog(onConfirm: () -> Unit): Dialog {
        return Dialog(
            title = AppLang.ALERT,
            message = AppLang.Dialog.DELETE_INFO,
            confirmButton = ActionButton(
                text = AppLang.Dialog.DELETE,
                action = {
                    onConfirm() // Invoke onConfirm
                }
            ),
            dismissButton = ActionButton(
                text = AppLang.Dialog.CANCEL,
                action = {
                    utility.setDialog(dialog = null)
                }
            )
        )
    }
    if (isRefreshing) {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            DotsPulsing(text = "Fetching Histories")
        }
    }
    if (list?.isNotEmpty() == true) {
        AppList(
            contentPadding = PaddingValues(bottom = 100.dp),
            modifier = Modifier.clipToBounds(),
            list = list,
            isDraggable = isDraggable,
            draggableListState = draggableListState,
            actionContent = { index, item, modifier ->
                AppButton(
                    onClick = {
                        utility.setDialog(
                            dialog = dialog(
                                onConfirm = {
                                    onDelete(item.id)
                                }
                            ))
                        draggableListState.reset()
                    },
                    modifier = Modifier.fillMaxSize(),
                    cardModifier = Modifier
                        .fillMaxSize()
                        .then(modifier),
                    containerColor = HiddenBackgroundColor,
                    shape = RectangleShape
                ) {
                    Icon(
                        Icons.Sharp.DeleteOutline,
                        null,
                        tint = White,
                        modifier = Modifier
                            .width(40.dp)

                    )
                }
            },
            staticContent = { index, item ->
                HistoryDetail(
                    state = expandedCardState,
                    historyData = item
                )
            }
        ) { history ->
            HistoryCard(
                id = history.id,
                opTimestamp = history.opTimestamp,
                sku = history.sku,
                state = expandedCardState,
                appType = history.appType,
                value = history.getDisplayValue(),
                unit = history.unit!!,
                enabled = history.getDisplayDetail().any {
                    OperationUtil.checkNotZero(it.value)
                }
            )
        }
    } else {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "No history found", color = PrimaryColor,
                fontFamily = OpenSansFont,
                textAlign = TextAlign.Center,
            )
        }
    }
}
