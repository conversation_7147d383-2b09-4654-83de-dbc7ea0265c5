package com.dmdbrands.library.bluetooth.model.api

import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.util.Source
import com.dmdbrands.library.ggbluetooth.model.GGDeviceDetail
import com.example.utilities.util.CalendarUtil
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlin.random.Random

sealed interface DeviceData {
    fun convertToBLEDevice(): BLEDevice
}


@Serializable
data class WGDeviceData(
    @SerialName("id") val id: String,
    @SerialName("nickname") val nickname: String?,
    @SerialName("type") val type: String,
    @SerialName("createdAt") val createdAt: String,
    @SerialName("userNumber") val userNumber: Int?,
    @SerialName("mac") val mac: String?,
    @SerialName("broadcastId") val broadcastId: Long?,
    @SerialName("password") val password: Long?,
    @SerialName("sku") val sku: String,
    @SerialName("name") val name: String,
    @SerialName("scaleToken") val scaleToken: String? = null,
    @SerialName("peripheralIdentifier") val peripheralIdentifier: String? = null,
    @SerialName("preference") val preference: Preferences? = null,
    @SerialName("latestVersion") val latestVersion: String? = null
) : DeviceData {
    @OptIn(ExperimentalStdlibApi::class)
    override fun convertToBLEDevice(): BLEDevice {
        return BLEDevice(
            device = GGDeviceDetail(
                systemID = id,
                deviceName = name,
                broadcastId = convertIntToHex(broadcastId, type),
                macAddress = mac ?: "",
                identifier = peripheralIdentifier ?: convertIntToHex(broadcastId, type) ?: "",
                password = convertIntToHex(password, type),
                protocolType = type,
            ),
            sku = sku,
            nickname = nickname ?: name,
            source = Source.API,
            primaryToken = scaleToken,
            token = scaleToken,
            alreadyPaired = true,
            userNumber = userNumber,
            createdAt = createdAt,
            preferences = preference
        )
    }
}

@Serializable
data class Preferences(
    val id: Long = Random.nextLong(),
    @SerialName("tzOffset") val tzOffset: Int? = null,
    @SerialName("timeFormat") val timeFormat: String? = null,
    @SerialName("displayName") val displayName: String? = null,
    @SerialName("displayMetrics") val displayMetrics: List<String>? = null,
    @SerialName("shouldMeasurePulse") val shouldMeasurePulse: Boolean? = null,
    @SerialName("shouldMeasureImpedance") val shouldMeasureImpedance: Boolean? = null,
    @SerialName("shouldFactoryReset") val shouldFactoryReset: Boolean? = null,
    @SerialName("wifiFotaScheduleTime") val wifiFotaScheduleTime: Long? = null
)

@Serializable
data class PreferencesRequest(
    @SerialName("scaleId") val scaleId: String? = null,
    @SerialName("tzOffset") val tzOffset: Int? = null,
    @SerialName("timeFormat") val timeFormat: String? = null,
    @SerialName("displayName") val displayName: String? = null,
    @SerialName("displayMetrics") val displayMetrics: List<String>? = null,
    @SerialName("shouldMeasurePulse") val shouldMeasurePulse: Boolean? = null,
    @SerialName("shouldMeasureImpedance") val shouldMeasureImpedance: Boolean? = null,
    @SerialName("shouldFactoryReset") val shouldFactoryReset: Boolean? = null,
    @SerialName("wifiFotaScheduleTime") val wifiFotaScheduleTime: Long? = null
)

@Serializable
data class PreferenceResponse(
    val id: Long = Random.nextLong(),
    @SerialName("tzOffset") val tzOffset: Int? = null,
    @SerialName("timeFormat") val timeFormat: String? = null,
    @SerialName("displayName") val displayName: String? = null,
    @SerialName("displayMetrics") val displayMetrics: List<String>? = null,
    @SerialName("shouldMeasurePulse") val shouldMeasurePulse: Boolean? = null,
    @SerialName("shouldMeasureImpedance") val shouldMeasureImpedance: Boolean? = null,
    @SerialName("shouldFactoryReset") val shouldFactoryReset: Boolean? = null,
    @SerialName("wifiFotaScheduleTime") val wifiFotaScheduleTime: String? = null
) {
    fun convertToPreferences(): Preferences {
        return Preferences(
            id = this.id,
            tzOffset = this.tzOffset,
            timeFormat = this.timeFormat,
            displayName = this.displayName,
            displayMetrics = this.displayMetrics,
            shouldMeasurePulse = this.shouldMeasurePulse,
            shouldMeasureImpedance = this.shouldMeasureImpedance,
            shouldFactoryReset = this.shouldFactoryReset,
            wifiFotaScheduleTime = if (this.wifiFotaScheduleTime == null) null else CalendarUtil.stringToCalender(
                this.wifiFotaScheduleTime
            ).timeInMillis
        )
    }
}

data class BHDeviceData(
    val id: String,
    val name: String,
    val sku: String,
    val type: String,
    val broadcastId: String,
    val peripheralIdentifier: String,
    val mac: String,
    val password: String?,
    val userNumber: Int,
    val nickname: String,
    val createdAt: String,
) : DeviceData {
    override fun convertToBLEDevice(): BLEDevice {
        return BLEDevice(
            device = GGDeviceDetail(
                systemID = id,
                deviceName = name,
                broadcastId = broadcastId,
                macAddress = mac,
                identifier = peripheralIdentifier,
                password = password,
                protocolType = type,
            ),
            sku = sku,
            nickname = nickname,
            alreadyPaired = true,
            source = Source.API,
            userNumber = userNumber,
            createdAt = createdAt
        )

    }
}

private fun convertIntToHex(value: Long?, protocolType: String): String? {
    if (value == null) return null
    else {
        // Scales' broadcastIds and passwords are stored as integers and need to be
        // converted to a Hex string before being sent to the app
        var convertedValue = value.toString(16)

        if (protocolType == "btWifiR4") {
            convertedValue = "000000000000$convertedValue".takeLast(12)
        } else {
            if (convertedValue.length < 8) {
                convertedValue = "0000000$convertedValue".takeLast(8)
            } else if (convertedValue.length > 8 && convertedValue.length < 12) {
                convertedValue = "0000000$convertedValue".takeLast(12)
            }
        }

        return convertedValue.chunked(2).reversed().joinToString("").uppercase()
    }
}
