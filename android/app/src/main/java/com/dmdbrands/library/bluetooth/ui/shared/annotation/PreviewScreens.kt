package com.dmdbrands.library.bluetooth.ui.shared.annotation

import androidx.compose.ui.tooling.preview.Preview

@Retention(AnnotationRetention.BINARY)
@Target(
    AnnotationTarget.ANNOTATION_CLASS,
    AnnotationTarget.FUNCTION
)
//@Preview(name = "Unfolded Foldable", device = FOLDABLE, showSystemUi = true)
//@Preview(name = "Phone - Landscape Dark",
//    device = "spec:width = 411dp, height = 891dp, orientation = landscape, dpi = 420",
//    showSystemUi = true, uiMode = UI_MODE_NIGHT_YES or UI_MODE_TYPE_NORMAL)

@Preview(name = "Phone", device = "spec:width=411dp,height=891dp", showSystemUi = true)
annotation class PreviewScreens