package com.dmdbrands.library.bluetooth.core

import androidx.lifecycle.viewModelScope
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.BLEStatus
import com.dmdbrands.library.bluetooth.model.DeviceAction
import com.dmdbrands.library.bluetooth.model.interfaces.IBaseRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IDeviceRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IHistoryRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IUserRepository
import com.dmdbrands.library.bluetooth.util.DeviceUtil.convertToGGBTDevice
import com.dmdbrands.library.bluetooth.util.Source
import com.dmdbrands.library.bluetooth.util.UserUtil.convertGGUserDetail
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.dmdbrands.library.ggbluetooth.enums.GGScanResponseType
import com.dmdbrands.library.ggbluetooth.model.GGBTUserProfile
import com.dmdbrands.library.ggbluetooth.model.GGDeviceDetail
import com.dmdbrands.library.ggbluetooth.model.GGEntry
import com.dmdbrands.library.ggbluetooth.model.GGScanResponse
import com.example.bluetoothwrapper.service.GGDeviceService
import com.example.bluetoothwrapper.service.GGPermissionService
import com.example.utilities.services.viewmodel.BaseViewModel
import com.example.utilities.util.onlyString
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AppViewModel @Inject constructor(
    private val deviceRepository: IDeviceRepository,
    private val userRepository: IUserRepository,
    private val ggPermissionService: GGPermissionService,
    private val baseRepository: IBaseRepository,
    private val ggDeviceService: GGDeviceService,
    private val historyRepository: IHistoryRepository,
) :
    BaseViewModel<AppState, AppIntent>(initialState = AppState(), reducer = AppReducer()) {

    init {
        subscribePermissions()
        subscribeAppType()
        subscribeUserDetails()
        subscribeToScanCallback()
        fetchDevices()
    }

    override fun handleIntent(intent: AppIntent) {
        super.handleIntent(intent)
        when (intent) {
            is AppIntent.onScanAction -> {
                handleScanIntent(intent)
            }
        }
    }

    private fun syncDevices(pairedDevices: List<BLEDevice>) {
        ggDeviceService.syncDevices(pairedDevices.map { it.convertToGGBTDevice() })
    }


    private fun fetchDevices() {
        viewModelScope.launch {
            userRepository.subscribeUsers().collect { users ->
                // Use mutable map to allow updates
                var accountsMap: MutableMap<String, GGBTUserProfile> = mutableMapOf()

                users.forEach { user ->
                    deviceRepository.getAppDevices(user.appType)  // Assuming you need to do something with this result

                    // Get user details and safely handle null response
                    val userDetails =
                        userRepository.getUserDetails(user.appType)?.body()
                    userDetails?.let {
                        val userProfile = it.convertToUserDetail().convertGGUserDetail()
                        // Update the map with new details
                        accountsMap[user.appType] = userProfile
                    }
                }
                ggDeviceService.syncAccount(accountsMap)
            }

        }
        viewModelScope.launch {
            deviceRepository.subscribeAllPairedDevices().collect {
                syncDevices(pairedDevices = it)
            }
        }
    }

    private fun subscribeUserDetails() {
        viewModelScope.launch {
            baseRepository.subscribeUserDetails().collect {
                super.handleIntent(AppIntent.setUser(it))
            }
        }
    }

    private fun subscribeAppType() {
        viewModelScope.launch {
            baseRepository.subscribeAppType().collect {
                disconnectDevices()
                super.handleIntent(AppIntent.onAppTypeChange(it))
            }
        }
    }

    private fun disconnectDevices() {
        viewModelScope.launch {
            deviceRepository.subscribePairedDevices().first().forEach {
                ggDeviceService.disconnectDevice(it.convertToGGBTDevice())
            }
        }
    }

    private fun subscribePermissions() {
        viewModelScope.launch {
            ggPermissionService.permissionCallBackFlow.collect { permissions ->
                if (state.value.permissions != permissions) {
                    val isEnabled = ggPermissionService.checkScanPermissions(permissions)
                    baseRepository.setPermissions(permissions)
                    super.handleIntent(AppIntent.updatePermissions(permissions))
                    if (isEnabled != state.value.isEnabled) {
                        if (!isEnabled) ggPermissionService.stopScan()
                        super.handleIntent(AppIntent.alterScanState(isEnabled = isEnabled))
                    }
                }
            }
        }

    }

    private fun subscribeToScanCallback() {
        viewModelScope.launch {
            ggDeviceService.deviceCallbackFlow.collect { response ->
                when (response) {
                    is GGScanResponse.DeviceDetail -> {
                        handleDeviceDetail(response)
                    }

                    is GGScanResponse.Entry -> {
                        handleHistory(response.data)
                    }

                    else -> {

                    }
                }
            }
        }
    }

    private fun handleDeviceDetail(response: GGScanResponse.DeviceDetail) {
        val device = response.data
        val bleDevice = BLEDevice(device)
        when (response.type) {
            GGScanResponseType.NEW_DEVICE -> {
                viewModelScope.launch {
                    deviceRepository.onDeviceAction(
                        bleDevice,
                        DeviceAction.ADD
                    )
                }
            }

            GGScanResponseType.KNOWN_DEVICE -> {
                val updatedDevice = bleDevice.copy(
                    connectionStatus = BLEStatus.CONNECTED,
                    alreadyPaired = true,
                )
                updateDevice(
                    deviceInfo = updatedDevice.device,
                    connectionStatus = BLEStatus.CONNECTED,
                    alreadyPaired = true
                )
                checkForWifiScales(updatedDevice)
                getLiveMeasurement(updatedDevice)
                this.setToastMessage("${device.deviceName.onlyString()}|${AppLang.Toast.CONNECTED}")
            }

            GGScanResponseType.DEVICE_DISCONNECTED -> {
                updateDevice(
                    deviceInfo = device,
                    connectionStatus = BLEStatus.DISCONNECTED
                )
                this.setToastMessage("${device.deviceName.onlyString()}|${AppLang.Toast.DISCONNECTED}")
                setLoader(loader = null)
            }

            GGScanResponseType.DEVICE_INFO_UPDATE -> {
                setLoader(null)
                updateDevice(
                    deviceInfo = device,
                )
            }

            GGScanResponseType.DEVICE_CONNECTION_FAILED -> {
                updateDevice(
                    deviceInfo = device,
                    connectionStatus = BLEStatus.DISCONNECTED
                )
                this.setToastMessage("${device.deviceName.onlyString()}|${AppLang.Toast.CONNECTION_FAILED}")
                setLoader(loader = null)
            }

            else -> {}
        }
    }

    private fun checkForWifiScales(device: BLEDevice) {
        viewModelScope.launch {
            if (device.sku == "0412") {
                ggDeviceService.getConnectedWifiSSID(device.convertToGGBTDevice()) {
                    updateDevice(
                        deviceInfo = device.device,
                        wifiSSID = it.onlyString()
                    )
                }
            }
        }

    }

    private fun handleHistory(history: List<GGEntry>) {
        viewModelScope.launch {
            historyRepository.addBulkHistory(history) { device ->
                if (device != null) {
                    val text = if (history.size == 1) "entry" else "entries"
                    if (device.source == Source.BLUETOOTH)
                        setToastMessage("${AppLang.Toast.HISTORY_ADDED}!|${history.size} $text from ${device.device.deviceName.onlyString()}")
                    else
                        setToastMessage("${AppLang.Toast.HISTORY_SENT}!|${history.size} $text from ${device.device.deviceName.onlyString()}")
                }
            }
        }
    }


    private fun updateDevice(
        deviceInfo: GGDeviceDetail? = null,
        connectionStatus: BLEStatus? = null,
        alreadyPaired: Boolean? = null,
        wifiSSID: String? = null,
    ) {
        val currentDevice = deviceRepository.getDevice(deviceInfo?.broadcastId!!)
        if (currentDevice != null) {
            viewModelScope.launch {
                deviceRepository.onDeviceAction(
                    currentDevice.copy(
                        device = deviceInfo,
                        connectionStatus = connectionStatus ?: currentDevice.connectionStatus,
                        alreadyPaired = alreadyPaired ?: currentDevice.alreadyPaired,
                        wifiSSID = wifiSSID ?: currentDevice.wifiSSID
                    ),
                    DeviceAction.UPDATE,
                )
            }
        }
    }

    private fun getLiveMeasurement(device: BLEDevice) {
        if (device.getAppType() == GGAppType.SAGE || device.getAppType() == GGAppType.RPM) {
            ggDeviceService.subscribeToLiveData(device.convertToGGBTDevice()) {

            }
        }
    }

    private fun handleScanIntent(intent: AppIntent.onScanAction) {
        viewModelScope.launch {
            if (!state.value.isEnabled) {
                ggPermissionService.requestScanPermissions(state.value.permissions)
            } else {
                if (state.value.isScanning) {
                    ggPermissionService.stopScan()
                    deviceRepository.refresh()
                    super.handleIntent(AppIntent.alterScanState(false))
                } else {
                    state.value.user.convertGGUserDetail()
                    val ggUser = state.value.user.convertGGUserDetail()
                    ggPermissionService.startScan(intent.appType, ggUser)
                    delay(100)
                    val isEnabled =
                        ggPermissionService.checkScanPermissions(state.value.permissions)
                    if (isEnabled) {
                        super.handleIntent(AppIntent.alterScanState(true))
                    }
                }
            }
        }
    }
}