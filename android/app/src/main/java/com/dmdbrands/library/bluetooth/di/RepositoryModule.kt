package com.dmdbrands.library.bluetooth.di

import com.dmdbrands.library.bluetooth.data.api.IDeviceApi
import com.dmdbrands.library.bluetooth.data.api.IOperationAPI
import com.dmdbrands.library.bluetooth.data.api.IUserAPI
import com.dmdbrands.library.bluetooth.data.datastore.interfaces.IUserDataStore
import com.dmdbrands.library.bluetooth.data.db.dao.DeviceDao
import com.dmdbrands.library.bluetooth.data.db.dao.HistoryDao
import com.dmdbrands.library.bluetooth.data.db.dao.UserDao
import com.dmdbrands.library.bluetooth.data.repository.BaseRepository
import com.dmdbrands.library.bluetooth.data.repository.DeviceRepository
import com.dmdbrands.library.bluetooth.data.repository.HistoryRepository
import com.dmdbrands.library.bluetooth.data.repository.UserRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IAppConfiguration
import com.dmdbrands.library.bluetooth.model.interfaces.IBaseRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IDeviceRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IHistoryRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IUserRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class RepositoryModule {

    @Singleton
    @Provides
    fun provideDeviceRepository(
        devicesDao: DeviceDao,
        deviceApi: IDeviceApi,
        appConfiguration: IAppConfiguration,
    ): IDeviceRepository {
        return DeviceRepository(appConfiguration, devicesDao, deviceApi)
    }

    @Singleton
    @Provides
    fun provideBaseRepository(userDataStore: IUserDataStore): IBaseRepository {
        return BaseRepository(userDataStore)
    }

    @Provides
    @Singleton
    fun provideUserRepository(
        userAPI: IUserAPI,
        userDao: UserDao,
        deviceDao: DeviceDao,
        deviceRepository: IDeviceRepository,
        appConfiguration: IAppConfiguration
    ): IUserRepository {
        return UserRepository(userAPI, userDao, deviceDao, deviceRepository, appConfiguration)
    }

    @Provides
    @Singleton
    fun provideHistoryRepository(
        operationAPI: IOperationAPI,
        historyDao: HistoryDao,
        userRepository: IUserRepository,
        deviceRepository: IDeviceRepository,
        appConfiguration: IAppConfiguration
    ): IHistoryRepository {
        return HistoryRepository(
            operationAPI,
            historyDao,
            userRepository,
            deviceRepository,
            appConfiguration
        )
    }

}