package com.dmdbrands.library.bluetooth.ui.shared.component.device

import androidx.compose.foundation.Image
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Circle
import androidx.compose.material.icons.filled.Link
import androidx.compose.material.icons.filled.LinkOff
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Color.Companion.Red
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.BLEStatus
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.SecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.TextSecondaryColor
import com.dmdbrands.library.bluetooth.util.DeviceUtil.getAppIcon
import com.dmdbrands.library.bluetooth.util.DeviceUtil.getDeviceImage
import com.dmdbrands.library.ggbluetooth.model.GGDeviceDetail
import com.example.utilities.util.onlyString

@Composable
fun DeviceSummary(modifier: Modifier = Modifier, device: BLEDevice) {
    val statusText = when (device.connectionStatus) {
        BLEStatus.CONNECTED -> "Online"
        BLEStatus.DISCONNECTED -> "Offline"
        else -> null
    }
    val statusColor = when (device.connectionStatus) {
        BLEStatus.CONNECTED -> Color(0xFF228B22)
        BLEStatus.DISCONNECTED -> Red
        else -> SecondaryColor
    }
    val skuIsNumeric = device.sku.all { it.isDigit() }


    val deviceInfo = buildAnnotatedString {
        withStyle(
            style = SpanStyle(
                fontFamily = OpenSansFont,
                fontSize = 12.sp,
                color = TextPrimaryColor
            )
        ) {
            append("${device.device.broadcastId}")
        }

        if (skuIsNumeric) {
            withStyle(
                style = SpanStyle(
                    fontFamily = OpenSansFont,
                    fontSize = 12.sp,
                    color = TextSecondaryColor
                )
            ) {
                append(" - ${device.sku}")
            }
        }
    }
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(Modifier.weight(1f)) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(device.getDeviceImage()) // Or device-specific image URL if dynamic
                    .crossfade(true)
                    .build(),
                contentDescription = "Scale Image",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .padding(start = 18.dp)
                    .size(60.dp)
            )
            Image(
                painter = painterResource(getAppIcon(device.getAppType())),
                contentDescription = null,
                modifier = Modifier
                    .size(24.dp)
                    .clip(CircleShape)
            )
        }
        Row(
            modifier = Modifier
                .weight(3f)
                .padding(start = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(2.dp)
            ) {
                Text(
                    text = device.device.deviceName.onlyString(),
                    fontWeight = FontWeight.Bold,
                    fontFamily = OpenSansFont,
                    maxLines = 1,
                    fontSize = 16.sp,
                    modifier = Modifier
                        .fillMaxWidth()
                        .basicMarquee()
                )
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = deviceInfo,
                    )
                    device.userNumber?.let {
                        if (it > 0 == true) {
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                Icon(
                                    imageVector = Icons.Default.Person,
                                    contentDescription = "person",
                                    tint = PrimaryColor,
                                    modifier = Modifier.size(14.dp)
                                )
                                Text(
                                    text = device.getUserLabel(),
                                    fontWeight = FontWeight.Normal,
                                    fontFamily = OpenSansFont,
                                    fontSize = 12.sp
                                )
                                Spacer(modifier = Modifier.width(16.dp))
                            }
                        }
                    }
                }

                if (statusText != null && device.alreadyPaired) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(end = 20.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(10.dp)
                    ) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Icon(
                                imageVector = Icons.Filled.Circle,
                                contentDescription = "status",
                                tint = statusColor,
                                modifier = Modifier.size(12.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = statusText,
                                fontWeight = FontWeight.Normal,
                                fontFamily = OpenSansFont,
                                fontSize = 12.sp
                            )
                        }
                        if (device.isLinked != null) {
                            Spacer(modifier = Modifier.width(4.dp))
                            val icon =
                                if (device.isLinked) Icons.Default.Link else Icons.Default.LinkOff
                            Icon(
                                imageVector = icon,
                                contentDescription = "link",
                                tint = PrimaryColor,
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}


@PreviewScreens
@Composable
private fun DeviceSummaryPreview() {
    GGBluetoothLibraryTheme {
        DeviceSummary(
            device = BLEDevice(
                device = GGDeviceDetail(
                    deviceName = "Samsung",
                    macAddress = "121:121:121:121",
                    identifier = "AJHFHUI12HJJDSFDS"
                ),
                connectionStatus = BLEStatus.CONNECTED,
                isLinked = true,
                alreadyPaired = true,
            )
        )
    }
}
