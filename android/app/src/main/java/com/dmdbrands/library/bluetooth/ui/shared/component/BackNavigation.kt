package com.dmdbrands.library.bluetooth.ui.shared.component

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBackIosNew
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.navigation.NavHostController
import com.dmdbrands.library.bluetooth.config.LocalNavController
import com.example.utilities.services.component.AppButton

@Composable
fun BackNavigation(
    navController: NavHostController = LocalNavController.current,
    navigateBack: (() -> Unit)? = null
) {
    AppButton(containerColor = Transparent, onClick = {
        if (navigateBack != null) {
            navigateBack()
        } else
            navController.navigateUp()
    }) {
        Icon(Icons.Filled.ArrowBackIosNew, null)
    }
}