package com.dmdbrands.library.bluetooth.ui.shared.component

import android.app.Activity
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.NavHostController
import com.dmdbrands.library.bluetooth.config.AppRoute
import com.example.utilities.modal.interfaces.NavigationIntent
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter

@Composable
fun NavigationObserver(
    navigationIntentFlow: Flow<NavigationIntent>,
    navController: NavHostController,
    baseRoute: Any = AppRoute.Init,
) {
    val activity = (LocalContext.current as? Activity)
    LaunchedEffect(activity) {
        navigationIntentFlow.filter { it.baseRoute == baseRoute }.collect { intent ->
            when (intent) {
                is NavigationIntent.NavigateTo -> {
                    navController.navigate(intent.route as AppRoute, builder = intent.builder)
                }

                is NavigationIntent.NavigateBack -> {
                    if (intent.route != null) {
                        navController.popBackStack(intent.route as AppRoute, intent.inclusive)
                    } else {
                        navController.navigateUp()
                    }
                }
            }
        }
    }
}
