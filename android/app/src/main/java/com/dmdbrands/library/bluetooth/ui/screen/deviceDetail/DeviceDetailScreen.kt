package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.BluetoothDisabled
import androidx.compose.material.icons.filled.Circle
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.dmdbrands.library.bluetooth.model.BLEStatus
import com.dmdbrands.library.bluetooth.model.DeviceBottomNavItem
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.DeviceDetailBottomNavigation
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.DeviceDetailView
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.DeviceSettings
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.UserList
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.liveEntry.LiveEntryView
import com.dmdbrands.library.bluetooth.ui.screen.history.components.HistoryList
import com.dmdbrands.library.bluetooth.ui.shared.component.AppScaffold
import com.dmdbrands.library.bluetooth.ui.shared.component.BackNavigation
import com.dmdbrands.library.bluetooth.ui.shared.component.device.DeviceSummary
import com.dmdbrands.library.bluetooth.util.CommonUtil.toHistoryData
import com.dmdbrands.library.bluetooth.util.DeviceUtil.deviceDetailBottomNavItems
import com.dmdbrands.library.bluetooth.util.DeviceUtil.getDetailFeatures
import com.example.utilities.modal.DropDownItem
import com.example.utilities.modal.rememberDropDownMenu
import com.example.utilities.services.component.ActionMenu
import com.example.utilities.util.onlyString

@RequiresApi(Build.VERSION_CODES.O)
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeviceDetail(broadCastId: String) {
    val viewmodel =
        hiltViewModel<DeviceDetailViewmodel, DeviceDetailViewmodel.DetailViewModelFactory> { factory ->
            factory.create(broadCastId)
        }
    val state by viewmodel.state.collectAsState()
    val pagerState = rememberPagerState(0) {
        (state.device?.getDetailFeatures() ?: deviceDetailBottomNavItems).size
    }

    AppScaffold(
        state.device?.device?.deviceName?.onlyString() ?: "--",
        titleComposable = {
            Icon(
                imageVector = Icons.Filled.Circle,
                contentDescription = "status",
                tint = if (state.device?.connectionStatus == BLEStatus.CONNECTED) Color(
                    0xFF228B22
                ) else Color.Red,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(10.dp))
        },
        actions = {
            val liste = listOf(
                DropDownItem(
                    "Disconnect",
                    icon = Icons.Default.BluetoothDisabled,
                    enabled = state.device?.connectionStatus == BLEStatus.CONNECTED
                ),
                DropDownItem(
                    "Delete",
                    icon = Icons.Default.Delete,
                )
            )
            val dropDownMenu by rememberDropDownMenu(liste)
            ActionMenu(
                dropDownMenu = dropDownMenu
            ) { action ->
                when (action.name) {
                    "Disconnect" -> {
                        viewmodel.disconnectDevice()
                    }

                    "Delete" -> {
                        viewmodel.deleteDevice()
                    }
                }

            }
        },
        navigationIcon = {
            BackNavigation()
        },
        setFabPadding = {
            viewmodel.setFabPadding(0.dp)
        }
    ) {

        DeviceDetailBottomNavigation(
            pagerState,
            device = state.device,
            liveEntry = state.liveEntry?.toHistoryData(
                state.device?.getAppType()!!,
                state.device?.sku
            )?.getDisplayValue()
        ) {
            when (it) {
                DeviceBottomNavItem.BasicDetail -> {
                    DeviceDetailView(
                        wifiList = state.wifiList,
                        device = state.device,
                        deviceLog = state.deviceLog,
                        handleIntent = viewmodel::handleIntent,
                        wifiMacAddress = state.connectedWifiMacAddress
                    )
                }

                DeviceBottomNavItem.History -> {
                    HistoryList(
                        list = state.historyData,
                        isDraggable = false
                    ) {
                    }
                }

                DeviceBottomNavItem.Users -> {
                    UserList(
                        state.device?.token,
                        state.users,
                        onSwitch = viewmodel::switchUser,
                        { viewmodel.createNewAccountPopup(state.device!!, state.users) },
                        { viewmodel.handleIntent(DeviceDetailReducer.Intent.removeProfile) }
                    ) {
                        viewmodel.showDeleteUserPopup(it)
                    }
                }

                DeviceBottomNavItem.LiveMeasurement -> {
                    LiveEntryView(
                        state = state
                    )
                }

                DeviceBottomNavItem.DeviceSettings -> {
                    DeviceSettings(
                        state.device,
                        state.users, viewmodel::handleIntent,
                        viewmodel::setModal
                    ) { message, confirmText, loaderMessage, onConfirm ->
                        viewmodel.showConfirmationDialog(
                            message = message,
                            confirmText = confirmText,
                            loaderMessage = loaderMessage,
                            onConfirm = onConfirm,
                        )
                    }
                }

                else -> {
                    DeviceSummary(device = state.device!!)
                }
            }
        }
    }
}



