package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail

import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.viewModelScope
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.config.AppRoute
import com.dmdbrands.library.bluetooth.config.assests.ggDevices
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.BLEStatus
import com.dmdbrands.library.bluetooth.model.DeviceAction
import com.dmdbrands.library.bluetooth.model.HistoryData
import com.dmdbrands.library.bluetooth.model.interfaces.IDeviceRepository
import com.dmdbrands.library.bluetooth.model.interfaces.IHistoryRepository
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.DeviceDetailReducer.Intent.saveError
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.DeviceDetailReducer.Intent.saveLastestEntry
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.DeviceDetailReducer.Intent.saveLiveEntry
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.DeviceDetailReducer.Intent.saveUsers
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.DeviceDetailReducer.Intent.setDevice
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.WifiSetupPrompt
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.sheet.DeviceMode
import com.dmdbrands.library.bluetooth.ui.shared.DeviceViewModel
import com.dmdbrands.library.bluetooth.util.CommonUtil.toHistoryData
import com.dmdbrands.library.bluetooth.util.DeviceUtil.convertToGGBTDevice
import com.dmdbrands.library.bluetooth.util.HistoryUtil.toReformedDetailItem
import com.dmdbrands.library.bluetooth.util.Source
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.dmdbrands.library.ggbluetooth.enums.GGBTSettingType
import com.dmdbrands.library.ggbluetooth.enums.GGScanResponseType
import com.dmdbrands.library.ggbluetooth.enums.GGUserActionResponseType
import com.dmdbrands.library.ggbluetooth.enums.TimeFormat
import com.dmdbrands.library.ggbluetooth.helper.CommonHelper
import com.dmdbrands.library.ggbluetooth.model.GGBTSettingValue
import com.dmdbrands.library.ggbluetooth.model.GGBTUser
import com.dmdbrands.library.ggbluetooth.model.GGBTWifiConfig
import com.dmdbrands.library.ggbluetooth.model.GGDeviceDetail
import com.dmdbrands.library.ggbluetooth.model.GGDeviceLogResponse
import com.dmdbrands.library.ggbluetooth.model.GGLiveDataResponse
import com.dmdbrands.library.ggbluetooth.model.GGScanResponse
import com.example.bluetoothwrapper.service.GGDeviceService
import com.example.utilities.modal.ActionButton
import com.example.utilities.modal.Dialog
import com.example.utilities.modal.Modal
import com.example.utilities.services.FileService
import com.example.utilities.util.onlyString
import com.greatergoods.ggbluetoothsdk.external.enums.GGWifiState
import com.greatergoods.ggbluetoothsdk.external.enums.TemperatureUnit
import com.greatergoods.ggbluetoothsdk.external.models.GGWifiInfo
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch

@HiltViewModel(assistedFactory = DeviceDetailViewmodel.DetailViewModelFactory::class)
class DeviceDetailViewmodel @AssistedInject constructor(
    @Assisted private val broadcastId: String,
    override var deviceRepository: IDeviceRepository,
    private val deviceService: GGDeviceService,
    private val historyRepository: IHistoryRepository,
    private val fileService: FileService,
) :
    DeviceViewModel<DeviceDetailReducer.State, DeviceDetailReducer.Intent>(
        initialState = DeviceDetailReducer.State(),
        reducer = DeviceDetailReducer()
    ) {

    @AssistedFactory
    interface DetailViewModelFactory {
        fun create(id: String): DeviceDetailViewmodel
    }

    init {
        getUsers(broadcastId)
        setDevice(broadcastId)
        subscribeToHistory(broadcastId)
        subscribeToEntry()
    }

    override fun handleIntent(intent: DeviceDetailReducer.Intent) {
        super.handleIntent(intent)
        when (intent) {
            is DeviceDetailReducer.Intent.updateSettings -> {
                setModal(null)
                setLoader("Updating settings...")
                ggDeviceService.updateSettings(
                    state.value.device!!.convertToGGBTDevice(),
                    intent.settings
                )
                setLoader(null)
                if (intent.settings.key == GGBTSettingType.TIME_FORMAT) {
                    val timeFormat =
                        if (intent.settings.value == GGBTSettingValue.Boolean(true)) TimeFormat.TWELVE else TimeFormat.TWENTY_FOUR
                    val updatedPreference =
                        state.value.device!!.preferences?.copy(timeFormat = timeFormat)
                    viewModelScope.launch {
                        deviceRepository.updatePreference(state.value.device!!.copy(preferences = updatedPreference))
                    }
                }
            }

            is DeviceDetailReducer.Intent.clearData -> {
                setLoader("Clearing data...")
                ggDeviceService.clearData(state.value.device!!.convertToGGBTDevice(), intent.type) {
                    setLoader(null)
                }
            }

            is DeviceDetailReducer.Intent.startUpgrade -> {
                startFirmwareUpgrade()
            }

            is DeviceDetailReducer.Intent.getWifiList -> {
                getWIfiList()
            }

            is DeviceDetailReducer.Intent.getConnectedWifiMacAddress -> {
                deviceService.getConnectedWifiMacAddress(state.value.device!!.convertToGGBTDevice()) {
                    handleIntent(DeviceDetailReducer.Intent.saveConnectedWifiMacAddress(it))
                }
            }

            is DeviceDetailReducer.Intent.onCopytoClipBoard -> {
                setToastMessage("Copied to clipboard")
            }

            is DeviceDetailReducer.Intent.saveLogsToMemory -> {
                viewModelScope.launch {
                    fileService.saveLog(state.value.deviceLog.logs.joinToString("\n"))
                }
            }

            is DeviceDetailReducer.Intent.fetchLogs -> {
                getDeviceLogs(broadcastId)
            }

            is DeviceDetailReducer.Intent.onSaveMetrics -> {
                setLoader("Saving metrics...")
                val updatedPreference = state.value.device!!.preferences?.copy(
                    displayMetrics = intent.metrics,
                    shouldMeasurePulse = intent.mode == DeviceMode.FULL_WITH_PULSE,
                    shouldMeasureImpedance = intent.mode != DeviceMode.WEIGHT_ONLY
                )
                if (state.value.device?.connectionStatus == BLEStatus.CONNECTED) {
                    deviceService.updateAccount(state.value.device!!.convertToGGBTDevice(preferences = updatedPreference)) {
                        if (it == GGUserActionResponseType.CREATION_COMPLETED || it == GGUserActionResponseType.UPDATE_COMPLETED) {
                            viewModelScope.launch {
                                deviceRepository.updatePreference(
                                    state.value.device!!.copy(
                                        preferences = updatedPreference
                                    )
                                )
                            }
                        }
                    }
                } else {
                    viewModelScope.launch {
                        deviceRepository.updatePreference(
                            state.value.device!!.copy(
                                preferences = updatedPreference
                            )
                        )
                    }
                }
                setLoader(null)
            }

            is DeviceDetailReducer.Intent.linkToAccount -> {
                connectToAppDialog()
            }

            is DeviceDetailReducer.Intent.unlinkFromAccount -> {
                unlinkDevice()
            }

            is DeviceDetailReducer.Intent.clearWifiList -> {
                ggDeviceService.cancelWifi(state.value.device!!.convertToGGBTDevice()) {}
            }

            is DeviceDetailReducer.Intent.connectWifi -> {
                showWifiSetupDialog(intent.wifiInfo)
            }

            is DeviceDetailReducer.Intent.removeProfile -> {
                viewModelScope.launch {
                    showConfirmationDialog(
                        message = "Are you sure you want to remove this profile? This action cannot be undone.",
                        confirmText = AppLang.Dialog.REMOVE,
                        loaderMessage = "Removing profile...",
                        onConfirm = {
                            deviceRepository.removeToken(state.value.device!!)
                        }
                    )
                }
            }

            else -> null
        }
    }

    private fun subscribeToEntry() {
        viewModelScope.launch {
            deviceService.deviceCallbackFlow.collect { response ->
                when (response) {
                    is GGScanResponse.Entry -> {
                        handleIntent(saveError(null))
                        if (response.data.isNotEmpty()) {
                            val history = response.data.first().toHistoryData(
                                appType = state.value.device!!.getAppType(),
                                sku = state.value.device!!.sku
                            )
                            if (history != null) {
                                handleIntent(
                                    saveLastestEntry(
                                        history.copy(
                                            detailItems = history.detailItems.toReformedDetailItem(
                                                history.appType,
                                                source = Source.BLUETOOTH,
                                                unit = history.unit
                                            )
                                        )
                                    )
                                )
                            }
                        }
                    }

                    is GGScanResponse.DeviceDetail -> {
                        if (response.data.broadcastId == broadcastId && response.type == GGScanResponseType.ERROR)
                            handleIntent(
                                saveError(response.data.error)
                            )
                    }

                    else -> null
                }
            }
        }
    }

    private fun subscribeToLiveEntry() {
        state.value.device ?: return
        viewModelScope.launch {
            deviceService.subscribeToLiveData(state.value.device!!.convertToGGBTDevice()) {
                when (it) {
                    is GGLiveDataResponse.Entry -> {
                        handleIntent(
                            saveLiveEntry(
                                it.data
                            )
                        )
                    }

                    is GGLiveDataResponse.Update<*> -> {
                        if (it.data is TemperatureUnit) {
                            handleIntent(
                                saveLastestEntry(
                                    (state.value.latestEntry as HistoryData).copy(
                                        unit = CommonHelper.getRpmUnit(
                                            it.data
                                        )
                                    )
                                )
                            )
                        }
                    }

                    else -> null
                }
            }
        }
    }

    private fun subscribeToHistory(broadcastId: String) {
        viewModelScope.launch {
            historyRepository.subscribeHistory(broadcastId).collect {
                handleIntent(DeviceDetailReducer.Intent.saveHistoryData(it.sortedByDescending {
                    it.entryTimestamp
                }))
            }
        }
    }

    private fun getUsers(broadcastId: String) {
        val device = deviceRepository.getDevice(broadcastId)
        if (device != null) {
            setLoader("Fetching users ...")
            deviceService.getUsers(device.convertToGGBTDevice()) {
                handleIntent(saveUsers(it.user))
                setLoader(null)
            }
        }
    }

    private fun setDevice(broadcastId: String) {
        viewModelScope.launch {
            deviceRepository.subscribeDevice(broadcastId).collect { device ->
                if (state.value.device?.token != device?.token && device?.device?.broadcastId != null) {
                    getUsers(device.device.broadcastId!!)
                }
                if (device != null) {
                    handleIntent(setDevice(device))
                    subscribeToLiveEntry()
                }
            }
        }
    }

    private fun showWifiSetupDialog(wifiInfo: GGWifiInfo) {
        setModal(
            Modal(
                onDismiss = {
                    setModal(null)
                },
                properties = DialogProperties(
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false
                ),
                content = {
                    WifiSetupPrompt(ssid = wifiInfo.ssid, onDismiss = {
                        setModal(null)
                    }) { wifiConfig ->
                        setupWifi(wifiConfig)
                    }
                }
            )
        )
    }

    private fun setupWifi(wifiConfig: GGBTWifiConfig) {
        setLoader("Connecting to wifi")
        ggDeviceService.setupWifi(
            state.value.device!!.convertToGGBTDevice(),
            wifiConfig
        ) {
            if (it.wifiState == GGWifiState.GG_WIFI_STATE_CONNECTED.name) {
                setModal(null)
                setLoader(null)
                setToastMessage("Connected to wifi | ${wifiConfig.ssid}")
                updateDevice(
                    bleDevice = state.value.device!!,
                    wifiSSID = wifiConfig.ssid
                )
            } else {
                val errorMessage = ggDevices.find { device ->
                    device.sku == state.value.device!!.sku
                }?.errorCodes?.find { error -> error.code == it.errorCode }?.description
                setLoader(null)
                setDialog(
                    Dialog(
                        title = "Failed",
                        message = "${it.wifiState.onlyString()}. \n ${it.errorCode} - ${errorMessage?.onlyString()}",
                        confirmButton = ActionButton(
                            text = "Try Again",
                            action = {
                                setDialog(null)
                                this.setupWifi(wifiConfig)
                            }
                        ),
                        dismissButton = ActionButton(
                            text = AppLang.Dialog.CANCEL,
                            action = {
                                setDialog(null)
                            }
                        )
                    )
                )
            }
        }
    }

    private fun updateDevice(
        bleDevice: BLEDevice? = null, deviceInfo: GGDeviceDetail? = null,
        connectionStatus: BLEStatus? = null, alreadyPaired: Boolean? = null,
        wifiSSID: String? = null,
    ) {
        val currentDevice = bleDevice ?: deviceRepository.getDevice(deviceInfo?.broadcastId!!)
        if (currentDevice != null) {
            viewModelScope.launch {
                deviceRepository.onDeviceAction(
                    currentDevice.copy(
                        connectionStatus = connectionStatus ?: currentDevice.connectionStatus,
                        alreadyPaired = alreadyPaired ?: currentDevice.alreadyPaired,
                        wifiSSID = wifiSSID ?: currentDevice.wifiSSID,
                    ),
                    DeviceAction.UPDATE,
                )
            }
        }
    }

    private fun showSwitchUserPopup(device: BLEDevice, user: GGBTUser?) {
        viewModelScope.launch {
            setDialog(
                Dialog(
                    title = AppLang.ALERT,
                    message = AppLang.Dialog.SWITCH_USER,
                    confirmButton = ActionButton(
                        text = AppLang.Dialog.SWITCH,
                        action = {
                            setLoader("Switching...")
                            viewModelScope.launch {
                                deviceRepository.updateToken(
                                    device,
                                    user,
                                )
                                ggDeviceService.updateAccount(
                                    device.convertToGGBTDevice(
                                        scaleToken = user?.token,
                                        preferences = device.preferences?.copy(
                                            displayName = user?.name,
                                            shouldMeasureImpedance = user?.isBodyMetricsEnabled
                                        )
                                    )
                                ) {}
                                setLoader(null)
                                setDialog(null)
                            }
                        }
                    ),
                    dismissButton = ActionButton(
                        text = AppLang.Dialog.CANCEL,
                        action = {
                            setDialog(dialog = null)
                        }
                    )
                )
            )
        }
    }

    fun showDeleteUserPopup(token: String) {
        viewModelScope.launch {
            setDialog(
                Dialog(
                    title = AppLang.ALERT,
                    message = AppLang.Dialog.DELETE_USER,
                    confirmButton = ActionButton(
                        text = AppLang.Dialog.DELETE,
                        action = {
                            deleteUsers(token)
                        }
                    ),
                    dismissButton = ActionButton(
                        text = AppLang.Dialog.CANCEL,
                        action = {
                            setDialog(dialog = null)
                        }
                    )
                )
            )
        }
    }

    private fun showDeletePopup(device: BLEDevice) {
        viewModelScope.launch {
            setDialog(
                Dialog(
                    title = AppLang.ALERT,
                    message = AppLang.Dialog.DELETE_INFO,
                    confirmButton = ActionButton(
                        text = AppLang.Dialog.DELETE,
                        action = {
                            setLoader("Deleting...")
                            if (state.value.device?.source == Source.API) {
                                viewModelScope.launch {
                                    deviceRepository.deleteRemoteDevice(
                                        appType = device.getAppType(),
                                        device
                                    )
                                }

                            } else {
                                viewModelScope.launch {
                                    deviceRepository.onDeviceAction(
                                        device = device,
                                        action = DeviceAction.DELETE
                                    )
                                }
                            }
                            setLoader(loader = null)
                            navigateBack(baseRoute = AppRoute.Init)
                        }
                    ),
                    dismissButton = ActionButton(
                        text = AppLang.Dialog.CANCEL,
                        action = {
                            setDialog(dialog = null)
                        }
                    )
                )
            )
        }
    }

    private fun showDisconnectPopup(device: BLEDevice) {
        viewModelScope.launch {
            setDialog(
                Dialog(
                    title = AppLang.ALERT,
                    message = "Are you sure you want to disconnect?",
                    confirmButton = ActionButton(
                        text = "DISCONNECT",
                        action = {
                            setLoader("Disconnecting...")
                            viewModelScope.launch {
                                deviceService.disconnectDevice(device.convertToGGBTDevice())
                            }
                        },
                    ),
                    dismissButton = ActionButton(
                        text = AppLang.Dialog.CANCEL,
                        action = {
                            setDialog(dialog = null)
                        }
                    )
                )
            )
        }
    }
    
    private fun getDeviceLogs(broadcastId: String) {
        val device = deviceRepository.getDevice(broadcastId)
        if (device?.connectionStatus == BLEStatus.CONNECTED) {
            deviceService.getDeviceLogs(device.convertToGGBTDevice()) { data ->
                if (data is GGDeviceLogResponse.Fetching) {
                    handleIntent(DeviceDetailReducer.Intent.saveDeviceLogFetchStatus(data.percentage))
                } else if (data is GGDeviceLogResponse.Completed) {
                    handleIntent(DeviceDetailReducer.Intent.saveDeviceLogs(data.logs))
                }
            }
        }
    }

    private fun connectToApp() {
        viewModelScope.launch {
            val device = state.value.device!!
            if (device.primaryToken == null && device.getAppType() == GGAppType.WEIGHT_GURUS) {
                setToastMessage("No token found | Add your profile")
            } else if (device.primaryToken != device.token) {
                setToastMessage("Cannot connect to app | switch to remote user")
            } else {
                deviceRepository.addDeviceToRemote(device.getAppType(), device)
            }
            setLoader(loader = null)
        }
    }

    fun startLiveMeasurement(device: BLEDevice) {
        deviceService.startMeasurement(device.convertToGGBTDevice())
    }

    private fun deleteUsers(token: String) {
        if (state.value.device == null) return
        setLoader("Deleting user ...")
        val updatedDevice = state.value.device!!.copy(token = token)
        deviceService.deleteAccount(updatedDevice.convertToGGBTDevice()) {
            setLoader(loader = null)
            getUsers(updatedDevice.device.broadcastId!!)
            if (token == state.value.device?.primaryToken) {
                viewModelScope.launch {
                    deviceRepository.removeToken(state.value.device!!, true)
                }
            }
        }
    }

    fun startFirmwareUpgrade() {
        if (state.value.device == null) return
        deviceService.startFirmwareUpgrade(state.value.device!!.convertToGGBTDevice())
    }

    fun changeUnit(unit: String, key: String = GGBTSettingType.UNIT) {
        deviceService.changeUnit(state.value.device!!.convertToGGBTDevice(), unit, key)
    }

    fun changeMode(mode: String) {
        deviceService.changeMode(state.value.device!!.convertToGGBTDevice(), mode)
    }

    fun tare() {
        deviceService.tare(state.value.device!!.convertToGGBTDevice())
    }

    fun connectToAppDialog() {
        setDialog(
            Dialog(
                title = AppLang.ALERT,
                message = "Connecting to app will delete the existing record in the database. Are you sure you want to continue?",
                confirmButton = ActionButton(
                    text = "CONFIRM",
                    action = {
                        setLoader("Connecting to app...")
                        connectToApp()
                    }
                ),
                dismissButton = ActionButton(
                    text = AppLang.Dialog.CANCEL,
                    action = {
                        setDialog(dialog = null)
                    }
                )
            ))
    }

    fun unlinkDevice() {
        setDialog(
            Dialog(
                title = AppLang.ALERT,
                message = "Are you sure you want to unlink?",
                confirmButton = ActionButton(
                    text = "UNLINK",
                    action = {
                        setLoader("Unlinking...")
                        viewModelScope.launch {
                            deviceRepository.deleteRemoteDevice(
                                appType = state.value.device!!.getAppType(),
                                state.value.device!!
                            )
                            setLoader(loader = null)
                        }
                    }
                ),
                dismissButton = ActionButton(
                    text = AppLang.Dialog.CANCEL,
                    action = {
                        setDialog(dialog = null)
                    }
                )))
    }

    fun disconnectDevice() {
        viewModelScope.launch {
            if (state.value.device != null) {
                showDisconnectPopup(state.value.device!!)
            }
        }
    }

    fun deleteDevice() {
        viewModelScope.launch {
            if (state.value.device != null) {
                showDeletePopup(state.value.device!!)
            }
        }
    }

    fun switchUser(user: GGBTUser?) {
        if (state.value.device != null) {
            showSwitchUserPopup(state.value.device!!, user)
        }
    }

    fun getWIfiList() {
        viewModelScope.launch {
            ggDeviceService.getWifiList(state.value.device!!.convertToGGBTDevice()) {
                handleIntent(DeviceDetailReducer.Intent.saveWifiList(it.wifi))
            }
        }
    }
}