package com.dmdbrands.library.bluetooth.model.api

import com.google.gson.annotations.SerializedName


sealed interface OperationResponse {
    val operations: List<Entry>
    val syncTime: String
}

data class BHOperationResponse(
    @SerializedName("operations") override val operations: List<BHEntry>,
    @SerializedName("syncTime") override val syncTime: String
) : OperationResponse

data class WGOperationResponse(
    @SerializedName("operations") override val operations: List<WGEntry>,
    @SerializedName("timestamp") override val syncTime: String
) : OperationResponse

data class SBOperationResponse(
    @SerializedName("operations") override val operations: List<BabyEntry>,
    @SerializedName("timestamp") override val syncTime: String
) : OperationResponse