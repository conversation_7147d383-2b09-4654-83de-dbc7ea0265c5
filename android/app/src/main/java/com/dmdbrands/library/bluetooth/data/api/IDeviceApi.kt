package com.dmdbrands.library.bluetooth.data.api

import com.dmdbrands.library.bluetooth.model.api.BHDeviceData
import com.dmdbrands.library.bluetooth.model.api.BHDeviceRequest
import com.dmdbrands.library.bluetooth.model.api.BHDeviceResponse
import com.dmdbrands.library.bluetooth.model.api.PreferenceResponse
import com.dmdbrands.library.bluetooth.model.api.PreferencesRequest
import com.dmdbrands.library.bluetooth.model.api.WGDeviceData
import com.dmdbrands.library.bluetooth.model.api.WGDeviceRequest
import com.dmdbrands.library.bluetooth.model.interfaces.IAppConfiguration
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path

interface IDeviceApi : IAppConfiguration {

    @GET("monitor")
    suspend fun getBhDevices(): Response<BHDeviceResponse>

    @POST("monitor")
    suspend fun addBhDevice(@Body device: BHDeviceRequest): Response<BHDeviceData>

    @DELETE("monitor/{id}")
    suspend fun deleteBhDevice(@Path("id") id: String): Response<Boolean>

    @POST("scale-r4/preference")
    suspend fun updateDevicePreference(@Body preferences: PreferencesRequest): Response<PreferenceResponse>

    @POST("paired-scale")
    suspend fun addWgDevice(@Body device: WGDeviceRequest): Response<WGDeviceData>

    @GET("paired-scale")
    suspend fun getWgDevices(): Response<List<WGDeviceData>>

    @DELETE("paired-scale/{id}")
    suspend fun deleteWgDevice(@Path("id") id: String): Response<Boolean>


}