package com.dmdbrands.library.bluetooth.ui.screen.apps

import com.dmdbrands.library.bluetooth.model.AppStatus
import com.example.utilities.modal.interfaces.IReducer

data class AppScreenState(
    val appsList: List<AppStatus> = listOf(),
) : IReducer.State

interface AppScreenIntent : IReducer.Intent {
    data class LoadApps(val apps: List<AppStatus>) : AppScreenIntent
}

class AppScreenReducer : IReducer<AppScreenState, AppScreenIntent> {
    override fun reduce(
        state: AppScreenState,
        intent: AppScreenIntent
    ): AppScreenState? {
        return when (intent) {
            is AppScreenIntent.LoadApps -> {

                state.copy(appsList = intent.apps)
            }


            else -> state
        }
    }

}