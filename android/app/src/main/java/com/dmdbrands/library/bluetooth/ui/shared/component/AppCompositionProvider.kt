package com.dmdbrands.library.bluetooth.ui.shared.component

import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.remember
import androidx.navigation.NavHostController
import com.dmdbrands.library.bluetooth.config.LocalNavController
import com.dmdbrands.library.bluetooth.config.LocalSnackBarState

@Composable
fun AppCompositionProvider(
    navController: NavHostController,
    snackbarHostState: SnackbarHostState,
    content: @Composable () -> Unit
) {
    val snackBarHostState = remember { SnackbarHostState() }

    CompositionLocalProvider(
        LocalNavController provides navController,
        LocalSnackBarState provides snackBarHostState,
    ) {
        content()
    }
}