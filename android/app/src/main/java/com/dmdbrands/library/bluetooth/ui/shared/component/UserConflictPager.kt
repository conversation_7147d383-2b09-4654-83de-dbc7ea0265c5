package com.dmdbrands.library.bluetooth.ui.shared.component

import android.annotation.SuppressLint
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.ggbluetooth.model.GGBTUser
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.component.input.Input
import kotlinx.coroutines.launch

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun UserConflictPager(
    userName: String,
    users: List<GGBTUser> = listOf(),
    onEditConfirmed: (String) -> Unit,
    onRestore: () -> Unit,
    onDismiss: () -> Unit
) {
    val pagerState = rememberPagerState { 2 }
    val scope = rememberCoroutineScope()

    Card(
        colors = CardDefaults.cardColors(containerColor = White),
        modifier = Modifier.wrapContentSize()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Common header
            Box(modifier = Modifier.fillMaxWidth()) {
                AppButton(
                    onClick = onDismiss,
                    containerColor = Transparent,
                    modifier = Modifier.align(Alignment.TopEnd)
                ) {
                    Icon(imageVector = Icons.Default.Close, contentDescription = "Close")
                }

                Text(
                    text = if (pagerState.currentPage == 0) "User already exists" else "Edit User Name",
                    fontSize = 18.sp,
                    fontFamily = OpenSansFont,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.align(Alignment.Center)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Pager inside fixed-height box to avoid janky transitions
            BoxWithConstraints {
                val maxH = maxHeight / 6
                HorizontalPager(
                    state = pagerState,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(maxH)
                ) { page ->
                    when (page) {
                        0 -> DuplicateUserContent(
                            onEdit = { scope.launch { pagerState.animateScrollToPage(1) } },
                            onRestore = onRestore
                        )

                        1 -> EditUserNameContent(
                            userName = userName,
                            users = users,
                            onEdit = { onEditConfirmed(it) },
                            onBack = { scope.launch { pagerState.animateScrollToPage(0) } }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun DuplicateUserContent(
    onEdit: () -> Unit,
    onRestore: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp),
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = "Choose a new user name or restore the existing account",
            fontSize = 14.sp,
            fontFamily = OpenSansFont,
            textAlign = TextAlign.Center
        )

        Row(horizontalArrangement = Arrangement.spacedBy(12.dp)) {
            AppButton(onClick = onEdit, cardModifier = Modifier.weight(1f)) {
                Text(
                    "Edit name".uppercase(), fontSize = 18.sp,
                    modifier = Modifier.fillMaxWidth(),
                    fontWeight = FontWeight.Medium,
                    fontFamily = OpenSansFont,
                    textAlign = TextAlign.Center,
                    color = White
                )
            }
            AppButton(onClick = onRestore, cardModifier = Modifier.weight(1f)) {
                Text(
                    "Restore".uppercase(), fontSize = 18.sp,
                    modifier = Modifier.fillMaxWidth(),
                    fontWeight = FontWeight.Medium,
                    fontFamily = OpenSansFont,
                    textAlign = TextAlign.Center,
                    color = White
                )
            }
        }
    }
}

@Composable
private fun EditUserNameContent(
    userName: String,
    users: List<GGBTUser> = listOf(),
    onEdit: (String) -> Unit,
    onBack: () -> Unit
) {
    var tempName by remember { mutableStateOf(userName) }

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp),
        modifier = Modifier.fillMaxWidth()
    ) {
        Input(
            name = "userName",
            value = tempName,
            onValueChange = { tempName = it as String },
            label = "New username"
        )

        Row(horizontalArrangement = Arrangement.spacedBy(12.dp)) {
            AppButton(onClick = onBack, cardModifier = Modifier.weight(1f)) {
                Text(
                    "Back".uppercase(), fontSize = 16.sp,
                    modifier = Modifier.fillMaxWidth(),
                    fontWeight = FontWeight.Medium,
                    fontFamily = OpenSansFont,
                    textAlign = TextAlign.Center,
                    color = White
                )
            }
            AppButton(
                onClick = {
                    onEdit(tempName)
                },
                cardModifier = Modifier.weight(1f),
                enabled = users.none { it.name == tempName }
            ) {
                Text(
                    "Save".uppercase(), fontSize = 16.sp,
                    modifier = Modifier.fillMaxWidth(),
                    fontWeight = FontWeight.Medium,
                    fontFamily = OpenSansFont,
                    textAlign = TextAlign.Center,
                    color = White
                )
            }
        }
    }
}

@PreviewScreens
@Composable
private fun UserConflictPagerPreview() {
    GGBluetoothLibraryTheme {
        UserConflictPager("Joe", onEditConfirmed = {}, onRestore = {}, onDismiss = {})
    }
}
