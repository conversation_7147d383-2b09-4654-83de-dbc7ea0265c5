package com.dmdbrands.library.bluetooth.ui.screen.history.components

import androidx.compose.material3.CardDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.graphics.RectangleShape
import com.example.utilities.modal.ExpandedCardState
import com.example.utilities.services.component.ExpandableCard
import com.example.utilities.services.component.rememberExpandedCardState
import com.example.utilities.services.theme.BackgroundSecondaryColor
import com.example.utilities.services.theme.PrimaryColor

@Composable
fun HistoryCard(
    id: Long,
    opTimestamp: String,
    appType: String,
    sku: String? = null,
    unit: String,
    value: String = "",
    state: ExpandedCardState = rememberExpandedCardState(),
    enabled: Boolean = true
) {
    val bgColor = if (state.value.contains(id)) PrimaryColor else BackgroundSecondaryColor

    ExpandableCard(
        id = id,
        state = state,
        colors = CardDefaults.cardColors(
            containerColor = bgColor,
            disabledContainerColor = BackgroundSecondaryColor
        ),
        shape = RectangleShape,
        arrowColor = if (state.value.contains(id)) White else PrimaryColor,
        title = {
            HistoryOverview(
                id = id,
                opTimestamp = opTimestamp,
                sku = sku,
                appType = appType,
                unit = unit,
                value = value,
                state = state
            )
        },
        enabled = enabled
    )
}

