package com.dmdbrands.library.bluetooth.util

import com.dmdbrands.library.bluetooth.model.UserDetails
import com.dmdbrands.library.bluetooth.ui.screen.settings.reducer.EUserDetails
import com.dmdbrands.library.ggbluetooth.enums.GoalType
import com.dmdbrands.library.ggbluetooth.model.GGBTUserProfile
import com.example.utilities.util.Form
import java.util.Calendar

object UserUtil {


    fun Form.getUserDetails(): UserDetails {
        val data = this.getAllValues()
        return UserDetails(
            name = data.getOrDefault(EUserDetails.NAME.name, "") as String,
            email = data.getOrDefault(EUserDetails.EMAIL.name, "") as String,
            height = data.getOrDefault(EUserDetails.HEIGHT.name, "") as String,
            birthday = data.getOrDefault(
                EUserDetails.BIRTHDAY.name,
                Calendar.getInstance()
            ) as Calendar,
            weight = data.getOrDefault(EUserDetails.WEIGHT.name, "") as String,
            goalWeight = data.getOrDefault(EUserDetails.GOAL_WEIGHT.name, "") as String,
            gender = data.getOrDefault(EUserDetails.GENDER.name, "") as String,
            unitType = data.getOrDefault(EUserDetails.UNIT_TYPE.name, "") as String,
            goalType = data.getOrDefault(EUserDetails.GOAL_TYPE.name, "") as String,
            isAthlete = data.getOrDefault(EUserDetails.BODY_TYPE.name, false) as Boolean
        )
    }

    fun UserDetails.convertGGUserDetail(): GGBTUserProfile {
        return GGBTUserProfile(
            name = name,
            height = height.toDouble(),
            age = this.getAge(),
            weight = weight?.toDouble(),
            goalWeight = goalWeight?.toDouble(),
            sex = gender,
            isAthlete = isAthlete,
            goalType = getGoalType(goalType),
            unit = getUnit(unitType)
        )
    }

    private fun getGoalType(goalType: String?) = if (goalType.isNullOrEmpty()) null else {
        if (goalType.contains(
                "lose",
                ignoreCase = true
            )
        ) GoalType.LOSE else if (goalType.contains(
                "gain",
                ignoreCase = true
            )
        ) GoalType.GAIN else GoalType.MAINTAIN
    }


    private fun getUnit(unitType: String) =
        if (unitType.contains("kg", ignoreCase = true)) "kg" else "lb"


}