package com.dmdbrands.library.bluetooth.data.repository

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import com.dmdbrands.library.bluetooth.data.api.IDeviceApi
import com.dmdbrands.library.bluetooth.data.db.dao.DeviceDao
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.BLEStatus
import com.dmdbrands.library.bluetooth.model.DeviceAction
import com.dmdbrands.library.bluetooth.model.interfaces.IAppConfiguration
import com.dmdbrands.library.bluetooth.model.interfaces.IDeviceRepository
import com.dmdbrands.library.bluetooth.util.DBUtil.convertBleDevice
import com.dmdbrands.library.bluetooth.util.DBUtil.convertDeviceEntity
import com.dmdbrands.library.bluetooth.util.DBUtil.convertToPreferenceEntity
import com.dmdbrands.library.bluetooth.util.DBUtil.convertToPreferences
import com.dmdbrands.library.bluetooth.util.DeviceUtil.convertToBHDeviceRequest
import com.dmdbrands.library.bluetooth.util.DeviceUtil.convertToPreferencesRequest
import com.dmdbrands.library.bluetooth.util.DeviceUtil.convertToWGDeviceRequest
import com.dmdbrands.library.bluetooth.util.Source
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.dmdbrands.library.ggbluetooth.model.GGBTUser
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import okhttp3.ResponseBody
import retrofit2.Response
import javax.inject.Inject

class DeviceRepository @Inject constructor(
    private val appConfiguration: IAppConfiguration,
    private val devicesDao: DeviceDao,
    private val deviceApi: IDeviceApi,
) : IDeviceRepository {


    init {
        CoroutineScope(Dispatchers.Main).launch {
            fetchDevices()
        }
    }

    private val _devices: MutableLiveData<List<BLEDevice>> = MutableLiveData(listOf())

    private val _appDevices: MutableLiveData<List<BLEDevice>> =
        MutableLiveData(listOf())


    override suspend fun subscribeNewDevices(): Flow<List<BLEDevice>> {
        return _devices.asFlow()
            .combine(_appDevices.asFlow()) { devices, appDevices ->
                devices.plus(appDevices)
                    .filterNot { it.alreadyPaired && it.connectionStatus != BLEStatus.CONNECTED }
            }

    }

    override suspend fun subscribePairedDevices(): Flow<List<BLEDevice>> {
        return _devices.asFlow()
            .map { devices -> devices.filter { it.alreadyPaired } }
    }

    override suspend fun subscribeRemoteDevices(): Flow<List<BLEDevice>> {
        return _appDevices.asFlow()
    }

    override suspend fun subscribeAllPairedDevices(): Flow<List<BLEDevice>> {
        return _devices.asFlow()
            .combine(_appDevices.asFlow()) { devices, appDevices ->
                devices.plus(appDevices).filter { it.alreadyPaired }
            }

    }

    override fun getDevice(broadcastId: String): BLEDevice? {
        return _devices.value?.plus(_appDevices.value ?: listOf())?.find {
            it.device.broadcastId == broadcastId
        }
    }

    override suspend fun subscribeDevice(broadCastId: String): Flow<BLEDevice?> {
        return _devices.asFlow()
            .combine(_appDevices.asFlow()) { devices, appDevices ->
                // Check in both lists
                val matchingDevice = devices
                    .plus(appDevices)
                    .firstOrNull { it.device.broadcastId == broadCastId } // Get the first match or null if no match
                matchingDevice
            }
    }

    override suspend fun checkDeviceAvailable(device: BLEDevice): Boolean {
        return withContext(IO) {
            devicesDao.checkDeviceAvailable(
                device.device.macAddress,
                device.userNumber
            )
        }
    }

    override suspend fun onDeviceAction(
        device: BLEDevice,
        action: DeviceAction,
    ) {
        when (action) {
            DeviceAction.ADD -> addDevice(device)
            DeviceAction.DELETE -> deleteDevice(device, true)
            DeviceAction.UPDATE -> updateDevice(device)
            DeviceAction.REMOVE -> deleteDevice(device)
        }
    }

    override suspend fun removeToken(device: BLEDevice, isPrimaryToken: Boolean) {
        val updatedPreferences = device.preferences?.copy(displayName = null)
        val updatedDevice = if (isPrimaryToken) device.copy(primaryToken = null) else device.copy(
            token = null,
            preferences = updatedPreferences
        )
        onDeviceAction(
            updatedDevice, DeviceAction.UPDATE
        )
        withContext(IO) {
            if (isPrimaryToken) {
                devicesDao.updatePrimaryToken(
                    device.device.broadcastId!!,
                    updatedDevice.primaryToken
                )
            } else {
                devicesDao.updateToken(device.device.broadcastId!!, updatedDevice.token)
                devicesDao.updatePreference(updatedDevice.convertToPreferenceEntity())
            }
        }
    }

    override suspend fun updatePreference(device: BLEDevice) {
        var updatedDevice = device
        val deferred = CoroutineScope(IO).async {
            if (device.source == Source.API) {
                if (device.preferences == null) return@async
                val updatedPreferences =
                    device.preferences.convertToPreferencesRequest(device.device.systemID)
                val response = deviceApi.updateDevicePreference(updatedPreferences)
                updatedDevice = updatedDevice.copy(
                    preferences = response.body()
                        ?.convertToPreferences()
                )
            } else {
                devicesDao.updatePreference(device.convertToPreferenceEntity())
            }
        }

        // Step 2: Await result, then switch to Main for UI-safe update
        deferred.await().let {
            updateDevice(updatedDevice)
        }
    }

    override suspend fun updateToken(device: BLEDevice, user: GGBTUser?, isPrimaryToken: Boolean) {
        val primaryToken = if (isPrimaryToken) user?.token else device.primaryToken
        val updatedDevice = device.copy(
            token = user?.token,
            preferences = device.preferences?.copy(
                displayName = user?.name,
                shouldMeasureImpedance = user?.isBodyMetricsEnabled
            ),
            primaryToken = primaryToken
        )
        onDeviceAction(
            updatedDevice, DeviceAction.UPDATE
        )
        withContext(IO) {
            if (isPrimaryToken) {
                devicesDao.updatePrimaryToken(device.device.broadcastId!!, user?.token)
            }
            devicesDao.updateToken(device.device.broadcastId!!, user?.token)
            devicesDao.updatePreference(updatedDevice.convertToPreferenceEntity())
        }
    }

    override suspend fun refresh() {
        fetchDevices()
    }

    override suspend fun onPostConnect(device: BLEDevice?) {
        if (device != null) {
            addDeviceToDB(device)
            updateDevice(device.copy(connectionStatus = BLEStatus.CONNECTED, alreadyPaired = true))
            _appDevices.value?.forEach { remoteDevice ->
                val isDeviceAvailable = withContext(IO) {
                    devicesDao.checkDeviceAvailable(
                        remoteDevice.device.macAddress,
                        remoteDevice.userNumber
                    )
                }
                if (isDeviceAvailable) {
                    updateLinkStatus(remoteDevice, true)
                }
            }

        }
    }

    override suspend fun getAppDevices(appType: String): List<BLEDevice> {
        appConfiguration.setAppType(appType)

        val response = when (appType) {
            GGAppType.BALANCE_HEALTH -> {
                val apiResponse = deviceApi.getBhDevices()
                if (apiResponse.isSuccessful && apiResponse.body() != null) {
                    apiResponse.body()?.data?.map { it.convertToBLEDevice() }
                } else null
            }

            GGAppType.WEIGHT_GURUS -> {
                val apiResponse = deviceApi.getWgDevices()
                if (apiResponse.isSuccessful && apiResponse.body() != null) {
                    apiResponse.body()?.map { it.convertToBLEDevice() }
                } else null
            }

            else -> null
        }

        if (response != null) {
            updateLinkStatusBasedOnAppType(appType, false)
            _appDevices.value = withContext(IO) {
                val existingDevices = _appDevices.value ?: listOf()
                existingDevices.filter { it.getAppType() != appType } + response
            }

            _appDevices.value?.forEach { remoteDevice ->
                val isDeviceAvailable = withContext(IO) {
                    devicesDao.checkDeviceAvailable(
                        remoteDevice.device.macAddress,
                        remoteDevice.userNumber
                    )
                }
                if (isDeviceAvailable) {
                    updateLinkStatus(remoteDevice, true)
                }
            }
        }

        return _appDevices.value?.filter { it.getAppType() == appType } ?: listOf()


    }

    private suspend fun updateLinkStatus(device: BLEDevice, isLinked: Boolean) {
        runBlocking {
            withContext(IO) {
                devicesDao.updateDevice(
                    broadcastId = device.device.broadcastId!!,
                    isLink = isLinked
                )
                devicesDao.updateDeviceSystemId(
                    device.device.systemID,
                    device.device.broadcastId!!
                )
            }
        }
        fetchDevices()
    }

    override suspend fun updateLinkStatusBasedOnAppType(appType: String, isLinked: Boolean?) {
        runBlocking {
            withContext(IO) {
                devicesDao.updateDeviceBasedOnAppType(appType, isLinked)
                _devices.postValue(
                    _devices.value?.map {
                        if (it.getAppType() == appType) {
                            it.copy(isLinked = isLinked)
                        } else {
                            it
                        }
                    }
                )
                if (isLinked == null) {
                    _appDevices.postValue(
                        _appDevices.value?.filter { it.getAppType() != appType }
                    )
                }
            }
        }
        fetchDevices()
    }

    override suspend fun addDeviceToRemote(
        appType: String,
        device: BLEDevice
    ): Response<Boolean> {

        appConfiguration.setAppType(appType)

        val response =
            if (_appDevices.value?.find { it.device.broadcastId == device.device.broadcastId } == null) {
                when (appType) {
                    GGAppType.BALANCE_HEALTH -> {
                        val apiResponse =
                            deviceApi.addBhDevice(device.convertToBHDeviceRequest())
                        if (apiResponse.isSuccessful && apiResponse.body() != null) {
                            apiResponse.body()?.convertToBLEDevice()
                        } else null
                    }

                    GGAppType.WEIGHT_GURUS -> {
                        val apiResponse =
                            deviceApi.addWgDevice(device.convertToWGDeviceRequest())
                        if (apiResponse.isSuccessful && apiResponse.body() != null) {
                            apiResponse.body()?.convertToBLEDevice()
                        } else null
                    }

                    else -> null
                }
            } else null

        if (response != null) {
            _appDevices.postValue(_appDevices.value?.plus(response))
            updateLinkStatus(response, true)
            return Response.success(true)
        } else {
            return Response.error(
                400,
                ResponseBody.create(null, "Failed to add device or device already exists")
            )
        }

    }

    override suspend fun deleteRemoteDevice(
        appType: String,
        device: BLEDevice
    ): Response<Boolean> {
        appConfiguration.setAppType(appType)

        val response = when (appType) {
            GGAppType.BALANCE_HEALTH -> {
                val apiResponse =
                    deviceApi.deleteBhDevice(device.device.systemID.toString())
                if (apiResponse.isSuccessful) {
                    apiResponse
                } else {
                    Response.error(
                        apiResponse.code(),
                        apiResponse.errorBody() ?: ResponseBody.create(
                            null,
                            "Error deleting BH device"
                        )
                    )
                }
            }

            GGAppType.WEIGHT_GURUS -> {
                val apiResponse =
                    deviceApi.deleteWgDevice(device.device.systemID.toString())
                if (apiResponse.isSuccessful) {
                    apiResponse
                } else {
                    Response.error(
                        apiResponse.code(),
                        apiResponse.errorBody() ?: ResponseBody.create(
                            null,
                            "Error deleting WG device"
                        )
                    )
                }
            }

            else -> Response.error(400, ResponseBody.create(null, "Unknown app type"))
        }

        if (response.isSuccessful) {
            // Refresh the device list
            getAppDevices(appType)
            // Since the original response might not be a Boolean, create a new one
            return Response.success(true)
        } else {
            return response
        }
    }


    private fun updateDevice(
        device: BLEDevice,
    ) {
        val tempList =
            if (device.source == Source.BLUETOOTH) _devices.value!!.toMutableList() else _appDevices.value!!.toMutableList()
        val index =
            tempList.indexOfFirst { it.device.broadcastId == device.device.broadcastId || (it.device.macAddress == device.device.macAddress && it.userNumber == device.userNumber) }
        tempList[index] = device
        if (device.source == Source.BLUETOOTH)
            _devices.value = tempList
        else
            _appDevices.value = tempList
    }

    private fun addDevice(
        device: BLEDevice,
    ) {

        if (!checkDeviceAvailable(
                _devices.value!!.plus(_appDevices.value!!).toMutableList(),
                device.device.broadcastId!!
            )
        ) {
            val tempList = _devices.value!!.toMutableList()
            tempList.add(device)
            _devices.value = tempList

        }
    }

    private suspend fun deleteDevice(device: BLEDevice, removeFromDB: Boolean = false) {

        if (checkDeviceAvailable(_devices.value!!.toMutableList(), device.device.broadcastId!!)
        ) {
            val tempList = _devices.value!!.toMutableList()
            _devices.value = tempList.filter { it.device.broadcastId != device.device.broadcastId }
        }
        if (removeFromDB)
            removeDeviceFromDB(device)
    }

    private suspend fun addDeviceToDB(device: BLEDevice) {
        withContext(IO) {
            if (!devicesDao.checkDeviceAvailable(device.device.macAddress, device.userNumber)) {
                devicesDao.addDevice(device.convertDeviceEntity())
                if (device.preferences != null) {
                    devicesDao.addDevicePreference(device.convertToPreferenceEntity())
                }
            }
        }
    }

    private suspend fun removeDeviceFromDB(device: BLEDevice) {
        withContext(IO) {
            if (devicesDao.checkDeviceAvailable(device.device.macAddress, device.userNumber)) {
                devicesDao.deleteDevice(device.device.broadcastId.toString())
            }
        }
    }

    private fun checkDeviceAvailable(devicesList: List<BLEDevice>, broadcastId: String): Boolean {
        val device = devicesList.find { it.device.broadcastId == broadcastId }

        return device != null
    }

    override suspend fun fetchDevices() {
        // Fetch the connected devices first
        val connectedDevices = _devices.value?.filter {
            it.alreadyPaired && it.connectionStatus == BLEStatus.CONNECTED
        }

        // Use async to fetch paired devices concurrently
        val pairedDevicesDeferred = withContext(IO) {
            async {
                val devices = devicesDao.getDevices()
                devices.mapNotNull { device ->
                    val preferencesEntity = devicesDao.getPreferencesForDevice(device.broadcastId!!)
                    val preferences = preferencesEntity?.convertToPreferences()

                    val bleDevice = device.convertBleDevice(preferences)

                    val isConnected = connectedDevices?.any {
                        it.device.broadcastId == bleDevice.device.broadcastId
                    } == true

                    if (!isConnected) bleDevice else null
                }
            }
        }

        // Await the result of paired devices and combine them with the connected devices
        val pairedDevices = pairedDevicesDeferred.await()

        // Update the devices list by combining the connected and paired devices
        _devices.value = (connectedDevices ?: listOf()) + pairedDevices
    }


}