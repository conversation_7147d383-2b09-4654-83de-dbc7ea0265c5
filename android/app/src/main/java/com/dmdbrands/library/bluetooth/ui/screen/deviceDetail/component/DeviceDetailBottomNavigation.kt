package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.pager.PagerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.RichTooltip
import androidx.compose.material3.Text
import androidx.compose.material3.TooltipBox
import androidx.compose.material3.TooltipDefaults
import androidx.compose.material3.rememberTooltipState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.BLEStatus
import com.dmdbrands.library.bluetooth.model.BottomNavItem
import com.dmdbrands.library.bluetooth.model.DeviceBottomNavItem
import com.dmdbrands.library.bluetooth.ui.theme.BackgroundSecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.dmdbrands.library.bluetooth.util.DeviceUtil.deviceDetailBottomNavItems
import com.dmdbrands.library.bluetooth.util.DeviceUtil.getDetailFeatures
import com.example.utilities.services.component.tab.BoxedTab
import com.example.utilities.services.theme.PrimaryLightShadeColor
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun DeviceDetailBottomNavigation(
    pagerState: PagerState,
    device: BLEDevice? = null,
    liveEntry: String? = null,
    deviceDetailContent: @Composable (DeviceBottomNavItem) -> Unit
) {
    val items: List<DeviceBottomNavItem> =
        (device?.getDetailFeatures() ?: deviceDetailBottomNavItems)

    var selectedItem by remember { mutableStateOf(items[0]) }
    val scope = rememberCoroutineScope()

    LaunchedEffect(device?.connectionStatus == BLEStatus.CONNECTED) {
        if (device?.connectionStatus != BLEStatus.CONNECTED && selectedItem != DeviceBottomNavItem.BasicDetail && selectedItem != DeviceBottomNavItem.History) {
            selectedItem = DeviceBottomNavItem.BasicDetail
        }
    }
    BoxedTab(
        titles = items,
        pagerState = pagerState,
        currentState = selectedItem,
        tabContent = { index, item ->
            // Simplified enabled check
            val enabled =
                item == DeviceBottomNavItem.BasicDetail || item == DeviceBottomNavItem.History ||
                        (item in (device?.getDetailFeatures() ?: emptyList()) &&
                                device?.connectionStatus == BLEStatus.CONNECTED)
            BottomNavContent(
                item,
                enabled,
                selectedItem == item && enabled,
                liveEntry = liveEntry,
                device = device
            ) {
                selectedItem = it
                scope.launch {
                    pagerState.scrollToPage(it.route)
                }
            }
        }) {
        deviceDetailContent(selectedItem)
    }

}


@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun <T : BottomNavItem> BottomNavContent(
    item: T,
    enabled: Boolean,
    isSelected: Boolean,
    device: BLEDevice?,
    liveEntry: String? = null,
    onCLick: (T) -> Unit
) {
    val deviceItem = item as DeviceBottomNavItem
    val tooltipState = rememberTooltipState()
    val scope = rememberCoroutineScope()


    // Simplified colors based on state
    val bgColor = if (!enabled) BackgroundSecondaryColor.copy(0.25f) else Transparent
    val textColor = if (!enabled) TextPrimaryColor.copy(0.25f) else TextPrimaryColor


    // Simplified tooltip text
    var tooltipText by remember { mutableStateOf(getToolTipText(deviceItem, device)) }


    // Handle live measurement updates
    LaunchedEffect(liveEntry) {
        if (liveEntry != null && liveEntry.split("/")
                .all { it != "0" } && deviceItem == DeviceBottomNavItem.LiveMeasurement && !isSelected
        ) {
            tooltipText = liveEntry.toString()
            tooltipState.show()
        } else {
            tooltipText = getToolTipText(deviceItem, device)
        }
    }

    TooltipBox(
        positionProvider = TooltipDefaults.rememberTooltipPositionProvider(),
        state = tooltipState,
        enableUserInput = !enabled,
        focusable = false,
        tooltip = {
            RichTooltip(
                caretSize = DpSize(14.dp, 14.dp),
                colors = TooltipDefaults.richTooltipColors(containerColor = PrimaryLightShadeColor),
                tonalElevation = 10.dp,
            ) {
                Text(
                    text = tooltipText,
                    fontFamily = OpenSansFont,
                    fontSize = 12.sp,
                    color = TextPrimaryColor
                )
            }
        }
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(bgColor)
                .clickable {
                    if (enabled) onCLick(item)
                    else scope.launch { tooltipState.show() }
                },
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = item.label,
                color = if (isSelected) White else textColor,
                fontSize = 14.sp,
                textAlign = TextAlign.Center,
            )
        }
    }
}

private fun getToolTipText(deviceItem: DeviceBottomNavItem, device: BLEDevice? = null): String {
    return when (deviceItem) {
        !in (device?.getDetailFeatures() ?: emptyList()) -> "Feature disabled"
        else -> {
            if (device?.connectionStatus != BLEStatus.CONNECTED) "Connect the device" else deviceItem.label
        }
    }
}

