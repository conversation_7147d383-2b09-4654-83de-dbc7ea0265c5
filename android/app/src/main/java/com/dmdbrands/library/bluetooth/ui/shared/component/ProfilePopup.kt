package com.dmdbrands.library.bluetooth.ui.shared.component

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PersonAddAlt1
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.dmdbrands.library.ggbluetooth.model.GGBTUser
import com.example.utilities.modal.PickerItem
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.component.picker.PickerPopup

@Composable
fun ProfilePopup(
    users: List<GGBTUser>,
    label: String = "Select User",
    buttonText: String = "Select",
    addNewProfile: Boolean = true,
    createAccount: () -> Unit = {},
    onSelect: (String?) -> Unit
) {
    val noItemPlaceHolder = "Scale has no profiles. Create new one."
    PickerPopup(
        label = label,
        buttonText = buttonText,
        visibleItems = 5,
        items = listOf(users.map {
            PickerItem(label = it.name + " (Connected)", value = it.token)
            PickerItem(label = it.name, value = it.token)
        }),
        noItemPlaceHolder = noItemPlaceHolder,
        key = PickerItem<String>::label,
        content = {
            if (!addNewProfile) null else {
                Spacer(modifier = Modifier.height(16.dp))
                HorizontalDivider()
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    AppButton(onClick = createAccount, containerColor = Transparent) {
                        Text(
                            text = "Add profile",
                            fontFamily = OpenSansFont,
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp,
                            color = TextPrimaryColor,
                        )
                        Spacer(modifier = Modifier.width(6.dp))
                        Icon(
                            imageVector = Icons.Default.PersonAddAlt1,
                            contentDescription = null,
                            tint = PrimaryColor,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.height(10.dp))
        }
    ) { selectedUser ->
        val user = users.find { selectedUser[0].value == it.token }
        onSelect(user?.name)
    }
}

