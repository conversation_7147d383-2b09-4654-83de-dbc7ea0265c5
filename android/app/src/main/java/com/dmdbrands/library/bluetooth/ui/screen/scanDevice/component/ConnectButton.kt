package com.dmdbrands.library.bluetooth.ui.screen.scanDevice.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.model.BLEStatus
import com.dmdbrands.library.bluetooth.ui.shared.DeviceIntent
import com.dmdbrands.library.bluetooth.ui.shared.ScanDeviceIntent
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.shared.component.device.UserNumberPopup
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.ggbluetooth.model.GGDeviceDetail
import com.example.utilities.modal.Modal
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.theme.TextSecondaryColor
import com.example.utilities.services.viewmodel.UtilitiesViewmodel

@Composable
fun ConnectButton(
    device: BLEDevice,
    utility: UtilitiesViewmodel,
    sendIntent: (DeviceIntent) -> Unit,
) {
    val requiredCondition =
        device.containsUsers() && device.userNumber == 0
    val buttonText = when (device.connectionStatus) {
        BLEStatus.CONNECTED -> AppLang.DISCONNECT
        BLEStatus.DISCONNECTED -> AppLang.CONNECT
        else -> device.connectionStatus.name.lowercase().replaceFirstChar { it.uppercaseChar() }
    }
    val buttonTextColor = when (device.connectionStatus) {
        BLEStatus.CONNECTED, BLEStatus.DISCONNECTED -> PrimaryColor
        else -> TextSecondaryColor
    }
    if (device.alreadyPaired) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            if (device.token == null && device.sku == "0412") {
                AppButton(
                    onClick = {
                        sendIntent(DeviceIntent.addProfile(device))
                    },
                    enabled = device.connectionStatus == BLEStatus.CONNECTED,
                    containerColor = Transparent,
                    disabledContainerColor = Transparent,
                ) {
                    Text(
                        textAlign = TextAlign.Center,
                        text = "Add Profile",
                        fontFamily = OpenSansFont,
                        fontSize = 12.sp,
                        color = if (device.connectionStatus == BLEStatus.CONNECTED) PrimaryColor else TextSecondaryColor,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
            Icon(
                imageVector = Icons.Filled.ChevronRight,
                contentDescription = null,
                modifier = Modifier.width(26.dp)
            )
        }
    } else {
        AppButton(
            onClick = {
                if (requiredCondition) {
                    utility.showUserNumberPopup(device) {
                        sendIntent(ScanDeviceIntent.onConnectAction(device.copy(userNumber = it)))
                    }
                } else {
                    sendIntent(ScanDeviceIntent.onConnectAction(device))
                }
            },
            containerColor = Transparent,
            disabledContainerColor = Transparent,
            enabled = (device.connectionStatus != BLEStatus.CONNECTING && device.connectionStatus != BLEStatus.DISCONNECTING),
        ) {
            Text(
                textAlign = TextAlign.Center,
                text = buttonText,
                fontFamily = OpenSansFont,
                fontSize = 12.sp,
                color = buttonTextColor,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

private fun UtilitiesViewmodel.showUserNumberPopup(device: BLEDevice, onClick: (Int) -> Unit) {
    this.setModal(
        Modal(
            content = {
                UserNumberPopup(
                    onDismissRequest = { this.setModal(null) },
                    device = device
                ) {
                    onClick(it)
                }
            },
            onDismiss = { this.setModal(null) }
        )
    )
}


@PreviewScreens
@Composable
private fun ConnectButtonPreview() {
    GGBluetoothLibraryTheme {
        ConnectButton(
            device = BLEDevice(
                GGDeviceDetail(
                    deviceName = "Food scale",
                    macAddress = "12:12:45:15:15:15",
                    identifier = "12121"
                ),
            ),
            utility = UtilitiesViewmodel(),
            sendIntent = {}
        )
    }
}