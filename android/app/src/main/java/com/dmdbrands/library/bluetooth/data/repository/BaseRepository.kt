package com.dmdbrands.library.bluetooth.data.repository

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import com.dmdbrands.library.bluetooth.config.assests.sampleUserDetail
import com.dmdbrands.library.bluetooth.data.datastore.interfaces.IUserDataStore
import com.dmdbrands.library.bluetooth.model.UserDetails
import com.dmdbrands.library.bluetooth.model.interfaces.IBaseRepository
import com.dmdbrands.library.ggbluetooth.model.GGPermissionStatusMap
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class BaseRepository @Inject constructor(private val userDataStore: IUserDataStore) :
    IBaseRepository {
    private val _userDetails: MutableLiveData<UserDetails> = MutableLiveData(sampleUserDetail)
    private val _appType: MutableLiveData<String> = MutableLiveData()
    private val _permissions: MutableLiveData<GGPermissionStatusMap> = MutableLiveData()

    override suspend fun subscribeAppType(): Flow<String> {
        _appType.value = userDataStore.getAppType()
        return _appType.asFlow()
    }

    override suspend fun subscribeUserDetails(): Flow<UserDetails> {
        _userDetails.value = userDataStore.getUser()
        return _userDetails.asFlow()
    }

    override fun getUserDetails(): UserDetails {
        return _userDetails.value ?: sampleUserDetail
    }

    override fun subscribePermissions(): Flow<GGPermissionStatusMap> {
        return _permissions.asFlow()
    }

    override suspend fun setAppType(appType: String) {
        userDataStore.updateAppType(appType)
        _appType.value = appType
    }

    override suspend fun setUserDetails(userDetails: UserDetails) {
        userDataStore.updateUser(userDetails)
        _userDetails.value = userDetails
    }

    override fun setPermissions(permissions: GGPermissionStatusMap) {
        _permissions.value = permissions
    }

}