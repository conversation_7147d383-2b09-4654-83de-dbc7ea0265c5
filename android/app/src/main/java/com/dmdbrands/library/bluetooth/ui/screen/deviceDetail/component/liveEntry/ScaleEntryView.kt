package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.liveEntry

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.dmdbrands.library.bluetooth.data.sample.Unit.WeightUnitList
import com.dmdbrands.library.bluetooth.model.HistoryData
import com.dmdbrands.library.bluetooth.model.UnitData
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.DeviceDetailViewmodel
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.ViewWithIndicator
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.shared.component.UnitPicker
import com.dmdbrands.library.bluetooth.ui.theme.BackgroundSecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.DarkGreen
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.ggbluetooth.model.GGEntry
import com.dmdbrands.library.ggbluetooth.model.GGWeightEntry
import com.example.utilities.modal.Modal
import com.example.utilities.services.component.AppButton
import com.greatergoods.ggbluetoothsdk.external.enums.WeightUnit

@Composable
fun ScaleEntryView(
    liveEntry: GGEntry? = null,
    weight: String? = null,
    latestEntry: HistoryData? = null,
    unitList: List<UnitData<WeightUnit>> = WeightUnitList,
    tareRequired: Boolean = true,
    viewModel: DeviceDetailViewmodel = hiltViewModel<DeviceDetailViewmodel>()
) {
    val weight = (weight ?: latestEntry?.getDisplayValue()).toString()
    val unit = liveEntry?.unit ?: latestEntry?.unit
    ElevatedCard(colors = CardDefaults.cardColors(containerColor = BackgroundSecondaryColor)) {
        ViewWithIndicator(
            isLive = liveEntry != null, modifier = Modifier
                .padding(10.dp)
                .clip(RoundedCornerShape(16.dp))
        ) {
            Column(
                modifier = Modifier
                    .background(DarkGreen)
                    .fillMaxWidth()
            ) {
                Row(
                    modifier = Modifier
                        .height(250.dp)
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    if (liveEntry == null && latestEntry == null) {
                        Text(
                            text = "Syncing ...",
                            fontSize = 16.sp,
                            color = White,
                            fontFamily = OpenSansFont
                        )
                    } else {
                        Text(
                            text = weight,
                            fontSize = 40.sp,
                            color = White,
                            fontFamily = OpenSansFont,
                            fontWeight = FontWeight.Bold
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = unit.toString(),
                            modifier = Modifier.padding(bottom = 10.dp),
                            fontSize = 26.sp,
                            color = White,
                            fontFamily = OpenSansFont
                        )
                    }
                }
                Row(
                    modifier = Modifier
                        .background(DarkGreen)
                        .fillMaxWidth()
                ) {
                    if (tareRequired) {
                        AppButton(
                            onClick = {
                                viewModel.tare()
                            },
                            cardModifier = Modifier
                                .weight(1f),
                            shape = RectangleShape,
                            containerColor = Transparent,
                            border = BorderStroke(1.dp, White)
                        ) {
                            Text(
                                text = "Tare",
                                fontSize = 20.sp,
                                color = White,
                                fontFamily = OpenSansFont,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(10.dp),
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                    AppButton(
                        cardModifier = Modifier
                            .weight(1f),
                        shape = RectangleShape,
                        containerColor = Transparent,
                        border = BorderStroke(1.dp, White),
                        onClick = {
                            viewModel.setModal(
                                Modal(
                                    onDismiss = {
                                        viewModel.setModal(null)
                                    },
                                    content = {
                                        UnitPicker(value = unit, unitList = unitList) {
                                            viewModel.changeUnit(it)
                                            viewModel.setModal(null)
                                        }
                                    }
                                )
                            )
                        }
                    ) {
                        Text(
                            text = "Unit",
                            fontSize = 20.sp,
                            color = White,
                            fontFamily = OpenSansFont,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(10.dp),

                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }
    }
}

@PreviewScreens
@Composable
private fun SageEntryViewPreview() {
    GGBluetoothLibraryTheme {
        ScaleEntryView(
            liveEntry = GGWeightEntry(
                weight = 100.0f,
                unit = WeightUnit.WEIGHT_LB_OZ.name,
                weightInMg = 100.0f,
                date = System.currentTimeMillis(),
                broadcastId = "",
                broadcastIdString = ""
            )
        )
    }
}
