package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChangeCircle
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.PersonAddAlt1
import androidx.compose.material.icons.filled.PersonRemove
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.ui.shared.component.AppList
import com.dmdbrands.library.ggbluetooth.model.GGBTUser
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.theme.BackgroundSecondaryColor
import com.example.utilities.services.theme.OpenSansFont
import com.example.utilities.services.theme.PrimaryColor
import com.example.utilities.services.theme.TextPrimaryColor
import com.example.utilities.services.theme.TextSecondaryColor
import com.example.utilities.util.CalendarUtil

@Composable
fun UserList(
    token: String?,
    users: List<GGBTUser>,
    onSwitch: (GGBTUser) -> Unit,
    createAccount: () -> Unit,
    removeProfile: () -> Unit,
    onDelete: (String) -> Unit
) {
    val currentUser = users.find { it.token == token }
    val otherUsers = users.filter { it.token != token }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.CenterEnd) {
            AppButton(onClick = createAccount, containerColor = Transparent) {
                Text(
                    text = "Add profile",
                    fontFamily = OpenSansFont,
                    fontWeight = FontWeight.Bold,
                    fontSize = 14.sp,
                    color = TextPrimaryColor,
                )
                Spacer(modifier = Modifier.width(6.dp))
                Icon(
                    imageVector = Icons.Default.PersonAddAlt1,
                    contentDescription = null,
                    tint = PrimaryColor,
                    modifier = Modifier.size(20.dp)
                )
            }
        }


        if (currentUser != null) {
            Text(
                text = "Current user",
                fontFamily = OpenSansFont,
                fontWeight = FontWeight.Bold,
                fontSize = 16.sp,
                color = TextPrimaryColor,
            )
            Spacer(modifier = Modifier.height(4.dp))
            UserCard(currentUser, false, onSwitch = onSwitch, removeProfile = removeProfile)
        } else {
            Text(
                text = "No current user",
                fontFamily = OpenSansFont,
                fontWeight = FontWeight.Bold,
                fontSize = 16.sp,
                color = TextPrimaryColor,
            )
        }
        if (otherUsers.isNotEmpty()) {
            Spacer(modifier = Modifier.height(10.dp))
            Text(
                text = "Other users",
                fontFamily = OpenSansFont,
                fontWeight = FontWeight.Bold,
                fontSize = 16.sp,
                color = TextPrimaryColor,
            )
            Spacer(modifier = Modifier.height(4.dp))
            AppList(
                list = otherUsers.sortedByDescending { it.lastActive },
                contentPadding = PaddingValues(bottom = 100.dp)
            ) {
                UserCard(it, onSwitch = {
                    onSwitch(it)
                }) {
                    onDelete(it)
                }
            }
            Spacer(modifier = Modifier.height(100.dp))

        }
    }
}


@Composable
private fun UserCard(
    user: GGBTUser,
    requiredDelete: Boolean = true,
    onSwitch: ((GGBTUser) -> Unit)? = null,
    removeProfile: (() -> Unit)? = null,
    onDelete: ((String) -> Unit)? = null
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RectangleShape,
        colors = CardDefaults.cardColors(containerColor = BackgroundSecondaryColor)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = user.name,
                    fontSize = 14.sp,
                    fontFamily = OpenSansFont,
                    color = TextPrimaryColor
                )
                Text(
                    text = "Last Active - " + CalendarUtil.timestampToMonthYear(
                        user.lastActive * 1000,
                        "MMM-yyyy"
                    ),
                    fontFamily = OpenSansFont,
                    fontSize = 12.sp,
                    color = TextSecondaryColor
                )
                Text(
                    text = "Impedance - ${if (user.isBodyMetricsEnabled) "On" else "Off"}",
                    fontFamily = OpenSansFont,
                    fontSize = 12.sp,
                    color = PrimaryColor
                )


            }
            if (requiredDelete) {
                Row {
                    Icon(
                        imageVector = Icons.Default.ChangeCircle,
                        contentDescription = null,
                        modifier = Modifier.clickable {
                            onSwitch?.invoke(user)
                        }
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = null,
                        modifier = Modifier.clickable {
                            onDelete?.invoke(user.token)
                        }
                    )
                }
            } else {
                Icon(
                    imageVector = Icons.Default.PersonRemove,
                    contentDescription = null,
                    modifier = Modifier.clickable {
                        removeProfile?.invoke()
                    }
                )
            }
        }
    }
}