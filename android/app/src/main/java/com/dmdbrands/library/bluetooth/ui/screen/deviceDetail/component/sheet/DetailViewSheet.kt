package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.sheet

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.ModalBottomSheetDefaults
import androidx.compose.material3.SheetValue
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.ui.theme.BackgroundSecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.SurfaceBackgroundColor
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.theme.OpenSansFont

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DetailViewSheet(
    title: String,
    action: (@Composable () -> Unit)? = null,
    onDismiss: () -> Unit,
    content: @Composable () -> Unit
) {
    ModalBottomSheet(
        onDismissRequest = {},
        sheetState = rememberModalBottomSheetState(
            skipPartiallyExpanded = true,
            confirmValueChange = {
                it != SheetValue.Hidden
            }),
        properties = ModalBottomSheetDefaults.properties(
            shouldDismissOnBackPress = false
        ),
        modifier = Modifier.statusBarsPadding(),
        sheetGesturesEnabled = false,
        dragHandle = {
            Column {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(BackgroundSecondaryColor)
                        .padding(horizontal = 16.dp, vertical = 6.dp),
                    contentAlignment = Alignment.Center
                ) {
                    AppButton(
                        onClick = onDismiss,
                        containerColor = Transparent,
                        cardModifier = Modifier.align(
                            Alignment.CenterStart
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = null,
                            tint = TextPrimaryColor,
                            modifier = Modifier
                                .size(30.dp)
                        )
                    }
                    Text(
                        title,
                        fontFamily = OpenSansFont,
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.sp,
                        color = TextPrimaryColor,
                        textAlign = TextAlign.Center
                    )
                    if (action != null) {
                        Box(modifier = Modifier.align(Alignment.CenterEnd)) {
                            action.invoke()
                        }
                    }
                }
                HorizontalDivider()
            }
        },
        containerColor = SurfaceBackgroundColor,
    ) {
        content()
    }

}