package com.dmdbrands.library.bluetooth.config

import com.dmdbrands.library.ggbluetooth.enums.GGAppType

object AppConfig {
    const val EMPTY_URL = "https://empty.com/"
    const val WG_API_URL = "https://api.weightgurus.com/v3/"
    const val SB_API_URL = "https://api.smartbaby.greatergoods.com/v1/"
    const val BH_API_URL = "https://api.balance.greatergoods.com/bpm/v2/"

    val APPS_LIST = listOf(
        GGAppType.ALL,
        GGAppType.WEIGHT_GURUS,
        GGAppType.SMART_BABY,
        GGAppType.BALANCE_HEALTH,
        GGAppType.SAGE,
        GGAppType.RPM
    ).sortedBy { it.first() }

    fun getAppsWithServer(): List<String> {
        return APPS_LIST.filterNot {
            it == GGAppType.ALL || it == GGAppType.RPM || it == GGAppType.SAGE
        }
    }


}