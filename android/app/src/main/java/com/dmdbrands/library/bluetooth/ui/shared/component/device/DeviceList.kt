package com.dmdbrands.library.bluetooth.ui.shared.component.device

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.sharp.DeleteOutline
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.model.BLEDevice
import com.dmdbrands.library.bluetooth.ui.shared.DeviceIntent
import com.dmdbrands.library.bluetooth.ui.shared.PairedDeviceIntent
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.shared.component.AppList
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.ggbluetooth.model.GGDeviceDetail
import com.example.utilities.modal.ActionButton
import com.example.utilities.modal.Dialog
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.component.animation.DotsPulsing
import com.example.utilities.services.component.list.rememberDraggableListState
import com.example.utilities.services.theme.HiddenBackgroundColor
import com.example.utilities.services.viewmodel.UtilitiesViewmodel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeviceList(
    devices: List<BLEDevice>?,
    isDraggable: Boolean = false,
    sendIntent: (DeviceIntent) -> Unit = {},
    isRemote: Boolean = false,
    isRefreshing: Boolean = false
) {
    val utility: UtilitiesViewmodel = hiltViewModel()
    val draggableListState = rememberDraggableListState()
    fun dialog(onConfirm: () -> Unit): Dialog {
        return Dialog(
            title = AppLang.ALERT,
            message = AppLang.Dialog.DELETE_INFO,
            confirmButton = ActionButton(
                text = AppLang.Dialog.DELETE,
                action = {
                    onConfirm() // Invoke onConfirm
                }
            ),
            dismissButton = ActionButton(
                text = AppLang.Dialog.CANCEL,
                action = {
                    utility.setDialog(dialog = null)
                }
            )
        )
    }
    if (isRefreshing) {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            DotsPulsing(text = "Fetching Devices")
        }
    } else {
        if (!devices.isNullOrEmpty()) {
            Box(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
            ) {
                AppList(
                    contentPadding = PaddingValues(bottom = 100.dp),
                    modifier = Modifier.clipToBounds(),
                    list = devices,
                    draggableListState = draggableListState,
                    isDraggable = isDraggable,
                    actionContent = { index, item, modifier ->
                        AppButton(
                            onClick = {
                                utility.setDialog(
                                    dialog = dialog(
                                    onConfirm = {
                                        if (isRemote) {
                                            sendIntent(
                                                PairedDeviceIntent.RemoveRemoteDevice(
                                                    item
                                                )
                                            )
                                        } else {
                                            sendIntent(DeviceIntent.RemoveDevice(item))
                                        }
                                    }
                                ))
                                draggableListState.reset()
                            },
                            cardModifier = Modifier
                                .fillMaxSize()
                                .then(modifier),
                            modifier = Modifier.fillMaxSize(),
                            containerColor = HiddenBackgroundColor,
                            shape = RectangleShape
                        ) {
                            Icon(
                                Icons.Sharp.DeleteOutline,
                                null,
                                tint = Color.White,
                                modifier = Modifier
                                    .width(40.dp)

                            )
                        }
                    }
                ) {
                    DeviceCard(it, utility = utility, sendIntent = sendIntent)
                }
            }
        } else {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "No devices found", color = PrimaryColor,
                    fontFamily = OpenSansFont,
                    textAlign = TextAlign.Center,
                )
            }
        }
    }
}


@PreviewScreens
@Composable
fun DeviceListPreview() {
    GGBluetoothLibraryTheme {
        DeviceList(
            devices = listOf(
                BLEDevice(
                    GGDeviceDetail(
                        deviceName = "Food scale",
                        macAddress = "12:12:45:15:15:15",
                        identifier = "12121"
                    )
                )
            ),
        )
    }
}