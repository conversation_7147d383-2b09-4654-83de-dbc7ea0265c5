package com.dmdbrands.library.bluetooth.config

import kotlinx.serialization.Serializable

sealed class AppRoute {
    @Serializable
    data object Init : AppRoute()

    @Serializable
    data object Home : AppRoute()

    @Serializable
    data object PairedDevice : AppRoute()

    @Serializable
    data class AppDetail(val appType: String) : AppRoute()

    @Serializable
    data class DeviceDetail(val broadcastId: String) : AppRoute()

    @Serializable
    data object ScanDevices : AppRoute()

    @Serializable
    data object Apps : AppRoute()

    @Serializable
    data object Devices : AppRoute()

    @Serializable
    data object History : AppRoute()

    @Serializable
    data object Settings : AppRoute()

    @Serializable
    data object Permission : AppRoute()

    @Serializable
    data object Profile : AppRoute()

    @Serializable
    data object AppDevices : AppRoute()

    @Serializable
    data object AppHistory : AppRoute()

    @Serializable
    data object AppSettings : AppRoute()

    @Serializable
    data class DeviceInfo(val id: Long) : AppRoute()
}