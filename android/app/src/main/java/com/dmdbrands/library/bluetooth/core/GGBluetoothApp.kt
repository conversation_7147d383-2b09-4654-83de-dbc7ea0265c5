package com.dmdbrands.library.bluetooth.core

import android.annotation.SuppressLint
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.tooling.preview.PreviewScreenSizes
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.rememberNavController
import com.dmdbrands.library.bluetooth.config.LocalSnackBarState
import com.dmdbrands.library.bluetooth.core.component.AppFab
import com.dmdbrands.library.bluetooth.core.component.AppNavGraph
import com.dmdbrands.library.bluetooth.ui.shared.component.AppCompositionProvider
import com.dmdbrands.library.bluetooth.ui.shared.component.NavigationObserver
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.example.utilities.services.component.prompt.PromptManager


@OptIn(ExperimentalMaterial3Api::class)
@Composable
@SuppressLint("UnusedMaterial3ScaffoldPaddingParameter")
fun GGBluetoothApp() {
    val navController = rememberNavController()
    val viewmodel = hiltViewModel<AppViewModel>()
    val state by viewmodel.state.collectAsState()
    val snackBarHostState = remember { SnackbarHostState() }



    NavigationObserver(
        navigationIntentFlow = viewmodel.appUtility.navigationIntent,
        navController
    )

    GGBluetoothLibraryTheme(darkTheme = false, dynamicColor = false) {
        AppCompositionProvider(navController, snackBarHostState) {
            Scaffold(
                modifier = Modifier
                    .fillMaxSize(),
                floatingActionButton = {
                    AppFab(
                        paddingFlow = viewmodel.appUtility.paddings,
                        isScanning = state.isScanning,
                        enabled = state.isEnabled,
                    ) {
                        viewmodel.handleIntent(AppIntent.onScanAction(state.appType!!))
                    }
                },
            ) { innerPadding ->
                Spacer(modifier = Modifier.height(innerPadding.calculateTopPadding()))
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                ) {
                    AppNavGraph(isScanning = state.isScanning, navController = navController)
                }
                PromptManager(LocalSnackBarState.current)
            }

        }
    }
}

@PreviewLightDark
@PreviewScreenSizes
@Composable
fun GGBluetoothAppPreview() {
    GGBluetoothLibraryTheme {
        GGBluetoothApp()
    }
}


