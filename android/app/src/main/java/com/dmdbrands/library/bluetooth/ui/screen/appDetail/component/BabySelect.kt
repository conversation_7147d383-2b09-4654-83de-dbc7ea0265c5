package com.dmdbrands.library.bluetooth.ui.screen.appDetail.component

import androidx.compose.runtime.Composable
import com.dmdbrands.library.bluetooth.model.api.Baby
import com.dmdbrands.library.bluetooth.model.api.SubUser
import com.example.utilities.modal.PickerItem
import com.example.utilities.services.component.picker.PickerPopup

@Composable
fun BabySelect(
    selected: Baby? = null,
    subUser: List<SubUser>,
    onSelect: (SubUser) -> Unit
) {
    val currentItem = PickerItem(
        value = selected?.name ?: subUser.first().name,
        label = selected?.name ?: subUser.first().name
    )
    val items = subUser.map { PickerItem(value = it.name, label = it.name) }
    PickerPopup(
        value = listOf(currentItem),
        label = "Set Baby",
        buttonText = "Select Baby",
        key = PickerItem<String>::label,
        items = listOf(items),
    ) { selected ->
        val baby = subUser.find { it.name == selected[0].label }!!
        onSelect(baby)
    }
}