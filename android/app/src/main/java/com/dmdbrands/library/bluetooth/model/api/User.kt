package com.dmdbrands.library.bluetooth.model.api

import com.dmdbrands.library.bluetooth.data.db.entity.UserEntity
import com.dmdbrands.library.bluetooth.model.UserDetails
import com.example.utilities.util.CalendarUtil
import com.google.gson.annotations.SerializedName
import kotlin.math.roundToInt

sealed class User(

) {
    abstract val id: String
    abstract val email: String
    abstract val firstName: String
    abstract val lastName: String
    abstract val gender: String?
    abstract val zipcode: String
    abstract val dob: String?
    abstract fun convertToUserEntity(token: String, appType: String): UserEntity
    abstract fun convertToUserDetail(): UserDetails
}

data class BHUser(
    override val id: String,
    override val email: String,
    override val firstName: String,
    override val lastName: String,
    override val gender: String,
    override val zipcode: String,
    override val dob: String,
) : User() {
    override fun convertToUserEntity(token: String, appType: String): UserEntity {
        return UserEntity(
            email = email,
            firstName = firstName,
            id = id,
            lastName = lastName,
            token = token,
            refreshToken = "",
            appType = appType
        )
    }

    override fun convertToUserDetail(): UserDetails {
        return UserDetails(
            name = firstName,
            email = email,
            gender = gender,
            birthday = CalendarUtil.stringToCalender(dob),
            zipCode = zipcode
        )
    }
}

val sampleBHUser: User =
    BHUser(
        dob = "",
        email = "<EMAIL>",
        firstName = "Selva",
        gender = "Male",
        id = "1",
        lastName = "Kumar",
        zipcode = "600001"
    )

data class WGUser(
    @SerializedName("id") override val id: String,
    @SerializedName("email") override val email: String,
    @SerializedName("firstName") override val firstName: String,
    @SerializedName("lastName") override val lastName: String,
    @SerializedName("gender") override val gender: String,
    @SerializedName("zipcode") override val zipcode: String,
    @SerializedName("weightUnit") val weightUnit: String,
    @SerializedName("isWeightlessOn") val isWeightlessOn: Boolean,
    @SerializedName("preferredInputMethod") val preferredInputMethod: String?,
    @SerializedName("height") val height: Int,
    @SerializedName("activityLevel") val activityLevel: String,
    @SerializedName("dob") override val dob: String, // Date as String
    @SerializedName("weightlessBodyFat") val weightlessBodyFat: Any?,
    @SerializedName("weightlessMuscle") val weightlessMuscle: Any?,
    @SerializedName("weightlessTimestamp") val weightlessTimestamp: String,
    @SerializedName("weightlessWeight") val weightlessWeight: Int,
    @SerializedName("isStreakOn") val isStreakOn: Boolean,
    @SerializedName("dashboardType") val dashboardType: String,
    @SerializedName("dashboardMetrics") val dashboardMetrics: List<String>,
    @SerializedName("goalType") val goalType: String,
    @SerializedName("goalWeight") val goalWeight: Int,
    @SerializedName("initialWeight") val initialWeight: Int,
    @SerializedName("shouldSendEntryNotifications") val shouldSendEntryNotifications: Boolean,
    @SerializedName("shouldSendWeightInEntryNotifications") val shouldSendWeightInEntryNotifications: Boolean
) : User() {
    override fun convertToUserEntity(token: String, appType: String): UserEntity {
        return UserEntity(
            email = email,
            firstName = firstName,
            id = id,
            lastName = lastName,
            token = token,
            refreshToken = "",
            appType = appType
        )
    }

    override fun convertToUserDetail(): UserDetails {
        return UserDetails(
            name = firstName,
            email = email,
            isAthlete = activityLevel == "athlete",
            birthday = CalendarUtil.stringToCalender(dob),
            gender = gender,
            height = convertStoredHeightToCm(height).toString(),
            unitType = weightUnit,
            goalType = goalType,
            goalWeight = convertStoredToKg(goalWeight).toString(),
            weight = convertStoredToKg(initialWeight).toString(),
            metrics = dashboardMetrics
        )
    }

    private fun convertStoredHeightToCm(stored: Int): Int {
        return (stored * 0.254).roundToInt()
    }

    private fun convertStoredToKg(stored: Int): Double {
        return String.format("%.1f", stored / 22.046).toDouble()
    }
}

data class SBUser(

    @SerializedName("email")
    override val email: String, // Unique email address

    @SerializedName("firstName")
    override val firstName: String,

    @SerializedName("id")
    override val id: String,

    @SerializedName("lastName")
    override val lastName: String,

    @SerializedName("measurementUnits")
    val measurementUnits: String, // Enum for metric, imperialLbOz, or imperialLbDecimal

    @SerializedName("zipcode")
    override val zipcode: String,

    @SerializedName("willReceiveEmails")
    val willReceiveEmails: Boolean,

    @SerializedName("accountSettings")
    val accountSettings: List<Map<String, Any>>, // A list of JSON objects representing account preferences

    @SerializedName("hasSeenAppReview")
    val hasSeenAppReview: Boolean,

    @SerializedName("hasSeenScaleReview")
    val hasSeenScaleReview: Boolean,

    override val dob: String? = null,
    override val gender: String? = null
) : User() {
    override fun convertToUserEntity(token: String, appType: String): UserEntity {
        return UserEntity(
            email = email,
            firstName = firstName,
            id = id,
            lastName = lastName,
            token = token,
            refreshToken = "",
            appType = appType,
            subCategory = Pair(true, null)
        )
    }

    override fun convertToUserDetail(): UserDetails {
        return UserDetails(
            name = firstName,
            email = email,
            zipCode = zipcode
        )
    }
}




