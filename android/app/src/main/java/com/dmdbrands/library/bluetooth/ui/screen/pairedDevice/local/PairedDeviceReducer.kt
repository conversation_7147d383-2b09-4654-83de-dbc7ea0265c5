package com.dmdbrands.library.bluetooth.ui.screen.pairedDevice.local

import com.dmdbrands.library.bluetooth.ui.shared.DeviceIntent
import com.dmdbrands.library.bluetooth.ui.shared.PairedDeviceIntent
import com.dmdbrands.library.bluetooth.ui.shared.PairedDeviceState
import com.example.utilities.modal.interfaces.IReducer


class PairedDeviceReducer : IReducer<PairedDeviceState, DeviceIntent> {
    override fun reduce(
        state: PairedDeviceState,
        intent: DeviceIntent
    ): PairedDeviceState? {
        return when (intent) {
            is DeviceIntent.AddDevice -> {
                state.copy(localDevices = intent.devices)
            }


            is PairedDeviceIntent.AddRemoteDevice -> {
                state.copy(remoteDevices = intent.devices)
            }

            is DeviceIntent.AlterLocalRefreshState -> {
                state.copy(localRefreshing = intent.isRefreshing)
            }

            is PairedDeviceIntent.ModifyAppStatus -> {
                state.copy(appStatus = intent.appStatus)
            }

            is DeviceIntent.ChangeAppType -> {
                state.copy(appType = intent.appType)
            }

            is PairedDeviceIntent.AlterRemoteRefreshState -> {
                state.copy(remoteRefreshing = intent.isRefreshing)
            }

            else -> state

        }
    }
}