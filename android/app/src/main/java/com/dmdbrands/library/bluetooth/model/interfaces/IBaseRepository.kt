package com.dmdbrands.library.bluetooth.model.interfaces

import com.dmdbrands.library.bluetooth.model.UserDetails
import com.dmdbrands.library.ggbluetooth.model.GGPermissionStatusMap
import kotlinx.coroutines.flow.Flow

interface IBaseRepository {
    suspend fun subscribeAppType(): Flow<String>
    suspend fun subscribeUserDetails(): Flow<UserDetails>
    fun getUserDetails(): UserDetails
    fun subscribePermissions(): Flow<GGPermissionStatusMap>

    suspend fun setAppType(appType: String)
    suspend fun setUserDetails(userDetails: UserDetails)
    fun setPermissions(permissions: GGPermissionStatusMap)
}