package com.dmdbrands.library.bluetooth.ui.screen.apps

import androidx.lifecycle.viewModelScope
import com.dmdbrands.library.bluetooth.config.AppRoute
import com.dmdbrands.library.bluetooth.model.api.LoginRequest
import com.dmdbrands.library.bluetooth.model.interfaces.IAppConfiguration
import com.dmdbrands.library.bluetooth.model.interfaces.IUserRepository
import com.dmdbrands.library.bluetooth.util.CommonUtil.responseWrapper
import com.example.utilities.services.viewmodel.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AppsScreenViewmodel @Inject constructor(
    private val userRepository: IUserRepository,
    private val appConfiguration: IAppConfiguration
) : BaseViewModel<AppScreenState, AppScreenIntent>(
    initialState = AppScreenState(),
    AppScreenReducer()
) {
    init {
        subscribeApps()
    }

    private fun subscribeApps() {
        viewModelScope.launch {
            userRepository.subscribeAppStatus().collect {
                handleIntent(AppScreenIntent.LoadApps(it))
            }
        }
    }

    fun login(email: String, password: String, appType: String, cb: (Boolean) -> Unit) {
        selectApp(appType)
        viewModelScope.launch {
            cb(false)
            val response = userRepository.login(
                LoginRequest(
                    email,
                    password,
                ),
                appType
            )
            responseWrapper(response = response) {
                setModal(null)
                navigateTo(AppRoute.AppDetail(appType), baseRoute = AppRoute.Init)
            }
            cb(true)
        }
    }

    fun selectApp(appType: String) {
        appConfiguration.setAppType(appType)
    }

    override fun handleIntent(intent: AppScreenIntent) {
        super.handleIntent(intent)
    }

}