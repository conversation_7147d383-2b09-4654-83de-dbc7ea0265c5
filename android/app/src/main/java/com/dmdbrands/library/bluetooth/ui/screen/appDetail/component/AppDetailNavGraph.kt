package com.dmdbrands.library.bluetooth.ui.screen.appDetail.component

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import com.dmdbrands.library.bluetooth.config.AppRoute
import com.dmdbrands.library.bluetooth.model.api.SubUser
import com.dmdbrands.library.bluetooth.ui.screen.appDetail.AppDetailState
import com.dmdbrands.library.bluetooth.ui.screen.history.app.AppHistoryScreen
import com.dmdbrands.library.bluetooth.ui.screen.pairedDevice.app.AppDevicesScreen
import com.dmdbrands.library.bluetooth.ui.screen.settings.app.AppSettingsScreen

@Composable
fun AppDetailNavGraph(
    appType: String,
    state: AppDetailState? = null,
    navController: NavHostController,
    startDestination: Any = AppRoute.AppDevices,
    onSelected: (SubUser) -> Unit = {},
    setTitle: (String) -> Unit = {},
) {
    NavHost(navController = navController, startDestination = startDestination) {
        composable<AppRoute.AppDevices> { backStackEntry ->
            backStackEntry.toRoute<AppRoute.AppDevices>().also {
                setTitle("Devices")
                AppDevicesScreen(appType)
            }

        }
        composable<AppRoute.AppHistory> { backStackEntry ->
            backStackEntry.toRoute<AppRoute.AppHistory>().also {
                setTitle("History")
                AppHistoryScreen(appType, state, onSelected)
            }
        }
        composable<AppRoute.AppSettings> { backStackEntry ->
            backStackEntry.toRoute<AppRoute.AppSettings>().also {
                setTitle("Settings")
                AppSettingsScreen(appType, state, onSelected)
            }
        }
    }
}