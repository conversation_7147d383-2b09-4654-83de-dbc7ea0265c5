package com.dmdbrands.library.bluetooth.ui.screen.scanDevice.component

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuAnchorType
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.dmdbrands.library.bluetooth.config.AppConfig
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.theme.DisabledIconColor
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.dmdbrands.library.ggbluetooth.enums.GGAppType
import com.example.utilities.modal.DropDownItem
import com.example.utilities.modal.rememberDropDownMenu
import com.example.utilities.services.component.ExposedAppDropDown
import com.example.utilities.util.onlyString

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppTypeDropDown(
    selected: String? = null,
    items: List<String> = AppConfig.APPS_LIST,
    enabled: Boolean = true,
    onAppTypeChange: (String) -> Unit
) {

    if (selected != null) {
        val title = when (selected) {
            GGAppType.ALL -> "All Devices"
            else -> selected.onlyString()
        }
        val fontColor = if (enabled) TextPrimaryColor else DisabledIconColor
        val iconColor = if (enabled) PrimaryColor else DisabledIconColor
        val dropDownMenu by rememberDropDownMenu(
            items.map { DropDownItem(it) }
        )
        ExposedDropdownMenuBox(expanded = dropDownMenu.isOpen, onExpandedChange = {
            if (enabled) {
                dropDownMenu.toggleMenu()
            }

        }) {
            Box(contentAlignment = Alignment.Center, modifier = Modifier.wrapContentSize()) {
                Row(
                    modifier = Modifier
                        .width(240.dp)
                        .menuAnchor(ExposedDropdownMenuAnchorType.PrimaryNotEditable),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.Bottom
                ) {
                    Spacer(modifier = Modifier.size(20.dp))
                    Text(
                        text = title.uppercase(),
                        fontFamily = OpenSansFont,
                        fontWeight = FontWeight.Bold,
                        color = fontColor
                    )
                    Icon(
                        imageVector = Icons.Filled.ArrowDropDown,
                        contentDescription = "drop down",
                        tint = iconColor,
                        modifier = Modifier.rotate(
                            animateFloatAsState(
                                targetValue = if (dropDownMenu.isOpen) 180f else 0f
                            ).value
                        )
                    )
                }
                ExposedAppDropDown(dropDownMenu, selected = DropDownItem(selected)) {
                    onAppTypeChange(it.name)
                }
            }
        }
    }
}


@PreviewScreens
@Composable
private fun AppTypeDropDownPreview() {
    GGBluetoothLibraryTheme {
        AppTypeDropDown(enabled = false) {}
    }
}
