package com.dmdbrands.library.bluetooth.data.api

import com.dmdbrands.library.bluetooth.model.api.BHLoginResponse
import com.dmdbrands.library.bluetooth.model.api.Baby
import com.dmdbrands.library.bluetooth.model.api.FetchUser
import com.dmdbrands.library.bluetooth.model.api.LoginRequest
import com.dmdbrands.library.bluetooth.model.api.SBLoginResponse
import com.dmdbrands.library.bluetooth.model.api.SBUser
import com.dmdbrands.library.bluetooth.model.api.ScaleTokenResponse
import com.dmdbrands.library.bluetooth.model.api.WGLoginResponse
import com.dmdbrands.library.bluetooth.model.api.WGUser
import com.dmdbrands.library.bluetooth.model.interfaces.IAppConfiguration
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST

interface IUserAPI : IAppConfiguration {
    @POST("account/login")
    suspend fun wgLogin(@Body loginRequest: LoginRequest): Response<WGLoginResponse>

    @POST("account/login")
    suspend fun sbLogin(@Body loginRequest: LoginRequest): Response<SBLoginResponse>

    @POST("user/login")
    suspend fun bhLogin(@Body loginRequest: LoginRequest): Response<BHLoginResponse>

    @GET("user")
    suspend fun getBHDetails(): Response<FetchUser>

    @GET("account")
    suspend fun getWGDetails(): Response<WGUser>

    @GET("account/scale?r=4")
    suspend fun getScaleToken(): Response<ScaleTokenResponse>

    @GET("account")
    suspend fun getSBDetails(): Response<SBUser>

    @GET("baby")
    suspend fun getSBSubUser(): Response<List<Baby>>
}