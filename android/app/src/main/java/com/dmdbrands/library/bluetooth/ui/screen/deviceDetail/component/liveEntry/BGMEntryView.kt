package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.liveEntry

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Battery6Bar
import androidx.compose.material.icons.filled.Bloodtype
import androidx.compose.material.icons.filled.BluetoothConnected
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.model.HistoryData
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.SimpleCircle
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.ViewWithIndicator
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.TextSecondaryColor
import com.example.utilities.util.CalendarUtil

@Composable
fun BGMEntryView(latestEntry: HistoryData?, changeUnit: (String) -> Unit) {
    // State to hold the current time and date
    var currentTime by remember { mutableStateOf(CalendarUtil.getTime()) }
    var currentDate by remember { mutableStateOf(CalendarUtil.getTime()) }

    // Use LaunchedEffect to update the time and date every second
    LaunchedEffect(Unit) {
        while (true) {
            currentTime = CalendarUtil.getTime()
            currentDate = CalendarUtil.getDate()
            kotlinx.coroutines.delay(1000L)  // Update every second
        }
    }
    Card(
        shape = RoundedCornerShape(
            topStart = 100.dp,
            topEnd = 100.dp,
            bottomStart = 150.dp,
            bottomEnd = 150.dp
        ),
        colors = CardDefaults.cardColors(containerColor = White),
        border = CardDefaults.outlinedCardBorder(),
        elevation = CardDefaults.cardElevation(12.dp)
    ) {
        Column(
            modifier = Modifier
                .padding(horizontal = 30.dp)
                .padding(top = 20.dp, bottom = 40.dp)
        ) {
            ViewWithIndicator(
                isLive = false
            ) {
                Text(
                    text = "MIO",
                    fontFamily = OpenSansFont,
                    color = TextSecondaryColor,
                    fontSize = 24.sp,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 10.dp),
                    textAlign = TextAlign.Center
                )
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color.Black)
                        .padding(10.dp),
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(text = currentTime, color = White, fontFamily = OpenSansFont)
                        Text(text = currentDate, color = White, fontFamily = OpenSansFont)
                    }
                    Row(
                        modifier = Modifier
                            .fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .padding(10.dp), contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.BluetoothConnected,
                                contentDescription = null,
                                tint = White
                            )
                        }
                        Box(
                            modifier = Modifier
                                .padding(10.dp)
                                .rotate(90f), contentAlignment = Alignment.CenterEnd
                        ) {
                            Icon(
                                imageVector = Icons.Default.Battery6Bar,
                                contentDescription = null,
                                tint = White
                            )
                        }
                    }
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 4.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = latestEntry?.getDisplayValue() ?: "---",
                            fontFamily = OpenSansFont,
                            color = White,
                            fontSize = 70.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    if (latestEntry != null) {
                        BGMUnitIndicator(unit = latestEntry.unit ?: "mg/dL")
                    } else {
                        BGMStripIndicator()
                    }
                }
                Spacer(modifier = Modifier.height(10.dp))
                SimpleCircle()
            }
        }
    }
}

@Composable
private fun BGMStripIndicator() {
    val infiniteTransition = rememberInfiniteTransition(label = "Unique Live Indicator")


    // Opacity animation
    val alphaAnimation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 1000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "Opacity Animation"
    )
    Row(
        modifier = Modifier
            .fillMaxWidth(0.6f)
            .alpha(alphaAnimation)
            .height(20.dp)
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .fillMaxHeight()
                .border(1.dp, White),
            horizontalAlignment = Alignment.End
        ) {
            HorizontalDivider(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 6.dp, start = 10.dp),
                thickness = 2.dp,
                color = White
            )
            Spacer(modifier = Modifier.height(6.dp))
            HorizontalDivider(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 6.dp, start = 10.dp),
                thickness = 2.dp,
                color = White
            )
        }
        Column(
            modifier = Modifier
                .weight(3f)
                .fillMaxHeight()
                .border(1.dp, White),
            horizontalAlignment = Alignment.End
        ) {
            HorizontalDivider(
                modifier = Modifier
                    .padding(top = 6.dp)
                    .fillMaxWidth(0.3f),
                thickness = 2.dp,
                color = White
            )
            Box(
                modifier = Modifier
                    .height(6.dp)
                    .padding(end = 20.dp)
                    .width(2.dp)
                    .background(color = White)
            )
            HorizontalDivider(
                modifier = Modifier
                    .fillMaxWidth(0.3f)
                    .padding(bottom = 6.dp),
                thickness = 2.dp,
                color = White
            )
        }
    }
}

@Composable
private fun BGMUnitIndicator(unit: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .weight(1f)
                .padding(10.dp), contentAlignment = Alignment.CenterEnd
        ) {
            Icon(
                imageVector = Icons.Default.Bloodtype,
                contentDescription = null,
                tint = White
            )
        }
        Box(
            modifier = Modifier
                .padding(10.dp), contentAlignment = Alignment.CenterEnd
        ) {
            Text(text = unit, color = White, fontFamily = OpenSansFont)
        }
    }
}

@RequiresApi(Build.VERSION_CODES.O)
@PreviewScreens
@Composable
private fun PreviewBGMEntryView() {
    GGBluetoothLibraryTheme {
        BGMEntryView(latestEntry = null) {

        }
    }
}