package com.dmdbrands.library.bluetooth.ui.screen.history.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.unit.dp
import com.dmdbrands.library.bluetooth.model.HistoryData
import com.dmdbrands.library.bluetooth.ui.shared.component.DetailCard
import com.dmdbrands.library.bluetooth.util.OperationUtil
import com.example.utilities.modal.ExpandedCardState
import com.example.utilities.services.theme.BackgroundSecondaryColor
import com.example.utilities.services.theme.DividerColor

@Composable
fun HistoryDetail(state: ExpandedCardState, historyData: HistoryData) {
    AnimatedVisibility(
        state.value.contains(historyData.id) && historyData.getDisplayDetail()
            .any { OperationUtil.checkNotZero(it.value) },
        modifier = Modifier.fillMaxWidth()
    ) {
        Column {
            historyData.getDisplayDetail().filter { OperationUtil.checkNotZero(it.value) }
                .forEachIndexed { index, detailItem ->
                    val bgColor =
                        if (index % 2 == 0 && historyData.getDisplayDetail().size % 2 == 0 || index % 2 != 0 && historyData.getDisplayDetail().size % 2 != 0) BackgroundSecondaryColor else White
                    Column {
                        DetailCard(
                            detailItem = detailItem,
                            bgColor = bgColor,
                            modifier = Modifier.padding(horizontal = 16.dp),
                        )
                        HorizontalDivider(thickness = 1.dp, color = DividerColor)
                    }
                }

        }
    }
}