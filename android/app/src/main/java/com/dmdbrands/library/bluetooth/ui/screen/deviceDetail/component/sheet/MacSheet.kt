package com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.component.sheet

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.library.bluetooth.ui.screen.deviceDetail.DeviceDetailReducer
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.theme.BackgroundSecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.TextSecondaryColor
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.theme.OpenSansFont

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MacSheet(
    macAddress: String?,
    handleIntent: (DeviceDetailReducer.Intent) -> Unit,
    onDismiss: () -> Unit
) {
    val clipboardManager = LocalClipboardManager.current
    DetailViewSheet(
        title = "Wi-Fi MAC Address",
        onDismiss = onDismiss
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .background(BackgroundSecondaryColor)
                    .clip(RoundedCornerShape(16.dp))
                    .padding(10.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    text = "The Wi-Fi MAC Address of your scale is:",
                    fontFamily = OpenSansFont,
                    fontWeight = FontWeight.Bold,
                    fontSize = 20.sp,
                    color = TextPrimaryColor,
                )
                Spacer(modifier = Modifier.height(20.dp))
                Card(
                    modifier = Modifier
                        .fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = Color.LightGray)
                ) {
                    Text(
                        macAddress ?: "Fetching ..",
                        fontFamily = OpenSansFont,
                        fontWeight = FontWeight.Medium,
                        fontSize = 20.sp,
                        color = TextPrimaryColor,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        textAlign = TextAlign.Center
                    )
                }
                AppButton(containerColor = Transparent, onClick = {
                    macAddress?.let {
                        clipboardManager.setText(AnnotatedString(it))
                        handleIntent(DeviceDetailReducer.Intent.onCopytoClipBoard)
                    }
                }) {
                    Text(
                        text = "Copy MAC address",
                        fontFamily = OpenSansFont,
                        fontWeight = FontWeight.Medium,
                        fontSize = 18.sp,
                        color = PrimaryColor,
                    )
                }
                Spacer(modifier = Modifier.height(20.dp))
                Text(
                    text = "Record the address to share with your Wi-Fi network or IT department. Once the scale is whitelisted, return to scale setup to pair your scale.",
                    fontFamily = OpenSansFont,
                    fontWeight = FontWeight.Normal,
                    fontSize = 16.sp,
                    color = TextSecondaryColor,
                    textAlign = TextAlign.Justify
                )
            }
        }
    }
}

@PreviewScreens
@Composable
private fun MacSheetPreview() {
    GGBluetoothLibraryTheme {
        MacSheet(macAddress = "12:34:56:78:90", handleIntent = {}, onDismiss = {})

    }
}