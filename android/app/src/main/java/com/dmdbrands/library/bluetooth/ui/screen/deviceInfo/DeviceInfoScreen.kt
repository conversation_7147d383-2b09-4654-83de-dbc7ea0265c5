package com.dmdbrands.library.bluetooth.ui.screen.deviceInfo

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.dmdbrands.library.bluetooth.config.assests.ggDevices
import com.dmdbrands.library.bluetooth.model.DetailItem
import com.dmdbrands.library.bluetooth.ui.screen.deviceInfo.component.ErrorCodeSheet
import com.dmdbrands.library.bluetooth.ui.shared.component.AppScaffold
import com.dmdbrands.library.bluetooth.ui.shared.component.BackNavigation
import com.dmdbrands.library.bluetooth.ui.shared.component.DetailContainer
import com.dmdbrands.library.bluetooth.util.CommonUtil.toDetailItems
import com.example.utilities.services.viewmodel.UtilitiesViewmodel
import com.example.utilities.util.openInAppBrowser


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeviceInfoScreen(id: Long) {
    val context = LocalContext.current

    val device = ggDevices.find { it.id == id }!!
    val detailItems = device.toDetailItems(excludedProperties = setOf("id", "image", "errorCodes"))
    val viewModel = hiltViewModel<UtilitiesViewmodel>()
    var isSheetOpen by remember { mutableStateOf(false) }
    if (isSheetOpen) {
        ErrorCodeSheet(device.errorCodes) {
            isSheetOpen = false
        }
    }
    AppScaffold(device.name, setFabPadding = {
        viewModel.setFabPadding(0.dp)
    }, navigationIcon = {
        BackNavigation()
    }) {
        Card(
            modifier = Modifier.fillMaxHeight(0.25f),
            colors = CardDefaults.cardColors(containerColor = Transparent)
        ) {
            Image(
                painter = painterResource(id = device.image),
                contentDescription = null,
                modifier = Modifier.fillMaxSize()
            )
        }
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            DetailContainer("Device Information", list = detailItems)
            DetailContainer(
                "Error Codes",
                list = listOf(DetailItem("Error Codes", imageVector = Icons.Default.ChevronRight) {
                    isSheetOpen = true
                })
            )
            DetailContainer(
                "Support",
                list = listOf(
                    DetailItem(
                        "Product Guide",
                        imageVector = Icons.Default.ChevronRight
                    ) {
                        openInAppBrowser(
                            context,
                            "https://greatergoods.com/service/${device.sku}"
                        )
                    })
            )
        }
    }
}

