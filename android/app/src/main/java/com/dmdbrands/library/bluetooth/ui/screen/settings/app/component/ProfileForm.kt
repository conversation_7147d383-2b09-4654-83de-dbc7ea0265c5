package com.dmdbrands.library.bluetooth.ui.screen.settings.app.component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import com.dmdbrands.library.bluetooth.model.api.User
import com.dmdbrands.library.bluetooth.ui.screen.settings.reducer.EUser
import com.example.utilities.modal.DropDownItem
import com.example.utilities.services.component.input.DatePickerType
import com.example.utilities.services.component.input.Input
import com.example.utilities.services.component.input.InputType
import com.example.utilities.services.component.input.TextFieldType
import com.example.utilities.util.Form

@Composable
fun ProfileForm(
    user: User,
    userForm: Form,
) {
    Column(
        modifier = Modifier
            .verticalScroll(state = rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(36.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Input(
            type = InputType.TEXT,
            label = "Name",
            form = userForm,
            name = EUser.NAME.name,
            imeAction = ImeAction.Next,
            textFieldType = TextFieldType.SIMPLE,
        )
        Input(
            type = InputType.EMAIL,
            label = "Email",
            name = EUser.EMAIL.name,
            form = userForm,
            imeAction = ImeAction.Next,
            textFieldType = TextFieldType.SIMPLE,
        )
        if (user.dob != null) {
            Input(
                type = InputType.DATE_PICKER,
                label = "Birthday",
                form = userForm,
                datePickerType = DatePickerType.BIRTHDAY,
                name = EUser.BIRTHDAY.name,
                textFieldType = TextFieldType.SIMPLE
            )
        }
        if (user.gender != null) {
            Input(
                type = InputType.DROP_DOWN,
                label = "Gender",
                form = userForm,
                menuItems = listOf(
                    DropDownItem("male"),
                    DropDownItem("female"),
                    DropDownItem("other")
                ),
                name = EUser.GENDER.name,
            )
        }
        Input(
            type = InputType.TEXT,
            label = "Zipcode",
            name = EUser.ZIPCODE.name,
            form = userForm,
            imeAction = ImeAction.Next,
            textFieldType = TextFieldType.SIMPLE,
        )
    }
}