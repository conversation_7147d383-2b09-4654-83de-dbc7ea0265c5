package com.dmdbrands.library.bluetooth.service


import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

class Converters {

    // Convert Pair<Boolean, String> to a single string (storable format)
    @TypeConverter
    fun fromPair(pair: Pair<Boolean, String>?): String? {
        return if (pair != null) {
            // If second value is null, store as "null" string in DB
            if (pair.second == null) {
                "${pair.first},null"  // Store as "Boolean,null"
            } else {
                "${pair.first},${pair.second}"  // Convert to "Boolean,String"
            }
        } else {
            null
        }
    }

    // Convert string back to Pair<Boolean, String>
    @TypeConverter
    fun toPair(value: String?): Pair<Boolean, String?>? {
        return (if (value != null) {
            val parts = value.split(",")
            if (parts.size == 2) {
                // If the second part is "null", return Pair(true, null)
                if (parts[1] == "null") {
                    Pair(parts[0].toBoolean(), null)
                } else {
                    Pair(parts[0].toBoolean(), parts[1])
                }
            } else {
                null
            }
        } else {
            null
        })
    }

    @TypeConverter
    fun fromList(value: List<String>): String = Gson().toJson(value)

    @TypeConverter
    fun toList(value: String): List<String> {
        val type = object : TypeToken<List<String>>() {}.type
        return Gson().fromJson(value, type)
    }
}
