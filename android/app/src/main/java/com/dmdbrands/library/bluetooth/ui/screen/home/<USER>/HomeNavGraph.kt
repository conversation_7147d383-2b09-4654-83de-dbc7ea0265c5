package com.dmdbrands.library.bluetooth.ui.screen.home.component

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.dmdbrands.library.bluetooth.config.AppRoute
import com.dmdbrands.library.bluetooth.ui.screen.apps.AppsScreen
import com.dmdbrands.library.bluetooth.ui.screen.device.DevicesScreen
import com.dmdbrands.library.bluetooth.ui.screen.history.local.HistoryScreen
import com.dmdbrands.library.bluetooth.ui.screen.pairedDevice.local.PairedDeviceScreen
import com.dmdbrands.library.bluetooth.ui.screen.scanDevice.ScanDeviceScreen
import com.dmdbrands.library.bluetooth.ui.screen.settings.local.SettingsScreen

@Composable
fun HomeNavGraph(
    isScanning: Boolean,
    navController: NavHostController,
    startDestination: Any = AppRoute.ScanDevices
) {
    NavHost(navController = navController, startDestination = startDestination) {
        composable<AppRoute.ScanDevices> {
            ScanDeviceScreen(isScanning)
        }
        composable<AppRoute.PairedDevice> {
            PairedDeviceScreen()
        }
        composable<AppRoute.History> {
            HistoryScreen()
        }
        composable<AppRoute.Apps> {
            AppsScreen()
        }
        composable<AppRoute.Settings> {
            SettingsScreen()
        }
        composable<AppRoute.Devices> {
            DevicesScreen()
        }
    }
}


