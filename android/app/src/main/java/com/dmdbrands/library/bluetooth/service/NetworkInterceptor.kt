package com.dmdbrands.library.bluetooth.service

import android.content.Context
import com.example.utilities.modal.Toast
import com.example.utilities.modal.interfaces.IAppUtility
import com.example.utilities.services.NetworkConnectionState
import com.example.utilities.services.currentConnectivityState
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers.Main
import kotlinx.coroutines.launch
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.Protocol
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import javax.inject.Inject

class NetworkInterceptor @Inject constructor(
    @ApplicationContext private val context: Context,
    private val utility: IAppUtility
) : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        if (context.currentConnectivityState != NetworkConnectionState.Available) {
            CoroutineScope(Main).launch {
                utility.setToast(
                    Toast(
                        message = "No Internet Connection",
                    )
                )
            }
            // Return a pre-made error response instead of proceeding
            val mediaType = "text/plain".toMediaType()
            val responseBody = "No internet connection".toResponseBody(mediaType)

            return Response.Builder()
                .request(request)
                .protocol(Protocol.HTTP_1_1)
                .code(599) // Custom code for no connectivity
                .message("No Internet Connection")
                .body(responseBody)
                .build()
        }
        return chain.proceed(chain.request())

    }
}
