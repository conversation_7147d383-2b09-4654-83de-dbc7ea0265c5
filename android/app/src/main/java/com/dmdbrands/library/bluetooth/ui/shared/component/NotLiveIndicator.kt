package com.dmdbrands.library.bluetooth.ui.shared.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.example.utilities.services.theme.SecondaryLightShadeColor
import com.example.utilities.util.normalize

@Composable
fun NotLiveIndicator(
    deviceName: String? = null,
    modifier: Modifier = Modifier
) {
    val name = deviceName?.normalize() ?: "This device"
    var showDetails by remember { mutableStateOf(false) }

    // Main Status Row

    Box(modifier = Modifier
        .clickable(
            interactionSource = remember { MutableInteractionSource() },
            indication = null
        ) { showDetails = !showDetails }
        .then(modifier), contentAlignment = Alignment.TopEnd
    ) {
        // Expandable Details
        AnimatedVisibility(
            visible = showDetails,
            enter = fadeIn(),
            exit = fadeOut(),
        ) {
            Box(
                modifier = Modifier
                    .zIndex(1f)
                    .shadow(8.dp, shape = RoundedCornerShape(8.dp))
                    .background(
                        color = White,
                        shape = RoundedCornerShape(10.dp)
                    )
                    .padding(horizontal = 8.dp, vertical = 2.dp)
            ) {
                Text(
                    text = "$name - Live Entry Unsupported",
                    color = SecondaryLightShadeColor,
                    fontWeight = FontWeight.Medium,
                    fontSize = 12.sp
                )
            }
        }
        AnimatedVisibility(
            visible = !showDetails,
            enter = fadeIn(),
            exit = fadeOut(),
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Info Icon
                Icon(
                    imageVector = Icons.Default.Info,
                    contentDescription = "Feature Information",
                    tint = Color.Gray,
                    modifier = Modifier.size(16.dp)
                )

                // Not Live Text
                Text(
                    text = "Not Live",
                    color = Color.Gray,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }
    }
}


@PreviewScreens
@Composable
private fun FeatureScreen() {
    GGBluetoothLibraryTheme {
        NotLiveIndicator(
            deviceName = "0062",
            modifier = Modifier.padding(16.dp)
        )
    }
}