package com.dmdbrands.library.bluetooth.core.component

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.dmdbrands.library.bluetooth.config.AppLang
import com.dmdbrands.library.bluetooth.ui.shared.annotation.PreviewScreens
import com.dmdbrands.library.bluetooth.ui.theme.GGBluetoothLibraryTheme
import com.dmdbrands.library.bluetooth.ui.theme.OpenSansFont
import com.dmdbrands.library.bluetooth.ui.theme.PrimaryColor
import com.dmdbrands.library.bluetooth.ui.theme.SecondaryColor
import com.dmdbrands.library.bluetooth.ui.theme.TextPrimaryColor
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.theme.HiddenBackgroundColor

@ExperimentalMaterial3Api
@Composable
fun DeleteDeviceAlert(onDismiss: () -> Unit, onDelete: () -> Unit) {
    AlertDialog(
        onDismissRequest = onDismiss,
        shape = RoundedCornerShape(16.dp),
        modifier = Modifier.padding(16.dp),
        containerColor = White,
        titleContentColor = HiddenBackgroundColor,
        confirmButton = {
            AppButton(onClick = {
                onDelete()
                onDismiss()
            }, containerColor = Transparent) {
                Text(
                    AppLang.Dialog.DELETE.uppercase(),
                    fontFamily = OpenSansFont,
                    color = PrimaryColor
                )
            }
        },
        dismissButton = {
            AppButton(onClick = {
                onDismiss()
            }, containerColor = Transparent) {
                Text(
                    AppLang.Dialog.CANCEL.uppercase(),
                    fontFamily = OpenSansFont,
                    color = SecondaryColor
                )
            }
        },
        tonalElevation = 16.dp,
        title = {
            Text(
                text = AppLang.ALERT,
                fontWeight = FontWeight.Bold,
                fontFamily = OpenSansFont,
            )
        },
        text = {
            Text(
                text = AppLang.Dialog.DELETE_INFO,
                fontFamily = OpenSansFont,
                fontWeight = FontWeight.Medium,
                color = TextPrimaryColor

            )
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
@PreviewScreens
private fun DeleteDeviceAlertPreview() {
    GGBluetoothLibraryTheme {
        DeleteDeviceAlert(onDismiss = {}, onDelete = {})

    }
}