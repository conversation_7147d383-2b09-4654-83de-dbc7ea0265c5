package com.example.utilities.modal

import androidx.compose.runtime.Composable
import androidx.compose.ui.window.DialogProperties
import com.example.utilities.config.GG_TOAST_DELIMITER

data class NotificationState(
    val alert: Alert? = null,
    val toast: Toast? = null,
    val loader: String? = null,
    val dialog: Dialog? = null,
    val modal: Modal? = null
)

data class Modal(
    val onDismiss: () -> Unit = {},
    val properties: DialogProperties = DialogProperties(),
    val content: @Composable () -> Unit
)

data class Alert(
    val title: String, val message: String, val confirmText: String
)

data class Dialog(
    val title: String? = null,
    val message: String? = null,
    val confirmButton: ActionButton,
    val dismissButton: ActionButton? = null,
)

data class Toast(
    val message: String,
    val delimiter: String = GG_TOAST_DELIMITER,
    val title: String? = null,
    val actionLabel: String? = null,
    val confirmAction: (() -> Unit)? = null,
    val dismissAction: (() -> Unit)? = null
)
