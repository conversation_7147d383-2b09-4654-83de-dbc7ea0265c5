package com.example.utilities.services.component.picker

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.sage.ui.shared.component.spinner.Picker
import com.dmdbrands.sage.ui.shared.component.spinner.rememberPickerState
import com.example.utilities.modal.PickerItem
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.theme.OpenSansFont
import com.example.utilities.services.theme.TextSecondaryColor
import kotlin.reflect.KProperty1

enum class PickerSelectionMode {
    SCROLL, RADIO
}

@Composable
fun <T> PickerPopup(
    pickerModifier: Modifier = Modifier,
    label: String = "",
    buttonText: String = "",
    key: KProperty1<T, String>,
    indicator: List<String?>? = null,
    visibleItems: Int = 5,
    isFocusNeeded: Boolean = true,
    selectionMode: PickerSelectionMode = PickerSelectionMode.SCROLL,
    items: List<List<T>> = listOf(listOf()),
    noItemPlaceHolder: String? = "No Data Available",
    value: List<T>? = items.mapNotNull { if (it.isNotEmpty()) it.first() else null },
    content: @Composable () -> Unit = {},
    customTextView: (@Composable (T, Boolean) -> Unit)? = null,
    prefix: @Composable () -> Unit = {},
    suffix: @Composable () -> Unit = {},
    onUpdate: (List<T>) -> Unit,
) {
    val valueStates = value?.map { rememberPickerState(it) }
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        shape = RoundedCornerShape(14.dp),
        colors = CardDefaults.cardColors(containerColor = White)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(20.dp)
        ) {
            // Header
            Box(modifier = Modifier.fillMaxWidth()) {
                Box(modifier = Modifier.align(Alignment.CenterStart)) { prefix() }
                Text(
                    text = label,
                    color = TextSecondaryColor,
                    modifier = Modifier.align(Alignment.Center),
                    fontWeight = FontWeight.Bold,
                    fontSize = 20.sp
                )
                Box(modifier = Modifier.align(Alignment.CenterEnd)) { suffix() }
            }


            // Picker or Radio selection
            Box(
                modifier = Modifier.wrapContentSize(),
                contentAlignment = Alignment.Center
            ) {
                if (items.flatten().isEmpty() || valueStates == null) {
                    noItemPlaceHolder?.let {
                        Text(
                            text = it,
                            color = TextSecondaryColor,
                            modifier = Modifier.align(Alignment.Center),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            fontFamily = OpenSansFont
                        )
                    }
                } else {
                    when (selectionMode) {
                        PickerSelectionMode.SCROLL -> {
                            Spacer(modifier = Modifier.height(30.dp))
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .align(Alignment.Center)
                                    .height(36.dp)
                                    .clip(RoundedCornerShape(12.dp))
                                    .background(Color(0xFFF1EDE9))
                            )

                            Row(
                                modifier = pickerModifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                items.forEachIndexed { index, pickerItems ->
                                    Picker(
                                        state = valueStates[index],
                                        customItemView = customTextView,
                                        visibleElements = visibleItems,
                                        items = pickerItems,
                                        indicator = indicator?.get(index),
                                        modifier = Modifier.weight(1f),
                                        key = key,
                                        isFocusNeeded = isFocusNeeded
                                    )
                                }
                            }
                            Spacer(modifier = Modifier.height(20.dp))
                        }

                        PickerSelectionMode.RADIO -> {
                            RadioPicker(
                                items = items.first(),
                                state = valueStates.first(),
                                key = key,
                                customItemView = customTextView
                            )
                        }
                    }
                }
            }

            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                content()
                AppButton(
                    modifier = Modifier.fillMaxWidth(0.4f),
                    enabled = valueStates?.any { it.item != PickerItem("0", 0) } == true,
                    onClick = {
                        onUpdate(valueStates?.map { it.item } ?: listOf())
                    }
                ) {
                    Text(
                        text = buttonText.uppercase(),
                        fontFamily = OpenSansFont,
                        fontSize = 14.sp,
                        color = White,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}
