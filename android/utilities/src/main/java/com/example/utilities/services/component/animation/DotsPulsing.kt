package com.example.utilities.services.component.animation

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.utilities.services.theme.OpenSansFont
import com.example.utilities.services.theme.PrimaryColor

@Composable
fun DotsPulsing(
    color: Color = PrimaryColor,
    numberOfDots: Int = 3,
    spaceBetween: Dp = 6.dp,
    text: String? = null
) {
    val numberOfDots = numberOfDots
    val dotSize = 10.dp
    val dotColor: Color = color
    val delayUnit = 200
    val duration = numberOfDots * delayUnit
    val spaceBetween = spaceBetween

    val minAlpha = 0.1f

    @Composable
    fun Dot(alpha: Float) = Spacer(
        Modifier
            .size(dotSize)
            .alpha(alpha)
            .background(
                color = dotColor, shape = CircleShape
            )
    )

    val infiniteTransition = rememberInfiniteTransition(label = "")

    @Composable
    fun animateAlphaWithDelay(delay: Int) = infiniteTransition.animateFloat(
        initialValue = minAlpha,
        targetValue = minAlpha,
        animationSpec = infiniteRepeatable(animation = keyframes {
            durationMillis = duration
            minAlpha at delay using LinearEasing
            0f at delay using LinearEasing
            1f at delay + delayUnit using LinearEasing
            0f at delay + duration
            minAlpha at delay + duration
        }), label = ""
    )

    val alphas = arrayListOf<State<Float>>()
    for (i in 0 until numberOfDots) {
        alphas.add(animateAlphaWithDelay(delay = i * delayUnit))
    }

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        if (text != null) {
            Text(text = text, fontFamily = OpenSansFont, fontSize = 12.sp, color = PrimaryColor)
            Spacer(Modifier.width(spaceBetween))
        }
        alphas.forEach {
            Dot(it.value)
            Spacer(Modifier.width(spaceBetween))
        }
    }
}