package com.example.utilities.services.component.input

import android.util.Log
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldColors
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Color.Companion.Red
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.utilities.services.theme.PrimaryColor
import com.example.utilities.services.theme.TextSecondaryColor
import com.example.utilities.services.theme.ToastColor

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TextInput(
    label: String,
    modifier: Modifier = Modifier,
    placeHolder: String = "",
    enabled: Boolean = true,
    readOnly: Boolean = false,
    onValueChange: ((String) -> Unit)? = null,
    value: String = "",
    visualTransformation: VisualTransformation = VisualTransformation.None,
    textStyle: TextStyle = TextStyle.Default.merge(LocalTextStyle.current),
    isError: Boolean = false,
    errorMessage: String? = null,
    maxLines: Int = Int.MAX_VALUE,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions? = null,
    singleLine: Boolean = true,
    colors: TextFieldColors = TextFieldDefaults.colors(),
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    focusRequester: FocusRequester = FocusRequester(),
    textFieldType: TextFieldType = TextFieldType.FILLED,
    onBlur: (() -> Unit?)? = null,
    onNext: (() -> Unit?)? = null,
    onClick: (() -> Unit)? = null
) {
    val interactionSource = remember {
        MutableInteractionSource()
    }
    val focusManager = LocalFocusManager.current
    val focusRequester = remember { focusRequester }
    var isFocused by remember { mutableStateOf(false) }
    val keyboardController = LocalSoftwareKeyboardController.current
    val updatedModifier = modifier
        .clickable(
            interactionSource = remember { MutableInteractionSource() },
            indication = null
        ) {
            if (onClick != null) {
                onClick()
            }
        }
        .focusRequester(focusRequester)
        .onFocusChanged {
            if (!it.isFocused && isFocused) {
                if (onBlur != null) {
                    onBlur()
                }
                isFocused = false
            } else if (it.isFocused) {
                Log.e("FOC", it.isFocused.toString())
                isFocused = true
            }
        }
    val keyboardActionsMerged = KeyboardActions(
        onDone = {
            keyboardActions?.onDone?.let { it() }
            focusManager.clearFocus()
            keyboardController?.hide()
        },
        onGo = keyboardActions?.onGo,
        onNext = keyboardActions?.onNext,
        onPrevious = keyboardActions?.onPrevious,
        onSearch = keyboardActions?.onSearch,
        onSend = keyboardActions?.onSend
    )
    if (onValueChange != null) {
        when (textFieldType) {
            TextFieldType.FILLED -> {
                TextField(
                    value = value,
                    label = { Text(text = label.uppercase()) },
                    placeholder = { Text(text = placeHolder) },
                    enabled = enabled,
                    readOnly = readOnly,
                    modifier = updatedModifier,
                    textStyle = textStyle,
                    maxLines = maxLines,
                    isError = isError,
                    keyboardActions = keyboardActionsMerged,
                    keyboardOptions = keyboardOptions,
                    singleLine = singleLine,
                    colors = colors,
                    trailingIcon = trailingIcon,
                    leadingIcon = leadingIcon,
                    onValueChange = onValueChange,
                    visualTransformation = visualTransformation,
                    supportingText = if (errorMessage != null) {
                        {
                            Text(text = errorMessage, color = Red)
                        }
                    } else null)
            }

            TextFieldType.OUTLINED -> {
                OutlinedTextField(
                    value = value,
                    label = { Text(text = label.uppercase()) },
                    placeholder = { Text(text = placeHolder) },
                    enabled = enabled,
                    readOnly = readOnly,
                    modifier = updatedModifier,
                    textStyle = textStyle,
                    maxLines = maxLines,
                    isError = isError,
                    keyboardActions = keyboardActionsMerged,
                    keyboardOptions = keyboardOptions,
                    singleLine = singleLine,
                    colors = colors,
                    trailingIcon = trailingIcon,
                    leadingIcon = leadingIcon,
                    onValueChange = onValueChange,
                    visualTransformation = visualTransformation,
                    supportingText = if (errorMessage != null) {
                        {
                            Text(text = errorMessage, color = Red)
                        }
                    } else null)
            }

            else -> {

                val borderColor = if (errorMessage != null) Red else Color.LightGray
                if (!enabled && readOnly) ToastColor else Transparent
                val textColor = if (!enabled && readOnly) PrimaryColor else TextSecondaryColor
                val fontSize = if (!enabled && readOnly) 14.sp else 16.sp
                if (!enabled && readOnly) Modifier.padding(
                    vertical = 6.dp,
                    horizontal = 12.dp
                ) else Modifier
                Column {
                    Column(
                        modifier = Modifier
                            .drawBehind {
                                drawLine(
                                    color = borderColor,
                                    start = Offset(0f, size.height),
                                    end = Offset(size.width, size.height),
                                    strokeWidth = 1.dp.toPx()
                                )
                            }
                    ) {
                        Text(
                            text = label.uppercase(),
                            style = MaterialTheme.typography.labelSmall,
                            fontSize = 14.sp,
                            color = if (enabled) TextSecondaryColor else TextSecondaryColor.copy(
                                0.4f
                            ),
                            fontWeight = FontWeight.Medium
                        )
                        Spacer(modifier = Modifier.height(6.dp))
                        BasicTextField(
                            value = value,
                            modifier = updatedModifier,
                            onValueChange = onValueChange,
                            enabled = enabled,
                            readOnly = readOnly,
                            textStyle = textStyle.copy(
                                fontSize = fontSize,
                                color = textColor
                            ),
                            visualTransformation = visualTransformation,
                            keyboardOptions = keyboardOptions,
                            keyboardActions = keyboardActionsMerged,
                            interactionSource = interactionSource,
                            singleLine = singleLine,
                            maxLines = maxLines,
                            cursorBrush = SolidColor(PrimaryColor),
                            decorationBox = { innerTextField ->
                                Box(
                                    modifier = Modifier.fillMaxWidth(),
                                ) {
                                    Box(
                                        modifier = modifier
                                    ) {
                                        if (value.isEmpty() || value == "") {
                                            Text(
                                                text = placeHolder,
                                                style = TextStyle(
                                                    color = Color.Gray,
                                                    fontSize = 16.sp
                                                )
                                            )
                                        }
                                        innerTextField()
                                    }

                                    Box(modifier = Modifier.align(Alignment.TopEnd)) {
                                        if (trailingIcon != null) {
                                            trailingIcon()
                                        }
                                    }
                                }
                            }
                        )
                        Spacer(modifier = Modifier.height(6.dp))
                    }
                    if (errorMessage != null) {
                        Text(
                            text = errorMessage,
                            color = Red,
                            style = MaterialTheme.typography.bodySmall,
                            fontSize = 12.sp,
                            modifier = Modifier.padding(start = 16.dp, top = 6.dp)
                        )
                    }
                }
            }
        }
    }
}