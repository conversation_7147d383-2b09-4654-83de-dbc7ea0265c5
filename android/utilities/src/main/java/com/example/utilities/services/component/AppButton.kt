package com.example.utilities.services.component

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.dp
import com.example.utilities.services.theme.DisabledIconColor
import com.example.utilities.services.theme.PrimaryColor

@Composable
fun AppButton(
    modifier: Modifier = Modifier,
    cardModifier: Modifier = Modifier,
    enabled: Boolean = true,
    shape: Shape = CardDefaults.shape,
    border: BorderStroke? = null,
    containerColor: Color = PrimaryColor,
    disabledContainerColor: Color = DisabledIconColor,
    onClick: () -> Unit = {},
    content: @Composable RowScope.(Boolean) -> Unit
) {
    Card(
        onClick = onClick,
        shape = shape,
        colors = CardDefaults.cardColors(
            containerColor = containerColor,
            disabledContainerColor = disabledContainerColor
        ),
        enabled = enabled,
        border = border,
        modifier = cardModifier
    ) {
        Row(
            modifier = modifier
                .padding(horizontal = 10.dp, vertical = 5.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            content(enabled)
        }
    }
}