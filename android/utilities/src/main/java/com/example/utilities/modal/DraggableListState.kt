package com.example.utilities.modal

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue


class DraggableListState(index: Int) {
    var openCardIndex by mutableIntStateOf(index)
    fun reset() {
        openCardIndex = -1
    }

    fun set(index: Int) {
        openCardIndex = index
    }
}