package com.dmdbrands.sage.ui.shared.component.spinner

import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.snapping.rememberSnapFlingBehavior
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.utilities.modal.PickerState
import com.example.utilities.services.theme.OpenSansFont
import com.example.utilities.util.PickerColors
import com.example.utilities.util.PickerDefaults
import com.example.utilities.util.PickerTextStyle
import kotlinx.coroutines.launch
import kotlin.reflect.KProperty1


@Composable
fun <T> rememberPickerState(initialValue: T) = remember { PickerState(initialValue) }


@Composable
fun <T> Picker(
    modifier: Modifier = Modifier,
    items: List<T>,
    state: PickerState<T>,
    visibleElements: Int = 5,
    indicator: String? = null,
    colors: PickerColors = PickerDefaults.pickerColors(),
    styles: PickerTextStyle = PickerDefaults.pickerTextStyle(),
    key: KProperty1<T, String>,
    isFocusNeeded: Boolean = false,
    customItemView: @Composable ((T, Boolean) -> Unit)? = null,
    onItemChange: (T) -> Unit = {}
) {
    val visibleItemsMiddle = visibleElements / 2
    val listScrollCount = items.size
    val scope = rememberCoroutineScope()

    val listState =
        rememberLazyListState(initialFirstVisibleItemIndex = items.indexOf(state.item))
    val flingBehavior = rememberSnapFlingBehavior(lazyListState = listState)

    val itemHeightDp = 40.dp

    LaunchedEffect(items) {
        snapshotFlow { listState.firstVisibleItemScrollOffset }
            .collect { hidden ->
                if (hidden > 40) {
                    state.setItem(
                        items[listState.firstVisibleItemIndex + 1]
                    )
                } else {
                    state.setItem(
                        items[listState.firstVisibleItemIndex]
                    )
                }
            }
    }
    Row(
        modifier = Modifier
            .height(itemHeightDp * (visibleElements - 1) + 40.dp)
            .then(modifier),
        horizontalArrangement = Arrangement.spacedBy(2.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        LazyColumn(
            state = listState,
            flingBehavior = flingBehavior,
            horizontalAlignment = Alignment.End,
            verticalArrangement = Arrangement.SpaceEvenly,
            contentPadding = PaddingValues(vertical = itemHeightDp * (visibleItemsMiddle)),
            modifier = Modifier
                .fillMaxHeight()
                .weight(1f)
        ) {
            items(listScrollCount) { index ->
                val currentItem = items[index]
                val isSelected = state.item == currentItem && isFocusNeeded
                val modifier = Modifier
                    .weight(1f)
                    .height(42.dp)
                    .wrapContentHeight(align = Alignment.CenterVertically)
                    .clickable {
                        scope.launch {
                            listState.animateScrollToItem(index)
                        }
                    }
                    .fillMaxWidth()
                    .padding(vertical = 7.dp)

                if (customItemView != null) {
                    Box(modifier = modifier) {
                        customItemView(currentItem, isSelected)
                    }
                } else {
                    val label = key.get(currentItem)
                    val color = if (isSelected) colors.focusedColor else colors.unFocusedColor
                    val style = if (isSelected) styles.focusedStyle else styles.unFocusedStyle

                    Text(
                        text = label,
                        modifier = modifier,
                        color = color,
                        textAlign = if (indicator.isNullOrBlank()) TextAlign.Center else TextAlign.End,
                        style = style,
                        fontFamily = OpenSansFont
                    )
                }
            }
        }
        if (!indicator.isNullOrBlank()) {
            Text(
                text = indicator,
                style = styles.indicatorStyle,
                color = colors.indicatorColor,
                modifier = Modifier.weight(1f),
                fontFamily = OpenSansFont
            )
        }
    }
}

