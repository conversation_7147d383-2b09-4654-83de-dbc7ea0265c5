package com.example.utilities.services.component.picker

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.unit.dp
import com.dmdbrands.sage.ui.shared.component.spinner.Picker
import com.example.utilities.config.UtilityLang
import com.example.utilities.modal.PickerState
import com.example.utilities.services.theme.PrimaryColor
import com.example.utilities.services.theme.SecondaryColor
import kotlinx.coroutines.launch
import kotlin.reflect.KProperty1

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <T> PickerSheet(
    onDismissRequest: () -> Unit,
    pickerModifier: Modifier = Modifier,
    valueStates: List<PickerState<T>>,
    key: KProperty1<T, String>,
    indicator: List<String?>? = null,
    visibleItems: Int = 5,
    isFocusNeeded: Boolean = true,
    items: List<List<T>> = listOf(listOf()),
    spiltAt: Int = 0,
    onItemChange: (T) -> Unit = {},
    onUpdate: (List<T>) -> Unit,
) {

    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scope = rememberCoroutineScope()

    fun closeBottomSheet() {
        scope.launch { sheetState.hide() }.invokeOnCompletion {
            if (!sheetState.isVisible) {
                onDismissRequest()
            }
        }
    }
    ModalBottomSheet(
        onDismissRequest = {
            onDismissRequest()
        },
        containerColor = White,
        dragHandle = null,
        shape = RectangleShape,
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(),
            color = Color.Transparent
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Spacer(modifier = Modifier.height(12.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                    horizontalArrangement = Arrangement.End

                ) {
                    TextButton(
                        onClick = { closeBottomSheet() },
                        modifier = Modifier.padding(horizontal = 3.dp),
                        colors = ButtonColors(
                            containerColor = Color.Transparent,
                            disabledContainerColor = Color.Transparent,
                            contentColor = SecondaryColor,
                            disabledContentColor = SecondaryColor
                        )
                    ) {
                        Text(UtilityLang.cancel.uppercase(), color = PrimaryColor)
                    }
                    TextButton(
                        onClick = {
                            closeBottomSheet()
                            onUpdate(valueStates.map { it.item })
                        },
                        modifier = Modifier.padding(horizontal = 3.dp),
                        colors = ButtonColors(
                            containerColor = Color.Transparent,
                            disabledContainerColor = Color.Transparent,
                            contentColor = PrimaryColor,
                            disabledContentColor = PrimaryColor
                        )
                    ) {
                        Text(UtilityLang.select.uppercase(), color = PrimaryColor)
                    }
                }
                Row(
                    modifier = pickerModifier
                        .padding(start = 16.dp)
                        .fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (spiltAt > 0) {
                        val subList1 = items.subList(0, spiltAt)
                        val subList2 = items.subList(spiltAt, items.size)
                        Row(
                            modifier = Modifier
                                .weight(2f),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            subList1.forEachIndexed { index, pickerItems ->
                                Picker(
                                    state = valueStates[index],
                                    visibleElements = visibleItems,
                                    items = pickerItems,
                                    indicator = indicator?.get(index),
                                    modifier = Modifier.weight(1f),
                                    key = key,
                                    onItemChange = onItemChange,
                                    isFocusNeeded = isFocusNeeded
                                )
                            }

                        }
                        Row(
                            modifier = Modifier.weight(2f),
                            horizontalArrangement = Arrangement.Center
                        ) {
                            subList2.forEachIndexed { index, pickerItems ->
                                Picker(
                                    state = valueStates[index + spiltAt],
                                    visibleElements = visibleItems,
                                    items = pickerItems,
                                    indicator = indicator?.get(index),
                                    modifier = Modifier.weight(3f),
                                    key = key,
                                    onItemChange = onItemChange,
                                    isFocusNeeded = isFocusNeeded
                                )
                            }
                        }
                    } else {
                        items.forEachIndexed { index, pickerItems ->
                            Picker(
                                state = valueStates[index],
                                visibleElements = visibleItems,
                                items = pickerItems,
                                indicator = indicator?.get(index),
                                modifier = Modifier.weight(1f),
                                key = key,
                                onItemChange = onItemChange,
                                isFocusNeeded = isFocusNeeded
                            )
                        }
                    }
                }
            }
        }
    }
}