package com.example.utilities.modal

import androidx.compose.runtime.mutableStateOf

class ExpandedCardState {
    private val _expandedState = mutableStateOf(listOf<Long>())
    fun addExpandedState(id: Long) {
        if (_expandedState.value.contains(id)) {
            _expandedState.value = _expandedState.value - id
            return
        }
        _expandedState.value = _expandedState.value + id
    }

    val value: List<Long>
        get() = _expandedState.value

}