package com.example.utilities.services.component.animation

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.*
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.Color.Companion.Black
import androidx.compose.ui.graphics.drawscope.*
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.utilities.R
import kotlinx.coroutines.delay
import kotlin.math.cos
import kotlin.math.sin

@Composable
fun RadarAnimation(
    icon: ImageVector,
    modifier: Modifier = Modifier,
    strokeColor: Color = Color.White
) {
    val iconsList = listOf(
        R.drawable.bpm_vector,
        R.drawable.monitor_vector,
        R.drawable.phone_vector,
        R.drawable.fitness_vector,
        R.drawable.heater_vector,
    )
    // Sweeping animation angle
    val sweepAnimation = rememberInfiniteTransition()
    val sweepAngle by sweepAnimation.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 6000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        )
    )

    var blipAngle by remember { mutableStateOf(0f) }
    var blipIcon by remember { mutableStateOf(iconsList[0]) }
    var alphaTrigger by remember { mutableStateOf(true) }
    val alpha by animateFloatAsState(
        targetValue = if (alphaTrigger) 1f else 0f,
        animationSpec = tween(durationMillis = 1000)
    )

    LaunchedEffect(Unit) {
        while (true) {
            alphaTrigger = true
            blipAngle = sweepAngle
            blipIcon = iconsList.random()
            delay(1000)
            alphaTrigger = false
            delay(1600)
        }
    }
    val painter = painterResource(blipIcon)

    Box(modifier = Modifier.size(56.dp)) {
        Canvas(modifier = Modifier.fillMaxSize()) {
            val canvasWidth = size.width
            val canvasHeight = size.height
            val center = Offset(canvasWidth / 2, canvasHeight / 2)
            val radius = size.minDimension / 1.5f

            val sweepColors = listOf(
                strokeColor.copy(0.005f),
                strokeColor,
                strokeColor.copy(0.5f),
                Color.Transparent
            )

            rotate(sweepAngle - 60f) {
                drawArc(
                    brush = Brush.sweepGradient(sweepColors),
                    startAngle = 0f,
                    sweepAngle = 60f, // Sweep covers 60 degrees
                    useCenter = true,
                    topLeft = center - Offset(radius, radius),
                    size = Size(radius * 2, radius * 2)
                )
                drawRadarLine(center, radius, 60f)

            }
            val blipMotionRadius = radius * 0.4f

            val offsetConflictRange = -0.4f..0.4f
            val blipX =
                center.x + if (cos(blipAngle.toRadians()) in offsetConflictRange) 40f else blipMotionRadius * cos(
                    blipAngle.toRadians()
                )
            val blipY =
                center.y + if (sin(blipAngle.toRadians()) in offsetConflictRange) 40f else blipMotionRadius * sin(
                    blipAngle.toRadians()
                )
            val blipOffset = Offset(blipX, blipY)

            // Ensure blip is drawn inside the circular bounding box
            this.drawContext.canvas.save()
            this.drawContext.canvas.translate(blipOffset.x, blipOffset.y)

            with(painter) {
                draw(
                    painter.intrinsicSize / 2f,
                    alpha = alpha,
                    colorFilter = ColorFilter.tint(strokeColor.copy(0.5f))
                )
            }
            this.drawContext.canvas.restore()
        }
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier
                .align(Alignment.Center)
                .size(24.dp),
            tint = Black
        )

    }
}

// Extension function to convert degrees to radians
fun Float.toRadians() = (this * Math.PI / 180).toFloat()

// **Modularized line drawing function**
fun DrawScope.drawRadarLine(center: Offset, radius: Float, angle: Float) {
    // Instead of hardcoding 1.15f, you can dynamically adjust the scaling factor as per your needs
    // Calculate start position for the line based on angle and scaling factor
    val lineStart = center + Offset(
        x = radius * cos(angle.toRadians()),
        y = radius * sin(angle.toRadians())
    )

    // Draw the line from the calculated start position to the center
    drawLine(
        color = Color.White,
        start = lineStart,
        end = center,
        strokeWidth = 2f
    )
}

@Preview(showBackground = true)
@Composable
fun PreviewRadarBlipEffect() {
    MaterialTheme {
        RadarAnimation(Icons.Default.Search, strokeColor = Color.Red)
    }
}
