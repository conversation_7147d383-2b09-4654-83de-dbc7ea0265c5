package com.example.utilities.services.component.input

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuAnchorType
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.utilities.modal.DropDownItem
import com.example.utilities.modal.rememberDropDownMenu
import com.example.utilities.services.component.ExposedAppDropDown
import com.example.utilities.services.theme.BorderColor
import com.example.utilities.services.theme.TextPrimaryColor
import com.example.utilities.services.theme.TextSecondaryColor
import com.example.utilities.util.camelCase

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DropDownInput(
    menuItems: List<DropDownItem>,
    label: String,
    value: String,
    onValueChanged: (DropDownItem) -> Unit,
) {
    val dropDownMenu by rememberDropDownMenu(
        menuItems = menuItems
    )
    Row(
        modifier = Modifier
            .drawBehind {
                drawLine(
                    color = BorderColor,
                    start = Offset(0f, size.height),
                    end = Offset(size.width, size.height),
                    strokeWidth = 1.dp.toPx()
                )
            }
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = label.uppercase(),
            style = MaterialTheme.typography.labelSmall,
            fontSize = 14.sp,
            color = TextSecondaryColor,
        )
        ExposedDropdownMenuBox(expanded = dropDownMenu.isOpen, onExpandedChange = {
            dropDownMenu.toggleMenu()
        }) {
            Row(
                modifier = Modifier
                    .width(180.dp)
                    .menuAnchor(ExposedDropdownMenuAnchorType.PrimaryNotEditable),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.End
            ) {
                Text(
                    text = value.camelCase(),
                    fontSize = 14.sp,
                    color = TextPrimaryColor,
                )
                Icon(
                    imageVector = Icons.Filled.ArrowDropDown,
                    contentDescription = "drop down",
                    modifier = Modifier.rotate(
                        animateFloatAsState(
                            targetValue = if (dropDownMenu.isOpen) 180f else 0f
                        ).value
                    )
                )

            }
            ExposedAppDropDown(
                dropDownMenu = dropDownMenu, selected = DropDownItem(value), onClick = {
                    onValueChanged(it)
                })

        }
    }

}

