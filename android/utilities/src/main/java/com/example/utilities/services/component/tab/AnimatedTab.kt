package com.example.utilities.services.component.tab

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.sage.ui.shared.component.tab.TabTitle
import com.example.utilities.services.theme.OpenSansFont
import com.example.utilities.services.theme.PrimaryColor
import kotlinx.coroutines.launch


@Composable
fun <T> AnimatedTabs(
    modifier: Modifier = Modifier,
    state: PagerState,
    titleHeight: Dp = AnimatedTabDefaults.titleHeight,
    contentPadding: PaddingValues = AnimatedTabDefaults.contentPadding,
    titles: List<T>,
    content: @Composable (Int) -> Unit,
) {
    val scope = rememberCoroutineScope()
    val totalTabWidth = 400.dp
    val tabWidth = (totalTabWidth / titles.size)
    val tabOffset: Dp by animateDpAsState(
        targetValue = tabWidth * state.currentPage,
        animationSpec = tween(easing = LinearEasing), label = "",
    )

    fun changeTab(tab: Int) {
        scope.launch {
            state.requestScrollToPage(tab)
        }
    }

    Column {
        Box(
            modifier = modifier
                .clip(RoundedCornerShape(50.dp))
                .background(AnimatedTabDefaults.tabRowBackgroundColor)
                .height(titleHeight),
        ) {
            ActiveTabIndicator(
                width = tabWidth,
                offset = tabOffset,
                color = AnimatedTabDefaults.tabActiveForegroundColor,
            )
            Row(
                horizontalArrangement = Arrangement.Center,
                modifier = Modifier.clip(RoundedCornerShape(50.dp)),
            ) {
                titles.forEachIndexed { index, tabItem ->
                    val isActive = (index == state.currentPage)
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .clip(CircleShape)
                            .height(titleHeight)
                            .width(tabWidth)
                            .clickable {
                                changeTab(index)
                            },
                    ) {
                        when (tabItem) {
                            is TabTitle.AnimatedTitle -> {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Icon(
                                        painter = painterResource(id = tabItem.icon),
                                        contentDescription = null,
                                        tint = getAnimatedActiveColor(isActive)
                                    )
                                    if (!isActive) {
                                        Spacer(modifier = Modifier.width(5.dp))
                                        Text(
                                            text = tabItem.title,
                                            fontWeight = FontWeight.W700,
                                            fontFamily = OpenSansFont,
                                            fontSize = 14.sp,
                                            color = getAnimatedActiveColor(isActive)
                                        )
                                    }
                                }
                            }

                            else -> {

                            }
                        }
                    }
                }
            }
        }
        Box(
            modifier = Modifier
                .padding(contentPadding)
                .background(Color.Transparent)
        ) {
            content(state.currentPage)
        }
    }
}

@Composable
private fun ActiveTabIndicator(
    width: Dp,
    offset: Dp,
    color: Color,
) {
    Box(
        modifier = Modifier
            .fillMaxHeight()
            .width(width = width)
            .offset(x = offset)
            .padding(2.dp)
            .clip(shape = RoundedCornerShape(50.dp))
            .background(color = color)
    )
}

@Composable
private fun getAnimatedActiveColor(isSelected: Boolean): Color {
    val color by animateColorAsState(
        targetValue = if (isSelected) {
            Color.White
        } else {
            Color(0xFF71756C)
        }, animationSpec = tween(easing = LinearEasing), label = ""
    )
    return color
}

private object AnimatedTabDefaults {
    val titleHeight get() = 50.dp
    val contentPadding get() = PaddingValues(horizontal = 0.dp)
    val tabRowBackgroundColor = PrimaryColor.copy(0.2f)
    val tabActiveForegroundColor = PrimaryColor
}
