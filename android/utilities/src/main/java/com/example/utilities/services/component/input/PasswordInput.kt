package com.example.utilities.services.component.input

import android.annotation.SuppressLint
import androidx.compose.foundation.clickable
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.TextFieldColors
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import com.example.utilities.R
import com.example.utilities.util.PasswordGlimpse
import kotlinx.coroutines.delay


@SuppressLint("CoroutineCreationDuringComposition")
@Composable
fun PasswordInput(
    label: String,
    modifier: Modifier = Modifier,
    placeHolder: String = "",
    enabled: Boolean = true,
    readOnly: Boolean = false,
    onValueChange: ((String) -> Unit)? = null,
    value: String = "",
    textStyle: TextStyle = LocalTextStyle.current,
    isError: Boolean = false,
    errorMessage: String? = null,
    maxLines: Int = Int.Companion.MAX_VALUE,
    keyboardActions: KeyboardActions = KeyboardActions(),
    keyboardOptions: KeyboardOptions? = null,
    singleLine: Boolean = true,
    colors: TextFieldColors = TextFieldDefaults.colors(),
    leadingIcon: @Composable() (() -> Unit)? = null,
    focusRequester: FocusRequester = FocusRequester(),
    onBlur: (() -> Unit)? = null,
    textFieldType: TextFieldType = TextFieldType.FILLED,
) {

    val passwordVisible = rememberSaveable { mutableStateOf(false) }
    var visual by remember {
        mutableStateOf(VisualTransformation.None)
    }
    var prevValueLength by remember {
        mutableIntStateOf(0)
    }
    LaunchedEffect(value) {
        if (prevValueLength <= value.length) {
            prevValueLength = value.length
            visual = PasswordGlimpse()
            delay(1500)
            visual = PasswordVisualTransformation()
        } else {
            prevValueLength = value.length
            visual = PasswordVisualTransformation()
        }
    }

    TextInput(
        label,
        modifier = modifier,
        visualTransformation = if (passwordVisible.value) VisualTransformation.None else PasswordVisualTransformation(),
        keyboardOptions = keyboardOptions ?: KeyboardOptions(keyboardType = KeyboardType.Password),
        trailingIcon = {
            val image = if (passwordVisible.value)
                R.drawable.eye_show
            else R.drawable.eye_donotshow

            val description = if (passwordVisible.value) "Hide password" else "Show password"

            Icon(
                painter = painterResource(id = image),
                description,
                modifier = Modifier.clickable { passwordVisible.value = !passwordVisible.value }
            )
        },
        value = value,
        placeHolder = placeHolder,
        enabled = enabled,
        readOnly = readOnly,
        textStyle = textStyle,
        isError = isError,
        maxLines = maxLines,
        keyboardActions = keyboardActions,
        singleLine = singleLine,
        colors = colors,
        leadingIcon = leadingIcon,
        onValueChange = onValueChange,
        errorMessage = errorMessage,
        onBlur = onBlur,
        focusRequester = focusRequester,
        textFieldType = textFieldType
    )

}