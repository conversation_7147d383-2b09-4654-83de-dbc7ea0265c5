package com.example.utilities.services.component.picker.date

import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import com.dmdbrands.sage.ui.shared.component.spinner.rememberPickerState
import com.example.utilities.config.months
import com.example.utilities.modal.PickerItem
import com.example.utilities.services.component.input.DatePickerType
import com.example.utilities.services.component.picker.PickerSheet
import com.example.utilities.util.CalendarUtil
import kotlinx.coroutines.launch
import java.util.Calendar
import kotlin.ranges.downTo

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DatePicker(
    value: Calendar,
    onSelect: (Calendar) -> Unit,
    type: DatePickerType = DatePickerType.BIRTHDAY,
    onDismissRequest: () -> Unit,
) {
    val scope = rememberCoroutineScope()
    val currentDate = Calendar.getInstance()
    val currentYear = currentDate.get(Calendar.YEAR)
    val currentMonth = currentDate.get(Calendar.MONTH)

    val yearRange = when (type) {
        DatePickerType.BIRTHDAY -> (currentYear downTo 1970).toList()
        DatePickerType.MANUAL_ENTRY -> (currentYear downTo 2000).toList()
    }.map { PickerItem(it.toString(), it, "year") }
    val yearState = rememberPickerState(yearRange.find { it.value == value.get(Calendar.YEAR) }!!)

    var monthRange by remember {
        mutableStateOf(

            when (yearState.item.value) {
                currentYear -> months.take(currentMonth + 1)
                else -> months
            }.mapIndexed { index, it ->
                PickerItem(it, index + 1, "month")
            })
    }

    val monthState =
        rememberPickerState(
            PickerItem(
                months[value.get(Calendar.MONTH)],
                value.get(Calendar.MONTH) + 1,
                "month"
            )
        )

    var daysInMonth by remember {
        mutableIntStateOf(
            CalendarUtil.getDaysInMonth(
                monthState.item.label,
                yearState.item.value
            )
        )
    }
    var dayRange by remember {
        mutableStateOf((1..31).map {
            PickerItem(
                it.toString(),
                it,
                "day"
            )
        })
    }
    val dayState =
        rememberPickerState(
            PickerItem(
                value.get(Calendar.DAY_OF_MONTH).toString(),
                value.get(Calendar.DAY_OF_MONTH),
                "day"
            )
        )


    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    fun closeBottomSheet() {
        scope.launch { sheetState.hide() }.invokeOnCompletion {
            if (!sheetState.isVisible) {
                onDismissRequest()
            }
        }
    }

    LaunchedEffect(monthState.item, yearState.item) {
        daysInMonth = CalendarUtil.getDaysInMonth(
            monthState.item.label,
            yearState.item.value
        )
        dayRange = (1..daysInMonth).map { PickerItem(it.toString(), it, "day") }
        monthRange =
            when (yearState.item.value) {
                currentYear -> months.take(currentMonth + 1)
                else -> months
            }.mapIndexed { index, it ->
                PickerItem(it, index + 1, "month")
            }
    }

    PickerSheet(
        onDismissRequest = { closeBottomSheet() },
        valueStates = listOf(
            monthState,
            dayState,
            yearState
        ),
        items = listOf(monthRange, dayRange, yearRange),
        key = PickerItem<Int>::label,
        spiltAt = 2,
        onItemChange = {

        }
    ) {
        val calendar = CalendarUtil.setDate(it[1].value, it[0].value - 1, it[2].value)
        onSelect(calendar)
    }
}