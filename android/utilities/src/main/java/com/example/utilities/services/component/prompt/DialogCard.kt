package com.example.utilities.services.component.prompt

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.utilities.modal.Dialog
import com.example.utilities.services.component.AppButton
import com.example.utilities.services.theme.HiddenBackgroundColor
import com.example.utilities.services.theme.OpenSansFont
import com.example.utilities.services.theme.PrimaryColor
import com.example.utilities.services.theme.SecondaryColor
import com.example.utilities.services.theme.TextPrimaryColor

@Composable
fun DialogCard(dialog: Dialog) {
    AlertDialog(
        onDismissRequest = {
            if (dialog.dismissButton != null) {
                dialog.dismissButton.action()
            }
        },
        shape = RoundedCornerShape(16.dp),
        modifier = Modifier.padding(16.dp),
        containerColor = White,
        titleContentColor = HiddenBackgroundColor,
        confirmButton = {
            AppButton(onClick = {
                dialog.confirmButton.action()
                if (dialog.dismissButton != null) {
                    dialog.dismissButton.action()
                }
            }, containerColor = Transparent) {
                Text(
                    dialog.confirmButton.text.uppercase(),
                    fontFamily = OpenSansFont,
                    color = PrimaryColor
                )
            }
        },
        dismissButton = {
            if (dialog.dismissButton != null) {
                AppButton(
                    onClick = dialog.dismissButton.action,
                    containerColor = Transparent
                ) {
                    Text(
                        dialog.dismissButton.text.uppercase(),
                        fontFamily = OpenSansFont,
                        color = SecondaryColor
                    )
                }
            } else null
        },
        tonalElevation = 16.dp,
        title = {
            if (dialog.title != null) {
                Text(
                    text = dialog.title,
                    fontWeight = FontWeight.Bold,
                    fontFamily = OpenSansFont,
                )
            }
        },
        text = {
            if (dialog.message != null) {
                Text(
                    text = dialog.message,
                    fontFamily = OpenSansFont,
                    fontWeight = FontWeight.Normal,
                    color = TextPrimaryColor,
                    fontSize = 13.sp
                )
            }

        }
    )
}