package com.example.utilities.services.component.picker.date

import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TextFieldColors
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import com.example.utilities.services.component.input.DatePickerType
import com.example.utilities.services.component.input.TextFieldType
import com.example.utilities.services.component.input.TextInput
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DatePickerInput(
    modifier: Modifier = Modifier,
    value: Calendar = Calendar.getInstance(),
    onValueChange: ((Any) -> Unit)? = null,
    label: String,
    placeHolder: String = "",
    onBlur: (() -> Unit?)? = null,
    isError: Boolean = false,
    errorMessage: String? = null,
    colors: TextFieldColors = TextFieldDefaults.colors(),
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    focusRequester: FocusRequester = FocusRequester(),
    textFieldType: TextFieldType = TextFieldType.FILLED,
    type: DatePickerType = DatePickerType.MANUAL_ENTRY
) {
    var showPicker by remember { mutableStateOf(false) }
    val dateFormatToStore = SimpleDateFormat("MMMM d yyyy", Locale.US)
    val dateText = (dateFormatToStore.format(value.timeInMillis)).split(" ")


    TextInput(
        value = dateText[0] + " " + dateText[1] + ", " + dateText[2],
        modifier = modifier,
        onValueChange = {},
        label = label,
        readOnly = true,
        enabled = false,
        onClick = { showPicker = true },
        placeHolder = placeHolder,
        onBlur = onBlur,
        isError = isError,
        errorMessage = errorMessage,
        colors = colors,
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        focusRequester = focusRequester,
        textFieldType = textFieldType
    )
    if (showPicker) {
        DatePicker(
            value = value,
            onSelect = {
                if (onValueChange != null) {
                    onValueChange(it)
                }
            },
            type = type
        ) {
            showPicker = false
        }
    }
}