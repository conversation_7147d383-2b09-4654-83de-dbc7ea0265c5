package com.example.utilities.services

import android.content.ContentValues
import android.content.Context
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import com.example.utilities.modal.Toast
import com.example.utilities.modal.interfaces.IAppUtility
import java.io.File
import javax.inject.Inject

class FileService @Inject constructor(
    private val context: Context,
    private val appUtility: IAppUtility
) {
    suspend fun saveLog(
        logText: String,
        fileName: String = defaultFileName(),
        showToast: Boolean = true
    ) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                val resolver = context.contentResolver
                val contentValues = ContentValues().apply {
                    put(MediaStore.Downloads.DISPLAY_NAME, fileName)
                    put(MediaStore.Downloads.MIME_TYPE, "text/plain")
                    put(MediaStore.Downloads.IS_PENDING, 1)
                }

                val collection =
                    MediaStore.Downloads.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)
                val uri = resolver.insert(collection, contentValues)

                uri?.let {
                    resolver.openOutputStream(it)?.use { stream ->
                        stream.write(logText.toByteArray())
                    }
                    contentValues.clear()
                    contentValues.put(MediaStore.Downloads.IS_PENDING, 0)
                    resolver.update(it, contentValues, null, null)
                }
            } else {
                val dir =
                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
                val file = File(dir, fileName)
                file.writeText(logText)
            }

            if (showToast) {
                appUtility.setToast(
                    Toast(
                        message = "Log saved to Downloads as $fileName",
                    )
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
            if (showToast) {
                appUtility.setToast(
                    Toast(
                        message = "Failed to save log: ${e.message}",
                    )
                )
            }
        }
    }

    private fun defaultFileName(): String {
        return "log-${System.currentTimeMillis()}.txt"
    }
}