package com.example.utilities.services.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBoxScope
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.layout.layout
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.example.utilities.modal.DropDownItem
import com.example.utilities.modal.DropDownMenu
import com.example.utilities.services.theme.BorderColor
import com.example.utilities.services.theme.DisabledIconColor
import com.example.utilities.services.theme.PrimaryColor
import com.example.utilities.services.theme.SelectedColor
import com.example.utilities.services.theme.TextPrimaryColor
import com.example.utilities.util.camelCase
import com.example.utilities.util.onlyString

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExposedDropdownMenuBoxScope.ExposedAppDropDown(
    dropDownMenu: DropDownMenu,
    selected: DropDownItem? = null,
    onClick: (DropDownItem) -> Unit
) {
    MaterialTheme(
        shapes = MaterialTheme.shapes.copy(extraSmall = RoundedCornerShape(16.dp))
    ) {
        ExposedDropdownMenu(
            expanded = dropDownMenu.isOpen,
            onDismissRequest = {
                dropDownMenu.toggleMenu()
            },
            modifier = Modifier
                .background(White)
                .crop(vertical = 8.dp),
        ) {
            val totalSize = dropDownMenu.getMenuEntries().size - 1
            dropDownMenu.getMenuEntries().sortedBy { it.name.first() }
                .forEachIndexed { index, item ->
                    val textColor = if (item == selected) TextPrimaryColor else PrimaryColor

                    val bgColor =
                        if (item == selected) SelectedColor else White

                    DropdownMenuItem(
                        text = {
                            Row(
                                horizontalArrangement = Arrangement.Center,
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier
                                    .padding(horizontal = 8.dp, vertical = 4.dp)
                            ) {
                                if (item.icon != null) {
                                    Icon(
                                        imageVector = item.icon,
                                        contentDescription = item.name,
                                    )
                                    Spacer(modifier = Modifier.width(10.dp))
                                }
                                Text(
                                    text = item.name.onlyString().camelCase(),
                                    color = textColor,
                                    modifier = Modifier
                                        .widthIn(min = 150.dp)
                                        .padding(10.dp)
                                        .fillMaxWidth(),
                                    textAlign = TextAlign.Center
                                )
                            }
                        }, onClick = {
                            onClick(item)
                            dropDownMenu.toggleMenu()
                        }, modifier = Modifier.background(bgColor),
                        enabled = item.enabled
                    )
                    if (totalSize != index) {
                        HorizontalDivider(color = BorderColor)
                    }
                }
        }
    }
}

@Composable
fun AppDropDownMenu(
    dropDownMenu: DropDownMenu,
    selected: DropDownItem? = null,
    onClick: (DropDownItem) -> Unit
) {
    MaterialTheme(
        shapes = MaterialTheme.shapes.copy(extraSmall = RoundedCornerShape(16.dp))
    ) {
        DropdownMenu(
            expanded = dropDownMenu.isOpen,
            onDismissRequest = {
                dropDownMenu.toggleMenu()
            },
            modifier = Modifier
                .background(White)
                .crop(vertical = 8.dp),
        ) {
            val totalSize = dropDownMenu.getMenuEntries().size - 1
            dropDownMenu.getMenuEntries().sortedBy { it.name.first() }
                .forEachIndexed { index, item ->
                    val textColor = if (!item.enabled) {
                        Color.DarkGray
                    } else {
                        if (item == selected)
                            TextPrimaryColor
                        else
                            PrimaryColor
                    }

                    val bgColor =
                        if (!item.enabled) {
                            DisabledIconColor
                        } else {
                            if (item == selected)
                                SelectedColor
                            else
                                White
                        }

                    DropdownMenuItem(
                        text = {
                            Row(
                                horizontalArrangement = Arrangement.Center,
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier
                                    .padding(horizontal = 8.dp, vertical = 4.dp)
                            ) {
                                if (item.icon != null) {
                                    Icon(
                                        imageVector = item.icon,
                                        contentDescription = item.name,
                                    )
                                    Spacer(modifier = Modifier.width(10.dp))
                                }
                                Text(
                                    text = item.name.onlyString().camelCase(),
                                    color = textColor,
                                    modifier = Modifier
                                        .widthIn(min = 150.dp)
                                        .padding(10.dp)
                                        .fillMaxWidth(),
                                    textAlign = TextAlign.Center
                                )
                            }
                        }, onClick = {
                            onClick(item)
                            dropDownMenu.toggleMenu()
                        }, modifier = Modifier.background(bgColor),
                        enabled = item.enabled
                    )
                    if (totalSize != index) {
                        HorizontalDivider(color = BorderColor)
                    }
                }
        }
    }
}


fun Modifier.crop(
    horizontal: Dp = 0.dp,
    vertical: Dp = 0.dp,
): Modifier = this.layout { measurable, constraints ->
    val placeable = measurable.measure(constraints)
    fun Dp.toPxInt(): Int = this.toPx().toInt()
    layout(
        placeable.width - (horizontal * 2).toPxInt(), placeable.height - (vertical * 2).toPxInt()
    ) {
        placeable.placeRelative(-horizontal.toPx().toInt(), -vertical.toPx().toInt())
    }
}