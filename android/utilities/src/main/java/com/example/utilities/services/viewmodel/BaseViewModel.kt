package com.example.utilities.services.viewmodel

import com.example.utilities.modal.interfaces.IReducer
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow

abstract class BaseViewModel<State : IReducer.State, Intent : IReducer.Intent>(
    initialState: State, private val reducer: IReducer<State, Intent>
) : UtilitiesViewmodel() {

    private val _state: MutableStateFlow<State> = MutableStateFlow(initialState)
    val state: StateFlow<State>
        get() = _state.asStateFlow()

    protected val _event: MutableSharedFlow<Intent> = MutableSharedFlow()
    val event: SharedFlow<Intent>
        get() = _event.asSharedFlow()

    private fun sendIntent(event: Intent) {
        val newState = reducer.reduce(_state.value!!, event)
        if (newState != null) {
            _state.value = newState
        }
    }

    open fun handleIntent(intent: Intent) {
        this.sendIntent(intent)
    }
}