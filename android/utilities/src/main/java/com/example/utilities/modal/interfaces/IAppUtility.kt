package com.example.utilities.modal.interfaces

import androidx.compose.ui.unit.Dp
import androidx.navigation.NavOptionsBuilder
import com.example.utilities.modal.Dialog
import com.example.utilities.modal.Modal
import com.example.utilities.modal.NotificationState
import com.example.utilities.modal.Toast
import kotlinx.coroutines.flow.Flow

interface IAppUtility {
    val navigationIntent: Flow<NavigationIntent>
    val paddings: Flow<Dp>
    val notificationState: Flow<NotificationState>

    suspend fun navigateTo(
        route: Any,
        builder: NavOptionsBuilder.() -> Unit = {},
        baseRoute: Any
    )

    suspend fun navigateBack(
        route: Any? = null,
        inclusive: Boolean = false,
        baseRoute: Any
    )

    suspend fun setFabPadding(padding: Dp)

    suspend fun setLoader(loader: String?)
    suspend fun setDialog(dialog: Dialog?)
    suspend fun setToast(toast: Toast)
    suspend fun setModal(content: Modal?)
    suspend fun clearToastMessage()


}

sealed interface NavigationIntent {
    val baseRoute: Any

    data class NavigateTo(
        val route: Any,
        val builder: NavOptionsBuilder.() -> Unit = {},
        override val baseRoute: Any
    ) : NavigationIntent

    data class NavigateBack(
        val route: Any? = null,
        val inclusive: Boolean = false,
        override val baseRoute: Any
    ) : NavigationIntent
}

