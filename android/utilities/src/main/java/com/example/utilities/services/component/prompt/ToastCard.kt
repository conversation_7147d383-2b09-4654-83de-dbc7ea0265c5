package com.example.utilities.services.component.prompt

import android.view.Gravity
import android.view.WindowManager
import androidx.compose.animation.core.exponentialDecay
import androidx.compose.animation.core.spring
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.gestures.AnchoredDraggableState
import androidx.compose.foundation.gestures.DraggableAnchors
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.anchoredDraggable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.ui.window.DialogWindowProvider
import com.example.utilities.modal.Toast
import com.example.utilities.services.theme.PrimaryLightShadeColor
import com.example.utilities.services.theme.TextPrimaryColor
import kotlinx.coroutines.delay
import kotlin.math.roundToInt

@Composable
fun ToastHandler(
    toast: Toast? = null,
    hostState: SnackbarHostState,
    clearToast: () -> Unit
) {
    LaunchedEffect(toast) {
        if (toast != null) {
            hostState.showSnackbar(message = toast.message)
        }
    }
    if (toast != null) {
        ToastCard(hostState, toast.delimiter, clearToast)
    }
}

enum class DragAnchors {
    Center,
    Left,
    Right
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ToastCard(
    hostState: SnackbarHostState,
    delimiter: String = "::",
    clearToast: () -> Unit,
) {
    val density = LocalDensity.current
    val configuration = LocalConfiguration.current
    val screenWidthPx = with(density) { configuration.screenWidthDp.dp.toPx() }

    val toastWidthPx = screenWidthPx * 0.8f // 80% of screen width

    val dragState = remember {
        AnchoredDraggableState(
            initialValue = DragAnchors.Center,
            positionalThreshold = { toastWidthPx * 0.25f }, // 25% of toast width
            velocityThreshold = { 100f }, // adjust this as needed
            snapAnimationSpec = spring(),
            decayAnimationSpec = exponentialDecay()
        ).apply {
            updateAnchors(
                DraggableAnchors {
                    DragAnchors.Center at 0f
                    DragAnchors.Left at toastWidthPx
                    DragAnchors.Right at -toastWidthPx
                }
            )
        }
    }

    val isDismissed = remember { mutableStateOf(false) }
    SnackbarHost(hostState = hostState) { snackbarData ->

        if (!isDismissed.value) {
            // Auto-dismiss after delay
            LaunchedEffect(snackbarData) {
                delay(3900)
                if (dragState.currentValue == DragAnchors.Center) {
                    clearToast()
                    snackbarData.dismiss()
                }
            }

            // Dismiss on swipe
            LaunchedEffect(dragState.currentValue) {
                if (dragState.currentValue != DragAnchors.Center) {
                    isDismissed.value = true
                    clearToast()
                    snackbarData.dismiss()
                }
            }
            Dialog(
                onDismissRequest = {},
                properties = DialogProperties(
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false,
                    decorFitsSystemWindows = false
                )
            ) {
                // Override Dialog window behavior to allow touch-through
                (LocalView.current.parent as DialogWindowProvider).window.apply {
                    setGravity(Gravity.TOP)
                    setDimAmount(0f)
                    addFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
                    addFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL)
                    clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
                    clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
                }


                Card(
                    modifier = Modifier
                        .offset {
                            IntOffset(-dragState.requireOffset().roundToInt(), 0)
                        }
                        .anchoredDraggable(
                            state = dragState,
                            orientation = Orientation.Horizontal,
                            reverseDirection = true,
                            enabled = true
                        )
                        .padding(horizontal = 16.dp, vertical = 10.dp)
                        .shadow(10.dp, RoundedCornerShape(10.dp)),
                    shape = RoundedCornerShape(10.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = PrimaryLightShadeColor,
                        contentColor = TextPrimaryColor
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 8.dp),
                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        val message = snackbarData.visuals.message
                        if (!message.contains(delimiter)) {
                            Text(
                                text = message,
                                fontWeight = FontWeight.Normal,
                                fontSize = 14.sp
                            )
                        } else {
                            val parts = message.split(delimiter)
                            Text(
                                text = parts[0],
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp
                            )
                            Text(
                                text = parts[1],
                                fontWeight = FontWeight.Normal,
                                fontSize = 16.sp
                            )
                        }
                    }
                }
            }
        }
    }
}

internal const val DialogTopPadding = 192
