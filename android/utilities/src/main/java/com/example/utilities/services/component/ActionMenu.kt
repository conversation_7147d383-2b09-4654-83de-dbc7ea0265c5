package com.example.utilities.services.component

import androidx.compose.foundation.layout.size
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuAnchorType
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.example.utilities.R
import com.example.utilities.modal.DropDownItem
import com.example.utilities.modal.DropDownMenu
import com.example.utilities.services.theme.TextPrimaryColor

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ActionMenu(
    dropDownMenu: DropDownMenu,
    onClick: (DropDownItem) -> Unit,
) {
    ExposedDropdownMenuBox(expanded = dropDownMenu.isOpen, onExpandedChange = {
        dropDownMenu.toggleMenu()
    }) {
        AppButton(
            containerColor = Transparent,
            modifier = Modifier
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_action),
                contentDescription = "status",
                tint = TextPrimaryColor,
                modifier = Modifier
                    .size(20.dp)
                    .menuAnchor(ExposedDropdownMenuAnchorType.PrimaryNotEditable),
            )
        }
        AppDropDownMenu(
            dropDownMenu = dropDownMenu,
            selected = null,
            onClick = onClick,
        )
    }
}