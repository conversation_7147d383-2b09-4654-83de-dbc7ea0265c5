package com.example.utilities.di

import android.content.Context
import com.example.utilities.modal.interfaces.IAppUtility
import com.example.utilities.services.FileService
import com.example.utilities.services.component.AppUtility
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class UtilityModule {
    @Singleton
    @Provides
    fun provideUtility(): IAppUtility {
        return AppUtility()
    }

    @Singleton
    @Provides
    fun provideFileService(
        @ApplicationContext context: Context,
        appUtility: IAppUtility
    ): FileService {
        return FileService(context, appUtility)
    }
}