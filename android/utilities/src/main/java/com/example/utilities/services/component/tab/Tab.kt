package com.example.utilities.services.component.tab

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerScope
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.dmdbrands.sage.ui.shared.component.tab.TabTitle
import com.dmdbrands.sage.ui.shared.component.tab.TabType

data class TabColors(
    val selectedTitleColor: Color,
    val unselectedTitleColor: Color,
    val titleBackgroundColor: Color,
    val contentBackgroundColor: Color,
)

@Composable
fun <K : TabType, V : TabTitle> Tab(
    modifier: Modifier = Modifier,
    type: K,
    userScrollEnabled : Boolean = true,
    pagerState: PagerState = rememberPagerState { titles.size },
    titleHeight: Dp = TabDefaults.titleHeight,
    contentPadding: PaddingValues = TabDefaults.contentPadding,
    colors: TabColors = TabDefaults.tabColors(),
    titles: List<V>,

    content: @Composable PagerScope.(currentTabIndex: Int) -> Unit = { },
) {
    when (type) {
        TabType.BASIC -> {
            BasicTab(
                state = pagerState,
                modifier = modifier,
                titleHeight = titleHeight,
                contentPadding = contentPadding,
                colors = colors,
                titles = titles
            ) {
                HorizontalPager(state = pagerState , userScrollEnabled = userScrollEnabled) { currentPage ->
                    Column(
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        content(currentPage)
                    }
                }
            }
        }

        TabType.BOXED -> {
            BoxedTab(
                pagerState = pagerState,
                modifier = modifier,
                titles = titles
            ) {
                HorizontalPager(state = pagerState , userScrollEnabled = userScrollEnabled) { currentPage ->
                    Column(
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        content(currentPage)
                    }
                }
            }
        }

        TabType.SIMPLE_CURVED -> {
            SimpleCurvedTab(
                modifier = modifier,
                state = pagerState,
                titleHeight = titleHeight,
                contentPadding = contentPadding,
                titles = titles
            ) {
                HorizontalPager(state = pagerState) { currentPage ->
                    Column(
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        content(currentPage)
                    }
                }
            }
        }

        TabType.ANIMATED -> {
            AnimatedTabs(
                modifier = modifier,
                state = pagerState,
                titleHeight = titleHeight,
                contentPadding = contentPadding,
                titles = titles
            ) {
                HorizontalPager(state = pagerState) { currentPage ->
                    Column(
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        content(currentPage)
                    }
                }
            }
        }
    }
}


object TabDefaults {
    val titleHeight get() = 50.dp
    val contentPadding get() = PaddingValues(0.dp)
    fun tabColors(
        titleBackgroundColor: Color = Color.White,
        contentBackgroundColor: Color = Color.White,
        selectedTitleColor: Color = Color.Black,
        unselectedTitleColor: Color = Color.Black,
    ) = TabColors(
        titleBackgroundColor = titleBackgroundColor,
        contentBackgroundColor = contentBackgroundColor,
        selectedTitleColor = selectedTitleColor,
        unselectedTitleColor = unselectedTitleColor,
    )

}

