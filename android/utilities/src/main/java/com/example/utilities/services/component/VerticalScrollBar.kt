package com.example.utilities.services.component

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Composable
fun Modifier.verticalColumnScrollbar(
    scrollState: ScrollState,
    width: Dp = 4.dp,
    showScrollBarTrack: Boolean = true,
    scrollBarTrackColor: Color = Color.Gray,
    scrollBarColor: Color = Color.Black,
    scrollBarCornerRadius: Float = 4f,
    endPadding: Float = 12f
): Modifier {
    return drawWithContent {
        // Draw the column's content
        drawContent()
        // Dimensions and calculations
        val viewportHeight = this.size.height
        val totalContentHeight = scrollState.maxValue.toFloat() + viewportHeight
        val scrollValue = scrollState.value.toFloat()
        // Compute scrollbar height and position
        val scrollBarHeight =
            (viewportHeight / totalContentHeight) * viewportHeight
        val scrollBarStartOffset =
            (scrollValue / totalContentHeight) * viewportHeight
        // Draw the track (optional)
        if (showScrollBarTrack) {
            drawRoundRect(
                cornerRadius = CornerRadius(scrollBarCornerRadius),
                color = scrollBarTrackColor,
                topLeft = Offset(this.size.width - endPadding, 0f),
                size = Size(width.toPx(), viewportHeight),
            )
        }
        // Draw the scrollbar
        drawRoundRect(
            cornerRadius = CornerRadius(scrollBarCornerRadius),
            color = scrollBarColor,
            topLeft = Offset(this.size.width - endPadding, scrollBarStartOffset),
            size = Size(width.toPx(), scrollBarHeight)
        )
    }
}

@Composable
fun Modifier.verticalLazyColumnScrollbar(
    listState: LazyListState,
    width: Dp = 4.dp,
    showScrollBarTrack: Boolean = true,
    scrollBarTrackColor: Color = Color.Gray,
    scrollBarColor: Color = Color.Black,
    scrollBarCornerRadius: Float = 4f,
    endPadding: Float = 12f,
    scrollBarHeightFactor: Float = 0.6f,
    minScrollBarHeight: Dp = 16.dp
): Modifier {
    return drawWithContent {
        drawContent()

        val layoutHeight = size.height
        val itemCount = listState.layoutInfo.totalItemsCount
        val visibleItems = listState.layoutInfo.visibleItemsInfo

        if (itemCount == 0 || visibleItems.isEmpty()) return@drawWithContent

        val firstVisibleIndex = listState.firstVisibleItemIndex
        val firstVisibleOffset = listState.firstVisibleItemScrollOffset.toFloat()

        val averageItemHeight = visibleItems.sumOf { it.size } / visibleItems.size.toFloat()
        val totalContentHeight = averageItemHeight * itemCount
        val scrollOffset = firstVisibleIndex * averageItemHeight + firstVisibleOffset

        // Full-size thumb height (unscaled)
        val fullScrollBarHeight = (layoutHeight / totalContentHeight) * layoutHeight

        // Apply height reduction factor and min height clamp
        val minHeightPx = minScrollBarHeight.toPx()
        val scrollBarHeight = maxOf(fullScrollBarHeight * scrollBarHeightFactor, minHeightPx)

        // ✨ Adjust scrollBarOffset so it still maps properly even if height is reduced
        val scrollProgress = scrollOffset / (totalContentHeight - layoutHeight)
        val maxScrollBarOffset = layoutHeight - scrollBarHeight
        val scrollBarOffset = scrollProgress.coerceIn(0f, 1f) * maxScrollBarOffset

        if (showScrollBarTrack) {
            drawRoundRect(
                color = scrollBarTrackColor,
                topLeft = Offset(size.width - endPadding, 0f),
                size = Size(width.toPx(), layoutHeight),
                cornerRadius = CornerRadius(scrollBarCornerRadius)
            )
        }

        drawRoundRect(
            color = scrollBarColor,
            topLeft = Offset(size.width - endPadding, scrollBarOffset),
            size = Size(width.toPx(), scrollBarHeight),
            cornerRadius = CornerRadius(scrollBarCornerRadius)
        )
    }
}

