package com.example.utilities.modal

import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.vector.ImageVector

@Composable
fun rememberDropDownMenu(menuItems: List<DropDownItem>): State<DropDownMenu> {
    val updatedMenuItems = rememberUpdatedState(menuItems)
    return remember {
        derivedStateOf { DropDownMenu(updatedMenuItems.value) }
    }
}


class DropDownMenu(private val menuItems: List<DropDownItem>) {
    var isOpen by mutableStateOf(false)
        private set

    fun toggleMenu() {
        isOpen = !isOpen
    }

    fun getMenuEntries(): List<DropDownItem> = menuItems
}

data class DropDownItem(
    val name: String,
    val icon: ImageVector? = null,
    val enabled: Boolean = true,
    val showAsAction: Boolean = false,
)


