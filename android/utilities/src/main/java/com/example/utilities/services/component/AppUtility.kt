package com.example.utilities.services.component

import androidx.compose.ui.unit.Dp
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.navigation.NavOptionsBuilder
import com.example.utilities.modal.Dialog
import com.example.utilities.modal.Modal
import com.example.utilities.modal.NotificationState
import com.example.utilities.modal.Toast
import com.example.utilities.modal.interfaces.IAppUtility
import com.example.utilities.modal.interfaces.NavigationIntent
import com.example.utilities.modal.interfaces.NavigationIntent.NavigateBack
import com.example.utilities.modal.interfaces.NavigationIntent.NavigateTo
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow


class AppUtility(
) : IAppUtility {
    private val _navigationIntent = MutableSharedFlow<NavigationIntent>()
    private val _paddings = MutableLiveData<Dp>()
    private val _notificationState = MutableLiveData(NotificationState())

    override val navigationIntent = _navigationIntent.asSharedFlow()
    override val paddings get() = _paddings.asFlow()
    override val notificationState get() = _notificationState.asFlow()


    override suspend fun navigateTo(
        route: Any,
        builder: NavOptionsBuilder.() -> Unit,
        baseRoute: Any
    ) {
        emitNavigationIntent(
            NavigateTo(
                route,
                builder,
                baseRoute
            )
        )
    }

    override suspend fun navigateBack(
        route: Any?,
        inclusive: Boolean,
        baseRoute: Any
    ) {
        emitNavigationIntent(
            NavigateBack(
                route,
                inclusive,
                baseRoute
            )
        )
    }

    override suspend fun setLoader(loader: String?) {
        _notificationState.value = _notificationState.value?.copy(loader = loader)
    }

    override suspend fun setFabPadding(padding: Dp) {
        _paddings.value = (padding)
    }

    override suspend fun setDialog(dialog: Dialog?) {
        _notificationState.value = _notificationState.value?.copy(dialog = dialog)

    }

    override suspend fun setToast(toast: Toast) {
        _notificationState.value = _notificationState.value?.copy(toast = toast)
    }

    override suspend fun setModal(content: Modal?) {
        _notificationState.value = _notificationState.value?.copy(modal = content)
    }

    override suspend fun clearToastMessage() {
        _notificationState.value = _notificationState.value?.copy(toast = null)
    }

    private suspend fun emitNavigationIntent(intent: NavigationIntent) {
        _navigationIntent.emit(intent)
    }
}
