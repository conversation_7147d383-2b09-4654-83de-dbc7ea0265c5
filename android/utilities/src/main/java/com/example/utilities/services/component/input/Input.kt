package com.example.utilities.services.component.input


import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import com.example.utilities.modal.DropDownItem
import com.example.utilities.services.component.picker.date.DatePickerInput
import com.example.utilities.services.component.picker.time.TimePickerInput
import com.example.utilities.util.Form
import java.util.Calendar

enum class InputType {
    TEXT, EMAIL, PASSWORD, CHECKBOX, DATE_PICKER, NUMBER, TIME_PICKER, DROP_DOWN
}

enum class DatePickerType {
    BIRTHDAY, MANUAL_ENTRY
}

enum class TextFieldType {
    OUTLINED, FILLED, BOX, SIMPLE
}


@Composable
fun Input(
    modifier: Modifier = Modifier.Companion,
    type: InputType = InputType.TEXT,
    form: Form? = null,
    name: String,
    label: String,
    placeHolder: String = "",
    onValueChange: ((Any) -> Unit)? = null,
    readOnly: Boolean = false,
    enabled: Boolean = true,
    value: Any? = null,
    textStyle: TextStyle = TextStyle.Default.merge(LocalTextStyle.current),
    errorFlag: Boolean = false,
    imeAction: ImeAction? = null,
    focusRequester: FocusRequester = FocusRequester(),
    onDone: (() -> Unit)? = null,
    onSearch: (() -> Unit)? = null,
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingIcon: (@Composable () -> Unit)? = null,
    menuItems: List<DropDownItem> = emptyList(),
    textFieldType: TextFieldType = TextFieldType.FILLED,
    datePickerType: DatePickerType = DatePickerType.MANUAL_ENTRY,
    showContent: @Composable ((content: @Composable (onDismiss: () -> Unit) -> Unit) -> Unit)? = null,
) {
    val keyboardController = LocalSoftwareKeyboardController.current

    fun onValueChanged(value: Any) {
        if (onValueChange != null) {
            onValueChange(value)
        } else if (form != null) {
            val formValue = form.getValue<Any>(name)!!
            if ((type == InputType.TEXT || type == InputType.NUMBER) && (formValue as String).length > 16) {
                form.update(name, "")
            } else {
                form.update(name, value)
            }
        }
    }


    fun onBlur() {
        form?.touched(name)
    }

    fun getKeyboardOption(keyboardType: KeyboardType): KeyboardOptions {
        return if (imeAction != null) {
            KeyboardOptions(
                keyboardType = keyboardType, imeAction = imeAction
            )
        } else {
            KeyboardOptions(
                keyboardType = keyboardType
            )
        }
    }

    fun getValue(): String = if (value != null) value as String
    else form?.getValue<String>(name) ?: ""

    fun getDate(): Calendar =
        if (form != null) {
            form.getValue<Calendar>(name)!!
        } else Calendar.getInstance()

    fun getCheckValue(): Boolean = if (form != null) form.getValue<Boolean>(name)!!
    else false

    fun isError(): Boolean = form?.getField(name)?.showError == true && errorFlag

    fun getErrorMessage(): String? = form?.getField(name)?.errorMessage

    val keyboardActions = KeyboardActions(onDone = {
        if (onDone != null) {
            keyboardController?.hide()
            onDone()
        }
    }, onSearch = {
        if (onSearch != null) {
            keyboardController?.hide()
            onSearch()
        }
    })

    val textColor = TextFieldDefaults.colors(
        focusedContainerColor = Color.Transparent,
        unfocusedContainerColor = Color.Transparent,
        errorContainerColor = Color.Transparent,
        focusedIndicatorColor = Color.LightGray.copy(alpha = 1f),
        unfocusedIndicatorColor = Color.LightGray.copy(alpha = 1f),
        errorIndicatorColor = Color.Red.copy(alpha = 0.5f),
        disabledIndicatorColor = Color.LightGray.copy(alpha = 1f),
    )
    when (type) {
        InputType.EMAIL -> {
            TextInput(
                label = label.uppercase(),
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.Transparent),
                value = getValue(),
                keyboardOptions = getKeyboardOption(KeyboardType.Email),
                onValueChange = {
                    onValueChanged(it)
                },
                textFieldType = textFieldType,
                onBlur = {
                    onBlur()
                },
                readOnly = readOnly,
                enabled = enabled,
                isError = isError(),
                errorMessage = getErrorMessage(),
                colors = textColor,
                keyboardActions = keyboardActions,
                focusRequester = focusRequester
            )
        }

        InputType.PASSWORD -> {
            PasswordInput(
                label = label,
                modifier = modifier
                    .fillMaxWidth()
                    .background(Color.Transparent),
                value = getValue(),
                onValueChange = {
                    onValueChanged(it)
                },
                onBlur = {
                    onBlur()
                },
                enabled = enabled,
                readOnly = readOnly,
                keyboardOptions = getKeyboardOption(KeyboardType.Password),
                errorMessage = getErrorMessage(),
                isError = isError(),
                colors = textColor,
                keyboardActions = keyboardActions,
                focusRequester = focusRequester,
                textFieldType = textFieldType

            )
        }

        InputType.CHECKBOX -> {
            CheckboxInput(label = label, isChecked = getCheckValue(), onCheckedChange = {
                onValueChanged(it)
            })
        }

        InputType.TIME_PICKER -> {
            TimePickerInput(
                label = label,
                modifier = modifier
                    .fillMaxWidth()
                    .background(Color.Transparent),
                onValueChange = {
                    onValueChanged(it)
                },
                value = getDate(),
                onBlur = {
                    onBlur()
                },
                errorMessage = getErrorMessage(),
                isError = isError(),
                colors = textColor,
                keyboardActions = keyboardActions,
                focusRequester = focusRequester,
                textFieldType = textFieldType
            )
        }

        InputType.DATE_PICKER -> {
            DatePickerInput(
                label = label,
                modifier = modifier
                    .fillMaxWidth()
                    .background(Color.Transparent),
                value = getDate(),
                onValueChange = {
                    onValueChanged(it)
                },
                onBlur = {
                    onBlur()
                },
                errorMessage = getErrorMessage(),
                isError = isError(),
                colors = textColor,
                keyboardActions = keyboardActions,
                focusRequester = focusRequester,
                textFieldType = textFieldType,
                type = datePickerType
            )
        }

        InputType.DROP_DOWN -> {
            DropDownInput(
                menuItems = menuItems,
                label = label,
                value = getValue(),
                onValueChanged = {
                    onValueChanged(it.name)
                },
            )
        }

        InputType.NUMBER, InputType.TEXT -> {
            val keyboardType =
                if (type == InputType.NUMBER) getKeyboardOption(KeyboardType.Number) else getKeyboardOption(
                    KeyboardType.Text
                )



            TextInput(
                label = label,
                modifier = modifier.fillMaxWidth(),
                value = getValue(),
                keyboardOptions = keyboardType,
                onValueChange = {
                    onValueChanged(it)
                },
                readOnly = readOnly,
                enabled = enabled,
                textStyle = textStyle,
                errorMessage = getErrorMessage(),
                isError = isError(),
                colors = textColor,
                keyboardActions = keyboardActions,
                onBlur = {
                    onBlur()
                },
                focusRequester = focusRequester,
                textFieldType = textFieldType,
                leadingIcon = leadingIcon,
                trailingIcon = trailingIcon,
                placeHolder = placeHolder
            )
        }
    }
}

