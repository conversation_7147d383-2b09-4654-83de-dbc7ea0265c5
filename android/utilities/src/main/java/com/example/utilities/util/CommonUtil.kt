package com.example.utilities.util

import android.content.Context
import android.net.Uri
import android.util.Log
import androidx.browser.customtabs.CustomTabsIntent

fun String.onlyString(): String {
    return this.replace("[^a-zA-Z0-9.%]".toRegex(), " ").replace("\\s+".toRegex(), " ").trim()
}

fun String.camelCase(): String {
    return this
        .lowercase()
        .split(" ")
        .joinToString(" ") { it.replaceFirstChar { char -> char.uppercase() } }
}

fun String.normalize(): String {
    return this
        .replace(Regex("([a-z])([A-Z])"), "$1 $2")
        .replaceFirstChar { it.uppercase() }
}

fun String.single(): String {
    return if (contains(" ")) {  // Check if there are spaces
        split(" ")  // Split by space
            .filter { it.isNotEmpty() }  // Remove empty substrings if any
            .joinToString("") { it.substring(0, 1) }  // Get first char of each substring
    } else {
        this  // If no spaces, return the original string
    }
}

fun openInAppBrowser(context: Context, url: String) {
    val builder: CustomTabsIntent.Builder = CustomTabsIntent.Builder()
    val customTabsIntent: CustomTabsIntent = builder.build()
    customTabsIntent.launchUrl(context, Uri.parse(url))
    Log.d("In App Browse", url)
}