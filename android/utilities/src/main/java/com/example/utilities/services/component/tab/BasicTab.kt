package com.example.utilities.services.component.tab

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.PagerState
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.sage.ui.shared.component.tab.TabTitle

object BasicTabDefaults {
    val titleHeight get() = 35.dp
    val contentPadding = PaddingValues(0.dp)
}

@Composable
fun <T> BasicTab(
    modifier: Modifier = Modifier,
    state: PagerState,
    titleHeight: Dp = BasicTabDefaults.titleHeight,
    contentPadding: PaddingValues = BasicTabDefaults.contentPadding,
    colors: TabColors = TabDefaults.tabColors(),
    titles: List<T>,
    content: @Composable () -> Unit
) {
    val selectedTabIndex = state.currentPage

    Column {
        TabRow(
            containerColor = colors.titleBackgroundColor,
            selectedTabIndex = selectedTabIndex,
            modifier = modifier
                .height(titleHeight),
            indicator = { tabPositions ->
                TabRowDefaults.PrimaryIndicator(
                    modifier = Modifier
                        .tabIndicatorOffset(tabPositions[selectedTabIndex])
                        .padding(top = 20.dp),
                    width = 60.dp,
                    color = Color.White,
                    height = 2.dp
                )
            },
            divider = {}
        ) {
            titles.forEachIndexed { index, tab ->
                Tab(
                    interactionSource = NoRippleInteractionSource(),
                    selectedContentColor = colors.selectedTitleColor,
                    unselectedContentColor = colors.unselectedTitleColor,
                    modifier = Modifier,
                    selected = selectedTabIndex == index,
                    onClick = { state.requestScrollToPage(index) },
                    content = {
                        when (tab) {
                            is TabTitle.TextTitle -> {
                                Text(
                                    text = tab.title.uppercase(),
                                    fontSize = 20.sp,
                                    color = Color.Gray
                                )
                            }
                        }
                    }
                )
            }
        }

        Box(
            modifier = Modifier
                .padding(contentPadding)
                .background(colors.contentBackgroundColor)
        ) {
            content()
        }

    }
}