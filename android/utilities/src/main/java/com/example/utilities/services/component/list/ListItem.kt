package com.example.utilities.services.component.list

import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.exponentialDecay
import androidx.compose.animation.core.spring
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.AnchoredDraggableState
import androidx.compose.foundation.gestures.DraggableAnchors
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.anchoredDraggable
import androidx.compose.foundation.gestures.animateTo
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.math.roundToInt

enum class DragAnchors { Center, End }

private object DragDefaults {
    const val POSITIONAL_THRESHOLD = 10f
    const val VELOCITY_THRESHOLD = 10f
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun DraggableItem(
    onActionOpened: (Int) -> Unit,
    isDraggable: Boolean,
    index: Int,
    iconWidth: Dp = 40.dp,
    showAction: Boolean,
    actionModifier: Modifier = Modifier,
    actionContent: @Composable (RowScope.(Modifier) -> Unit),
    content: @Composable (() -> Unit),
) {
    val scope = rememberCoroutineScope()
    val state = rememberDraggableState(
        iconWidth = iconWidth,
        positionalThreshold = DragDefaults.POSITIONAL_THRESHOLD,
        velocityThreshold = DragDefaults.VELOCITY_THRESHOLD
    )

    HandleActionState(
        state = state,
        onActionOpened = { scope.launch { withContext(IO) { onActionOpened(it) } } },
        index = index,
        showAction = showAction
    )

    Box(modifier = Modifier) {
        Box(
            modifier = actionModifier
                .matchParentSize()
                .background(Transparent),
        ) {
            Row(
                modifier = Modifier
                    .width(iconWidth + 20.dp)
                    .fillMaxHeight()
                    .align(Alignment.CenterEnd),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                actionContent(Modifier.padding(start = 20.dp))
            }
        }

        DraggableContentBox(state, isDraggable, content)
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun rememberDraggableState(
    iconWidth: Dp,
    positionalThreshold: Float,
    velocityThreshold: Float
): AnchoredDraggableState<DragAnchors> {
    val density = LocalDensity.current
    val screenSizePx = with(density) { iconWidth.toPx() }
    val anchors = remember {
        derivedStateOf {
            DraggableAnchors {
                DragAnchors.Center at 0f
                DragAnchors.End at screenSizePx
            }
        }
    }
    return remember {
        derivedStateOf {
            AnchoredDraggableState(
                initialValue = DragAnchors.Center,
                positionalThreshold = { positionalThreshold },
                velocityThreshold = { velocityThreshold },
                snapAnimationSpec = spring(
                    dampingRatio = Spring.DampingRatioLowBouncy,
                    stiffness = Spring.StiffnessMedium,
                    visibilityThreshold = 2f
                ),
                decayAnimationSpec = exponentialDecay()
            )
        }.value.apply {
            updateAnchors(anchors.value)
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun HandleActionState(
    state: AnchoredDraggableState<DragAnchors>,
    onActionOpened: (Int) -> Unit,
    index: Int,
    showAction: Boolean
) {
    LaunchedEffect(
        key1 = state.progress(DragAnchors.End, DragAnchors.Center) < 1,
        key2 = state.progress(DragAnchors.Center, DragAnchors.End) > 0
    ) {
        if (state.progress(DragAnchors.End, DragAnchors.Center) < 1.0f && !showAction
        ) {
            withContext(IO) {
                onActionOpened(index)
            }
        } else if (
            state.targetValue == DragAnchors.Center && showAction
        ) {
            withContext(IO) {
                onActionOpened(-1)
            }
        }
    }
    LaunchedEffect(showAction) {
        if (!showAction && state.currentValue != DragAnchors.Center) {
            withContext(IO) {
                state.animateTo(DragAnchors.Center)
            }
        }
    }
}


@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun DraggableContentBox(
    state: AnchoredDraggableState<DragAnchors>,
    isDraggable: Boolean,
    content: @Composable () -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }
    Box(
        modifier = Modifier
            .offset {
                IntOffset(
                    x = -state
                        .requireOffset()
                        .roundToInt(),
                    y = 0
                )
            }
            .anchoredDraggable(
                state = state,
                orientation = Orientation.Horizontal,
                enabled = isDraggable,
                reverseDirection = true,
                interactionSource = interactionSource
            )
    ) {
        content()
    }
}
