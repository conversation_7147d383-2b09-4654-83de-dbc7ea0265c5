package com.example.utilities.config

import com.example.utilities.modal.PickerItem

val months =
    listOf(
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec"
    )

val hours =
    (1..11).map { PickerItem(it.toString(), it.toString()) }

val minutes = (0..59).map { PickerItem(it.toString(), it.toString()) }

val amPm = listOf("AM", "PM").map { PickerItem(it.toString(), it) }