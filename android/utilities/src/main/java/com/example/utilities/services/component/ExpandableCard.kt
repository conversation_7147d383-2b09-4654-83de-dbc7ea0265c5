package com.example.utilities.services.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.Card
import androidx.compose.material3.CardColors
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.dp
import com.example.utilities.modal.ExpandedCardState
import com.example.utilities.services.theme.PrimaryColor
import com.example.utilities.util.NoRippleInteractionSource
import kotlin.random.Random

@Composable
fun rememberExpandedCardState() = remember {
    ExpandedCardState()
}


@Composable
fun ExpandableCard(
    id: Long = Random.nextLong(),
    title: @Composable () -> Unit,
    onCLick: (() -> Unit)? = null,
    shape: Shape = CardDefaults.shape,
    colors: CardColors = CardDefaults.cardColors(),
    state: ExpandedCardState = rememberExpandedCardState(),
    arrowColor: Color = PrimaryColor,
    enabled: Boolean = true,
    border: BorderStroke? = null,
    content: (@Composable () -> Unit)? = null,
) {
    val rotationState by animateFloatAsState(
        targetValue = if (state.value.contains(id)) 180f else 0f, label = "Rotation Animation"
    )

    Card(
        modifier = Modifier
            .fillMaxWidth(),
        onClick = {
            if (onCLick != null)
                onCLick()
            else
                state.addExpandedState(id)
        },
        enabled = enabled,
        shape = shape,
        colors = colors,
        border = border
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth(),
            verticalArrangement = Arrangement.Center,
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                contentAlignment = Alignment.Center
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    title()
                }
                if (enabled) {
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = null,
                        modifier = Modifier
                            .align(Alignment.CenterEnd)
                            .rotate(rotationState)
                            .size(20.dp)
                            .clickable(
                                interactionSource = NoRippleInteractionSource(),
                                indication = null
                            ) {
                                state.addExpandedState(id)
                            },
                        tint = arrowColor,
                    )
                }
            }
            if (content != null) {
                AnimatedVisibility(
                    modifier = Modifier
                        .fillMaxWidth(),
                    visible = state.value.contains(id) && enabled
                ) {
                    Spacer(modifier = Modifier.height(10.dp))
                    Column {
                        content()
                    }
                }
            }
        }

    }
}