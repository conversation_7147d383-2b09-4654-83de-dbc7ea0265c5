package com.example.utilities.util

import android.util.Patterns
import java.util.Calendar
import kotlin.toString

object ValidationType {
    const val MATCH_PASSWORD = "matchPassword"
    const val NOT_SAME = "notSame"
    const val REQUIRED = "required"
    const val EMAIL = "email"
    const val MIN_LENGTH = "min_length"
    const val MAX_LENGTH = "max_length"
    const val PATTERN = "pattern"
    const val NOT_IN_RANGE = "not_in_range"
    const val GREATER = "greatere"
    const val LESSER = "lesser"
    const val FUTURE_TIME = "future_time"
}

object FormValidations {

    fun required(): (formField: FormField<Any>) -> String? = { formField ->
        when (val value = formField.value) {
            is String -> {
                if (value.toString().isEmpty()) ValidationType.REQUIRED else null
            }

            is Boolean -> {
                if (value == false) ValidationType.REQUIRED else null
            }

            else -> {
                null
            }
        }
    }

    fun minLength(length: Int): (formField: FormField<Any>) -> String? = { formField ->
        val value = formField.value
        if (value.toString().length < length) {
            ValidationType.MIN_LENGTH
        } else {
            null
        }
    }

    fun maxLength(length: Int): (formField: FormField<Any>) -> String? = { formField ->
        val value = formField.value
        if (value.toString().length > length) {
            ValidationType.MAX_LENGTH
        } else {
            null
        }
    }

    fun email(): (formField: FormField<Any>) -> String? = { formField ->
        val value = formField.value
        if (!Patterns.EMAIL_ADDRESS.matcher(value.toString())
                .matches()
        ) {
            ValidationType.EMAIL
        } else {
            null
        }
    }

    fun pattern(pattern: String): (formField: FormField<Any>) -> String? = { formField ->
        val value = formField.value
        if (!pattern.toRegex().matches(value.toString())) {
            ValidationType.PATTERN
        } else {
            null
        }
    }

    fun matchPassword(fieldName: String): (formField: FormField<Any>) -> String? = { formField ->
        val value = formField.value
        val form = formField.parent

        if (value.toString()
                .isNotEmpty() && (form != null && form.getValue<Any>(fieldName) != value)
        ) {
            ValidationType.MATCH_PASSWORD
        } else {
            null
        }
    }

    fun notSame(fieldName: String): (formField: FormField<Any>) -> String? = { formField ->
        val value = formField.value
        val form = formField.parent

        if (value.toString()
                .isNotEmpty() && (form != null && form.getValue<Any>(fieldName) == value)
        ) {
            ValidationType.NOT_SAME
        } else {
            null
        }
    }

    fun range(range: IntRange): (formField: FormField<Any>) -> String? = { formField ->
        try {
            val value = formField.value.toString().toInt()
            if (value !in range) {
                ValidationType.NOT_IN_RANGE
            } else {
                null
            }
        } catch (e: NumberFormatException) {
            // Handle the case where the value is not convertible to an Int
            ValidationType.NOT_IN_RANGE
        }
    }

    fun greaterThan(fieldName: String): (formField: FormField<Any>) -> String? = { formField ->
        val value: Int = formField.value.toString().toIntOrNull() ?: 0
        val form = formField.parent
        if (value.toString()
                .isNotEmpty() && form != null
        ) {
            val comparingValue: Int = form.getValue<Int>(fieldName).toString().toIntOrNull() ?: 0
            if (value < comparingValue)
                ValidationType.LESSER
            else
                null
        } else
            null

    }

    fun lesserThan(fieldName: String): (formField: FormField<Any>) -> String? = { formField ->
        val value: Int = formField.value.toString().toIntOrNull() ?: 0
        val form = formField.parent
        if (value.toString()
                .isNotEmpty() && form != null
        ) {
            val comparingValue: Int = form.getValue<Int>(fieldName).toString().toIntOrNull() ?: 0
            if (value > comparingValue)
                ValidationType.GREATER
            else
                null
        } else
            null

    }

    fun futureTime(): (formField: FormField<Any>) -> String? = { formField ->
        val value = formField.value as Calendar
        val currTime = CalendarUtil.getCurrentDate()
        if (value.timeInMillis > currTime.timeInMillis) {
            ValidationType.FUTURE_TIME
        } else {
            null
        }
    }

}