package com.example.utilities.services.viewmodel

import androidx.compose.ui.unit.Dp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavOptionsBuilder
import com.example.utilities.config.GG_TOAST_DELIMITER
import com.example.utilities.modal.ActionButton
import com.example.utilities.modal.Dialog
import com.example.utilities.modal.Modal
import com.example.utilities.modal.Toast
import com.example.utilities.modal.interfaces.IAppUtility
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
open class UtilitiesViewmodel @Inject constructor() :
    ViewModel() {
    @Inject
    lateinit var appUtility: IAppUtility

    fun navigateTo(
        route: Any,
        baseRoute: Any,
        builder: NavOptionsBuilder.() -> Unit = {}
    ) {
        viewModelScope.launch {
            <EMAIL>(route, builder, baseRoute)
        }
    }

    fun navigateBack(
        route: Any? = null,
        inclusive: Boolean = false,
        baseRoute: Any
    ) {
        viewModelScope.launch {
            <EMAIL>(route, inclusive, baseRoute)
        }
    }

    fun setFabPadding(padding: Dp) {
        viewModelScope.launch {
            <EMAIL>(padding)
        }
    }

    fun setLoader(loader: String?) {
        viewModelScope.launch {
            if (this@UtilitiesViewmodel::appUtility.isInitialized)
                <EMAIL>(loader)
        }
    }

    fun setDialog(dialog: Dialog?) {
        viewModelScope.launch {
            if (this@UtilitiesViewmodel::appUtility.isInitialized)
                <EMAIL>(dialog)
        }
    }

    fun setAlert(message: String) {
        viewModelScope.launch {
            if (this@UtilitiesViewmodel::appUtility.isInitialized)
                <EMAIL>(
                    Dialog(
                        title = "Alert",
                        message = message,
                        confirmButton = ActionButton(
                            text = "Ok",
                            action = {
                                <EMAIL>(null)
                            }
                        )
                    )
                )
        }
    }

    fun setToastMessage(message: String, delimiter: String = GG_TOAST_DELIMITER) {
        viewModelScope.launch {
            if (this@UtilitiesViewmodel::appUtility.isInitialized)
                <EMAIL>(
                    Toast(
                        message = message,
                        delimiter = delimiter
                    )
                )
        }
    }

    fun setModal(modal: Modal?) {
        viewModelScope.launch {
            if (this@UtilitiesViewmodel::appUtility.isInitialized)
                <EMAIL>(
                    modal?.copy(
                        onDismiss = {
                            <EMAIL>(null)
                            modal.onDismiss()
                        }
                    ))
        }
    }

    fun clearToastMessage() {
        viewModelScope.launch {
            if (this@UtilitiesViewmodel::appUtility.isInitialized)
                <EMAIL>()
        }
    }

}