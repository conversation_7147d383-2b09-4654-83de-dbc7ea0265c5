package com.example.utilities.services.component.tab

import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.graphics.drawscope.ContentDrawScope
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.sage.ui.shared.component.tab.TabTitle
import com.example.utilities.services.theme.BorderColor
import com.example.utilities.services.theme.PrimaryColor
import kotlinx.coroutines.launch

fun ContentDrawScope.drawWithLayer(block: ContentDrawScope.() -> Unit) {
    with(drawContext.canvas.nativeCanvas) {
        val checkPoint = saveLayer(null, null)
        block()
        restoreToCount(checkPoint)
    }
}

@Composable
fun <T> BoxedTab(
    modifier: Modifier = Modifier,
    pagerState: PagerState = rememberPagerState(pageCount = { titles.size }),
    currentState: T? = null,
    titles: List<T>,
    containerColor: Color = Color(0xfff3f3f2),
    tabContent: (@Composable (Int, T) -> Unit)? = null,
    content: (@Composable () -> Unit)? = null
) {
    val scope = rememberCoroutineScope()
    val index = titles.indexOf(currentState)
    val currentPage = if (index != -1) index else pagerState.currentPage
    val density = LocalDensity.current
    val scrollState = rememberScrollState()

    // Determine if we need scrolling based on list size
    val needsScrolling = titles.size > 3
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        BoxWithConstraints(
            modifier
                .padding(8.dp)
                .height(36.dp)
                .clip(RoundedCornerShape(4.dp))
                .background(containerColor)
        ) {
            if (titles.isNotEmpty()) {
                val maxWidth = this.maxWidth
                // Fixed tab width for non-scrolling tabs (divide by size)
                // For scrolling tabs, we'll use a fixed tab width
                val tabWidth = if (needsScrolling) 120.dp else maxWidth / minOf(titles.size, 3)

                val indicatorOffset by animateDpAsState(
                    targetValue = tabWidth * currentPage,
                    animationSpec = tween(durationMillis = 150, easing = FastOutSlowInEasing),
                    label = "indicator offset"
                )
                val border = BorderStroke(
                    1.dp,
                    BorderColor
                )

                val indicatorOffsetFloat = with(density) { indicatorOffset.toPx() }
                val tabWidthPx = with(density) { tabWidth.toPx() }

                // Apply scrolling modifier if needed
                val rowModifier = if (needsScrolling) {
                    Modifier
                        .fillMaxWidth()
                        .horizontalScroll(scrollState)
                        .drawWithContent {
                            drawRoundRect(
                                topLeft = Offset(x = indicatorOffsetFloat, 0f),
                                size = Size(tabWidthPx, size.height),
                                color = White,
                            )

                            drawWithLayer {
                                drawContent()

                                // Highlight for selected tab
                                drawRoundRect(
                                    topLeft = Offset(x = indicatorOffsetFloat, 0f),
                                    size = Size(tabWidthPx, size.height),
                                    color = PrimaryColor,
                                    blendMode = BlendMode.SrcOut
                                )
                            }
                        }
                } else {
                    Modifier
                        .fillMaxWidth()
                        .drawWithContent {
                            drawRoundRect(
                                topLeft = Offset(x = indicatorOffsetFloat, 0f),
                                size = Size(tabWidthPx, size.height),
                                color = White,
                            )

                            drawWithLayer {
                                drawContent()

                                drawRoundRect(
                                    topLeft = Offset(x = indicatorOffsetFloat, 0f),
                                    size = Size(tabWidthPx, size.height),
                                    color = PrimaryColor,
                                    blendMode = BlendMode.SrcOut
                                )
                            }
                        }
                }

                // No separate Box for the indicator - it's now handled entirely in the drawing

                Row(modifier = rowModifier) {
                    Row(modifier = Modifier.width(if (needsScrolling) (tabWidth * titles.size) else maxWidth)) {
                        titles.forEachIndexed { index, item ->
                            Box(
                                modifier = Modifier
                                    .border(border)
                                    .width(tabWidth)
                                    .fillMaxHeight()
                                    .clickable(
                                        interactionSource = NoRippleInteractionSource(),
                                        indication = null
                                    ) {
                                        scope.launch {
                                            pagerState.scrollToPage(index)
                                        }
                                    },
                                contentAlignment = Alignment.Center
                            ) {
                                when (item) {
                                    is TabTitle.TextTitle -> {
                                        Text(
                                            text = item.title,
                                            fontSize = 14.sp,
                                            color = if (currentPage == index) White else Color.Gray
                                        )
                                    }

                                    else -> {
                                        tabContent?.invoke(index, item)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        content?.invoke()
    }
}