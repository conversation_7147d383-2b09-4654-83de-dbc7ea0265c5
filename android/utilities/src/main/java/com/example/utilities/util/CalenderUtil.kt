package com.example.utilities.util

import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone

object CalendarUtil {
    fun setDate(
        day: Int = 1,
        month: Int = 0,
        year: Int = 2000,
    ): Calendar {
        val calendar = Calendar.getInstance()
        calendar.set(year, month, day)
        return calendar
    }

    fun getCurrentDate(): Calendar {
        return Calendar.getInstance()
    }

    // Get the current date as a Calendar object
    fun getTime(): String {
        val calender = Calendar.getInstance()
        return this.dateStringFromTimeStamp(calender.timeInMillis.toString(), "hh:mm a")
    }

    // Get the current date as a Calendar object
    fun getDate(): String {
        val calender = Calendar.getInstance()
        return this.dateStringFromTimeStamp(calender.timeInMillis.toString(), "dd - MM")

    }


    fun calendarToString(calendar: Calendar, format: String? = null): String {
        val simpleDateFormat = SimpleDateFormat(format ?: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US)
        simpleDateFormat.timeZone = TimeZone.getTimeZone("UTC") // Set time zone to IST
        return simpleDateFormat.format(calendar.time)
    }


    fun stringToCalender(dateString: String): Calendar {
        return if (dateString.isBlank() || dateString == "null") {
            this.getCurrentDate()
        } else {
            val simpleDateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US)
            simpleDateFormat.timeZone = TimeZone.getTimeZone("UTC")
            val calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC"))
            calendar.time = simpleDateFormat.parse(dateString)!!
            calendar
        }
    }

    fun timeStringToCalender(timeString: String, pattern: String = "yyyy-MM-dd"): Calendar {
        val simpleDateFormat = SimpleDateFormat(pattern, Locale.US)
        val calendar = Calendar.getInstance()
        calendar.time = simpleDateFormat.parse(timeString)!!
        return calendar
    }

    fun timestampToMonthYear(timestamp: Long, format: String? = null): String {
        val simpleDateFormat = SimpleDateFormat(format ?: "MMM-yyyy", Locale.US)
        return simpleDateFormat.format(timestamp)
    }

    fun dateStringFromTimeStamp(timeStamp: String, pattern: String = "yyyy-MM-dd hh:mm a"): String {
        val timestampValue = timeStamp.toLongOrNull() ?: 0L
        val date = Date(timestampValue)
        val formatter = SimpleDateFormat(pattern, Locale.getDefault()).apply {
            timeZone = TimeZone.getDefault()
        }
        return formatter.format(date)
    }

    fun getDaysInMonth(month: String, year: Int): Int {
        return when (month) {
            "Feb" -> if (isLeapYear(year)) 29 else 28
            "Apr", "Jun", "Sep", "Nov" -> 30
            else -> 31
        }
    }

    fun isLeapYear(year: Int): Boolean = when {
        year % 4 != 0 -> false
        year % 100 == 0 -> year % 400 == 0
        else -> true
    }
}
