package com.example.utilities.services.component.tab

import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.Interaction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Color.Companion.White
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.dmdbrands.sage.ui.shared.component.tab.TabTitle
import com.example.utilities.services.theme.BackgroundSecondaryColor
import com.example.utilities.services.theme.OpenSansFont
import com.example.utilities.services.theme.PrimaryColor
import com.example.utilities.services.theme.TextPrimaryColor
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.launch


@Composable
fun <T> SimpleCurvedTab(
    modifier: Modifier = Modifier,
    state: PagerState,
    titleHeight: Dp = SimpleCurvedTabDefaults.titleHeight,
    contentPadding: PaddingValues = SimpleCurvedTabDefaults.contentPadding,
    titles: List<T>,
    content: @Composable (Int) -> Unit,
) {
    val totalTabsInIndex = state.pageCount - 1
    val contentBorder = getContentCorners(state.currentPage, totalTabsInIndex)
    val scope = rememberCoroutineScope()

    fun changeTab(tab: Int) {
        scope.launch {
            state.requestScrollToPage(tab)
        }
    }

    Box(modifier = modifier) {
        Box(modifier = Modifier.background(Color.Transparent)) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(titleHeight)
            ) {
                Spacer(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(titleHeight)
                        .background(
                            Brush.verticalGradient(
                                colors = listOf(
                                    Color.Transparent,
                                    White
                                )
                            )
                        )
                )

                TabRow(
                    selectedTabIndex = state.currentPage,
                    indicator = {},
                    divider = {},
                    containerColor = Color.Transparent
                ) {
                    titles.forEachIndexed { index, tab ->
                        Tab(
                            selected = state.currentPage == index,
                            onClick = { changeTab(index) },
                            text = {
                                when (tab) {
                                    is TabTitle.TextTitle -> {
                                        Text(
                                            text = tab.title,
                                            fontFamily = OpenSansFont,
                                            fontSize = 14.sp,
                                            fontWeight = FontWeight.Medium,
                                            color = TextPrimaryColor
                                        )
                                    }

                                    is TabTitle.ComposableTitle -> tab.title()
                                }
                            },
                            selectedContentColor = PrimaryColor,
                            interactionSource = NoRippleInteractionSource(),
                            modifier = if (index == state.currentPage) {
                                Modifier
                                    .height(titleHeight)
                                    .clip(RoundedCornerShape(topStart = 10.dp, topEnd = 10.dp))
                                    .background(BackgroundSecondaryColor)
                            } else {
                                Modifier
                                    .height(titleHeight)
                                    .getTabBottomCorners(index, state.currentPage)
                            }
                        )
                    }
                }
            }
            Box(modifier = Modifier.padding(top = titleHeight)) {
                Card(
                    colors = CardDefaults.cardColors(containerColor = Color.Transparent),
                    shape = contentBorder,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Surface(
                        color = White, modifier = Modifier.padding(contentPadding)
                    ) {
                        content(state.currentPage)
                    }
                }
            }
        }
    }
}

fun Modifier.getTabBottomCorners(index: Int, activeTab: Int): Modifier {
    return when (index) {
        activeTab - 1 -> this.clip(RoundedCornerShape(bottomEnd = 10.dp))
        activeTab + 1 -> this.clip(RoundedCornerShape(bottomStart = 10.dp))
        else -> this
    }
}

private fun getContentCorners(activeTab: Int, totalTabs: Int): RoundedCornerShape {
    return when (activeTab) {
        0 -> RoundedCornerShape(
            topStart = 0.dp, topEnd = 10.dp, bottomStart = 10.dp, bottomEnd = 10.dp
        )

        totalTabs -> RoundedCornerShape(
            topStart = 10.dp, topEnd = 0.dp, bottomStart = 10.dp, bottomEnd = 10.dp
        )

        else -> RoundedCornerShape(
            topStart = 10.dp, topEnd = 10.dp, bottomStart = 10.dp, bottomEnd = 10.dp
        )
    }
}

class NoRippleInteractionSource : MutableInteractionSource {
    override val interactions = emptyFlow<Interaction>()
    override suspend fun emit(interaction: Interaction) {}
    override fun tryEmit(interaction: Interaction) = true
}


private object SimpleCurvedTabDefaults {
    val titleHeight get() = 50.dp
    val contentPadding get() = PaddingValues(5.dp)
}

