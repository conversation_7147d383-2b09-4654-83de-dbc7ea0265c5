package com.example.utilities.util

import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.input.OffsetMapping
import androidx.compose.ui.text.input.TransformedText
import androidx.compose.ui.text.input.VisualTransformation

class PasswordGlimpse : VisualTransformation {

    override fun filter(text: AnnotatedString): TransformedText {

        val originalText = text.text
        var formattedText = ""
        originalText.forEachIndexed { index, char ->
            if (index != originalText.lastIndex) {
                formattedText += '\u2022'
            } else {
                formattedText += char
            }
        }

        return TransformedText(
            text = AnnotatedString(formattedText),
            offsetMapping = OffsetMapping.Identity
        )
    }
}