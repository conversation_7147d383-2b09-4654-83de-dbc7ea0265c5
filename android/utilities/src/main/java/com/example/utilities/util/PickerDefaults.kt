package com.example.utilities.util

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import com.example.utilities.services.theme.OpenSansFont
import com.example.utilities.services.theme.PrimaryColor
import com.example.utilities.services.theme.TextPrimaryColor

object PickerDefaults {
    @Composable
    fun pickerColors(
        focusedColor: Color = PrimaryColor,
        unFocusedColor: Color = TextPrimaryColor,
        indicatorColor: Color = Color.Unspecified,
    ): PickerColors {
        return PickerColors(
            focusedColor = focusedColor,
            unFocusedColor = unFocusedColor,
            indicatorColor = indicatorColor
        )
    }

    @Composable
    fun pickerTextStyle(
        focusedStyle: TextStyle = TextStyle.Default.copy(
            fontSize = 22.sp,
            fontWeight = FontWeight.SemiBold,
            fontFamily = OpenSansFont
        ),
        unFocusedStyle: TextStyle = TextStyle.Default.copy(
            fontSize = 18.sp,
            fontWeight = FontWeight.Normal,
            fontFamily = OpenSansFont
        ),
        indicatorStyle: TextStyle = TextStyle.Default,
    ): PickerTextStyle {
        return PickerTextStyle(
            focusedStyle = focusedStyle,
            unFocusedStyle = unFocusedStyle,
            indicatorStyle = indicatorStyle
        )
    }
}


data class PickerColors(
    val focusedColor: Color,
    val unFocusedColor: Color,
    val indicatorColor: Color
)

data class PickerTextStyle(
    val focusedStyle: TextStyle,
    val unFocusedStyle: TextStyle,
    val indicatorStyle: TextStyle
)