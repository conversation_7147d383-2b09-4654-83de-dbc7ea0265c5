package com.example.utilities.services.component.prompt

import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.graphics.Color.Companion.Transparent
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.utilities.modal.NotificationState
import com.example.utilities.services.viewmodel.UtilitiesViewmodel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PromptManager(
    snackBarHostState: SnackbarHostState
) {
    val viewmodel: UtilitiesViewmodel = hiltViewModel()
    val notificationState by viewmodel.appUtility.notificationState.collectAsState(NotificationState())
    if (notificationState.toast != null) {
        ToastHandler(notificationState.toast, snackBarHostState, viewmodel::clearToastMessage)
    }

    if (notificationState.loader != null) {
        LoaderCard(notificationState.loader)
    }

    if (notificationState.dialog != null) {
        DialogCard(notificationState.dialog!!)
    }

    if (notificationState.modal != null) {
        Dialog(
            onDismissRequest = {
                notificationState.modal!!.onDismiss()
            },
            properties = notificationState.modal!!.properties
        ) {
            Card(
                elevation = CardDefaults.elevatedCardElevation(6.dp),
                colors = CardDefaults.cardColors(containerColor = Transparent)
            ) {
                notificationState.modal!!.content()
            }
        }
    }


}
