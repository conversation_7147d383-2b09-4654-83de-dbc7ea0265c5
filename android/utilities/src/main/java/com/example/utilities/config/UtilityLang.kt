package com.example.utilities.config

object UtilityLang {
    const val FILTER = "Filter"
    const val exit = "exit"
    const val ok = "OK"
    const val back = "BACK"
    const val cancel = "Cancel"
    const val next = "NEXT"
    const val select = "Select"
    const val complete = "COMPLETE"
    const val close = "Close"
    const val delete = "Delete"
    const val reTurn = "Return"
    const val manual = "manual"
    const val device = "device"
    const val create = "create"
    const val finish = "finish"
    const val tryAgain = "Try Again"
}

const val GG_TOAST_DELIMITER = "|"