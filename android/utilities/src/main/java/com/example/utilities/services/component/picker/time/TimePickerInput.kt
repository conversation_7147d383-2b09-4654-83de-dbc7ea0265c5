package com.example.utilities.services.component.picker.time

import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.TextFieldColors
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import com.example.utilities.services.component.input.TextFieldType
import com.example.utilities.services.component.input.TextInput
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

@Composable
fun TimePickerInput(
    value: Calendar = Calendar.getInstance(),
    onValueChange: ((Any) -> Unit)? = null,
    modifier: Modifier = Modifier,
    label: String,
    placeHolder: String = "",
    onBlur: (() -> Unit?)? = null,
    isError: Boolean = false,
    errorMessage: String? = null,
    colors: TextFieldColors = TextFieldDefaults.colors(),
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    focusRequester: FocusRequester = FocusRequester(),
    textFieldType: TextFieldType = TextFieldType.FILLED
) {
    var showTimePicker by remember { mutableStateOf(false) }
    val dateFormatToStore = SimpleDateFormat("hh:mm a", Locale.US)
    var timeText = dateFormatToStore.format(value.time)

    TextInput(
        value = timeText,
        modifier = modifier,
        onValueChange = {},
        label = label,
        readOnly = true,
        enabled = false,
        onClick = { showTimePicker = true },
        placeHolder = placeHolder,
        onBlur = onBlur,
        isError = isError,
        errorMessage = errorMessage,
        colors = colors,
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        focusRequester = focusRequester,
        textFieldType = textFieldType
    )
    if (showTimePicker) {
        TimePicker(
            onSelect = {
                if (onValueChange != null) {
                    onValueChange(it)
                }
            },
        ) {
            showTimePicker = false
        }
    }
}