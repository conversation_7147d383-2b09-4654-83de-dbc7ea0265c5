package com.example.utilities.services.component.input

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.selection.toggleable
import androidx.compose.material3.Checkbox
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp

@Composable
fun CheckboxInput(
    label: String,
    isChecked: Boolean = false,
    onCheckedChange: (status: Boolean) -> Unit = {}
) {
    val (checkedState, onStateChange) = remember { mutableStateOf(isChecked) }

    val valueChanged: (Boolean) -> Unit = {
        onStateChange(it)
        onCheckedChange(it)
    }

    Row(
        Modifier
            .height(56.dp)
            .toggleable(
                value = checkedState,
                onValueChange = {
                    valueChanged(it)
                },
                role = Role.Checkbox
            ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Checkbox(
            checked = checkedState,
            onCheckedChange = {
                valueChanged(it)
            },
        )
        Text(
            text = label.uppercase(),
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(start = 4.dp)
        )
    }
}