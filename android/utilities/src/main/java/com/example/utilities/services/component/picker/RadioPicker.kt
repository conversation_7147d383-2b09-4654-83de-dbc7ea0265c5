package com.example.utilities.services.component.picker

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.utilities.modal.PickerState
import com.example.utilities.services.theme.OpenSansFont
import com.example.utilities.services.theme.PrimaryColor
import kotlin.reflect.KProperty1

@Composable
fun <T> RadioPicker(
    modifier: Modifier = Modifier,
    items: List<T>,
    state: PickerState<T>,
    key: KProperty1<T, String>,
    customItemView: (@Composable (T, <PERSON>olean) -> Unit)? = null
) {
    Column(
        modifier = modifier,
    ) {
        items.forEach { item ->
            val isSelected = state.item == item

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { state.setItem(item) }
            ) {
                RadioButton(
                    selected = isSelected,
                    onClick = { state.setItem(item) },
                    colors = RadioButtonDefaults.colors(selectedColor = PrimaryColor)
                )
                if (customItemView != null) {
                    Box(modifier = Modifier.padding(start = 8.dp)) {
                        customItemView(item, isSelected)
                    }
                } else {
                    Text(
                        text = key.get(item),
                        fontFamily = OpenSansFont,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        }
    }
}
