package com.example.utilities.services.component.picker.time

import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import com.dmdbrands.sage.ui.shared.component.spinner.rememberPickerState
import com.example.utilities.config.amPm
import com.example.utilities.config.hours
import com.example.utilities.config.minutes
import com.example.utilities.modal.PickerItem
import com.example.utilities.services.component.picker.PickerSheet
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TimePicker(
    onSelect: (Any) -> Unit,
    onDismissRequest: () -> Unit,
) {
    val scope = rememberCoroutineScope()
    val hourState = rememberPickerState(hours[0])
    val minuteState = rememberPickerState(minutes[0])
    val amPmState = rememberPickerState(amPm[0])
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    fun closeBottomSheet() {
        scope.launch {
            if (!sheetState.isVisible) {
                onDismissRequest()
            }
        }
    }

    PickerSheet(
        onDismissRequest = { closeBottomSheet() },
        valueStates = listOf(
            hourState,
            minuteState,
            amPmState
        ),
        items = listOf(hours, minutes, amPm),
        key = PickerItem<String>::value,
        onItemChange = {

        }) {
        onSelect(it)
    }

}