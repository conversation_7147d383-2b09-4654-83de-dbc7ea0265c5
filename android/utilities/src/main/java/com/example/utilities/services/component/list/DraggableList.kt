package com.example.utilities.services.component.list

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.example.utilities.modal.DraggableListState
import com.example.utilities.services.component.verticalLazyColumnScrollbar

@Composable
fun rememberDraggableListState() = remember {
    DraggableListState(-1)
}

@Composable
fun <T> DraggableList(
    list: List<T>,
    draggableListState: DraggableListState,
    modifier: Modifier = Modifier,
    contentPadding: PaddingValues = PaddingValues(0.dp),
    iconWidth: Dp = 40.dp,
    userScrollEnabled: Boolean = true,
    actionModifier: Modifier = Modifier,
    actionContent: @Composable (RowScope.(Int, T, Modifier) -> Unit) = { _, _, _ -> },
    staticContent: (@Composable (Int, T) -> Unit)? = null,
    content: @Composable (T, Int) -> Unit,
) {
    val lazyListState = rememberLazyListState()
    LazyColumn(
        state = lazyListState,
        userScrollEnabled = userScrollEnabled,
        contentPadding = contentPadding,
        modifier = modifier
    ) {
        itemsIndexed(list) { index, anyItem ->
            Column(modifier = Modifier.fillMaxWidth()) {
                DraggableItem(
                    actionContent = { modifier ->
                        actionContent(index, anyItem, modifier)
                    },
                    isDraggable = !lazyListState.isScrollInProgress,
                    iconWidth = iconWidth,
                    index = index,
                    actionModifier = actionModifier,
                    onActionOpened = { draggableListState.set(it) },
                    showAction = draggableListState.openCardIndex == index,
                ) {
                    content(anyItem, index)
                }
                if (staticContent != null) {
                    staticContent(index, anyItem)
                }
            }
        }
    }
}

@Composable
fun <T> StaticList(
    list: List<T>,
    modifier: Modifier = Modifier,
    contentPadding: PaddingValues = PaddingValues(0.dp),
    userScrollEnabled: Boolean = true,
    scrollBarEnabled: Boolean = false,
    staticContent: (@Composable (Int, T) -> Unit)? = null,
    content: @Composable (Int, T) -> Unit,
) {
    val listState = rememberLazyListState()
    val updatedModifier =
        if (scrollBarEnabled) Modifier
            .verticalLazyColumnScrollbar(listState, showScrollBarTrack = false)
            .then(modifier) else modifier
    LazyColumn(
        state = listState,
        userScrollEnabled = userScrollEnabled,
        contentPadding = contentPadding,
        modifier = updatedModifier
    ) {
        itemsIndexed(list) { index, anyItem ->
            Column(modifier = Modifier.fillMaxWidth()) {
                content(index, anyItem)
                if (staticContent != null) {
                    staticContent(index, anyItem)
                }
            }
        }
    }
}


