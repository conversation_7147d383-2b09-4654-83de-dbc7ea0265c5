pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven {
            url =
                uri("https://maven.pkg.github.com/dmdbrands/ggBluetoothNativeLibrary") // Github Package
            credentials {
                username = System.getenv("GPR_USER") ?: ""
                password =
                    System.getenv("GPR_API_KEY") ?: ""
            }
        }
    }
}

rootProject.name = "GG Bluetooth Library"
include(":app")
include(":ggBluetoothLibrary")
include(":bluetoothWrapper")
include(":utilities")
